import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:share_plus/share_plus.dart';
import '../../../core/app_state.dart';
import '../../../config/app_theme.dart';
import '../../../config/constants.dart';
import '../../../services/api/translation_service.dart';
import '../../../utils/helpers.dart';
import 'document_translation_service.dart';

/// شاشة ترجمة المستندات
class DocumentTranslationScreen extends StatefulWidget {
  const DocumentTranslationScreen({super.key});

  @override
  State<DocumentTranslationScreen> createState() =>
      _DocumentTranslationScreenState();
}

class _DocumentTranslationScreenState extends State<DocumentTranslationScreen> {
  File? _selectedFile;
  String _extractedText = '';
  String _translatedText = '';
  String _sourceLanguage = 'ar';
  String _targetLanguage = 'en';
  bool _isLoading = false;
  bool _isExtracting = false;
  bool _isTranslating = false;
  File? _translatedFile;

  /// اختيار ملف
  Future<void> _pickFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'doc', 'docx', 'txt'],
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);

        setState(() {
          _selectedFile = file;
          _extractedText = '';
          _translatedText = '';
          _translatedFile = null;
          _isExtracting = true;
        });

        // استخراج النص من الملف
        await _extractTextFromFile(file);
      }
    } catch (e) {
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء اختيار الملف: $e',
          isError: true,
        );
      }
    }
  }

  /// استخراج النص من الملف
  Future<void> _extractTextFromFile(File file) async {
    try {
      final appState = Provider.of<AppState>(context, listen: false);
      final remoteConfig = appState.remoteConfig;

      final translationService = TranslationService(remoteConfig);
      final documentService = DocumentTranslationService(translationService);

      String text = '';

      // التحقق من نوع الملف
      final String fileName = file.path.toLowerCase();

      if (fileName.endsWith('.pdf')) {
        text = await documentService.extractTextFromPdf(file);
      } else if (fileName.endsWith('.txt')) {
        text = await file.readAsString();
      } else {
        throw Exception('نوع الملف غير مدعوم');
      }

      if (mounted) {
        setState(() {
          _extractedText = text;
          _isExtracting = false;
        });

        // اكتشاف لغة النص
        _detectLanguage(text);
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isExtracting = false;
        });

        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء استخراج النص: $e',
          isError: true,
        );
      }
    }
  }

  /// اكتشاف لغة النص
  Future<void> _detectLanguage(String text) async {
    if (text.isEmpty) return;

    try {
      final appState = Provider.of<AppState>(context, listen: false);
      final remoteConfig = appState.remoteConfig;

      final translationService = TranslationService(remoteConfig);

      final detectedLanguage = await translationService.detectLanguage(text);

      if (mounted) {
        setState(() {
          _sourceLanguage = detectedLanguage;

          // تعيين اللغة الهدف بناءً على اللغة المكتشفة
          if (_sourceLanguage == 'ar') {
            _targetLanguage = 'en';
          } else {
            _targetLanguage = 'ar';
          }
        });
      }
    } catch (e) {
      debugPrint('Error detecting language: $e');
    }
  }

  /// ترجمة النص
  Future<void> _translateText() async {
    if (_extractedText.isEmpty) {
      AppHelpers.showSnackBar(context, 'لا يوجد نص للترجمة', isError: true);
      return;
    }

    setState(() {
      _isTranslating = true;
    });

    try {
      final appState = Provider.of<AppState>(context, listen: false);
      final remoteConfig = appState.remoteConfig;

      final translationService = TranslationService(remoteConfig);
      final documentService = DocumentTranslationService(translationService);

      // ترجمة نص المستند
      final translatedText = await documentService.translateDocumentText(
        text: _extractedText,
        sourceLanguage: _sourceLanguage,
        targetLanguage: _targetLanguage,
      );

      if (mounted) {
        setState(() {
          _translatedText = translatedText;
          _isTranslating = false;
        });

        // إنشاء ملف PDF مترجم
        _createTranslatedPdf();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isTranslating = false;
        });

        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء الترجمة: $e',
          isError: true,
        );
      }
    }
  }

  /// إنشاء ملف PDF مترجم
  Future<void> _createTranslatedPdf() async {
    if (_extractedText.isEmpty ||
        _translatedText.isEmpty ||
        _selectedFile == null) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final appState = Provider.of<AppState>(context, listen: false);
      final remoteConfig = appState.remoteConfig;

      final translationService = TranslationService(remoteConfig);
      final documentService = DocumentTranslationService(translationService);

      // الحصول على اسم الملف بدون الامتداد
      final String fileName =
          _selectedFile!.path.split('/').last.split('.').first;

      // إنشاء ملف PDF مترجم
      final translatedFile = await documentService.createTranslatedPdf(
        originalText: _extractedText,
        translatedText: _translatedText,
        sourceLanguage: _getLanguageName(_sourceLanguage),
        targetLanguage: _getLanguageName(_targetLanguage),
        fileName: fileName,
      );

      if (mounted) {
        setState(() {
          _translatedFile = translatedFile;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء إنشاء ملف PDF مترجم: $e',
          isError: true,
        );
      }
    }
  }

  /// عرض ملف PDF المترجم
  void _viewTranslatedPdf() {
    if (_translatedFile == null) return;

    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => Scaffold(
              appBar: AppBar(
                title: const Text('المستند المترجم'),
                centerTitle: true,
                backgroundColor: AppTheme.primaryColor,
                actions: [
                  // زر المشاركة
                  IconButton(
                    icon: const Icon(Icons.share),
                    onPressed: _shareTranslatedFile,
                  ),
                ],
              ),
              body: PDFView(
                filePath: _translatedFile!.path,
                enableSwipe: true,
                swipeHorizontal: true,
                autoSpacing: false,
                pageFling: false,
                pageSnap: true,
                defaultPage: 0,
                fitPolicy: FitPolicy.BOTH,
                preventLinkNavigation: false,
                onError: (error) {
                  AppHelpers.showSnackBar(
                    context,
                    'حدث خطأ أثناء عرض الملف: $error',
                    isError: true,
                  );
                },
              ),
            ),
      ),
    );
  }

  /// مشاركة ملف PDF المترجم
  void _shareTranslatedFile() {
    if (_translatedFile == null) return;

    Share.shareXFiles([
      XFile(_translatedFile!.path),
    ], text: 'المستند المترجم من المترجم الذكي AI');
  }

  /// تبديل اللغات
  void _swapLanguages() {
    setState(() {
      final temp = _sourceLanguage;
      _sourceLanguage = _targetLanguage;
      _targetLanguage = temp;

      // إعادة تعيين النص المترجم
      _translatedText = '';
      _translatedFile = null;
    });
  }

  /// عرض مربع حوار اختيار اللغة
  Future<void> _showLanguageSelector(bool isSource) async {
    final selectedLanguage = await showModalBottomSheet<String>(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => _buildLanguageSelector(isSource),
    );

    if (selectedLanguage != null) {
      setState(() {
        if (isSource) {
          _sourceLanguage = selectedLanguage;
        } else {
          _targetLanguage = selectedLanguage;
        }

        // إعادة تعيين النص المترجم
        _translatedText = '';
        _translatedFile = null;
      });
    }
  }

  /// بناء مربع حوار اختيار اللغة
  Widget _buildLanguageSelector(bool isSource) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            isSource ? 'اختر اللغة المصدر' : 'اختر اللغة الهدف',
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: ListView.builder(
              itemCount: AppConstants.supportedLanguages.length,
              itemBuilder: (context, index) {
                final language = AppConstants.supportedLanguages[index];
                return ListTile(
                  leading: Text(
                    language['flag'] as String,
                    style: const TextStyle(fontSize: 24),
                  ),
                  title: Text(language['name'] as String),
                  onTap: () {
                    Navigator.pop(context, language['code'] as String);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على اسم اللغة من الرمز
  String _getLanguageName(String languageCode) {
    final language = AppConstants.supportedLanguages.firstWhere(
      (lang) => lang['code'] == languageCode,
      orElse: () => {'code': languageCode, 'name': languageCode, 'flag': '🏳️'},
    );

    return '${language['flag']} ${language['name']}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('ترجمة المستندات'),
        centerTitle: true,
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // اختيار اللغات
            Row(
              children: [
                // اللغة المصدر
                Expanded(
                  child: InkWell(
                    onTap: () => _showLanguageSelector(true),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        vertical: 12,
                        horizontal: 16,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _getLanguageName(_sourceLanguage),
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),

                // زر تبديل اللغات
                IconButton(
                  onPressed: _swapLanguages,
                  icon: const Icon(Icons.swap_horiz),
                  color: Theme.of(context).colorScheme.primary,
                ),

                // اللغة الهدف
                Expanded(
                  child: InkWell(
                    onTap: () => _showLanguageSelector(false),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        vertical: 12,
                        horizontal: 16,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _getLanguageName(_targetLanguage),
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // زر اختيار الملف
            ElevatedButton.icon(
              onPressed:
                  _isLoading || _isExtracting || _isTranslating
                      ? null
                      : _pickFile,
              icon: const Icon(Icons.file_upload),
              label: const Text('اختيار ملف'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                minimumSize: const Size(double.infinity, 56),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
            ),

            const SizedBox(height: 20),

            // معلومات الملف المحدد
            if (_selectedFile != null)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.description, color: AppTheme.primaryColor),
                    const SizedBox(width: 10),
                    Expanded(
                      child: Text(
                        _selectedFile!.path.split('/').last,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    if (_isExtracting)
                      const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                  ],
                ),
              ),

            const SizedBox(height: 20),

            // زر الترجمة
            ElevatedButton(
              onPressed:
                  _isLoading ||
                          _isExtracting ||
                          _isTranslating ||
                          _extractedText.isEmpty
                      ? null
                      : _translateText,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                minimumSize: const Size(double.infinity, 56),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              child:
                  _isTranslating
                      ? const CircularProgressIndicator(color: Colors.white)
                      : const Text(
                        'ترجم المستند',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
            ),

            const SizedBox(height: 20),

            // حالة الترجمة
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(16),
                ),
                child:
                    _isLoading
                        ? const Center(child: CircularProgressIndicator())
                        : _translatedFile != null
                        ? _buildTranslatedFileView()
                        : _buildStatusView(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عرض حالة الترجمة
  Widget _buildStatusView() {
    if (_extractedText.isEmpty) {
      return const Center(
        child: Text(
          'اختر ملفًا للترجمة',
          style: TextStyle(fontSize: 16, color: AppTheme.onSurfaceVariant),
        ),
      );
    }

    if (_isTranslating) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 20),
            Text(
              'جاري ترجمة المستند...',
              style: TextStyle(fontSize: 16, color: AppTheme.onSurfaceVariant),
            ),
          ],
        ),
      );
    }

    if (_translatedText.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.check_circle,
              color: AppTheme.successColor,
              size: 48,
            ),
            const SizedBox(height: 20),
            const Text(
              'تم استخراج النص بنجاح',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppTheme.onBackground,
              ),
            ),
            const SizedBox(height: 10),
            Text(
              'عدد الأحرف: ${_extractedText.length}',
              style: const TextStyle(
                fontSize: 14,
                color: AppTheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              'اضغط على زر "ترجم المستند" للبدء',
              style: TextStyle(fontSize: 14, color: AppTheme.onSurfaceVariant),
            ),
          ],
        ),
      );
    }

    return const Center(child: CircularProgressIndicator());
  }

  /// بناء عرض الملف المترجم
  Widget _buildTranslatedFileView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.check_circle,
            color: AppTheme.successColor,
            size: 48,
          ),
          const SizedBox(height: 20),
          const Text(
            'تمت ترجمة المستند بنجاح',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppTheme.onBackground,
            ),
          ),
          const SizedBox(height: 20),
          ElevatedButton.icon(
            onPressed: _viewTranslatedPdf,
            icon: const Icon(Icons.visibility),
            label: const Text('عرض المستند المترجم'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
          const SizedBox(height: 10),
          TextButton.icon(
            onPressed: _shareTranslatedFile,
            icon: const Icon(Icons.share),
            label: const Text('مشاركة المستند'),
          ),
        ],
      ),
    );
  }
}
