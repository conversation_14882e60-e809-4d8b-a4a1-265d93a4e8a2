import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import '../../models/usage_model.dart';

/// خدمة تتبع استخدام الخدمات
class UsageTrackingService {
  // مثيل Firestore
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  // معرف المستخدم
  final String? _userId;
  
  // حد الاستخدام المجاني (بالدقائق)
  static const double _freeTierMinutesLimit = 30.0;
  
  /// المُنشئ
  UsageTrackingService({String? userId}) : _userId = userId ?? FirebaseAuth.instance.currentUser?.uid;
  
  /// الحصول على حد الاستخدام المجاني
  double get freeTierMinutesLimit => _freeTierMinutesLimit;
  
  /// تسجيل استخدام خدمة
  Future<void> trackUsage(String serviceType, double durationInMinutes) async {
    if (_userId == null) {
      debugPrint('لا يمكن تتبع الاستخدام: المستخدم غير مسجل الدخول');
      return;
    }
    
    try {
      final now = DateTime.now();
      final currentPeriod = '${now.year}-${now.month.toString().padLeft(2, '0')}';
      
      // الحصول على بيانات الاستخدام الحالية
      final usageDoc = await _firestore
          .collection('users')
          .doc(_userId)
          .collection('usage')
          .doc(currentPeriod)
          .get();
      
      UsageModel usage;
      if (usageDoc.exists) {
        usage = UsageModel.fromFirestore(usageDoc.data()!, _userId, currentPeriod);
      } else {
        usage = UsageModel.empty(_userId);
      }
      
      // تحديث بيانات الاستخدام
      UsageModel updatedUsage;
      switch (serviceType) {
        case 'voice_translation':
          updatedUsage = usage.addUsage(voiceMinutes: durationInMinutes);
          break;
        case 'speaker_recognition':
          updatedUsage = usage.addUsage(speakerMinutes: durationInMinutes);
          break;
        case 'real_time_translation':
          updatedUsage = usage.addUsage(realTimeMinutes: durationInMinutes);
          break;
        case 'image_translation':
          updatedUsage = usage.addUsage(imageCount: 1);
          break;
        case 'document_translation':
          updatedUsage = usage.addUsage(documentCount: 1);
          break;
        case 'ai_chat':
          updatedUsage = usage.addUsage(aiChatCount: 1);
          break;
        default:
          updatedUsage = usage.addUsage(voiceMinutes: durationInMinutes);
      }
      
      // حفظ بيانات الاستخدام المحدثة
      await _firestore
          .collection('users')
          .doc(_userId)
          .collection('usage')
          .doc(currentPeriod)
          .set(updatedUsage.toFirestore());
      
      // حفظ تفاصيل الاستخدام
      final usageDetail = UsageDetailModel(
        userId: _userId,
        serviceType: serviceType,
        durationMinutes: durationInMinutes > 0 ? durationInMinutes : null,
        count: durationInMinutes > 0 ? null : 1,
        timestamp: now,
      );
      
      await _firestore
          .collection('users')
          .doc(_userId)
          .collection('usage')
          .doc(currentPeriod)
          .collection('details')
          .add(usageDetail.toFirestore());
    } catch (e) {
      debugPrint('خطأ في تتبع الاستخدام: $e');
    }
  }
  
  /// التحقق مما إذا كان المستخدم قد تجاوز حد الاستخدام المجاني
  Future<bool> hasExceededFreeUsage() async {
    if (_userId == null) {
      return false;
    }
    
    try {
      final now = DateTime.now();
      final currentPeriod = '${now.year}-${now.month.toString().padLeft(2, '0')}';
      
      final doc = await _firestore
          .collection('users')
          .doc(_userId)
          .collection('usage')
          .doc(currentPeriod)
          .get();
      
      if (!doc.exists) {
        return false;
      }
      
      final usage = UsageModel.fromFirestore(doc.data()!, _userId, currentPeriod);
      return usage.totalMinutes >= _freeTierMinutesLimit;
    } catch (e) {
      debugPrint('خطأ في التحقق من حد الاستخدام: $e');
      return false;
    }
  }
  
  /// الحصول على إحصائيات الاستخدام الشهرية
  Future<UsageModel> getMonthlyUsageStats() async {
    if (_userId == null) {
      return UsageModel.empty('guest');
    }
    
    try {
      final now = DateTime.now();
      final currentPeriod = '${now.year}-${now.month.toString().padLeft(2, '0')}';
      
      final doc = await _firestore
          .collection('users')
          .doc(_userId)
          .collection('usage')
          .doc(currentPeriod)
          .get();
      
      if (!doc.exists) {
        return UsageModel.empty(_userId);
      }
      
      return UsageModel.fromFirestore(doc.data()!, _userId, currentPeriod);
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات الاستخدام: $e');
      return UsageModel.empty(_userId);
    }
  }
  
  /// الحصول على تفاصيل الاستخدام
  Future<List<UsageDetailModel>> getUsageDetails() async {
    if (_userId == null) {
      return [];
    }
    
    try {
      final now = DateTime.now();
      final currentPeriod = '${now.year}-${now.month.toString().padLeft(2, '0')}';
      
      final snapshot = await _firestore
          .collection('users')
          .doc(_userId)
          .collection('usage')
          .doc(currentPeriod)
          .collection('details')
          .orderBy('timestamp', descending: true)
          .limit(50)
          .get();
      
      return snapshot.docs
          .map((doc) => UsageDetailModel.fromFirestore(doc.data(), doc.id))
          .toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على تفاصيل الاستخدام: $e');
      return [];
    }
  }
  
  /// التحقق مما إذا كان يمكن استخدام الخدمة
  Future<bool> canUseService(bool isSubscribed) async {
    // إذا كان المستخدم مشتركًا، يمكنه استخدام الخدمة بدون قيود
    if (isSubscribed) {
      return true;
    }
    
    // التحقق مما إذا كان المستخدم قد تجاوز حد الاستخدام المجاني
    return !(await hasExceededFreeUsage());
  }
}
