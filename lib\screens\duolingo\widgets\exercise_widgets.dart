import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../models/duolingo_models.dart';

/// ويدجت الاختيار المتعدد
class MultipleChoiceWidget extends StatelessWidget {
  final Exercise exercise;
  final String? selectedAnswer;
  final Function(String) onAnswerSelected;
  final bool showFeedback;

  const MultipleChoiceWidget({
    super.key,
    required this.exercise,
    this.selectedAnswer,
    required this.onAnswerSelected,
    this.showFeedback = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: exercise.options.map((option) {
        return _buildOptionCard(option);
      }).toList(),
    );
  }

  Widget _buildOptionCard(String option) {
    final isSelected = selectedAnswer == option;
    final isCorrect = option == exercise.correctAnswer;
    
    Color backgroundColor = Colors.white;
    Color borderColor = Colors.grey.shade300;
    
    if (showFeedback) {
      if (isSelected && isCorrect) {
        backgroundColor = Colors.green.shade50;
        borderColor = Colors.green;
      } else if (isSelected && !isCorrect) {
        backgroundColor = Colors.red.shade50;
        borderColor = Colors.red;
      } else if (isCorrect) {
        backgroundColor = Colors.green.shade50;
        borderColor = Colors.green;
      }
    } else if (isSelected) {
      backgroundColor = Colors.blue.shade50;
      borderColor = Colors.blue;
    }

    return GestureDetector(
      onTap: showFeedback ? null : () {
        HapticFeedback.lightImpact();
        onAnswerSelected(option);
      },
      child: Container(
        width: double.infinity,
        margin: const EdgeInsets.only(bottom: 15),
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(15),
          border: Border.all(color: borderColor, width: 2),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected ? borderColor : Colors.grey.shade400,
                  width: 2,
                ),
                color: isSelected ? borderColor : Colors.transparent,
              ),
              child: isSelected
                  ? const Icon(
                      Icons.check,
                      size: 16,
                      color: Colors.white,
                    )
                  : null,
            ),
            const SizedBox(width: 15),
            Expanded(
              child: Text(
                option,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  color: isSelected ? borderColor : Colors.black87,
                ),
              ),
            ),
            if (showFeedback && isCorrect)
              const Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 24,
              )
            else if (showFeedback && isSelected && !isCorrect)
              const Icon(
                Icons.cancel,
                color: Colors.red,
                size: 24,
              ),
          ],
        ),
      ),
    );
  }
}

/// ويدجت الترجمة
class TranslationWidget extends StatelessWidget {
  final Exercise exercise;
  final String? selectedAnswer;
  final Function(String) onAnswerSelected;
  final bool showFeedback;

  const TranslationWidget({
    super.key,
    required this.exercise,
    this.selectedAnswer,
    required this.onAnswerSelected,
    this.showFeedback = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // النص المراد ترجمته
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          margin: const EdgeInsets.only(bottom: 30),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(15),
            border: Border.all(color: Colors.blue.shade200),
          ),
          child: Text(
            exercise.question,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        
        // خيارات الترجمة
        Expanded(
          child: GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 2.5,
              crossAxisSpacing: 10,
              mainAxisSpacing: 10,
            ),
            itemCount: exercise.options.length,
            itemBuilder: (context, index) {
              return _buildTranslationOption(exercise.options[index]);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildTranslationOption(String option) {
    final isSelected = selectedAnswer == option;
    final isCorrect = option == exercise.correctAnswer;
    
    Color backgroundColor = Colors.white;
    Color borderColor = Colors.grey.shade300;
    
    if (showFeedback) {
      if (isSelected && isCorrect) {
        backgroundColor = Colors.green.shade50;
        borderColor = Colors.green;
      } else if (isSelected && !isCorrect) {
        backgroundColor = Colors.red.shade50;
        borderColor = Colors.red;
      } else if (isCorrect) {
        backgroundColor = Colors.green.shade50;
        borderColor = Colors.green;
      }
    } else if (isSelected) {
      backgroundColor = Colors.blue.shade50;
      borderColor = Colors.blue;
    }

    return GestureDetector(
      onTap: showFeedback ? null : () {
        HapticFeedback.lightImpact();
        onAnswerSelected(option);
      },
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: borderColor, width: 2),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: Text(
            option,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: isSelected ? borderColor : Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }
}

/// ويدجت الاستماع
class ListeningWidget extends StatelessWidget {
  final Exercise exercise;
  final String? selectedAnswer;
  final Function(String) onAnswerSelected;
  final bool showFeedback;
  final VoidCallback onPlayAudio;

  const ListeningWidget({
    super.key,
    required this.exercise,
    this.selectedAnswer,
    required this.onAnswerSelected,
    this.showFeedback = false,
    required this.onPlayAudio,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // زر تشغيل الصوت
        Container(
          margin: const EdgeInsets.only(bottom: 30),
          child: GestureDetector(
            onTap: onPlayAudio,
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF1CB0F6), Color(0xFF0E8FCC)],
                ),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.blue.withValues(alpha: 0.3),
                    spreadRadius: 5,
                    blurRadius: 20,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: const Icon(
                Icons.volume_up,
                color: Colors.white,
                size: 50,
              ),
            ),
          ),
        ),
        
        const Text(
          'اضغط لسماع الصوت مرة أخرى',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
        ),
        
        const SizedBox(height: 30),
        
        // خيارات الإجابة
        Expanded(
          child: Column(
            children: exercise.options.map((option) {
              return _buildListeningOption(option);
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildListeningOption(String option) {
    final isSelected = selectedAnswer == option;
    final isCorrect = option == exercise.correctAnswer;
    
    Color backgroundColor = Colors.white;
    Color borderColor = Colors.grey.shade300;
    
    if (showFeedback) {
      if (isSelected && isCorrect) {
        backgroundColor = Colors.green.shade50;
        borderColor = Colors.green;
      } else if (isSelected && !isCorrect) {
        backgroundColor = Colors.red.shade50;
        borderColor = Colors.red;
      } else if (isCorrect) {
        backgroundColor = Colors.green.shade50;
        borderColor = Colors.green;
      }
    } else if (isSelected) {
      backgroundColor = Colors.blue.shade50;
      borderColor = Colors.blue;
    }

    return GestureDetector(
      onTap: showFeedback ? null : () {
        HapticFeedback.lightImpact();
        onAnswerSelected(option);
      },
      child: Container(
        width: double.infinity,
        margin: const EdgeInsets.only(bottom: 15),
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(15),
          border: Border.all(color: borderColor, width: 2),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Icon(
              Icons.hearing,
              color: isSelected ? borderColor : Colors.grey.shade400,
              size: 24,
            ),
            const SizedBox(width: 15),
            Expanded(
              child: Text(
                option,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  color: isSelected ? borderColor : Colors.black87,
                ),
              ),
            ),
            if (showFeedback && isCorrect)
              const Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 24,
              )
            else if (showFeedback && isSelected && !isCorrect)
              const Icon(
                Icons.cancel,
                color: Colors.red,
                size: 24,
              ),
          ],
        ),
      ),
    );
  }
}

/// ويدجت الكتابة
class WritingWidget extends StatefulWidget {
  final Exercise exercise;
  final Function(String) onAnswerSubmitted;
  final bool showFeedback;

  const WritingWidget({
    super.key,
    required this.exercise,
    required this.onAnswerSubmitted,
    this.showFeedback = false,
  });

  @override
  State<WritingWidget> createState() => _WritingWidgetState();
}

class _WritingWidgetState extends State<WritingWidget> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // حقل الكتابة
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(15),
            border: Border.all(color: Colors.grey.shade300, width: 2),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                spreadRadius: 1,
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TextField(
            controller: _controller,
            focusNode: _focusNode,
            decoration: const InputDecoration(
              hintText: 'اكتب ترجمتك هنا...',
              border: InputBorder.none,
              hintStyle: TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
            onChanged: (value) {
              if (value.isNotEmpty) {
                widget.onAnswerSubmitted(value);
              }
            },
            onSubmitted: widget.onAnswerSubmitted,
          ),
        ),
        
        const SizedBox(height: 20),
        
        // لوحة مفاتيح مساعدة (اختيارية)
        if (widget.exercise.metadata.containsKey('helpWords'))
          _buildHelpKeyboard(),
      ],
    );
  }

  Widget _buildHelpKeyboard() {
    final helpWords = widget.exercise.metadata['helpWords'] as List<String>? ?? [];
    
    return Container(
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Wrap(
        spacing: 10,
        runSpacing: 10,
        children: helpWords.map((word) {
          return GestureDetector(
            onTap: () {
              final currentText = _controller.text;
              final newText = currentText.isEmpty ? word : '$currentText $word';
              _controller.text = newText;
              _controller.selection = TextSelection.fromPosition(
                TextPosition(offset: newText.length),
              );
              widget.onAnswerSubmitted(newText);
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Text(
                word,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}
