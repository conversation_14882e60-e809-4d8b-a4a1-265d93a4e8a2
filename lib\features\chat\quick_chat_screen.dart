import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:flutter/services.dart';

import '../../config/app_theme.dart';
import '../../config/constants.dart';
import '../../services/api/gemini_chat_service.dart' as gemini_service;
import '../../utils/app_helpers.dart';

/// شاشة الدردشة السريعة مع الذكاء الاصطناعي
class QuickChatScreen extends StatefulWidget {
  const QuickChatScreen({super.key});

  @override
  State<QuickChatScreen> createState() => _QuickChatScreenState();
}

class ChatMessage {
  final String role;
  final String content;
  final DateTime timestamp;

  ChatMessage({
    required this.role,
    required this.content,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() => {
    'role': role,
    'content': content,
    'timestamp': timestamp.toIso8601String(),
  };

  factory ChatMessage.fromJson(Map<String, dynamic> json) => ChatMessage(
    role: json['role'],
    content: json['content'],
    timestamp: DateTime.parse(json['timestamp']),
  );
}

class _QuickChatScreenState extends State<QuickChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final List<ChatMessage> _messages = [];
  final String _geminiApiKey = AppConstants.keyGeminiApiKeyPaid;
  bool _isTyping = false;
  late gemini_service.GeminiChatService _chatService;

  @override
  void initState() {
    super.initState();
    _chatService = gemini_service.GeminiChatService(apiKey: _geminiApiKey);
    _loadChatHistory();
    if (_messages.isEmpty) {
      _addBotMessage('مرحباً بك في المساعد الذكي! كيف يمكنني مساعدتك اليوم؟');
    }
  }

  Future<void> _loadChatHistory() async {
    final prefs = await SharedPreferences.getInstance();
    final history = prefs.getStringList('ai_chat_history') ?? [];
    setState(() {
      _messages.clear();
      _messages.addAll(history.map((e) => ChatMessage.fromJson(jsonDecode(e))));
    });
  }

  Future<void> _saveChatHistory() async {
    final prefs = await SharedPreferences.getInstance();
    final history = _messages.map((e) => jsonEncode(e.toJson())).toList();
    await prefs.setStringList('ai_chat_history', history);
  }

  /// إضافة رسالة من المستخدم
  void _addUserMessage(String message) {
    setState(() {
      _messages.add(
        ChatMessage(role: 'user', content: message, timestamp: DateTime.now()),
      );
    });
    _saveChatHistory();
    _scrollToBottom();
  }

  /// إضافة رسالة من الروبوت
  void _addBotMessage(String message) {
    setState(() {
      _messages.add(
        ChatMessage(
          role: 'assistant',
          content: message,
          timestamp: DateTime.now(),
        ),
      );
    });
    _saveChatHistory();
    _scrollToBottom();
  }

  /// مسح سجل الدردشة
  void _clearChat() async {
    setState(() {
      _messages.clear();
    });
    await _saveChatHistory();
    _addBotMessage('تم مسح سجل الدردشة. كيف يمكنني مساعدتك؟');
  }

  /// التمرير إلى أسفل المحادثة
  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  /// تحويل ChatMessage من الدردشة إلى ChatMessage الخاص بالخدمة
  List<gemini_service.ChatMessage> _toGeminiMessages(
    List<ChatMessage> messages,
  ) {
    return messages
        .map(
          (m) => gemini_service.ChatMessage(role: m.role, content: m.content),
        )
        .toList();
  }

  /// إرسال رسالة
  void _sendMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty || _isTyping) return;

    _addUserMessage(message);
    _messageController.clear();

    setState(() {
      _isTyping = true;
    });

    try {
      // تحويل رسائل المحادثة إلى التنسيق المطلوب للخدمة
      final chatHistory = _toGeminiMessages(_messages);

      final response = await _chatService.getChatResponseWithContext(
        message: message,
        responseType: 'general',
        chatHistory:
            chatHistory.length > 1
                ? chatHistory.sublist(0, chatHistory.length - 1)
                : null,
      );

      if (mounted) {
        _addBotMessage(response);
      }
    } catch (e) {
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء الاتصال بالذكاء الاصطناعي: $e',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isTyping = false;
        });
      }
    }
  }

  /// تنسيق التاريخ
  String _formatTimestamp(DateTime timestamp) {
    return DateFormat('HH:mm').format(timestamp);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المساعد الذكي'),
        centerTitle: true,
        backgroundColor: AppTheme.primaryColor,
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.delete_outline),
            tooltip: 'مسح المحادثة',
            onPressed: _clearChat,
          ),
        ],
      ),
      body: Column(
        children: [
          // منطقة المحادثة
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: ListView.builder(
                controller: _scrollController,
                itemCount: _messages.length,
                itemBuilder: (context, index) {
                  final message = _messages[index];
                  return _buildMessageItem(message);
                },
              ),
            ),
          ),

          // مؤشر الكتابة
          if (_isTyping)
            Container(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              alignment: Alignment.centerLeft,
              child: Row(
                children: [
                  const Text(
                    'جاري الكتابة',
                    style: TextStyle(color: Colors.grey, fontSize: 12),
                  ),
                  const SizedBox(width: 8),
                  _buildTypingIndicator(),
                ],
              ),
            ),

          // حقل إدخال الرسالة
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(26),
                  blurRadius: 10,
                  offset: const Offset(0, -3),
                ),
              ],
            ),
            child: Row(
              children: [
                // حقل الإدخال
                Expanded(
                  child: TextField(
                    controller: _messageController,
                    decoration: const InputDecoration(
                      hintText: 'اكتب رسالتك هنا...',
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                    ),
                    textInputAction: TextInputAction.send,
                    onSubmitted: (_) => _sendMessage(),
                    maxLines: null,
                  ),
                ),

                // زر الإرسال
                IconButton(
                  icon: const Icon(Icons.send),
                  color: AppTheme.primaryColor,
                  onPressed: _isTyping ? null : _sendMessage,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء مؤشر الكتابة
  Widget _buildTypingIndicator() {
    return Row(
      children: List.generate(
        3,
        (index) => Container(
              width: 6,
              height: 6,
              margin: const EdgeInsets.symmetric(horizontal: 2),
              decoration: BoxDecoration(
                color: Colors.grey,
                borderRadius: BorderRadius.circular(3),
              ),
            )
            .animate(onPlay: (controller) => controller.repeat())
            .scaleY(
              begin: 0.5,
              end: 1.5,
              duration: 600.ms,
              curve: Curves.easeInOut,
            )
            .then(delay: (index * 100).ms),
      ),
    );
  }

  /// بناء عنصر الرسالة
  Widget _buildMessageItem(ChatMessage message) {
    final isUser = message.role == 'user';
    return Align(
          alignment: isUser ? Alignment.centerRight : Alignment.centerLeft,
          child: GestureDetector(
            onLongPress: () {
              Clipboard.setData(ClipboardData(text: message.content));
              AppHelpers.showSnackBar(context, 'تم نسخ الرسالة');
            },
            child: Container(
              margin: const EdgeInsets.only(bottom: 16, top: 8),
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.75,
              ),
              decoration: BoxDecoration(
                color: isUser ? AppTheme.primaryColor : Colors.grey.shade200,
                borderRadius: BorderRadius.circular(16),
              ),
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // نص الرسالة
                  Text(
                    message.content,
                    style: TextStyle(
                      fontSize: 16,
                      color: isUser ? Colors.white : Colors.black87,
                    ),
                  ),

                  const SizedBox(height: 4),

                  // توقيت الرسالة
                  Text(
                    _formatTimestamp(message.timestamp),
                    style: TextStyle(
                      fontSize: 10,
                      color:
                          isUser ? Colors.white.withAlpha(179) : Colors.black54,
                    ),
                  ),
                ],
              ),
            ),
          ),
        )
        .animate()
        .fadeIn(duration: 300.ms)
        .slideY(begin: 0.2, end: 0, duration: 300.ms);
  }
}
