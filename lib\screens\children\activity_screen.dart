import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/educational_content.dart';
import '../../services/educational_service.dart';

/// شاشة النشاط التفاعلي
class ActivityScreen extends StatefulWidget {
  final LessonActivity activity;
  final EducationalService educationalService;
  final VoidCallback? onCompleted;

  const ActivityScreen({
    super.key,
    required this.activity,
    required this.educationalService,
    this.onCompleted,
  });

  @override
  State<ActivityScreen> createState() => _ActivityScreenState();
}

class _ActivityScreenState extends State<ActivityScreen>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late AnimationController _feedbackController;
  late Animation<double> _progressAnimation;
  late Animation<double> _feedbackAnimation;

  int _currentItemIndex = 0;
  int _score = 0;
  int _correctAnswers = 0;
  List<String> _userAnswers = [];
  bool _isAnswered = false;
  bool _showFeedback = false;
  String? _selectedAnswer;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeActivity();
  }

  @override
  void dispose() {
    _progressController.dispose();
    _feedbackController.dispose();
    super.dispose();
  }

  /// تهيئة الرسوم المتحركة
  void _initializeAnimations() {
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _feedbackController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _progressAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _progressController, curve: Curves.easeInOut),
    );

    _feedbackAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _feedbackController, curve: Curves.elasticOut),
    );
  }

  /// تهيئة النشاط
  void _initializeActivity() {
    _userAnswers = List.filled(widget.activity.items.length, '');
    _progressController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            _buildProgressBar(),
            Expanded(child: _buildActivityContent()),
            _buildBottomControls(),
          ],
        ),
      ),
    );
  }

  /// رأس الشاشة
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.purple.shade400, Colors.blue.shade400],
        ),
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: () => _showExitDialog(),
            icon: const Icon(Icons.close, color: Colors.white),
          ),
          Expanded(
            child: Column(
              children: [
                Text(
                  widget.activity.titleAr,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 5),
                Text(
                  widget.activity.instructionsAr,
                  style: const TextStyle(fontSize: 14, color: Colors.white70),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(15),
            ),
            child: Text(
              '$_score نقطة',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// شريط التقدم
  Widget _buildProgressBar() {
    final progress = (_currentItemIndex + 1) / widget.activity.items.length;

    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'السؤال ${_currentItemIndex + 1}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '${widget.activity.items.length} من',
                style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
              ),
            ],
          ),
          const SizedBox(height: 10),
          AnimatedBuilder(
            animation: _progressAnimation,
            builder: (context, child) {
              return LinearProgressIndicator(
                value: progress * _progressAnimation.value,
                backgroundColor: Colors.grey.shade200,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.blue.shade400),
                minHeight: 8,
              );
            },
          ),
        ],
      ),
    );
  }

  /// محتوى النشاط
  Widget _buildActivityContent() {
    if (widget.activity.items.isEmpty) {
      return const Center(child: Text('لا توجد عناصر في هذا النشاط'));
    }

    final currentItem = widget.activity.items[_currentItemIndex];

    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // السؤال
          _buildQuestionSection(currentItem),

          const SizedBox(height: 30),

          // الخيارات أو المحتوى التفاعلي
          Expanded(child: _buildInteractiveContent(currentItem)),

          // ردود الفعل
          if (_showFeedback) _buildFeedback(),
        ],
      ),
    );
  }

  /// قسم السؤال
  Widget _buildQuestionSection(ActivityItem item) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 2,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // الصورة إذا كانت متوفرة
          if (item.imagePath != null) ...[
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                image: DecorationImage(
                  image: AssetImage(item.imagePath!),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            const SizedBox(height: 15),
          ],

          // السؤال
          Text(
            item.questionAr,
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),

          // زر النطق
          const SizedBox(height: 10),
          IconButton(
            onPressed: () => _speakQuestion(item.questionAr),
            icon: Icon(Icons.volume_up, color: Colors.blue.shade400, size: 28),
          ),
        ],
      ),
    );
  }

  /// المحتوى التفاعلي
  Widget _buildInteractiveContent(ActivityItem item) {
    switch (widget.activity.type) {
      case ActivityType.multipleChoice:
        return _buildMultipleChoice(item);
      case ActivityType.matching:
        return _buildMatching(item);
      case ActivityType.listening:
        return _buildListening(item);
      default:
        return _buildMultipleChoice(item);
    }
  }

  /// اختيار متعدد
  Widget _buildMultipleChoice(ActivityItem item) {
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.2,
        crossAxisSpacing: 15,
        mainAxisSpacing: 15,
      ),
      itemCount: item.options.length,
      itemBuilder: (context, index) {
        final option = item.options[index];
        final isSelected = _selectedAnswer == option;
        final isCorrect = option == item.correctAnswer;

        Color backgroundColor = Colors.white;
        Color borderColor = Colors.grey.shade300;

        if (_isAnswered) {
          if (isCorrect) {
            backgroundColor = Colors.green.shade50;
            borderColor = Colors.green;
          } else if (isSelected && !isCorrect) {
            backgroundColor = Colors.red.shade50;
            borderColor = Colors.red;
          }
        } else if (isSelected) {
          backgroundColor = Colors.blue.shade50;
          borderColor = Colors.blue;
        }

        return GestureDetector(
          onTap: _isAnswered ? null : () => _selectAnswer(option),
          child: Container(
            decoration: BoxDecoration(
              color: backgroundColor,
              borderRadius: BorderRadius.circular(15),
              border: Border.all(color: borderColor, width: 2),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  spreadRadius: 1,
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  option,
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color:
                        _isAnswered && isCorrect
                            ? Colors.green
                            : _isAnswered && isSelected && !isCorrect
                            ? Colors.red
                            : Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                IconButton(
                  onPressed: () => _speakOption(option),
                  icon: Icon(
                    Icons.volume_up,
                    color: Colors.grey.shade600,
                    size: 20,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// مطابقة
  Widget _buildMatching(ActivityItem item) {
    return const Center(
      child: Text(
        'نشاط المطابقة\nسيتم تطويره قريباً',
        style: TextStyle(fontSize: 18),
        textAlign: TextAlign.center,
      ),
    );
  }

  /// استماع
  Widget _buildListening(ActivityItem item) {
    return const Center(
      child: Text(
        'نشاط الاستماع\nسيتم تطويره قريباً',
        style: TextStyle(fontSize: 18),
        textAlign: TextAlign.center,
      ),
    );
  }

  /// ردود الفعل
  Widget _buildFeedback() {
    final isCorrect =
        _selectedAnswer ==
        widget.activity.items[_currentItemIndex].correctAnswer;

    return AnimatedBuilder(
      animation: _feedbackAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _feedbackAnimation.value,
          child: Container(
            margin: const EdgeInsets.only(top: 20),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: isCorrect ? Colors.green.shade50 : Colors.red.shade50,
              borderRadius: BorderRadius.circular(15),
              border: Border.all(
                color: isCorrect ? Colors.green : Colors.red,
                width: 2,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  isCorrect ? Icons.check_circle : Icons.cancel,
                  color: isCorrect ? Colors.green : Colors.red,
                  size: 30,
                ),
                const SizedBox(width: 15),
                Expanded(
                  child: Text(
                    isCorrect ? 'ممتاز! إجابة صحيحة' : 'حاول مرة أخرى',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: isCorrect ? Colors.green : Colors.red,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// أدوات التحكم السفلية
  Widget _buildBottomControls() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          if (_currentItemIndex > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: _previousQuestion,
                child: const Text('السابق'),
              ),
            ),

          if (_currentItemIndex > 0) const SizedBox(width: 15),

          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: _isAnswered ? _nextQuestion : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue.shade400,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 15),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: Text(
                _currentItemIndex == widget.activity.items.length - 1
                    ? 'إنهاء'
                    : 'التالي',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// اختيار إجابة
  void _selectAnswer(String answer) {
    if (_isAnswered) return;

    HapticFeedback.lightImpact();

    setState(() {
      _selectedAnswer = answer;
      _isAnswered = true;
      _showFeedback = true;
    });

    _userAnswers[_currentItemIndex] = answer;

    // تحديث النتيجة
    final isCorrect =
        answer == widget.activity.items[_currentItemIndex].correctAnswer;
    if (isCorrect) {
      _correctAnswers++;
      _score += 10; // 10 نقاط لكل إجابة صحيحة
    }

    // تشغيل رسوم متحركة للردود
    _feedbackController.forward();

    // تشغيل صوت الرد
    _playFeedbackSound(isCorrect);
  }

  /// السؤال التالي
  void _nextQuestion() {
    if (_currentItemIndex < widget.activity.items.length - 1) {
      setState(() {
        _currentItemIndex++;
        _isAnswered = false;
        _showFeedback = false;
        _selectedAnswer = null;
      });

      _feedbackController.reset();
    } else {
      _completeActivity();
    }
  }

  /// السؤال السابق
  void _previousQuestion() {
    if (_currentItemIndex > 0) {
      setState(() {
        _currentItemIndex--;
        _isAnswered = _userAnswers[_currentItemIndex].isNotEmpty;
        _selectedAnswer =
            _userAnswers[_currentItemIndex].isNotEmpty
                ? _userAnswers[_currentItemIndex]
                : null;
        _showFeedback = _isAnswered;
      });
    }
  }

  /// إكمال النشاط
  void _completeActivity() {
    final percentage = (_correctAnswers / widget.activity.items.length) * 100;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            title: Text(percentage >= 70 ? '🎉 ممتاز!' : '💪 حاول مرة أخرى'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'النتيجة: $_correctAnswers من ${widget.activity.items.length}',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 10),
                Text(
                  'النسبة: ${percentage.toInt()}%',
                  style: const TextStyle(fontSize: 16),
                ),
              ],
            ),
            actions: [
              if (percentage < 70)
                TextButton(
                  onPressed: _restartActivity,
                  child: const Text('إعادة المحاولة'),
                ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  Navigator.pop(context);
                  widget.onCompleted?.call();
                },
                child: const Text('متابعة'),
              ),
            ],
          ),
    );
  }

  /// إعادة بدء النشاط
  void _restartActivity() {
    Navigator.pop(context);
    setState(() {
      _currentItemIndex = 0;
      _score = 0;
      _correctAnswers = 0;
      _userAnswers = List.filled(widget.activity.items.length, '');
      _isAnswered = false;
      _showFeedback = false;
      _selectedAnswer = null;
    });
    _feedbackController.reset();
  }

  /// نطق السؤال
  Future<void> _speakQuestion(String text) async {
    await widget.educationalService.speakArabic(text);
  }

  /// نطق الخيار
  Future<void> _speakOption(String text) async {
    await widget.educationalService.speakArabic(text);
  }

  /// تشغيل صوت الرد
  void _playFeedbackSound(bool isCorrect) {
    // يمكن إضافة أصوات ردود الفعل هنا
    HapticFeedback.mediumImpact();
  }

  /// حوار الخروج
  void _showExitDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('هل تريد الخروج؟'),
            content: const Text('سيتم فقدان التقدم الحالي'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  Navigator.pop(context);
                },
                child: const Text('خروج'),
              ),
            ],
          ),
    );
  }
}
