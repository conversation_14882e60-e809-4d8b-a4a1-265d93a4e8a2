import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math';

/// نشاط عد الأرقام التفاعلي
class NumbersCountingActivity extends StatefulWidget {
  final Function(int score) onComplete;

  const NumbersCountingActivity({super.key, required this.onComplete});

  @override
  State<NumbersCountingActivity> createState() => _NumbersCountingActivityState();
}

class _NumbersCountingActivityState extends State<NumbersCountingActivity>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _itemController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _itemAnimation;

  int _currentNumber = 1;
  final int _maxNumber = 10;
  int _score = 0;
  int _selectedAnswer = 0;
  bool _showResult = false;
  bool _gameCompleted = false;

  List<String> _items = [];
  List<int> _answerOptions = [];

  final List<String> _emojis = ['🍎', '🌟', '🎈', '🍭', '🎁', '🌸', '🦋', '🍓'];

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _generateQuestion();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _itemController.dispose();
    super.dispose();
  }

  void _initAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _itemController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _itemAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _itemController,
      curve: Curves.easeOutBack,
    ));

    _animationController.forward();
    _itemController.forward();
  }

  void _generateQuestion() {
    if (_currentNumber > _maxNumber) {
      setState(() {
        _gameCompleted = true;
      });
      return;
    }

    setState(() {
      // إنشاء العناصر للعد
      _items = List.generate(
        _currentNumber,
        (index) => _emojis[Random().nextInt(_emojis.length)],
      );

      // إنشاء خيارات الإجابة
      _answerOptions = [_currentNumber];
      while (_answerOptions.length < 4) {
        int option = Random().nextInt(10) + 1;
        if (!_answerOptions.contains(option)) {
          _answerOptions.add(option);
        }
      }
      _answerOptions.shuffle();

      _selectedAnswer = 0;
      _showResult = false;
    });

    _animationController.reset();
    _itemController.reset();
    _animationController.forward();
    _itemController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.green.shade100,
              Colors.teal.shade100,
              Colors.blue.shade100,
            ],
          ),
        ),
        child: SafeArea(
          child: _gameCompleted
              ? _buildCompletionScreen()
              : Column(
                  children: [
                    _buildHeader(),
                    _buildProgressBar(),
                    Expanded(child: _buildGameScreen()),
                  ],
                ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.teal),
            onPressed: () => Navigator.pop(context),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'تعلم العد 🔢',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.teal,
                  ),
                ),
                Text(
                  'عد العناصر واختر الرقم الصحيح',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.teal.shade600,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(15),
              boxShadow: [
                BoxShadow(
                  color: Colors.teal.withValues(alpha: 0.2),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Text(
              'النقاط: $_score',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.teal,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressBar() {
    final progress = (_currentNumber - 1) / _maxNumber;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'الرقم $_currentNumber من $_maxNumber',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.teal,
                ),
              ),
              Text(
                '${(progress * 100).round()}%',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.teal,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.teal.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(Colors.teal),
            minHeight: 8,
          ),
        ],
      ),
    );
  }

  Widget _buildGameScreen() {
    return ScaleTransition(
      scale: _scaleAnimation,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // منطقة العد
            Expanded(
              flex: 2,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.teal.withValues(alpha: 0.2),
                      blurRadius: 15,
                      offset: const Offset(0, 8),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    const Text(
                      'عد العناصر:',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.teal,
                      ),
                    ),
                    const SizedBox(height: 20),
                    Expanded(
                      child: _buildItemsGrid(),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // خيارات الإجابة
            const Text(
              'اختر الرقم الصحيح:',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.teal,
              ),
            ),

            const SizedBox(height: 16),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: _answerOptions.map((option) {
                return _buildAnswerButton(option);
              }).toList(),
            ),

            if (_showResult) ...[
              const SizedBox(height: 20),
              _buildResultMessage(),
            ],

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildItemsGrid() {
    return AnimatedBuilder(
      animation: _itemAnimation,
      builder: (context, child) {
        return GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: _currentNumber <= 4 ? 2 : 3,
            childAspectRatio: 1,
            crossAxisSpacing: 10,
            mainAxisSpacing: 10,
          ),
          itemCount: _items.length,
          itemBuilder: (context, index) {
            final delay = index * 0.1;
            final itemScale = Tween<double>(
              begin: 0.0,
              end: 1.0,
            ).animate(CurvedAnimation(
              parent: _itemController,
              curve: Interval(delay, 1.0, curve: Curves.elasticOut),
            ));

            return ScaleTransition(
              scale: itemScale,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.teal.shade50,
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(
                    color: Colors.teal.withValues(alpha: 0.3),
                    width: 2,
                  ),
                ),
                child: Center(
                  child: Text(
                    _items[index],
                    style: const TextStyle(fontSize: 40),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildAnswerButton(int number) {
    final isSelected = _selectedAnswer == number;
    final isCorrect = number == _currentNumber;
    final showResult = _showResult && isSelected;

    return GestureDetector(
      onTap: _showResult ? null : () => _selectAnswer(number),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: showResult
              ? (isCorrect ? Colors.green : Colors.red)
              : (isSelected ? Colors.teal : Colors.white),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: Colors.teal,
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.teal.withValues(alpha: 0.3),
              blurRadius: isSelected ? 15 : 5,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (showResult)
                Icon(
                  isCorrect ? Icons.check : Icons.close,
                  color: Colors.white,
                  size: 16,
                ),
              Text(
                '$number',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: showResult
                      ? Colors.white
                      : (isSelected ? Colors.white : Colors.teal),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildResultMessage() {
    final isCorrect = _selectedAnswer == _currentNumber;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isCorrect ? Colors.green : Colors.red,
        borderRadius: BorderRadius.circular(15),
      ),
      child: Text(
        isCorrect 
            ? 'ممتاز! الإجابة صحيحة! 🎉' 
            : 'الإجابة الصحيحة هي $_currentNumber',
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  void _selectAnswer(int answer) {
    setState(() {
      _selectedAnswer = answer;
      _showResult = true;
    });

    final isCorrect = answer == _currentNumber;
    
    HapticFeedback.lightImpact();

    if (isCorrect) {
      _score += 10;
      HapticFeedback.heavyImpact();
    }

    // الانتقال للسؤال التالي
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _currentNumber++;
        });
        _generateQuestion();
      }
    });
  }

  Widget _buildCompletionScreen() {
    final percentage = (_score / (_maxNumber * 10) * 100).round();
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(40),
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.teal.withValues(alpha: 0.3),
                    blurRadius: 30,
                    offset: const Offset(0, 15),
                  ),
                ],
              ),
              child: Text(
                percentage >= 80 ? '🏆' : percentage >= 60 ? '⭐' : '🔢',
                style: const TextStyle(fontSize: 80),
              ),
            ),

            const SizedBox(height: 30),

            Text(
              percentage >= 80 ? 'عبقري!' : percentage >= 60 ? 'ممتاز!' : 'أحسنت!',
              style: const TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Colors.teal,
              ),
            ),

            const SizedBox(height: 16),

            Text(
              'لقد حصلت على $_score نقطة من ${_maxNumber * 10}',
              style: const TextStyle(
                fontSize: 18,
                color: Colors.teal,
              ),
            ),

            Text(
              'النسبة: $percentage%',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.teal,
              ),
            ),

            const SizedBox(height: 40),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  onPressed: _restartGame,
                  icon: const Icon(Icons.refresh),
                  label: const Text('إعادة اللعب'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 12,
                    ),
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () {
                    widget.onComplete(_score);
                    Navigator.pop(context);
                  },
                  icon: const Icon(Icons.check),
                  label: const Text('إنهاء'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 12,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _restartGame() {
    setState(() {
      _currentNumber = 1;
      _score = 0;
      _selectedAnswer = 0;
      _showResult = false;
      _gameCompleted = false;
    });
    
    _generateQuestion();
  }
}
