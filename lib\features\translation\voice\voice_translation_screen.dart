import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:share_plus/share_plus.dart';
import '../../../config/constants.dart';
import '../../../config/app_theme.dart';
import '../../../core/app_state.dart';
import '../../../services/api/translation_service.dart';
import 'voice_translation_service.dart';
import '../../../utils/helpers.dart';

/// شاشة الترجمة الصوتية
class VoiceTranslationScreen extends StatefulWidget {
  const VoiceTranslationScreen({super.key});

  @override
  State<VoiceTranslationScreen> createState() => _VoiceTranslationScreenState();
}

class _VoiceTranslationScreenState extends State<VoiceTranslationScreen> {
  final SpeechToText _speechToText = SpeechToText();
  final FlutterTts _flutterTts = FlutterTts();
  final _voiceTranslationService = VoiceTranslationService();

  String _recognizedText = '';
  String _translatedText = '';
  String _sourceLanguage = 'ar';
  String _targetLanguage = 'en';
  bool _isListening = false;
  bool _isTranslating = false;
  bool _isSpeaking = false;
  bool _isFavorite = false;

  @override
  void initState() {
    super.initState();
    _initSpeech();
    _initTts();
  }

  @override
  void dispose() {
    _flutterTts.stop();
    super.dispose();
  }

  /// تهيئة خدمة التعرف على الكلام
  Future<void> _initSpeech() async {
    try {
      bool available = await _speechToText.initialize(
        onStatus: (status) async {
          debugPrint('Speech status: $status');
          if (status == 'done' || status == 'notListening') {
            setState(() {
              _isListening = false;
            });
            // تنفيذ الترجمة تلقائياً بعد انتهاء الاستماع إذا كان هناك نص
            if (_recognizedText.isNotEmpty && !_isTranslating) {
              await _translateText();
            }
          }
        },
        onError: (error) {
          debugPrint('Speech error: $error');
          setState(() {
            _isListening = false;
          });
          AppHelpers.showSnackBar(
            context,
            'حدث خطأ أثناء الاستماع: ${error.errorMsg}',
            isError: true,
          );
        },
      );

      if (!available) {
        if (mounted) {
          AppHelpers.showSnackBar(
            context,
            'التعرف على الكلام غير متاح على هذا الجهاز',
            isError: true,
          );
        }
      }
    } catch (e) {
      debugPrint('Speech initialization error: $e');
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء تهيئة خدمة التعرف على الكلام: $e',
          isError: true,
        );
      }
    }
  }

  /// تهيئة خدمة تحويل النص إلى كلام
  Future<void> _initTts() async {
    try {
      // استخدام معرف اللغة المناسب
      final localeId = _getLocaleId(_targetLanguage);
      await _flutterTts.setLanguage(localeId);

      // تعيين معدل الكلام (أبطأ للغة العربية)
      await _flutterTts.setSpeechRate(_targetLanguage == 'ar' ? 0.4 : 0.5);

      // تعيين مستوى الصوت
      await _flutterTts.setVolume(1.0);

      // تعيين طبقة الصوت (أعلى قليلاً للغة العربية)
      await _flutterTts.setPitch(_targetLanguage == 'ar' ? 1.1 : 1.0);

      // الحصول على اللغات المدعومة للتحقق
      final availableLanguages = await _flutterTts.getLanguages;
      final availableVoices = await _flutterTts.getVoices;

      debugPrint('Available TTS languages: $availableLanguages');
      debugPrint('Available TTS voices: $availableVoices');

      // تعيين معالج الانتهاء
      _flutterTts.setCompletionHandler(() {
        setState(() {
          _isSpeaking = false;
        });
      });

      // تعيين معالج الخطأ
      _flutterTts.setErrorHandler((error) {
        debugPrint('TTS error: $error');
        setState(() {
          _isSpeaking = false;
        });
      });
    } catch (e) {
      debugPrint('TTS initialization error: $e');
    }
  }

  /// الحصول على معرف اللغة المناسب لـ TTS
  String _getLocaleId(String languageCode) {
    switch (languageCode) {
      case 'ar':
        // محاولة استخدام عدة معرفات للغة العربية لزيادة فرص التوافق
        return 'ar-SA';
      case 'en':
        return 'en-US';
      case 'fr':
        return 'fr-FR';
      case 'es':
        return 'es-ES';
      case 'de':
        return 'de-DE';
      case 'it':
        return 'it-IT';
      case 'ru':
        return 'ru-RU';
      case 'zh':
        return 'zh-CN';
      case 'ja':
        return 'ja-JP';
      case 'ko':
        return 'ko-KR';
      default:
        return languageCode;
    }
  }

  /// بدء الاستماع
  Future<void> _startListening() async {
    try {
      if (!_speechToText.isAvailable) {
        await _initSpeech();
      }

      if (_speechToText.isAvailable) {
        setState(() {
          _isListening = true;
          _recognizedText = '';
          _translatedText = '';
        });

        await _speechToText.listen(
          onResult: (result) {
            setState(() {
              _recognizedText = result.recognizedWords;
            });
          },
          localeId: _sourceLanguage,
        );
      }
    } catch (e) {
      setState(() {
        _isListening = false;
      });

      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء بدء الاستماع: $e',
          isError: true,
        );
      }
    }
  }

  /// إيقاف الاستماع
  Future<void> _stopListening() async {
    try {
      await _speechToText.stop();
      setState(() {
        _isListening = false;
      });
    } catch (e) {
      debugPrint('Stop listening error: $e');
    }
  }

  /// ترجمة النص
  Future<void> _translateText() async {
    if (_recognizedText.isEmpty) {
      AppHelpers.showSnackBar(
        context,
        'يرجى التحدث أولاً للحصول على نص للترجمة',
        isError: true,
      );
      return;
    }
    setState(() {
      _isTranslating = true;
    });
    try {
      final appState = Provider.of<AppState>(context, listen: false);
      final remoteConfig = appState.remoteConfig;
      final translationService = TranslationService(remoteConfig);
      _voiceTranslationService.setTranslationService(translationService);
      final translatedText = await _voiceTranslationService.translateText(
        text: _recognizedText,
        sourceLanguage: _sourceLanguage,
        targetLanguage: _targetLanguage,
      );
      setState(() {
        _translatedText = translatedText;
        _isTranslating = false;
      });
    } catch (e) {
      setState(() {
        _isTranslating = false;
      });
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء الترجمة: $e',
          isError: true,
        );
      }
    }
  }

  /// نطق النص المترجم
  Future<void> _speakTranslatedText() async {
    if (_translatedText.isEmpty) return;

    if (_isSpeaking) {
      await _flutterTts.stop();
      setState(() {
        _isSpeaking = false;
      });
      return;
    }

    try {
      // استخدام معرف اللغة المناسب
      final localeId = _getLocaleId(_targetLanguage);
      await _flutterTts.setLanguage(localeId);

      // تعيين معدل الكلام (أبطأ للغة العربية)
      await _flutterTts.setSpeechRate(_targetLanguage == 'ar' ? 0.4 : 0.5);

      // تعيين طبقة الصوت (أعلى قليلاً للغة العربية)
      await _flutterTts.setPitch(_targetLanguage == 'ar' ? 1.1 : 1.0);

      debugPrint('Speaking text in language: $localeId');

      setState(() {
        _isSpeaking = true;
      });

      // استخدام await للتأكد من أن الأمر تم تنفيذه
      final result = await _flutterTts.speak(_translatedText);
      debugPrint('TTS speak result: $result');

      // إذا فشل النطق، نعيد المحاولة مرة واحدة
      if (result == 0) {
        await Future.delayed(const Duration(milliseconds: 500));

        // محاولة استخدام لغة بديلة إذا كانت اللغة العربية
        if (_targetLanguage == 'ar') {
          final success = await _tryAlternativeLanguage(_translatedText);
          if (!success) {
            // إذا فشلت اللغة البديلة، نعيد المحاولة باللغة الأصلية
            await _flutterTts.setLanguage(localeId);
            await _flutterTts.speak(_translatedText);
          }
        } else {
          // إذا كانت لغة أخرى، نعيد المحاولة فقط
          await _flutterTts.speak(_translatedText);
        }
      }
    } catch (e) {
      setState(() {
        _isSpeaking = false;
      });

      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء النطق: $e',
          isError: true,
        );
      }
    }
  }

  /// محاولة استخدام لغة بديلة إذا فشلت اللغة الأصلية
  Future<bool> _tryAlternativeLanguage(String text) async {
    try {
      if (_targetLanguage == 'ar') {
        // محاولة استخدام اللغة العربية (مصر) إذا فشلت اللغة العربية (السعودية)
        await _flutterTts.setLanguage('ar-EG');
        await _flutterTts.speak(text);
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error trying alternative language: $e');
      return false;
    }
  }

  /// نسخ النص المترجم
  void _copyTranslatedText() {
    if (_translatedText.isEmpty) return;

    Clipboard.setData(ClipboardData(text: _translatedText));

    AppHelpers.showSnackBar(context, 'تم نسخ النص المترجم');
  }

  /// مشاركة النص المترجم
  void _shareTranslatedText() {
    if (_translatedText.isEmpty) return;

    Share.share(_translatedText);
  }

  /// إضافة/إزالة من المفضلة
  void _toggleFavorite() {
    if (_translatedText.isEmpty) return;

    setState(() {
      _isFavorite = !_isFavorite;
    });

    AppHelpers.showSnackBar(
      context,
      _isFavorite ? 'تمت الإضافة إلى المفضلة' : 'تمت الإزالة من المفضلة',
    );
  }

  /// تبديل اللغات
  void _swapLanguages() {
    setState(() {
      final temp = _sourceLanguage;
      _sourceLanguage = _targetLanguage;
      _targetLanguage = temp;

      // إعادة تعيين النصوص
      _recognizedText = '';
      _translatedText = '';
    });
  }

  /// عرض مربع حوار اختيار اللغة
  Future<void> _showLanguageSelector(bool isSource) async {
    final selectedLanguage = await showModalBottomSheet<String>(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => _buildLanguageSelector(isSource),
    );

    if (selectedLanguage != null) {
      setState(() {
        if (isSource) {
          _sourceLanguage = selectedLanguage;
        } else {
          _targetLanguage = selectedLanguage;
        }

        // إعادة تعيين النصوص
        _recognizedText = '';
        _translatedText = '';
      });
    }
  }

  /// بناء مربع حوار اختيار اللغة
  Widget _buildLanguageSelector(bool isSource) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            isSource ? 'اختر لغة الاستماع' : 'اختر لغة الترجمة',
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: ListView.builder(
              itemCount: AppConstants.supportedLanguages.length,
              itemBuilder: (context, index) {
                final language = AppConstants.supportedLanguages[index];
                return ListTile(
                  leading: Text(
                    language['flag'] as String,
                    style: const TextStyle(fontSize: 24),
                  ),
                  title: Text(language['name'] as String),
                  onTap: () {
                    Navigator.pop(context, language['code'] as String);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على اسم اللغة من الرمز
  String _getLanguageName(String languageCode) {
    final language = AppConstants.supportedLanguages.firstWhere(
      (lang) => lang['code'] == languageCode,
      orElse: () => {'code': languageCode, 'name': languageCode, 'flag': '🏳️'},
    );

    return '${language['flag']} ${language['name']}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الترجمة الصوتية'),
        centerTitle: true,
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // اختيار اللغات
            Row(
              children: [
                // اللغة المصدر
                Expanded(
                  child: InkWell(
                    onTap: () => _showLanguageSelector(true),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        vertical: 12,
                        horizontal: 16,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _getLanguageName(_sourceLanguage),
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),

                // زر تبديل اللغات
                IconButton(
                  onPressed: _swapLanguages,
                  icon: const Icon(Icons.swap_horiz),
                  color: Theme.of(context).colorScheme.primary,
                ),

                // اللغة الهدف
                Expanded(
                  child: InkWell(
                    onTap: () => _showLanguageSelector(false),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        vertical: 12,
                        horizontal: 16,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _getLanguageName(_targetLanguage),
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 30),

            // زر الاستماع
            GestureDetector(
              onTap: _isListening ? _stopListening : _startListening,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Container(
                    width: 150,
                    height: 150,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: _isListening ? Colors.red : AppTheme.primaryColor,
                      boxShadow: [
                        BoxShadow(
                          color:
                              _isListening
                                  ? const Color.fromRGBO(255, 0, 0, 0.3)
                                  : const Color.fromRGBO(0, 122, 255, 0.3),
                          blurRadius: 20,
                          spreadRadius: 10,
                        ),
                      ],
                    ),
                    child: Icon(
                      _isListening ? Icons.stop : Icons.mic,
                      size: 60,
                      color: Colors.white,
                    ),
                  ),
                  if (_isListening)
                    const Positioned(
                      child: SizedBox(
                        width: 60,
                        height: 60,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 4,
                        ),
                      ),
                    ),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // حالة الاستماع
            Text(
              _isListening ? 'جاري الاستماع...' : 'انقر للتحدث',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _isListening ? Colors.red : AppTheme.onBackground,
              ),
            ),

            const SizedBox(height: 30),

            // النص المتعرف عليه
            if (_recognizedText.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'النص المتعرف عليه (${_getLanguageName(_sourceLanguage)}):',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.onBackground,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _recognizedText,
                      style: const TextStyle(
                        fontSize: 16,
                        color: AppTheme.onBackground,
                      ),
                    ),
                  ],
                ),
              ),

            const SizedBox(height: 20),

            // زر الترجمة
            if (_recognizedText.isNotEmpty)
              ElevatedButton(
                onPressed: _isTranslating ? null : _translateText,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  minimumSize: const Size(double.infinity, 56),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                child:
                    _isTranslating
                        ? const CircularProgressIndicator(color: Colors.white)
                        : const Text(
                          'ترجم',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
              ),

            const SizedBox(height: 20),

            // النص المترجم
            if (_translatedText.isNotEmpty)
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'الترجمة (${_getLanguageName(_targetLanguage)}):',
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: AppTheme.onBackground,
                            ),
                          ),
                          Row(
                            children: [
                              // زر النطق
                              IconButton(
                                onPressed: _speakTranslatedText,
                                icon: Icon(
                                  _isSpeaking ? Icons.stop : Icons.volume_up,
                                  color:
                                      _isSpeaking
                                          ? Colors.red
                                          : AppTheme.primaryColor,
                                ),
                                tooltip:
                                    _isSpeaking ? 'إيقاف النطق' : 'نطق النص',
                              ),

                              // زر النسخ
                              IconButton(
                                onPressed: _copyTranslatedText,
                                icon: const Icon(Icons.copy),
                                tooltip: 'نسخ النص',
                              ),

                              // زر المشاركة
                              IconButton(
                                onPressed: _shareTranslatedText,
                                icon: const Icon(Icons.share),
                                tooltip: 'مشاركة النص',
                              ),

                              // زر المفضلة
                              IconButton(
                                onPressed: _toggleFavorite,
                                icon: Icon(
                                  _isFavorite
                                      ? Icons.favorite
                                      : Icons.favorite_border,
                                  color: _isFavorite ? Colors.red : null,
                                ),
                                tooltip:
                                    _isFavorite
                                        ? 'إزالة من المفضلة'
                                        : 'إضافة إلى المفضلة',
                              ),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Text(
                            _translatedText,
                            style: const TextStyle(
                              fontSize: 16,
                              color: AppTheme.onBackground,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
