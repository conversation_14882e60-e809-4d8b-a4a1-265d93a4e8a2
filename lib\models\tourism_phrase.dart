import 'package:uuid/uuid.dart';

/// نموذج العبارة السياحية
class TourismPhrase {
  final String id;
  final String category;
  final String subcategory;
  final Map<String, String> translations;
  final String pronunciation;
  final bool isFavorite;
  final int usageCount;
  final DateTime? addedToFavoritesAt;
  final DateTime createdAt;

  TourismPhrase({
    String? id,
    required this.category,
    required this.subcategory,
    required this.translations,
    this.pronunciation = '',
    this.isFavorite = false,
    this.usageCount = 0,
    this.addedToFavoritesAt,
    DateTime? createdAt,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now();

  /// إنشاء نسخة جديدة مع تعديل بعض الخصائص
  TourismPhrase copyWith({
    String? id,
    String? category,
    String? subcategory,
    Map<String, String>? translations,
    String? pronunciation,
    bool? isFavorite,
    int? usageCount,
    DateTime? addedToFavoritesAt,
    DateTime? createdAt,
  }) {
    return TourismPhrase(
      id: id ?? this.id,
      category: category ?? this.category,
      subcategory: subcategory ?? this.subcategory,
      translations: translations ?? Map.from(this.translations),
      pronunciation: pronunciation ?? this.pronunciation,
      isFavorite: isFavorite ?? this.isFavorite,
      usageCount: usageCount ?? this.usageCount,
      addedToFavoritesAt: addedToFavoritesAt ?? this.addedToFavoritesAt,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'category': category,
      'subcategory': subcategory,
      'translations': translations,
      'pronunciation': pronunciation,
      'isFavorite': isFavorite,
      'usageCount': usageCount,
      'addedToFavoritesAt': addedToFavoritesAt?.millisecondsSinceEpoch,
      'createdAt': createdAt.millisecondsSinceEpoch,
    };
  }

  /// إنشاء نموذج من Map
  factory TourismPhrase.fromMap(Map<String, dynamic> map) {
    return TourismPhrase(
      id: map['id'],
      category: map['category'],
      subcategory: map['subcategory'] ?? '',
      translations: Map<String, String>.from(map['translations']),
      pronunciation: map['pronunciation'] ?? '',
      isFavorite: map['isFavorite'] ?? false,
      usageCount: map['usageCount'] ?? 0,
      addedToFavoritesAt:
          map['addedToFavoritesAt'] != null
              ? DateTime.fromMillisecondsSinceEpoch(map['addedToFavoritesAt'])
              : null,
      createdAt:
          map['createdAt'] != null
              ? DateTime.fromMillisecondsSinceEpoch(map['createdAt'])
              : DateTime.now(),
    );
  }

  /// تحويل النموذج إلى JSON
  Map<String, dynamic> toJson() => toMap();

  /// إنشاء نموذج من JSON
  factory TourismPhrase.fromJson(Map<String, dynamic> json) =>
      TourismPhrase.fromMap(json);

  @override
  String toString() {
    return 'TourismPhrase(id: $id, category: $category, subcategory: $subcategory, translations: $translations, isFavorite: $isFavorite)';
  }
}

/// فئات العبارات السياحية
class TourismCategories {
  static const String airport = 'airport';
  static const String hotel = 'hotel';
  static const String restaurant = 'restaurant';
  static const String transportation = 'transportation';
  static const String shopping = 'shopping';
  static const String emergency = 'emergency';
  static const String directions = 'directions';
  static const String attractions = 'attractions';
  static const String general = 'general';
  static const String clothing = 'clothing';
  static const String food = 'food';
  static const String markets = 'markets';
  static const String introduction = 'introduction'; // التعارف والتعريف

  static const Map<String, String> categoryNames = {
    airport: 'المطار',
    hotel: 'الفندق',
    restaurant: 'المطعم',
    transportation: 'المواصلات',
    shopping: 'التسوق',
    emergency: 'الطوارئ',
    directions: 'الاتجاهات',
    attractions: 'المعالم السياحية',
    general: 'عام',
    clothing: 'الملابس والأزياء',
    food: 'أنواع الطعام',
    markets: 'الأسواق والتسوق',
    introduction: 'التعارف والتعريف',
  };

  static const Map<String, List<String>> subcategories = {
    airport: [
      'تسجيل الوصول',
      'الأمان',
      'الجمارك',
      'الأمتعة',
      'تأخير الرحلات',
      'البوابات',
    ],
    hotel: [
      'الحجز',
      'تسجيل الدخول',
      'خدمة الغرف',
      'المرافق',
      'الشكاوى',
      'تسجيل الخروج',
    ],
    restaurant: [
      'الطلب',
      'القائمة',
      'الحساسية الغذائية',
      'الدفع',
      'التوصيات',
      'الشكاوى',
    ],
    transportation: [
      'التاكسي',
      'الحافلة',
      'القطار',
      'المترو',
      'تأجير السيارات',
    ],
    shopping: ['الأسعار', 'المقاسات', 'المساومة', 'الدفع', 'الإرجاع', 'الضمان'],
    emergency: ['الطبيب', 'الشرطة', 'الإسعاف', 'السفارة', 'فقدان الوثائق'],
    directions: ['الطرق', 'المواقع', 'المسافات', 'النقل العام'],
    attractions: ['ساعات العمل', 'أسعار التذاكر', 'الجولات المرشدة', 'المتاحف'],
    general: ['التحيات', 'الشكر', 'الاعتذار', 'الأرقام', 'الوقت', 'الطقس'],
    clothing: [
      'الأسعار والمساومة',
      'الألوان والمقاسات',
      'غرف القياس',
      'الأقمشة والجودة',
      'الدفع والضمان',
    ],
    food: [
      'اللحوم والدواجن',
      'الخضروات والفواكه',
      'المشروبات',
      'الحلويات',
      'الطعام الحلال',
    ],
    markets: [
      'البحث عن المحلات',
      'ساعات العمل',
      'التفاوض',
      'طرق الدفع',
      'التوصيل',
    ],
    introduction: [
      'التعريف الشخصي',
      'السؤال عن الآخرين',
      'بدء المحادثة',
      'الجنسية والمهنة',
      'الهوايات والاهتمامات',
      'التواصل الاجتماعي',
    ],
  };

  static const Map<String, String> categoryIcons = {
    airport: '✈️',
    hotel: '🏨',
    restaurant: '🍽️',
    transportation: '🚗',
    shopping: '🛍️',
    emergency: '🚨',
    directions: '🗺️',
    attractions: '🏛️',
    general: '💬',
    clothing: '👕',
    food: '🍎',
    markets: '🏪',
    introduction: '🤝', // التعارف والتعريف
  };
}

/// اللغات المدعومة
class SupportedLanguages {
  static const String arabic = 'ar';
  static const String english = 'en';
  static const String french = 'fr';
  static const String german = 'de';
  static const String spanish = 'es';
  static const String italian = 'it';
  static const String japanese = 'ja';
  static const String chinese = 'zh';
  static const String turkish = 'tr';
  static const String russian = 'ru';

  static const Map<String, String> languageNames = {
    arabic: 'العربية',
    english: 'English',
    french: 'Français',
    german: 'Deutsch',
    spanish: 'Español',
    italian: 'Italiano',
    japanese: '日本語',
    chinese: '中文',
    turkish: 'Türkçe',
    russian: 'Русский',
  };

  static const Map<String, String> languageFlags = {
    arabic: '🇸🇦',
    english: '🇺🇸',
    french: '🇫🇷',
    german: '🇩🇪',
    spanish: '🇪🇸',
    italian: '🇮🇹',
    japanese: '🇯🇵',
    chinese: '🇨🇳',
    turkish: '🇹🇷',
    russian: '🇷🇺',
  };

  static const Map<String, String> ttsLanguageCodes = {
    arabic: 'ar-SA',
    english: 'en-US',
    french: 'fr-FR',
    german: 'de-DE',
    spanish: 'es-ES',
    italian: 'it-IT',
    japanese: 'ja-JP',
    chinese: 'zh-CN',
    turkish: 'tr-TR',
    russian: 'ru-RU',
  };

  static List<String> getAllLanguages() {
    return languageNames.keys.toList();
  }

  static String getLanguageName(String code) {
    return languageNames[code] ?? code;
  }

  static String getLanguageFlag(String code) {
    return languageFlags[code] ?? '🌍';
  }

  static String getTTSCode(String code) {
    return ttsLanguageCodes[code] ?? 'en-US';
  }
}
