import 'package:flutter/material.dart';
import '../../config/app_theme.dart';
import '../../widgets/enhanced_app_bar.dart';

/// شاشة شروط الاستخدام
class TermsOfServiceScreen extends StatelessWidget {
  const TermsOfServiceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: EnhancedAppBar(
        title: const Text('شروط الاستخدام'),
        backgroundColor: AppTheme.primaryColor,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'شروط الاستخدام',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const Text(
              'آخر تحديث: 1 يونيو 2024',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
            const SizedBox(height: 24),
            _buildSection(
              'مقدمة',
              'مرحبًا بك في تطبيق المترجم الذكي AI. باستخدامك لهذا التطبيق، فإنك توافق على الالتزام بهذه الشروط. يرجى قراءتها بعناية.',
            ),
            _buildSection(
              'استخدام التطبيق',
              '''يمنحك تطبيق المترجم الذكي AI ترخيصًا محدودًا وغير حصري وغير قابل للتحويل لاستخدام التطبيق للاستخدام الشخصي غير التجاري.

يجب عليك:
• الالتزام بجميع القوانين المعمول بها عند استخدام التطبيق.
• الحفاظ على سرية معلومات حسابك.
• إخطارنا فورًا بأي استخدام غير مصرح به لحسابك.

لا يجوز لك:
• استخدام التطبيق لأي غرض غير قانوني.
• محاولة الوصول إلى أي جزء من التطبيق بوسائل أخرى غير الواجهة التي نوفرها.
• نسخ أو تعديل أو توزيع أو بيع أو تأجير أي جزء من التطبيق.''',
            ),
            _buildSection(
              'المحتوى والملكية الفكرية',
              '''جميع المحتويات المقدمة من خلال التطبيق، بما في ذلك النصوص والرسومات والشعارات والصور والبرامج، هي ملك لنا أو لمرخصينا وتخضع لحماية حقوق النشر والعلامات التجارية وغيرها من قوانين الملكية الفكرية.

المحتوى الذي تقدمه (مثل النصوص للترجمة) يظل ملكًا لك. ومع ذلك، فإنك تمنحنا ترخيصًا عالميًا غير حصري وخالٍ من حقوق الملكية لاستخدام هذا المحتوى لتقديم خدمات الترجمة وتحسينها.''',
            ),
            _buildSection(
              'الاشتراكات والمدفوعات',
              '''يقدم التطبيق خطة مجانية وخطط اشتراك مدفوعة. عند الاشتراك في خطة مدفوعة:
• توافق على دفع جميع الرسوم المرتبطة بالخطة التي اخترتها.
• سيتم تجديد اشتراكك تلقائيًا ما لم تلغِه قبل تاريخ التجديد.
• يمكنك إلغاء اشتراكك في أي وقت من خلال إعدادات حسابك.

جميع المدفوعات نهائية وغير قابلة للاسترداد، إلا إذا كان القانون المعمول به يتطلب خلاف ذلك.''',
            ),
            _buildSection(
              'إخلاء المسؤولية',
              '''يتم توفير التطبيق "كما هو" و"كما هو متاح" دون أي ضمانات من أي نوع، صريحة أو ضمنية.

لا نضمن دقة الترجمات أو ملاءمتها لأي غرض معين. يجب عليك مراجعة جميع الترجمات قبل الاعتماد عليها، خاصة للأغراض الرسمية أو الحساسة.''',
            ),
            _buildSection(
              'تحديد المسؤولية',
              'إلى أقصى حد يسمح به القانون، لن نكون مسؤولين عن أي أضرار مباشرة أو غير مباشرة أو عرضية أو خاصة أو تبعية أو عقابية، بما في ذلك خسارة الأرباح أو البيانات، الناشئة عن استخدامك للتطبيق.',
            ),
            _buildSection(
              'التعديلات على الشروط',
              'قد نقوم بتعديل هذه الشروط من وقت لآخر. سنخطرك بأي تغييرات جوهرية من خلال إشعار في التطبيق. استمرارك في استخدام التطبيق بعد نشر التغييرات يشكل موافقتك على الشروط المعدلة.',
            ),
            _buildSection(
              'الإنهاء',
              'يمكننا إنهاء أو تعليق وصولك إلى التطبيق فورًا، دون إشعار مسبق أو مسؤولية، لأي سبب، بما في ذلك على سبيل المثال لا الحصر، انتهاك هذه الشروط.',
            ),
            _buildSection(
              'القانون الحاكم',
              'تخضع هذه الشروط وتفسر وفقًا لقوانين المملكة العربية السعودية، دون اعتبار لمبادئ تنازع القوانين.',
            ),
            _buildSection(
              'اتصل بنا',
              'إذا كانت لديك أي أسئلة حول شروط الاستخدام هذه، يرجى الاتصال بنا على: <EMAIL>',
            ),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  /// بناء قسم من شروط الاستخدام
  Widget _buildSection(String title, String content) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(content, style: const TextStyle(fontSize: 16, height: 1.5)),
        ],
      ),
    );
  }
}
