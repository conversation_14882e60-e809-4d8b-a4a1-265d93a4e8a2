import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// عنصر شبكة محسن مع تأثيرات بصرية ولمسية
class EnhancedGridItem extends StatefulWidget {
  /// أيقونة العنصر
  final IconData? icon;

  /// صورة العنصر (بديل للأيقونة)
  final Widget? image;

  /// عنوان العنصر
  final String title;

  /// وصف العنصر (اختياري)
  final String? subtitle;

  /// دالة يتم تنفيذها عند الضغط على العنصر
  final VoidCallback? onTap;

  /// دالة يتم تنفيذها عند الضغط المطول على العنصر
  final VoidCallback? onLongPress;

  /// لون خلفية العنصر
  final Color? backgroundColor;

  /// لون العنصر عند الضغط عليه
  final Color? splashColor;

  /// لون الأيقونة
  final Color? iconColor;

  /// لون العنوان
  final Color? titleColor;

  /// لون الوصف
  final Color? subtitleColor;

  /// حجم الأيقونة
  final double? iconSize;

  /// حجم خط العنوان
  final double? titleSize;

  /// حجم خط الوصف
  final double? subtitleSize;

  /// نصف قطر الحواف
  final double borderRadius;

  /// ارتفاع العنصر
  final double? height;

  /// عرض العنصر
  final double? width;

  /// تباعد داخلي
  final EdgeInsetsGeometry padding;

  /// ما إذا كان العنصر معطلاً
  final bool isDisabled;

  /// ما إذا كان العنصر محدداً
  final bool isSelected;

  /// منشئ عنصر الشبكة المحسن
  const EnhancedGridItem({
    super.key,
    this.icon,
    this.image,
    required this.title,
    this.subtitle,
    this.onTap,
    this.onLongPress,
    this.backgroundColor,
    this.splashColor,
    this.iconColor,
    this.titleColor,
    this.subtitleColor,
    this.iconSize = 40,
    this.titleSize = 16,
    this.subtitleSize = 12,
    this.borderRadius = 16,
    this.height,
    this.width,
    this.padding = const EdgeInsets.all(16),
    this.isDisabled = false,
    this.isSelected = false,
  });

  @override
  State<EnhancedGridItem> createState() => _EnhancedGridItemState();
}

class _EnhancedGridItemState extends State<EnhancedGridItem>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 150),
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (!widget.isDisabled &&
        (widget.onTap != null || widget.onLongPress != null)) {
      setState(() {
        _isPressed = true;
      });
      _controller.forward();
      HapticFeedback.selectionClick();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    if (!widget.isDisabled && _isPressed) {
      setState(() {
        _isPressed = false;
      });
      _controller.reverse();
    }
  }

  void _handleTapCancel() {
    if (!widget.isDisabled && _isPressed) {
      setState(() {
        _isPressed = false;
      });
      _controller.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveBackgroundColor =
        widget.backgroundColor ??
        (widget.isSelected
            ? theme.colorScheme.primary.withAlpha(26)
            : theme.cardColor);
    final effectiveIconColor =
        widget.iconColor ??
        (widget.isSelected
            ? theme.colorScheme.primary
            : theme.colorScheme.primary);
    final effectiveTitleColor =
        widget.titleColor ?? theme.textTheme.titleLarge?.color;
    final effectiveSubtitleColor =
        widget.subtitleColor ?? theme.textTheme.bodyMedium?.color;

    return ScaleTransition(
      scale: _scaleAnimation,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTapDown: _handleTapDown,
          onTapUp: _handleTapUp,
          onTapCancel: _handleTapCancel,
          onTap: widget.isDisabled ? null : widget.onTap,
          onLongPress: widget.isDisabled ? null : widget.onLongPress,
          splashColor:
              widget.splashColor ?? theme.colorScheme.primary.withAlpha(51),
          highlightColor: theme.colorScheme.primary.withAlpha(26),
          borderRadius: BorderRadius.circular(widget.borderRadius),
          child: Ink(
            height: widget.height,
            width: widget.width,
            padding: widget.padding,
            decoration: BoxDecoration(
              color:
                  widget.isDisabled
                      ? effectiveBackgroundColor.withAlpha(128)
                      : effectiveBackgroundColor,
              borderRadius: BorderRadius.circular(widget.borderRadius),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(13),
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // أيقونة أو صورة العنصر
                if (widget.image != null) ...[
                  widget.image!,
                  const SizedBox(height: 12),
                ] else if (widget.icon != null) ...[
                  Icon(
                    widget.icon,
                    size: widget.iconSize,
                    color:
                        widget.isDisabled
                            ? effectiveIconColor.withAlpha(128)
                            : effectiveIconColor,
                  ),
                  const SizedBox(height: 12),
                ],

                // عنوان العنصر
                Text(
                  widget.title,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: widget.titleSize,
                    fontWeight: FontWeight.bold,
                    color:
                        widget.isDisabled
                            ? effectiveTitleColor?.withAlpha(128)
                            : effectiveTitleColor,
                  ),
                ),

                // وصف العنصر (إذا كان موجوداً)
                if (widget.subtitle != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    widget.subtitle!,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: widget.subtitleSize,
                      color:
                          widget.isDisabled
                              ? effectiveSubtitleColor?.withAlpha(128)
                              : effectiveSubtitleColor,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
