import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../config/app_theme.dart';
import '../core/animations/app_animations.dart';

/// بطاقة محسنة مع رسوم متحركة وتأثيرات بصرية
class AnimatedCard extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? width;
  final double? height;
  final Color? backgroundColor;
  final double elevation;
  final double borderRadius;
  final Border? border;
  final List<BoxShadow>? boxShadow;
  final bool enableHoverEffect;
  final bool enableTapEffect;
  final Duration animationDuration;
  final Curve animationCurve;

  const AnimatedCard({
    super.key,
    required this.child,
    this.onTap,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.backgroundColor,
    this.elevation = 2,
    this.borderRadius = AppTheme.largeRadius,
    this.border,
    this.boxShadow,
    this.enableHoverEffect = true,
    this.enableTapEffect = true,
    this.animationDuration = AppAnimations.normalDuration,
    this.animationCurve = AppAnimations.smoothCurve,
  });

  @override
  State<AnimatedCard> createState() => _AnimatedCardState();
}

class _AnimatedCardState extends State<AnimatedCard>
    with TickerProviderStateMixin {
  late AnimationController _hoverController;
  late AnimationController _tapController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;
  late Animation<Color?> _colorAnimation;

  bool _isTapped = false;

  @override
  void initState() {
    super.initState();

    _hoverController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _tapController = AnimationController(
      duration: AppAnimations.fastDuration,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.02).animate(
      CurvedAnimation(parent: _hoverController, curve: widget.animationCurve),
    );

    _elevationAnimation = Tween<double>(
      begin: widget.elevation,
      end: widget.elevation + 4,
    ).animate(
      CurvedAnimation(parent: _hoverController, curve: widget.animationCurve),
    );

    _colorAnimation = ColorTween(
      begin: widget.backgroundColor ?? AppTheme.surfaceColor,
      end: (widget.backgroundColor ?? AppTheme.surfaceColor).withValues(
        alpha: 0.95,
      ),
    ).animate(
      CurvedAnimation(parent: _hoverController, curve: widget.animationCurve),
    );
  }

  @override
  void dispose() {
    _hoverController.dispose();
    _tapController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (!widget.enableTapEffect || widget.onTap == null) return;

    setState(() {
      _isTapped = true;
    });

    _tapController.forward();
    HapticFeedback.lightImpact();
  }

  void _handleTapUp(TapUpDetails details) {
    _handleTapEnd();
  }

  void _handleTapCancel() {
    _handleTapEnd();
  }

  void _handleTapEnd() {
    if (!mounted) return;

    setState(() {
      _isTapped = false;
    });

    _tapController.reverse();
  }

  void _handleTap() {
    if (widget.onTap == null) return;

    HapticFeedback.mediumImpact();
    widget.onTap!();
  }

  void _handleHoverEnter(PointerEnterEvent event) {
    if (!widget.enableHoverEffect) return;

    _hoverController.forward();
  }

  void _handleHoverExit(PointerExitEvent event) {
    if (!widget.enableHoverEffect) return;

    _hoverController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    Widget cardContent = Container(
      width: widget.width,
      height: widget.height,
      padding: widget.padding ?? const EdgeInsets.all(AppTheme.spacing16),
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? theme.cardColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.border,
        boxShadow:
            widget.boxShadow ??
            [
              BoxShadow(
                color: AppTheme.shadowColor.withValues(alpha: 0.1),
                blurRadius: widget.elevation * 2,
                offset: Offset(0, widget.elevation),
              ),
            ],
      ),
      child: widget.child,
    );

    if (widget.enableHoverEffect || widget.enableTapEffect) {
      cardContent = AnimatedBuilder(
        animation: Listenable.merge([_hoverController, _tapController]),
        builder: (context, child) {
          double scale = _scaleAnimation.value;
          if (_isTapped) {
            scale *= 0.98; // تأثير الضغط
          }

          return Transform.scale(
            scale: scale,
            child: Container(
              width: widget.width,
              height: widget.height,
              padding:
                  widget.padding ?? const EdgeInsets.all(AppTheme.spacing16),
              decoration: BoxDecoration(
                color: _colorAnimation.value ?? theme.cardColor,
                borderRadius: BorderRadius.circular(widget.borderRadius),
                border: widget.border,
                boxShadow:
                    widget.boxShadow ??
                    [
                      BoxShadow(
                        color: AppTheme.shadowColor.withValues(alpha: 0.1),
                        blurRadius: _elevationAnimation.value * 2,
                        offset: Offset(0, _elevationAnimation.value),
                      ),
                    ],
              ),
              child: widget.child,
            ),
          );
        },
      );
    }

    if (widget.onTap != null) {
      cardContent = GestureDetector(
        onTapDown: _handleTapDown,
        onTapUp: _handleTapUp,
        onTapCancel: _handleTapCancel,
        onTap: _handleTap,
        child: cardContent,
      );
    }

    if (widget.enableHoverEffect) {
      cardContent = MouseRegion(
        onEnter: _handleHoverEnter,
        onExit: _handleHoverExit,
        child: cardContent,
      );
    }

    return Container(
      margin: widget.margin ?? const EdgeInsets.all(AppTheme.spacing8),
      child: cardContent,
    );
  }
}

/// بطاقة بسيطة مع تأثيرات أساسية
class SimpleAnimatedCard extends StatelessWidget {
  final Widget child;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;

  const SimpleAnimatedCard({
    super.key,
    required this.child,
    this.onTap,
    this.padding,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedCard(
      onTap: onTap,
      padding: padding,
      margin: margin,
      elevation: 1,
      enableHoverEffect: true,
      enableTapEffect: onTap != null,
      child: child,
    );
  }
}

/// بطاقة مع تدرج لوني
class GradientAnimatedCard extends StatelessWidget {
  final Widget child;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Gradient gradient;

  const GradientAnimatedCard({
    super.key,
    required this.child,
    this.onTap,
    this.padding,
    this.margin,
    this.gradient = AppTheme.primaryGradient,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedCard(
      onTap: onTap,
      padding: padding,
      margin: margin,
      elevation: 3,
      child: Container(
        decoration: BoxDecoration(
          gradient: gradient,
          borderRadius: BorderRadius.circular(AppTheme.largeRadius),
        ),
        child: child,
      ),
    );
  }
}
