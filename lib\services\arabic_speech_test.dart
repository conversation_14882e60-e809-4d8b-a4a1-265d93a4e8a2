import 'package:flutter/foundation.dart';
import 'arabic_speech_service.dart';
import 'tourism_service.dart';

/// اختبار شامل للنطق العربي على جميع العبارات السياحية
class ArabicSpeechTest {
  static final ArabicSpeechService _speechService = ArabicSpeechService();
  static final TourismService _tourismService = TourismService();

  /// تشغيل اختبار شامل للنطق العربي
  static Future<void> runComprehensiveTest() async {
    try {
      debugPrint(
        '🧪 بدء الاختبار الشامل للنطق العربي على جميع العبارات السياحية',
      );

      // تهيئة الخدمات
      await _speechService.initialize();
      await _tourismService.initialize();

      // الحصول على جميع العبارات
      final allPhrases = _tourismService.getAllPhrases();
      debugPrint('📊 إجمالي العبارات للاختبار: ${allPhrases.length}');

      // إحصائيات الاختبار
      int successCount = 0;
      int failureCount = 0;
      Map<String, int> categoryResults = {};

      // اختبار عينة من كل فئة
      final testPhrases = _selectTestSamples(allPhrases);
      debugPrint('🎯 عدد العبارات المختارة للاختبار: ${testPhrases.length}');

      for (int i = 0; i < testPhrases.length; i++) {
        final phrase = testPhrases[i];
        final arabicText = phrase.translations['ar'] ?? '';

        if (arabicText.isEmpty) continue;

        try {
          debugPrint(
            '🎤 اختبار العبارة ${i + 1}/${testPhrases.length}: "$arabicText"',
          );
          debugPrint(
            '📂 الفئة: ${phrase.category.name} - الفئة الفرعية: ${phrase.subcategory}',
          );

          // اختبار النطق
          await _speechService.speakArabic(arabicText);

          // انتظار قصير بين العبارات
          await Future.delayed(const Duration(milliseconds: 1500));

          successCount++;
          final categoryName = phrase.category.toString().split('.').last;
          categoryResults[categoryName] =
              (categoryResults[categoryName] ?? 0) + 1;

          debugPrint('✅ نجح نطق العبارة: "$arabicText"');
        } catch (e) {
          failureCount++;
          debugPrint('❌ فشل نطق العبارة: "$arabicText" - الخطأ: $e');
        }

        // انتظار بين العبارات لتجنب التداخل
        await Future.delayed(const Duration(milliseconds: 500));
      }

      // طباعة النتائج النهائية
      _printTestResults(
        successCount,
        failureCount,
        categoryResults,
        testPhrases.length,
      );
    } catch (e) {
      debugPrint('❌ خطأ في الاختبار الشامل: $e');
    }
  }

  /// اختيار عينة تمثيلية من العبارات للاختبار
  static List<dynamic> _selectTestSamples(List<dynamic> allPhrases) {
    final Map<String, List<dynamic>> categorizedPhrases = {};

    // تجميع العبارات حسب الفئة
    for (final phrase in allPhrases) {
      final category = phrase.category.toString().split('.').last;
      categorizedPhrases[category] = categorizedPhrases[category] ?? [];
      categorizedPhrases[category]!.add(phrase);
    }

    final List<dynamic> testSamples = [];

    // اختيار 3 عبارات من كل فئة
    for (final category in categorizedPhrases.keys) {
      final phrases = categorizedPhrases[category]!;
      final sampleSize = phrases.length >= 3 ? 3 : phrases.length;

      for (int i = 0; i < sampleSize; i++) {
        testSamples.add(phrases[i]);
      }
    }

    return testSamples;
  }

  /// طباعة نتائج الاختبار
  static void _printTestResults(
    int successCount,
    int failureCount,
    Map<String, int> categoryResults,
    int totalTested,
  ) {
    debugPrint('\n📊 ===== نتائج الاختبار الشامل للنطق العربي =====');
    debugPrint('🎯 إجمالي العبارات المختبرة: $totalTested');
    debugPrint('✅ العبارات الناجحة: $successCount');
    debugPrint('❌ العبارات الفاشلة: $failureCount');

    final successRate =
        totalTested > 0
            ? (successCount / totalTested * 100).toStringAsFixed(1)
            : '0.0';
    debugPrint('📈 معدل النجاح: $successRate%');

    debugPrint('\n📂 النتائج حسب الفئة:');
    for (final category in categoryResults.keys) {
      final count = categoryResults[category]!;
      debugPrint('   • $category: $count عبارة ناجحة');
    }

    if (successRate == '100.0') {
      debugPrint('\n🎉 ممتاز! جميع العبارات تم نطقها بنجاح!');
    } else if (double.parse(successRate) >= 80) {
      debugPrint('\n👍 جيد جداً! معظم العبارات تعمل بشكل صحيح');
    } else if (double.parse(successRate) >= 60) {
      debugPrint('\n⚠️ مقبول، ولكن يحتاج تحسين');
    } else {
      debugPrint('\n❌ يحتاج إلى مراجعة وإصلاح');
    }

    debugPrint('================================================\n');
  }

  /// اختبار سريع للنطق العربي
  static Future<void> runQuickTest() async {
    try {
      debugPrint('⚡ بدء الاختبار السريع للنطق العربي');

      await _speechService.initialize();

      final testPhrases = [
        'مرحبا',
        'أهلا وسهلا',
        'شكرا لك',
        'من فضلك',
        'أين المطار؟',
        'كم الساعة؟',
        'أحتاج مساعدة',
        'أين الفندق؟',
        'كم السعر؟',
        'أريد طعام حلال',
      ];

      debugPrint('🎯 اختبار ${testPhrases.length} عبارات أساسية');

      int successCount = 0;

      for (int i = 0; i < testPhrases.length; i++) {
        final phrase = testPhrases[i];

        try {
          debugPrint('🎤 اختبار العبارة ${i + 1}: "$phrase"');
          await _speechService.speakArabic(phrase);
          await Future.delayed(const Duration(milliseconds: 1000));

          successCount++;
          debugPrint('✅ نجح نطق العبارة: "$phrase"');
        } catch (e) {
          debugPrint('❌ فشل نطق العبارة: "$phrase" - الخطأ: $e');
        }

        await Future.delayed(const Duration(milliseconds: 300));
      }

      final successRate = (successCount / testPhrases.length * 100)
          .toStringAsFixed(1);
      debugPrint('\n⚡ نتائج الاختبار السريع:');
      debugPrint('✅ العبارات الناجحة: $successCount/${testPhrases.length}');
      debugPrint('📈 معدل النجاح: $successRate%');

      if (successRate == '100.0') {
        debugPrint('🎉 ممتاز! النطق العربي يعمل بشكل مثالي!');
      } else {
        debugPrint('⚠️ يحتاج إلى مراجعة وتحسين');
      }
    } catch (e) {
      debugPrint('❌ خطأ في الاختبار السريع: $e');
    }
  }

  /// اختبار النطق العربي لفئة معينة
  static Future<void> testCategory(String categoryName) async {
    try {
      debugPrint('🎯 اختبار النطق العربي لفئة: $categoryName');

      await _speechService.initialize();
      await _tourismService.initialize();

      final allPhrases = _tourismService.getAllPhrases();
      final categoryPhrases =
          allPhrases
              .where(
                (phrase) =>
                    phrase.category.toString().split('.').last == categoryName,
              )
              .toList();

      if (categoryPhrases.isEmpty) {
        debugPrint('⚠️ لم يتم العثور على عبارات في فئة: $categoryName');
        return;
      }

      debugPrint('📊 عدد العبارات في الفئة: ${categoryPhrases.length}');

      int successCount = 0;

      for (int i = 0; i < categoryPhrases.length && i < 5; i++) {
        final phrase = categoryPhrases[i];
        final arabicText = phrase.translations['ar'] ?? '';

        if (arabicText.isEmpty) continue;

        try {
          debugPrint('🎤 اختبار العبارة ${i + 1}: "$arabicText"');
          await _speechService.speakArabic(arabicText);
          await Future.delayed(const Duration(milliseconds: 1200));

          successCount++;
          debugPrint('✅ نجح نطق العبارة: "$arabicText"');
        } catch (e) {
          debugPrint('❌ فشل نطق العبارة: "$arabicText" - الخطأ: $e');
        }

        await Future.delayed(const Duration(milliseconds: 400));
      }

      final testedCount =
          categoryPhrases.length > 5 ? 5 : categoryPhrases.length;
      final successRate = (successCount / testedCount * 100).toStringAsFixed(1);

      debugPrint('\n🎯 نتائج اختبار فئة $categoryName:');
      debugPrint('✅ العبارات الناجحة: $successCount/$testedCount');
      debugPrint('📈 معدل النجاح: $successRate%');
    } catch (e) {
      debugPrint('❌ خطأ في اختبار الفئة: $e');
    }
  }
}
