import 'dart:convert';
import 'dart:math' as math;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:flutter/foundation.dart';
import '../models/children_content.dart';

/// خدمة تعليم الأطفال
class ChildrenEducationService {
  static const String _progressKey = 'children_progress';
  static const String _achievementsKey = 'children_achievements';
  static const String _settingsKey = 'children_settings';

  final FlutterTts _tts = FlutterTts();
  List<ChildrenContent> _allContent = [];
  List<ChildProgress> _progress = [];
  List<Achievement> _achievements = [];
  Map<String, dynamic> _settings = {};

  /// تهيئة الخدمة
  Future<void> initialize() async {
    await _initializeTTS();
    await _loadContent();
    await _loadProgress();
    await _loadAchievements();
    await _loadSettings();
  }

  /// تهيئة خدمة النطق
  Future<void> _initializeTTS() async {
    await _tts.setLanguage('ar-SA');
    await _tts.setSpeechRate(0.4); // أبطأ للأطفال
    await _tts.setVolume(1.0);
    await _tts.setPitch(1.2); // صوت أعلى للأطفال
  }

  /// تحميل المحتوى
  Future<void> _loadContent() async {
    _allContent = _generateChildrenContent();
  }

  /// تحميل التقدم
  Future<void> _loadProgress() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final progressJson = prefs.getStringList(_progressKey) ?? [];

      _progress =
          progressJson
              .map((json) => ChildProgress.fromMap(jsonDecode(json)))
              .toList();
    } catch (e) {
      debugPrint('خطأ في تحميل التقدم: $e');
    }
  }

  /// تحميل الإنجازات
  Future<void> _loadAchievements() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final achievementsJson = prefs.getStringList(_achievementsKey) ?? [];

      _achievements =
          achievementsJson
              .map((json) => Achievement.fromMap(jsonDecode(json)))
              .toList();

      // إضافة الإنجازات الافتراضية إذا لم تكن موجودة
      if (_achievements.isEmpty) {
        _achievements = _generateDefaultAchievements();
        await _saveAchievements();
      }
    } catch (e) {
      debugPrint('خطأ في تحميل الإنجازات: $e');
    }
  }

  /// تحميل الإعدادات
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_settingsKey) ?? '{}';
      _settings = jsonDecode(settingsJson);

      // إعدادات افتراضية
      _settings.putIfAbsent('soundEnabled', () => true);
      _settings.putIfAbsent('musicEnabled', () => true);
      _settings.putIfAbsent('difficulty', () => 1);
      _settings.putIfAbsent('language', () => 'ar');
    } catch (e) {
      debugPrint('خطأ في تحميل الإعدادات: $e');
    }
  }

  /// حفظ التقدم
  Future<void> _saveProgress() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final progressJson =
          _progress.map((progress) => jsonEncode(progress.toMap())).toList();

      await prefs.setStringList(_progressKey, progressJson);
    } catch (e) {
      debugPrint('خطأ في حفظ التقدم: $e');
    }
  }

  /// حفظ الإنجازات
  Future<void> _saveAchievements() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final achievementsJson =
          _achievements
              .map((achievement) => jsonEncode(achievement.toMap()))
              .toList();

      await prefs.setStringList(_achievementsKey, achievementsJson);
    } catch (e) {
      debugPrint('خطأ في حفظ الإنجازات: $e');
    }
  }

  /// حفظ الإعدادات
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_settingsKey, jsonEncode(_settings));
    } catch (e) {
      debugPrint('خطأ في حفظ الإعدادات: $e');
    }
  }

  /// الحصول على جميع المحتوى
  List<ChildrenContent> getAllContent() => _allContent;

  /// الحصول على المحتوى حسب الفئة
  List<ChildrenContent> getContentByCategory(String category) {
    return _allContent
        .where((content) => content.category == category)
        .toList();
  }

  /// الحصول على المحتوى حسب النوع
  List<ChildrenContent> getContentByType(String type) {
    return _allContent.where((content) => content.type == type).toList();
  }

  /// الحصول على المحتوى حسب الفئة العمرية
  List<ChildrenContent> getContentByAgeGroup(int ageGroup) {
    return _allContent
        .where((content) => content.ageGroup == ageGroup)
        .toList();
  }

  /// البحث في المحتوى
  List<ChildrenContent> searchContent(String query) {
    if (query.isEmpty) return _allContent;

    final lowerQuery = query.toLowerCase();
    return _allContent.where((content) {
      return content.title.toLowerCase().contains(lowerQuery) ||
          content.content.values.any(
            (text) => text.toLowerCase().contains(lowerQuery),
          );
    }).toList();
  }

  /// نطق النص
  Future<void> speakText(String text, String language) async {
    try {
      if (!_settings['soundEnabled']) return;

      await _tts.setLanguage(language == 'ar' ? 'ar-SA' : 'en-US');
      await _tts.speak(text);
    } catch (e) {
      debugPrint('خطأ في النطق: $e');
    }
  }

  /// تسجيل إكمال المحتوى
  Future<void> recordCompletion(String contentId, int score) async {
    try {
      final existingIndex = _progress.indexWhere(
        (p) => p.contentId == contentId,
      );

      if (existingIndex >= 0) {
        final existing = _progress[existingIndex];
        _progress[existingIndex] = ChildProgress(
          childId: existing.childId,
          contentId: contentId,
          attempts: existing.attempts + 1,
          bestScore: math.max(existing.bestScore, score),
          totalTime: existing.totalTime,
          lastPlayed: DateTime.now(),
          isUnlocked: true,
        );
      } else {
        _progress.add(
          ChildProgress(
            childId: 'default',
            contentId: contentId,
            attempts: 1,
            bestScore: score,
            totalTime: 0,
            lastPlayed: DateTime.now(),
            isUnlocked: true,
          ),
        );
      }

      await _saveProgress();
      await _checkAchievements(score);
    } catch (e) {
      debugPrint('خطأ في تسجيل الإكمال: $e');
    }
  }

  /// فحص الإنجازات الجديدة
  Future<void> _checkAchievements(int score) async {
    bool hasNewAchievement = false;

    for (int i = 0; i < _achievements.length; i++) {
      final achievement = _achievements[i];
      if (!achievement.isUnlocked && score >= achievement.requiredScore) {
        _achievements[i] = Achievement(
          id: achievement.id,
          title: achievement.title,
          description: achievement.description,
          icon: achievement.icon,
          color: achievement.color,
          requiredScore: achievement.requiredScore,
          category: achievement.category,
          isUnlocked: true,
          unlockedAt: DateTime.now(),
        );
        hasNewAchievement = true;
      }
    }

    if (hasNewAchievement) {
      await _saveAchievements();
    }
  }

  /// الحصول على التقدم
  List<ChildProgress> getProgress() => _progress;

  /// الحصول على الإنجازات
  List<Achievement> getAchievements() => _achievements;

  /// الحصول على الإنجازات المفتوحة
  List<Achievement> getUnlockedAchievements() {
    return _achievements.where((a) => a.isUnlocked).toList();
  }

  /// الحصول على الإعدادات
  Map<String, dynamic> getSettings() => Map.from(_settings);

  /// تحديث إعداد
  Future<void> updateSetting(String key, dynamic value) async {
    _settings[key] = value;
    await _saveSettings();
  }

  /// الحصول على إحصائيات الطفل
  Map<String, dynamic> getChildStats() {
    final totalContent = _allContent.length;
    final completedContent = _progress.length;
    final totalScore = _progress.fold<int>(0, (sum, p) => sum + p.bestScore);
    final totalTime = _progress.fold<int>(0, (sum, p) => sum + p.totalTime);
    final unlockedAchievements = getUnlockedAchievements().length;

    return {
      'totalContent': totalContent,
      'completedContent': completedContent,
      'completionRate':
          totalContent > 0
              ? (completedContent / totalContent * 100).round()
              : 0,
      'totalScore': totalScore,
      'averageScore':
          completedContent > 0 ? (totalScore / completedContent).round() : 0,
      'totalTime': totalTime,
      'unlockedAchievements': unlockedAchievements,
      'totalAchievements': _achievements.length,
    };
  }

  /// توليد المحتوى التعليمي
  List<ChildrenContent> _generateChildrenContent() {
    final content = <ChildrenContent>[];

    // محتوى الحيوانات
    content.addAll(_generateAnimalsContent());

    // محتوى الألوان
    content.addAll(_generateColorsContent());

    // محتوى الأرقام
    content.addAll(_generateNumbersContent());

    // محتوى الحروف
    content.addAll(_generateLettersContent());

    // محتوى الأشكال
    content.addAll(_generateShapesContent());

    return content;
  }

  /// توليد الإنجازات الافتراضية
  List<Achievement> _generateDefaultAchievements() {
    return [
      Achievement(
        id: 'first_lesson',
        title: 'أول درس',
        description: 'أكمل أول درس لك',
        icon: '🎓',
        color: '#4CAF50',
        requiredScore: 1,
        category: 'general',
      ),
      Achievement(
        id: 'perfect_score',
        title: 'نتيجة مثالية',
        description: 'احصل على نتيجة 100%',
        icon: '⭐',
        color: '#FFD700',
        requiredScore: 100,
        category: 'general',
      ),
      Achievement(
        id: 'animal_expert',
        title: 'خبير الحيوانات',
        description: 'أكمل جميع دروس الحيوانات',
        icon: '🐾',
        color: '#FF6B6B',
        requiredScore: 50,
        category: 'animals',
      ),
    ];
  }

  /// توليد محتوى الحيوانات
  List<ChildrenContent> _generateAnimalsContent() {
    return [
      ChildrenContent(
        title: 'أصوات الحيوانات',
        type: ContentTypes.lesson,
        category: ChildrenCategories.animals,
        content: {
          'ar': 'تعلم أصوات الحيوانات المختلفة',
          'en': 'Learn different animal sounds',
        },
        difficulty: 1,
        ageGroup: 3,
      ),
      ChildrenContent(
        title: 'قصة الأسد والفأر',
        type: ContentTypes.story,
        category: ChildrenCategories.animals,
        content: {
          'ar': 'قصة جميلة عن الأسد والفأر وأهمية المساعدة',
          'en': 'A beautiful story about the lion and the mouse',
        },
        difficulty: 2,
        ageGroup: 6,
      ),
      ChildrenContent(
        title: 'لعبة تطابق الحيوانات',
        type: ContentTypes.game,
        category: ChildrenCategories.animals,
        content: {
          'ar': 'اربط كل حيوان بصوته الصحيح',
          'en': 'Match each animal with its correct sound',
        },
        difficulty: 2,
        ageGroup: 6,
      ),
    ];
  }

  /// توليد محتوى الألوان
  List<ChildrenContent> _generateColorsContent() {
    return [
      ChildrenContent(
        title: 'تعلم الألوان الأساسية',
        type: ContentTypes.lesson,
        category: ChildrenCategories.colors,
        content: {
          'ar': 'تعرف على الألوان الأحمر والأزرق والأصفر',
          'en': 'Learn about red, blue, and yellow colors',
        },
        difficulty: 1,
        ageGroup: 3,
      ),
      ChildrenContent(
        title: 'أغنية قوس قزح',
        type: ContentTypes.song,
        category: ChildrenCategories.colors,
        content: {
          'ar': 'أغنية جميلة عن ألوان قوس قزح',
          'en': 'A beautiful song about rainbow colors',
        },
        difficulty: 1,
        ageGroup: 3,
      ),
    ];
  }

  /// توليد محتوى الأرقام
  List<ChildrenContent> _generateNumbersContent() {
    return [
      ChildrenContent(
        title: 'العد من 1 إلى 10',
        type: ContentTypes.lesson,
        category: ChildrenCategories.numbers,
        content: {
          'ar': 'تعلم العد من واحد إلى عشرة',
          'en': 'Learn to count from one to ten',
        },
        difficulty: 1,
        ageGroup: 3,
      ),
      ChildrenContent(
        title: 'لعبة الجمع البسيط',
        type: ContentTypes.game,
        category: ChildrenCategories.numbers,
        content: {
          'ar': 'تعلم جمع الأرقام الصغيرة',
          'en': 'Learn to add small numbers',
        },
        difficulty: 3,
        ageGroup: 6,
      ),
    ];
  }

  /// توليد محتوى الحروف
  List<ChildrenContent> _generateLettersContent() {
    return [
      ChildrenContent(
        title: 'الحروف العربية',
        type: ContentTypes.lesson,
        category: ChildrenCategories.letters,
        content: {
          'ar': 'تعلم الحروف العربية من الألف إلى الياء',
          'en': 'Learn Arabic letters from Alif to Ya',
        },
        difficulty: 2,
        ageGroup: 6,
      ),
      ChildrenContent(
        title: 'الحروف الإنجليزية',
        type: ContentTypes.lesson,
        category: ChildrenCategories.letters,
        content: {
          'ar': 'تعلم الحروف الإنجليزية من A إلى Z',
          'en': 'Learn English letters from A to Z',
        },
        difficulty: 2,
        ageGroup: 6,
      ),
    ];
  }

  /// توليد محتوى الأشكال
  List<ChildrenContent> _generateShapesContent() {
    return [
      ChildrenContent(
        title: 'الأشكال الهندسية',
        type: ContentTypes.lesson,
        category: ChildrenCategories.shapes,
        content: {
          'ar': 'تعرف على الدائرة والمربع والمثلث',
          'en': 'Learn about circle, square, and triangle',
        },
        difficulty: 1,
        ageGroup: 3,
      ),
      ChildrenContent(
        title: 'لعبة تطابق الأشكال',
        type: ContentTypes.game,
        category: ChildrenCategories.shapes,
        content: {
          'ar': 'اربط كل شكل بالاسم الصحيح',
          'en': 'Match each shape with its correct name',
        },
        difficulty: 2,
        ageGroup: 6,
      ),
    ];
  }
}
