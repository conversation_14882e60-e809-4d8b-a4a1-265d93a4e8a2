import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../config/app_theme.dart';
import '../../utils/app_helpers.dart';

/// شاشة الإعدادات الحديثة والاحترافية
class ModernSettingsScreen extends StatefulWidget {
  const ModernSettingsScreen({super.key});

  @override
  State<ModernSettingsScreen> createState() => _ModernSettingsScreenState();
}

class _ModernSettingsScreenState extends State<ModernSettingsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // إعدادات التطبيق
  String _selectedLanguage = 'ar';
  String _selectedTheme = 'light';
  bool _soundEnabled = true;
  bool _notificationsEnabled = true;
  bool _hapticFeedbackEnabled = true;
  String _translationQuality = 'high';
  String _preferredApiService = 'gemini';
  bool _autoDetectLanguage = true;
  bool _saveTranslationHistory = true;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _loadSettings();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// تهيئة الرسوم المتحركة
  void _initAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutQuart),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutQuart),
    );

    _animationController.forward();
  }

  /// تحميل الإعدادات المحفوظة
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      setState(() {
        _selectedLanguage = prefs.getString('language') ?? 'ar';
        _selectedTheme = prefs.getString('theme') ?? 'light';
        _soundEnabled = prefs.getBool('sound_enabled') ?? true;
        _notificationsEnabled = prefs.getBool('notifications_enabled') ?? true;
        _hapticFeedbackEnabled =
            prefs.getBool('haptic_feedback_enabled') ?? true;
        _translationQuality = prefs.getString('translation_quality') ?? 'high';
        _preferredApiService =
            prefs.getString('preferred_api_service') ?? 'gemini';
        _autoDetectLanguage = prefs.getBool('auto_detect_language') ?? true;
        _saveTranslationHistory =
            prefs.getBool('save_translation_history') ?? true;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل الإعدادات: $e');
    }
  }

  /// حفظ إعداد معين
  Future<void> _saveSetting(String key, dynamic value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (value is bool) {
        await prefs.setBool(key, value);
      } else if (value is String) {
        await prefs.setString(key, value);
      }

      // تطبيق haptic feedback
      if (_hapticFeedbackEnabled) {
        HapticFeedback.lightImpact();
      }
    } catch (e) {
      debugPrint('خطأ في حفظ الإعداد: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      body: CustomScrollView(
        physics: const BouncingScrollPhysics(),
        slivers: [
          // شريط التطبيق المتقدم
          _buildModernAppBar(colorScheme),

          // محتوى الإعدادات
          SliverToBoxAdapter(
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      // قسم اللغة والسمة
                      _buildSettingsSection(
                        'اللغة والمظهر',
                        Icons.palette_outlined,
                        [_buildLanguageSelector(), _buildThemeSelector()],
                      ),

                      const SizedBox(height: 24),

                      // قسم الصوت والإشعارات
                      _buildSettingsSection(
                        'الصوت والإشعارات',
                        Icons.volume_up_outlined,
                        [
                          _buildSwitchTile(
                            'تفعيل الأصوات',
                            'تشغيل الأصوات في التطبيق',
                            Icons.volume_up,
                            _soundEnabled,
                            (value) {
                              setState(() => _soundEnabled = value);
                              _saveSetting('sound_enabled', value);
                            },
                          ),
                          _buildSwitchTile(
                            'الإشعارات',
                            'تلقي إشعارات التطبيق',
                            Icons.notifications,
                            _notificationsEnabled,
                            (value) {
                              setState(() => _notificationsEnabled = value);
                              _saveSetting('notifications_enabled', value);
                            },
                          ),
                          _buildSwitchTile(
                            'الاهتزاز التفاعلي',
                            'اهتزاز خفيف عند اللمس',
                            Icons.vibration,
                            _hapticFeedbackEnabled,
                            (value) {
                              setState(() => _hapticFeedbackEnabled = value);
                              _saveSetting('haptic_feedback_enabled', value);
                            },
                          ),
                        ],
                      ),

                      const SizedBox(height: 24),

                      // قسم إعدادات الترجمة
                      _buildSettingsSection(
                        'إعدادات الترجمة',
                        Icons.translate_outlined,
                        [
                          _buildTranslationQualitySelector(),
                          _buildApiServiceSelector(),
                          _buildSwitchTile(
                            'اكتشاف اللغة تلقائياً',
                            'تحديد لغة النص تلقائياً',
                            Icons.auto_awesome,
                            _autoDetectLanguage,
                            (value) {
                              setState(() => _autoDetectLanguage = value);
                              _saveSetting('auto_detect_language', value);
                            },
                          ),
                          _buildSwitchTile(
                            'حفظ سجل الترجمات',
                            'حفظ الترجمات السابقة',
                            Icons.history,
                            _saveTranslationHistory,
                            (value) {
                              setState(() => _saveTranslationHistory = value);
                              _saveSetting('save_translation_history', value);
                            },
                          ),
                        ],
                      ),

                      const SizedBox(height: 24),

                      // قسم الحساب والخصوصية
                      _buildSettingsSection(
                        'الحساب والخصوصية',
                        Icons.security_outlined,
                        [
                          _buildActionTile(
                            'إدارة الحساب',
                            'تعديل بيانات الحساب',
                            Icons.account_circle,
                            () => _showComingSoon('إدارة الحساب'),
                          ),
                          _buildActionTile(
                            'الخصوصية والأمان',
                            'إعدادات الخصوصية',
                            Icons.privacy_tip,
                            () => _showComingSoon('إعدادات الخصوصية'),
                          ),
                          _buildActionTile(
                            'مسح البيانات',
                            'حذف جميع البيانات المحلية',
                            Icons.delete_outline,
                            () => _showClearDataDialog(),
                            isDestructive: true,
                          ),
                        ],
                      ),

                      const SizedBox(height: 24),

                      // قسم معلومات التطبيق
                      _buildSettingsSection(
                        'معلومات التطبيق',
                        Icons.info_outline,
                        [
                          _buildActionTile(
                            'حول التطبيق',
                            'الإصدار 1.0.0',
                            Icons.info,
                            () => _showAboutDialog(),
                          ),
                          _buildActionTile(
                            'الدعم والمساعدة',
                            'تواصل معنا للمساعدة',
                            Icons.help,
                            () => _showComingSoon('الدعم والمساعدة'),
                          ),
                          _buildActionTile(
                            'تقييم التطبيق',
                            'قيم التطبيق في المتجر',
                            Icons.star,
                            () => _showComingSoon('تقييم التطبيق'),
                          ),
                        ],
                      ),

                      const SizedBox(height: 32),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء شريط التطبيق الحديث
  Widget _buildModernAppBar(ColorScheme colorScheme) {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: colorScheme.surface,
      foregroundColor: colorScheme.onSurface,
      flexibleSpace: FlexibleSpaceBar(
        title: const Text(
          'الإعدادات',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                colorScheme.primaryContainer,
                colorScheme.secondaryContainer,
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء قسم إعدادات
  Widget _buildSettingsSection(
    String title,
    IconData icon,
    List<Widget> children,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerLow,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: AppTheme.primaryColor, size: 20),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          // محتوى القسم
          ...children,
        ],
      ),
    );
  }

  /// بناء مفتاح تبديل
  Widget _buildSwitchTile(
    String title,
    String subtitle,
    IconData icon,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return ListTile(
      leading: Icon(icon, color: AppTheme.primaryColor),
      title: Text(title, style: const TextStyle(fontWeight: FontWeight.w500)),
      subtitle: Text(subtitle, style: TextStyle(color: Colors.grey[600])),
      trailing: Switch.adaptive(
        value: value,
        onChanged: onChanged,
        activeColor: AppTheme.primaryColor,
      ),
      onTap: () => onChanged(!value),
    );
  }

  /// بناء عنصر إجراء
  Widget _buildActionTile(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap, {
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: isDestructive ? Colors.red : AppTheme.primaryColor,
      ),
      title: Text(
        title,
        style: TextStyle(
          fontWeight: FontWeight.w500,
          color: isDestructive ? Colors.red : null,
        ),
      ),
      subtitle: Text(subtitle, style: TextStyle(color: Colors.grey[600])),
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap,
    );
  }

  /// بناء منتقي اللغة
  Widget _buildLanguageSelector() {
    return ListTile(
      leading: const Icon(Icons.language, color: AppTheme.primaryColor),
      title: const Text('اللغة', style: TextStyle(fontWeight: FontWeight.w500)),
      subtitle: Text(_selectedLanguage == 'ar' ? 'العربية' : 'English'),
      trailing: const Icon(Icons.chevron_right),
      onTap: () => _showLanguageDialog(),
    );
  }

  /// بناء منتقي السمة
  Widget _buildThemeSelector() {
    String themeText;
    switch (_selectedTheme) {
      case 'light':
        themeText = 'فاتح';
        break;
      case 'dark':
        themeText = 'داكن';
        break;
      case 'amoled':
        themeText = 'AMOLED';
        break;
      default:
        themeText = 'فاتح';
    }

    return ListTile(
      leading: const Icon(Icons.brightness_6, color: AppTheme.primaryColor),
      title: const Text('السمة', style: TextStyle(fontWeight: FontWeight.w500)),
      subtitle: Text(themeText),
      trailing: const Icon(Icons.chevron_right),
      onTap: () => _showThemeDialog(),
    );
  }

  /// بناء منتقي جودة الترجمة
  Widget _buildTranslationQualitySelector() {
    String qualityText;
    switch (_translationQuality) {
      case 'high':
        qualityText = 'عالية';
        break;
      case 'medium':
        qualityText = 'متوسطة';
        break;
      case 'fast':
        qualityText = 'سريعة';
        break;
      default:
        qualityText = 'عالية';
    }

    return ListTile(
      leading: const Icon(Icons.high_quality, color: AppTheme.primaryColor),
      title: const Text(
        'جودة الترجمة',
        style: TextStyle(fontWeight: FontWeight.w500),
      ),
      subtitle: Text(qualityText),
      trailing: const Icon(Icons.chevron_right),
      onTap: () => _showQualityDialog(),
    );
  }

  /// بناء منتقي خدمة API
  Widget _buildApiServiceSelector() {
    String serviceText;
    switch (_preferredApiService) {
      case 'gemini':
        serviceText = 'Google Gemini';
        break;
      case 'openai':
        serviceText = 'OpenAI';
        break;
      case 'openrouter':
        serviceText = 'OpenRouter';
        break;
      default:
        serviceText = 'Google Gemini';
    }

    return ListTile(
      leading: const Icon(Icons.api, color: AppTheme.primaryColor),
      title: const Text(
        'خدمة الترجمة المفضلة',
        style: TextStyle(fontWeight: FontWeight.w500),
      ),
      subtitle: Text(serviceText),
      trailing: const Icon(Icons.chevron_right),
      onTap: () => _showApiServiceDialog(),
    );
  }

  /// عرض حوار اختيار اللغة
  void _showLanguageDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('اختر اللغة'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                RadioListTile<String>(
                  title: const Text('العربية'),
                  value: 'ar',
                  groupValue: _selectedLanguage,
                  onChanged: (value) {
                    setState(() => _selectedLanguage = value!);
                    _saveSetting('language', value!);
                    Navigator.pop(context);
                    AppHelpers.showSnackBar(
                      context,
                      'تم تغيير اللغة إلى العربية',
                    );
                  },
                ),
                RadioListTile<String>(
                  title: const Text('English'),
                  value: 'en',
                  groupValue: _selectedLanguage,
                  onChanged: (value) {
                    setState(() => _selectedLanguage = value!);
                    _saveSetting('language', value!);
                    Navigator.pop(context);
                    AppHelpers.showSnackBar(
                      context,
                      'Language changed to English',
                    );
                  },
                ),
              ],
            ),
          ),
    );
  }

  /// عرض حوار اختيار السمة
  void _showThemeDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('اختر السمة'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                RadioListTile<String>(
                  title: const Text('فاتح'),
                  value: 'light',
                  groupValue: _selectedTheme,
                  onChanged: (value) {
                    setState(() => _selectedTheme = value!);
                    _saveSetting('theme', value!);
                    Navigator.pop(context);
                    AppHelpers.showSnackBar(
                      context,
                      'تم تغيير السمة إلى الوضع الفاتح',
                    );
                  },
                ),
                RadioListTile<String>(
                  title: const Text('داكن'),
                  value: 'dark',
                  groupValue: _selectedTheme,
                  onChanged: (value) {
                    setState(() => _selectedTheme = value!);
                    _saveSetting('theme', value!);
                    Navigator.pop(context);
                    AppHelpers.showSnackBar(
                      context,
                      'تم تغيير السمة إلى الوضع الداكن',
                    );
                  },
                ),
                RadioListTile<String>(
                  title: const Text('AMOLED'),
                  value: 'amoled',
                  groupValue: _selectedTheme,
                  onChanged: (value) {
                    setState(() => _selectedTheme = value!);
                    _saveSetting('theme', value!);
                    Navigator.pop(context);
                    AppHelpers.showSnackBar(
                      context,
                      'تم تغيير السمة إلى وضع AMOLED',
                    );
                  },
                ),
              ],
            ),
          ),
    );
  }

  /// عرض حوار اختيار جودة الترجمة
  void _showQualityDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('اختر جودة الترجمة'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                RadioListTile<String>(
                  title: const Text('عالية'),
                  subtitle: const Text('أفضل جودة، أبطأ قليلاً'),
                  value: 'high',
                  groupValue: _translationQuality,
                  onChanged: (value) {
                    setState(() => _translationQuality = value!);
                    _saveSetting('translation_quality', value!);
                    Navigator.pop(context);
                    AppHelpers.showSnackBar(
                      context,
                      'تم تعيين الجودة إلى عالية',
                    );
                  },
                ),
                RadioListTile<String>(
                  title: const Text('متوسطة'),
                  subtitle: const Text('توازن بين الجودة والسرعة'),
                  value: 'medium',
                  groupValue: _translationQuality,
                  onChanged: (value) {
                    setState(() => _translationQuality = value!);
                    _saveSetting('translation_quality', value!);
                    Navigator.pop(context);
                    AppHelpers.showSnackBar(
                      context,
                      'تم تعيين الجودة إلى متوسطة',
                    );
                  },
                ),
                RadioListTile<String>(
                  title: const Text('سريعة'),
                  subtitle: const Text('أسرع ترجمة، جودة أقل'),
                  value: 'fast',
                  groupValue: _translationQuality,
                  onChanged: (value) {
                    setState(() => _translationQuality = value!);
                    _saveSetting('translation_quality', value!);
                    Navigator.pop(context);
                    AppHelpers.showSnackBar(
                      context,
                      'تم تعيين الجودة إلى سريعة',
                    );
                  },
                ),
              ],
            ),
          ),
    );
  }

  /// عرض حوار اختيار خدمة API
  void _showApiServiceDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('اختر خدمة الترجمة'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                RadioListTile<String>(
                  title: const Text('Google Gemini'),
                  subtitle: const Text('الأفضل للغة العربية'),
                  value: 'gemini',
                  groupValue: _preferredApiService,
                  onChanged: (value) {
                    setState(() => _preferredApiService = value!);
                    _saveSetting('preferred_api_service', value!);
                    Navigator.pop(context);
                    AppHelpers.showSnackBar(
                      context,
                      'تم تعيين خدمة Google Gemini',
                    );
                  },
                ),
                RadioListTile<String>(
                  title: const Text('OpenAI'),
                  subtitle: const Text('جودة عالية ومتنوعة'),
                  value: 'openai',
                  groupValue: _preferredApiService,
                  onChanged: (value) {
                    setState(() => _preferredApiService = value!);
                    _saveSetting('preferred_api_service', value!);
                    Navigator.pop(context);
                    AppHelpers.showSnackBar(context, 'تم تعيين خدمة OpenAI');
                  },
                ),
                RadioListTile<String>(
                  title: const Text('OpenRouter'),
                  subtitle: const Text('خدمة بديلة موثوقة'),
                  value: 'openrouter',
                  groupValue: _preferredApiService,
                  onChanged: (value) {
                    setState(() => _preferredApiService = value!);
                    _saveSetting('preferred_api_service', value!);
                    Navigator.pop(context);
                    AppHelpers.showSnackBar(
                      context,
                      'تم تعيين خدمة OpenRouter',
                    );
                  },
                ),
              ],
            ),
          ),
    );
  }

  /// عرض رسالة "قريباً"
  void _showComingSoon(String feature) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(feature),
            content: const Text(
              'هذه الميزة ستكون متاحة قريباً في التحديثات القادمة.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('حسناً'),
              ),
            ],
          ),
    );
  }

  /// عرض حوار مسح البيانات
  void _showClearDataDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('مسح البيانات'),
            content: const Text(
              'هل أنت متأكد من أنك تريد حذف جميع البيانات المحلية؟ '
              'سيتم حذف سجل الترجمات والمفضلة والإعدادات.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.pop(context);
                  await _clearAllData();
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('حذف'),
              ),
            ],
          ),
    );
  }

  /// مسح جميع البيانات
  Future<void> _clearAllData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();

      // إعادة تعيين الإعدادات للقيم الافتراضية
      setState(() {
        _selectedLanguage = 'ar';
        _selectedTheme = 'light';
        _soundEnabled = true;
        _notificationsEnabled = true;
        _hapticFeedbackEnabled = true;
        _translationQuality = 'high';
        _preferredApiService = 'gemini';
        _autoDetectLanguage = true;
        _saveTranslationHistory = true;
      });

      if (mounted) {
        AppHelpers.showSnackBar(context, 'تم حذف جميع البيانات بنجاح');
      }
    } catch (e) {
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء حذف البيانات',
          isError: true,
        );
      }
    }
  }

  /// عرض حوار حول التطبيق
  void _showAboutDialog() {
    showAboutDialog(
      context: context,
      applicationName: 'المترجم الذكي AI',
      applicationVersion: '1.0.0',
      applicationIcon: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppTheme.primaryColor,
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Icon(Icons.translate, color: Colors.white, size: 32),
      ),
      children: [
        const Text(
          'تطبيق ترجمة ذكي يستخدم أحدث تقنيات الذكاء الاصطناعي '
          'لتوفير ترجمة دقيقة وسريعة بين أكثر من 34 لغة.',
        ),
        const SizedBox(height: 16),
        const Text(
          'المطور: فريق AI Smart Translator\n'
          'البريد الإلكتروني: <EMAIL>\n'
          'الموقع: www.aitranslator.com',
          style: TextStyle(fontSize: 12),
        ),
      ],
    );
  }
}
