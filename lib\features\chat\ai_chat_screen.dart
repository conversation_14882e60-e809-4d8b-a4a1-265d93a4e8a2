import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:intl/intl.dart';
import '../../core/app_state.dart';
import '../../config/app_theme.dart';
import '../../config/constants.dart';
import '../../services/api/ai_chat_service.dart';
import '../../utils/helpers.dart';

/// نموذج رسالة المحادثة
class ChatMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;

  ChatMessage({required this.text, required this.isUser, DateTime? timestamp})
    : timestamp = timestamp ?? DateTime.now();
}

/// شاشة محادثة الذكاء الاصطناعي
class AIChatScreen extends StatefulWidget {
  const AIChatScreen({super.key});

  @override
  State<AIChatScreen> createState() => _AIChatScreenState();
}

class _AIChatScreenState extends State<AIChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final List<ChatMessage> _messages = [];

  bool _isTyping = false;
  String _selectedResponseType = 'general';

  late AIChatService _chatService;

  @override
  void initState() {
    super.initState();

    final appState = Provider.of<AppState>(context, listen: false);
    final remoteConfig = appState.remoteConfig;

    _chatService = AIChatService(remoteConfig);

    // إضافة رسالة ترحيب
    _addBotMessage('مرحباً بك في المساعد الذكي! كيف يمكنني مساعدتك اليوم؟');
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// إضافة رسالة من المستخدم
  void _addUserMessage(String message) {
    setState(() {
      _messages.add(ChatMessage(text: message, isUser: true));
    });

    _scrollToBottom();
  }

  /// إضافة رسالة من الروبوت
  void _addBotMessage(String message) {
    setState(() {
      _messages.add(ChatMessage(text: message, isUser: false));
    });

    _scrollToBottom();
  }

  /// التمرير إلى أسفل المحادثة
  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  /// إرسال رسالة
  Future<void> _sendMessage() async {
    final message = _messageController.text.trim();

    if (message.isEmpty) return;

    _messageController.clear();
    _addUserMessage(message);

    setState(() {
      _isTyping = true;
    });

    try {
      final appState = Provider.of<AppState>(context, listen: false);

      // التحقق مما إذا كان المستخدم مشتركًا في الخطة المميزة
      if (!appState.isPremium && _messages.length > 10) {
        _addBotMessage(
          'عذراً، لقد وصلت إلى الحد الأقصى للرسائل في الخطة المجانية. '
          'يرجى الترقية إلى الخطة المميزة للاستمرار في استخدام المساعد الذكي بلا حدود.',
        );

        setState(() {
          _isTyping = false;
        });
        return;
      }

      // الحصول على رد من الذكاء الاصطناعي
      final response = await _chatService.getChatResponse(
        message: message,
        responseType: _selectedResponseType,
        chatHistory:
            _messages
                .map(
                  (m) => {
                    'role': m.isUser ? 'user' : 'assistant',
                    'content': m.text,
                  },
                )
                .toList(),
      );

      if (mounted) {
        _addBotMessage(response);
      }
    } catch (e) {
      if (mounted) {
        _addBotMessage(
          'عذراً، حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى.',
        );

        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء إرسال الرسالة: ${e.toString()}',
          isError: true,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isTyping = false;
        });
      }
    }
  }

  /// تغيير نوع الاستجابة
  void _changeResponseType(String type) {
    setState(() {
      _selectedResponseType = type;
    });

    AppHelpers.showSnackBar(
      context,
      'تم تغيير نوع الاستجابة إلى: ${_getResponseTypeName(type)}',
    );
  }

  /// الحصول على اسم نوع الاستجابة
  String _getResponseTypeName(String type) {
    final responseType = AppConstants.aiResponseTypes.firstWhere(
      (t) => t['id'] == type,
      orElse: () => {'id': type, 'title': type},
    );

    return responseType['title'] as String;
  }

  /// عرض مربع حوار اختيار نوع الاستجابة
  void _showResponseTypeSelector() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'اختر نوع الاستجابة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 20),
                Wrap(
                  spacing: 10,
                  runSpacing: 10,
                  children:
                      AppConstants.aiResponseTypes.map((type) {
                        final id = type['id'] as String;
                        final title = type['title'] as String;

                        return ChoiceChip(
                          label: Text(title),
                          selected: _selectedResponseType == id,
                          onSelected: (selected) {
                            if (selected) {
                              Navigator.pop(context);
                              _changeResponseType(id);
                            }
                          },
                          selectedColor: AppTheme.primaryColor,
                          labelStyle: TextStyle(
                            color:
                                _selectedResponseType == id
                                    ? Colors.white
                                    : AppTheme.onBackground,
                          ),
                        );
                      }).toList(),
                ),
              ],
            ),
          ),
    );
  }

  /// تنسيق التاريخ
  String _formatTimestamp(DateTime timestamp) {
    return DateFormat('HH:mm').format(timestamp);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المساعد الذكي'),
        centerTitle: true,
        backgroundColor: AppTheme.primaryColor,
        actions: [
          // زر اختيار نوع الاستجابة
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showResponseTypeSelector,
            tooltip: 'نوع الاستجابة',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط نوع الاستجابة
          Container(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            color: Colors.grey.shade100,
            child: Row(
              children: [
                const Text(
                  'نوع الاستجابة:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.onBackground,
                  ),
                ),
                const SizedBox(width: 8),
                Chip(
                  label: Text(_getResponseTypeName(_selectedResponseType)),
                  backgroundColor: Color.fromRGBO(0, 122, 255, 0.1),
                  labelStyle: const TextStyle(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          // قائمة الرسائل
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.all(16),
              itemCount: _messages.length,
              itemBuilder: (context, index) {
                final message = _messages[index];
                return _buildMessageItem(message);
              },
            ),
          ),

          // مؤشر الكتابة
          if (_isTyping)
            Container(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              alignment: Alignment.centerLeft,
              child: const Text(
                'جاري الكتابة...',
                style: TextStyle(
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                  color: AppTheme.onSurfaceVariant,
                ),
              ),
            ),

          // حقل إدخال الرسالة
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Color.fromRGBO(128, 128, 128, 0.2),
                  blurRadius: 10,
                  offset: const Offset(0, -3),
                ),
              ],
            ),
            child: Row(
              children: [
                // حقل الإدخال
                Expanded(
                  child: TextField(
                    controller: _messageController,
                    decoration: const InputDecoration(
                      hintText: 'اكتب رسالتك هنا...',
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                    ),
                    textInputAction: TextInputAction.send,
                    onSubmitted: (_) => _sendMessage(),
                    maxLines: null,
                  ),
                ),

                // زر الإرسال
                IconButton(
                  icon: const Icon(Icons.send),
                  color: AppTheme.primaryColor,
                  onPressed: _isTyping ? null : _sendMessage,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر الرسالة
  Widget _buildMessageItem(ChatMessage message) {
    return Align(
          alignment:
              message.isUser ? Alignment.centerRight : Alignment.centerLeft,
          child: Container(
            margin: const EdgeInsets.only(bottom: 16),
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.75,
            ),
            decoration: BoxDecoration(
              color:
                  message.isUser ? AppTheme.primaryColor : Colors.grey.shade200,
              borderRadius: BorderRadius.circular(16),
            ),
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // نص الرسالة
                Text(
                  message.text,
                  style: TextStyle(
                    fontSize: 16,
                    color:
                        message.isUser ? Colors.white : AppTheme.onBackground,
                  ),
                ),

                const SizedBox(height: 4),

                // توقيت الرسالة
                Text(
                  _formatTimestamp(message.timestamp),
                  style: TextStyle(
                    fontSize: 10,
                    color:
                        message.isUser
                            ? Color.fromRGBO(255, 255, 255, 0.7)
                            : AppTheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        )
        .animate()
        .fadeIn(duration: 300.ms)
        .slideY(begin: 0.2, end: 0, duration: 300.ms);
  }
}
