import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/duolingo_models.dart';
import '../../services/duolingo_service.dart';
import '../../utils/responsive_helper.dart';
import 'widgets/unit_card.dart';
import 'widgets/progress_header.dart';
import 'widgets/language_selector_widget.dart';
import 'lesson_screen.dart';

/// الشاشة الرئيسية مثل Duolingo
class DuolingoMainScreen extends StatefulWidget {
  const DuolingoMainScreen({super.key});

  @override
  State<DuolingoMainScreen> createState() => _DuolingoMainScreenState();
}

class _DuolingoMainScreenState extends State<DuolingoMainScreen>
    with TickerProviderStateMixin {
  final DuolingoService _duolingoService = DuolingoService();
  late AnimationController _animationController;
  late ScrollController _scrollController;

  String _selectedLanguage = 'en';
  List<LearningUnit> _units = [];
  UserProfile? _userProfile;
  LearningStats? _stats;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _scrollController = ScrollController();
    _initializeApp();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// تهيئة التطبيق
  Future<void> _initializeApp() async {
    try {
      await _duolingoService.initialize();

      // إنشاء ملف مستخدم تجريبي إذا لم يوجد
      if (_duolingoService.currentUser == null) {
        await _duolingoService.createUserProfile(
          name: 'مستخدم تجريبي',
          email: '<EMAIL>',
          nativeLanguage: 'ar',
          learningLanguages: [
            'en',
            'fr',
            'de',
            'es',
            'it',
            'ja',
            'zh',
            'tr',
            'ru',
          ],
        );
      }

      _loadLanguageContent();
      _animationController.forward();
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة التطبيق: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// تحميل محتوى اللغة
  void _loadLanguageContent() {
    setState(() {
      _units = _duolingoService.getUnitsForLanguage(_selectedLanguage);
      _userProfile = _duolingoService.currentUser;
      _stats = _duolingoService.getStatsForLanguage(_selectedLanguage);
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return _buildLoadingScreen(context);
    }

    return Scaffold(
      backgroundColor: const Color(0xFFF7F8FA),
      body: ResponsiveLayout(
        child: CustomScrollView(
          controller: _scrollController,
          slivers: [
            _buildAppBar(context),
            SliverToBoxAdapter(
              child: Column(
                children: [
                  _buildProgressHeader(context),
                  _buildLanguageSelector(context),
                  SizedBox(
                    height: ResponsiveHelper.getResponsivePadding(
                      context,
                      mobile: 16,
                      tablet: 20,
                      desktop: 24,
                    ),
                  ),
                ],
              ),
            ),
            _buildUnitsGrid(context),
            SliverToBoxAdapter(
              child: SizedBox(
                height: ResponsiveHelper.getResponsivePadding(
                  context,
                  mobile: 80,
                  tablet: 100,
                  desktop: 120,
                ),
              ), // مساحة إضافية في الأسفل
            ),
          ],
        ),
      ),
      floatingActionButton:
          ResponsiveHelper.isSmallScreen(context)
              ? _buildFloatingActionButton(context)
              : null,
    );
  }

  /// شاشة التحميل
  Widget _buildLoadingScreen(BuildContext context) {
    final logoSize = ResponsiveHelper.getResponsiveIconSize(
      context,
      mobile: 100,
      tablet: 120,
      desktop: 140,
    );

    return Scaffold(
      backgroundColor: const Color(0xFF58CC02),
      body: ResponsiveLayout(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // شعار Duolingo
              Container(
                width: logoSize,
                height: logoSize,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(
                    ResponsiveHelper.getResponsiveBorderRadius(context) * 1.5,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      spreadRadius: 5,
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Icon(
                  Icons.translate,
                  size: logoSize * 0.5,
                  color: const Color(0xFF58CC02),
                ),
              ),
              SizedBox(
                height: ResponsiveHelper.getResponsivePadding(
                  context,
                  mobile: 24,
                  tablet: 30,
                  desktop: 36,
                ),
              ),
              ResponsiveText(
                'AI Smart Translator',
                mobileFontSize: 24,
                tabletFontSize: 28,
                desktopFontSize: 32,
                fontWeight: FontWeight.bold,
                color: Colors.white,
                textAlign: TextAlign.center,
              ),
              SizedBox(
                height: ResponsiveHelper.getResponsivePadding(
                  context,
                  mobile: 8,
                  tablet: 10,
                  desktop: 12,
                ),
              ),
              ResponsiveText(
                'جاري تحميل المحتوى التعليمي...',
                mobileFontSize: 14,
                tabletFontSize: 16,
                desktopFontSize: 18,
                color: Colors.white70,
                textAlign: TextAlign.center,
              ),
              SizedBox(
                height: ResponsiveHelper.getResponsivePadding(
                  context,
                  mobile: 24,
                  tablet: 30,
                  desktop: 36,
                ),
              ),
              const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// شريط التطبيق
  Widget _buildAppBar(BuildContext context) {
    final expandedHeight = ResponsiveHelper.getResponsiveHeight(
      context,
      mobile: 100,
      tablet: 120,
      desktop: 140,
    );

    return SliverAppBar(
      expandedHeight: expandedHeight,
      floating: false,
      pinned: true,
      backgroundColor: const Color(0xFF58CC02),
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Color(0xFF58CC02), Color(0xFF89E219)],
            ),
          ),
        ),
      ),
      leading: IconButton(
        onPressed: () => Navigator.pop(context),
        icon: Icon(
          Icons.arrow_back,
          color: Colors.white,
          size: ResponsiveHelper.getResponsiveIconSize(context),
        ),
      ),
      title: ResponsiveText(
        'تعلم اللغات',
        mobileFontSize: 18,
        tabletFontSize: 20,
        desktopFontSize: 22,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      ),
      actions: [
        // أيقونة الإعدادات
        IconButton(
          onPressed: _showSettings,
          icon: Icon(
            Icons.settings,
            color: Colors.white,
            size: ResponsiveHelper.getResponsiveIconSize(context),
          ),
        ),
        // أيقونة الملف الشخصي
        IconButton(
          onPressed: _showProfile,
          icon: Icon(
            Icons.person,
            color: Colors.white,
            size: ResponsiveHelper.getResponsiveIconSize(context),
          ),
        ),
      ],
    );
  }

  /// رأس التقدم
  Widget _buildProgressHeader(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0, -1),
            end: Offset.zero,
          ).animate(
            CurvedAnimation(
              parent: _animationController,
              curve: const Interval(0.0, 0.5, curve: Curves.easeOut),
            ),
          ),
          child: ProgressHeader(
            userProfile: _userProfile,
            stats: _stats,
            languageCode: _selectedLanguage,
          ),
        );
      },
    );
  }

  /// اختيار اللغة
  Widget _buildLanguageSelector(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(-1, 0),
            end: Offset.zero,
          ).animate(
            CurvedAnimation(
              parent: _animationController,
              curve: const Interval(0.2, 0.7, curve: Curves.easeOut),
            ),
          ),
          child: LanguageSelectorWidget(
            selectedLanguage: _selectedLanguage,
            onLanguageChanged: _onLanguageChanged,
          ),
        );
      },
    );
  }

  /// شبكة الوحدات
  Widget _buildUnitsGrid(BuildContext context) {
    final horizontalPadding = ResponsiveHelper.getResponsivePadding(
      context,
      mobile: 16,
      tablet: 24,
      desktop: 32,
    );

    final bottomSpacing = ResponsiveHelper.getResponsivePadding(
      context,
      mobile: 16,
      tablet: 20,
      desktop: 24,
    );

    return SliverPadding(
      padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate((context, index) {
          final unit = _units[index];
          return AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(1, 0),
                  end: Offset.zero,
                ).animate(
                  CurvedAnimation(
                    parent: _animationController,
                    curve: Interval(
                      0.4 + (index * 0.1),
                      0.9 + (index * 0.1),
                      curve: Curves.easeOut,
                    ),
                  ),
                ),
                child: Padding(
                  padding: EdgeInsets.only(bottom: bottomSpacing),
                  child: UnitCard(
                    unit: unit,
                    onTap: () => _openUnit(unit),
                    onLessonTap: (lesson) => _openLesson(lesson, unit),
                  ),
                ),
              );
            },
          );
        }, childCount: _units.length),
      ),
    );
  }

  /// زر الإجراء العائم
  Widget _buildFloatingActionButton(BuildContext context) {
    return FloatingActionButton.extended(
      onPressed: _showDailyChallenge,
      backgroundColor: const Color(0xFFFF9600),
      icon: Icon(
        Icons.flash_on,
        color: Colors.white,
        size: ResponsiveHelper.getResponsiveIconSize(
          context,
          mobile: 20,
          tablet: 22,
          desktop: 24,
        ),
      ),
      label: ResponsiveText(
        'التحدي اليومي',
        mobileFontSize: 12,
        tabletFontSize: 14,
        desktopFontSize: 16,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      ),
    );
  }

  /// تغيير اللغة
  void _onLanguageChanged(String languageCode) {
    setState(() {
      _selectedLanguage = languageCode;
    });
    _loadLanguageContent();

    // رسوم متحركة للانتقال
    _animationController.reset();
    _animationController.forward();
  }

  /// فتح الوحدة
  void _openUnit(LearningUnit unit) {
    if (!unit.isUnlocked) {
      _showLockedUnitDialog(unit);
      return;
    }

    HapticFeedback.lightImpact();
    // يمكن إضافة شاشة تفاصيل الوحدة هنا
  }

  /// فتح الدرس
  void _openLesson(Lesson lesson, LearningUnit unit) {
    if (lesson.status == LessonStatus.locked) {
      _showLockedLessonDialog(lesson);
      return;
    }

    HapticFeedback.mediumImpact();
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => LessonScreen(
              lesson: lesson,
              unit: unit,
              duolingoService: _duolingoService,
            ),
      ),
    ).then((_) {
      // تحديث البيانات عند العودة
      _loadLanguageContent();
    });
  }

  /// عرض حوار الوحدة المقفلة
  void _showLockedUnitDialog(LearningUnit unit) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('🔒 وحدة مقفلة'),
            content: Text(
              'يجب إكمال الوحدة السابقة لفتح "${unit.titleAr}".\n\n'
              'نقاط الخبرة المطلوبة: ${unit.requiredXP} XP',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('حسناً'),
              ),
            ],
          ),
    );
  }

  /// عرض حوار الدرس المقفل
  void _showLockedLessonDialog(Lesson lesson) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('🔒 درس مقفل'),
            content: Text('يجب إكمال الدرس السابق لفتح "${lesson.titleAr}".'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('حسناً'),
              ),
            ],
          ),
    );
  }

  /// عرض الإعدادات
  void _showSettings() {
    // سيتم تنفيذ شاشة الإعدادات لاحقاً
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم إضافة الإعدادات قريباً')),
    );
  }

  /// عرض الملف الشخصي
  void _showProfile() {
    // سيتم تنفيذ شاشة الملف الشخصي لاحقاً
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم إضافة الملف الشخصي قريباً')),
    );
  }

  /// عرض التحدي اليومي
  void _showDailyChallenge() {
    // سيتم تنفيذ التحدي اليومي لاحقاً
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم إضافة التحدي اليومي قريباً')),
    );
  }
}
