/// نموذج بيانات صفحة الترحيب
class OnboardingItem {
  /// عنوان الصفحة
  final String title;
  
  /// وصف الصفحة
  final String description;
  
  /// مسار ملف الرسوم المتحركة
  final String animationPath;

  OnboardingItem({
    required this.title,
    required this.description,
    required this.animationPath,
  });
}

/// بيانات صفحات الترحيب
final List<OnboardingItem> onboardingData = [
  OnboardingItem(
    title: 'ترجمة فورية',
    description: 'ترجم النصوص والصوت والصور بشكل فوري وبدقة عالية باستخدام أحدث تقنيات الذكاء الاصطناعي',
    animationPath: 'assets/animations/translation.json',
  ),
  OnboardingItem(
    title: 'دعم متعدد اللغات',
    description: 'أكثر من 100 لغة مدعومة بما فيها اللغات النادرة والأقل استخداماً حول العالم',
    animationPath: 'assets/animations/languages.json',
  ),
  OnboardingItem(
    title: 'ترجمة الصور والمستندات',
    description: 'التقط صورة لأي نص أو مستند وترجمه فوراً مع الحفاظ على التنسيق الأصلي',
    animationPath: 'assets/animations/document.json',
  ),
  OnboardingItem(
    title: 'محادثة ذكية',
    description: 'تحدث مع المساعد الذكي للحصول على ترجمات دقيقة ومساعدة في تعلم اللغات',
    animationPath: 'assets/animations/chat.json',
  ),
];
