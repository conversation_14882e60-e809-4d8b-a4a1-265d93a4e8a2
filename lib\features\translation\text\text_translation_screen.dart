import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:flutter_tts/flutter_tts.dart';
import '../../../core/app_state.dart';
import '../../../services/api/translation_service.dart';
import '../../../config/app_theme.dart';
import '../../../config/constants.dart';
import '../../../utils/helpers.dart';

/// شاشة الترجمة النصية
class TextTranslationScreen extends StatefulWidget {
  const TextTranslationScreen({super.key});

  @override
  State<TextTranslationScreen> createState() => _TextTranslationScreenState();
}

class _TextTranslationScreenState extends State<TextTranslationScreen> {
  final TextEditingController _sourceTextController = TextEditingController();

  String _translatedText = '';
  String _sourceLanguage = 'ar';
  String _targetLanguage = 'en';
  bool _isTranslating = false;
  bool _isFavorite = false;
  bool _isSpeaking = false;

  // إضافة متغير FlutterTts
  final FlutterTts _flutterTts = FlutterTts();

  @override
  void initState() {
    super.initState();
    _initTts();
  }

  /// تهيئة خدمة تحويل النص إلى كلام
  Future<void> _initTts() async {
    try {
      // استخدام _getLocaleId للحصول على معرف اللغة المناسب
      await _flutterTts.setLanguage(_getLocaleId(_targetLanguage));

      // تعيين معدل الكلام (أبطأ للغة العربية)
      await _flutterTts.setSpeechRate(_targetLanguage == 'ar' ? 0.4 : 0.5);

      // تعيين مستوى الصوت
      await _flutterTts.setVolume(1.0);

      // تعيين طبقة الصوت (أعلى قليلاً للغة العربية)
      await _flutterTts.setPitch(_targetLanguage == 'ar' ? 1.1 : 1.0);

      // الحصول على اللغات المدعومة للتحقق
      final availableLanguages = await _flutterTts.getLanguages;
      final availableVoices = await _flutterTts.getVoices;

      debugPrint('Available TTS languages: $availableLanguages');
      debugPrint('Available TTS voices: $availableVoices');

      // تعيين معالج الانتهاء
      _flutterTts.setCompletionHandler(() {
        setState(() {
          _isSpeaking = false;
        });
      });

      // تعيين معالج الخطأ
      _flutterTts.setErrorHandler((error) {
        debugPrint('TTS error: $error');
        setState(() {
          _isSpeaking = false;
        });
      });
    } catch (e) {
      debugPrint('TTS initialization error: $e');
    }
  }

  @override
  void dispose() {
    _sourceTextController.dispose();
    _flutterTts.stop();
    super.dispose();
  }

  /// ترجمة النص
  Future<void> _translateText() async {
    final sourceText = _sourceTextController.text.trim();

    if (sourceText.isEmpty) {
      AppHelpers.showSnackBar(context, 'يرجى إدخال نص للترجمة', isError: true);
      return;
    }

    setState(() {
      _isTranslating = true;
    });

    try {
      final appState = Provider.of<AppState>(context, listen: false);
      final remoteConfig = appState.remoteConfig;
      final translationService = TranslationService(remoteConfig);
      final translatedText = await translationService.translateText(
        text: sourceText,
        sourceLanguage: _sourceLanguage,
        targetLanguage: _targetLanguage,
      );

      setState(() {
        _translatedText = translatedText;
        _isTranslating = false;
      });
    } catch (e) {
      setState(() {
        _isTranslating = false;
      });

      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء الترجمة: $e',
          isError: true,
        );
      }
    }
  }

  /// تبديل اللغات
  void _swapLanguages() {
    setState(() {
      final temp = _sourceLanguage;
      _sourceLanguage = _targetLanguage;
      _targetLanguage = temp;

      // إذا كان هناك نص مترجم، قم بتبديله مع النص المصدر
      if (_translatedText.isNotEmpty) {
        _sourceTextController.text = _translatedText;
        _translatedText = '';
      }
    });
  }

  /// نسخ النص المترجم
  void _copyTranslatedText() {
    if (_translatedText.isEmpty) return;

    Clipboard.setData(ClipboardData(text: _translatedText));

    AppHelpers.showSnackBar(context, 'تم نسخ النص المترجم');
  }

  /// مشاركة النص المترجم
  void _shareTranslatedText() {
    if (_translatedText.isEmpty) return;

    Share.share(_translatedText);
  }

  /// نطق النص المترجم
  Future<void> _speakTranslatedText() async {
    if (_translatedText.isEmpty) return;

    if (_isSpeaking) {
      await _flutterTts.stop();
      setState(() {
        _isSpeaking = false;
      });
      return;
    }

    try {
      // استخدام _getLocaleId للحصول على معرف اللغة المناسب
      final localeId = _getLocaleId(_targetLanguage);
      await _flutterTts.setLanguage(localeId);

      // تعيين معدل الكلام (أبطأ للغة العربية)
      await _flutterTts.setSpeechRate(_targetLanguage == 'ar' ? 0.4 : 0.5);

      // تعيين طبقة الصوت (أعلى قليلاً للغة العربية)
      await _flutterTts.setPitch(_targetLanguage == 'ar' ? 1.1 : 1.0);

      debugPrint('Speaking text in language: $localeId');

      setState(() {
        _isSpeaking = true;
      });

      // استخدام await للتأكد من أن الأمر تم تنفيذه
      final result = await _flutterTts.speak(_translatedText);
      debugPrint('TTS speak result: $result');

      // إذا فشل النطق، نعيد المحاولة مرة واحدة
      if (result == 0) {
        await Future.delayed(const Duration(milliseconds: 500));

        // محاولة استخدام لغة بديلة إذا كانت اللغة العربية
        if (_targetLanguage == 'ar') {
          final success = await _tryAlternativeLanguage(_translatedText);
          if (!success) {
            // إذا فشلت اللغة البديلة، نعيد المحاولة باللغة الأصلية
            await _flutterTts.setLanguage(localeId);
            await _flutterTts.speak(_translatedText);
          }
        } else {
          // إذا كانت لغة أخرى، نعيد المحاولة فقط
          await _flutterTts.speak(_translatedText);
        }
      }
    } catch (e) {
      setState(() {
        _isSpeaking = false;
      });

      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء النطق: $e',
          isError: true,
        );
      }
    }
  }

  /// الحصول على معرف اللغة المناسب لـ TTS
  String _getLocaleId(String languageCode) {
    switch (languageCode) {
      case 'ar':
        // محاولة استخدام عدة معرفات للغة العربية لزيادة فرص التوافق
        // ar-SA: العربية (السعودية)، ar-EG: العربية (مصر)، ar: العربية (عام)
        return 'ar-SA';
      case 'en':
        return 'en-US';
      case 'fr':
        return 'fr-FR';
      case 'es':
        return 'es-ES';
      case 'de':
        return 'de-DE';
      case 'it':
        return 'it-IT';
      case 'ru':
        return 'ru-RU';
      case 'zh':
        return 'zh-CN';
      case 'ja':
        return 'ja-JP';
      case 'ko':
        return 'ko-KR';
      default:
        return languageCode;
    }
  }

  /// محاولة استخدام لغة بديلة إذا فشلت اللغة الأصلية
  Future<bool> _tryAlternativeLanguage(String text) async {
    try {
      if (_targetLanguage == 'ar') {
        // محاولة استخدام اللغة العربية (مصر) إذا فشلت اللغة العربية (السعودية)
        await _flutterTts.setLanguage('ar-EG');
        await _flutterTts.speak(text);
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error trying alternative language: $e');
      return false;
    }
  }

  /// إضافة/إزالة من المفضلة
  void _toggleFavorite() {
    if (_translatedText.isEmpty) return;

    setState(() {
      _isFavorite = !_isFavorite;
    });

    AppHelpers.showSnackBar(
      context,
      _isFavorite ? 'تمت الإضافة إلى المفضلة' : 'تمت الإزالة من المفضلة',
    );
  }

  /// مسح النص
  void _clearText() {
    setState(() {
      _sourceTextController.clear();
      _translatedText = '';
    });
  }

  /// عرض مربع حوار اختيار اللغة
  Future<void> _showLanguageSelector(bool isSource) async {
    final selectedLanguage = await showModalBottomSheet<String>(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => _buildLanguageSelector(isSource),
    );

    if (selectedLanguage != null) {
      setState(() {
        if (isSource) {
          _sourceLanguage = selectedLanguage;
        } else {
          _targetLanguage = selectedLanguage;
        }
      });
    }
  }

  /// بناء مربع حوار اختيار اللغة
  Widget _buildLanguageSelector(bool isSource) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            isSource ? 'اختر اللغة المصدر' : 'اختر اللغة الهدف',
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: ListView.builder(
              itemCount: AppConstants.supportedLanguages.length,
              itemBuilder: (context, index) {
                final language = AppConstants.supportedLanguages[index];
                return ListTile(
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ), // Adjusted padding
                  leading: Text(
                    language['flag'] as String,
                    style: const TextStyle(fontSize: 24),
                  ),
                  title: Text(
                    language['name'] as String,
                    style: const TextStyle(fontSize: 16), // Adjusted font size
                  ),
                  onTap: () {
                    Navigator.pop(context, language['code'] as String);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على اسم اللغة من الرمز
  String _getLanguageName(String languageCode) {
    final language = AppConstants.supportedLanguages.firstWhere(
      (lang) => lang['code'] == languageCode,
      orElse: () => {'code': languageCode, 'name': languageCode, 'flag': '🏳️'},
    );

    return '${language['flag']} ${language['name']}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('ترجمة النصوص'),
        centerTitle: true,
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // اختيار اللغات
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // اللغة المصدر
                Expanded(
                  child: InkWell(
                    onTap: () => _showLanguageSelector(true),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        vertical: 12,
                        horizontal: 8, // Reduced horizontal padding
                      ),
                      decoration: BoxDecoration(
                        color:
                            Theme.of(context).brightness == Brightness.dark
                                ? Theme.of(
                                  context,
                                ).inputDecorationTheme.fillColor
                                : Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _getLanguageName(_sourceLanguage),
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),

                // زر تبديل اللغات
                const VerticalDivider(
                  width: 16,
                  thickness: 1,
                ), // Added left divider
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8.0,
                  ), // Added horizontal padding
                  child: IconButton(
                    onPressed: _swapLanguages,
                    icon: const Icon(Icons.swap_horiz),
                    color: AppTheme.primaryColor,
                  ),
                ),
                const VerticalDivider(
                  width: 16,
                  thickness: 1,
                ), // Added right divider
                // اللغة الهدف
                Expanded(
                  child: InkWell(
                    onTap: () => _showLanguageSelector(false),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        vertical: 12,
                        horizontal: 8, // Reduced horizontal padding
                      ),
                      decoration: BoxDecoration(
                        color:
                            Theme.of(context).brightness == Brightness.dark
                                ? Theme.of(
                                  context,
                                ).inputDecorationTheme.fillColor
                                : Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _getLanguageName(_targetLanguage),
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16), // Reduced spacing
            // حقل النص المصدر
            Expanded(
              child: TextField(
                controller: _sourceTextController,
                maxLines: null,
                expands: true,
                textAlignVertical: TextAlignVertical.top,
                decoration: InputDecoration(
                  hintText: 'أدخل النص للترجمة...',
                  labelText:
                      'النص المصدر (${_getLanguageName(_sourceLanguage)})',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(16),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor:
                      Theme.of(context).brightness == Brightness.dark
                          ? Theme.of(context).inputDecorationTheme.fillColor
                          : Colors.grey.shade100,
                  contentPadding: const EdgeInsets.all(16),
                  suffixIcon: IconButton(
                    onPressed: _clearText,
                    icon: const Icon(Icons.clear),
                    tooltip: 'مسح النص',
                  ),
                ),
                // استخدام null بدلاً من TextDirection لأن TextField يتوقع TextDirection؟
                textDirection: null,
                onChanged: (text) {
                  setState(() {
                    // تحديث اتجاه النص
                  });
                },
              ),
            ),

            const SizedBox(height: 16), // Reduced spacing
            // زر الترجمة
            ElevatedButton(
              onPressed: _isTranslating ? null : _translateText,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Theme.of(context).colorScheme.onPrimary,
                minimumSize: const Size(double.infinity, 56),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              child:
                  _isTranslating
                      ? const CircularProgressIndicator(color: Colors.white)
                      : const Text(
                        'ترجم',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
            ),

            const SizedBox(height: 16), // Reduced spacing
            // حقل النص المترجم
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color:
                      Theme.of(context).brightness == Brightness.dark
                          ? Theme.of(context).inputDecorationTheme.fillColor
                          : Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // عنوان الحقل
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'النص المترجم (${_getLanguageName(_targetLanguage)})',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // زر النطق
                            IconButton(
                              onPressed:
                                  _translatedText.isEmpty
                                      ? null
                                      : _speakTranslatedText,
                              icon: Icon(
                                _isSpeaking ? Icons.stop : Icons.volume_up,
                                color:
                                    _isSpeaking
                                        ? Theme.of(context).colorScheme.primary
                                        : null,
                              ),
                              tooltip: _isSpeaking ? 'إيقاف النطق' : 'نطق النص',
                            ),

                            // زر النسخ
                            IconButton(
                              onPressed:
                                  _translatedText.isEmpty
                                      ? null
                                      : _copyTranslatedText,
                              icon: const Icon(Icons.copy),
                              tooltip: 'نسخ النص',
                            ),

                            // زر المشاركة
                            IconButton(
                              onPressed:
                                  _translatedText.isEmpty
                                      ? null
                                      : _shareTranslatedText,
                              icon: const Icon(Icons.share),
                              tooltip: 'مشاركة النص',
                            ),

                            // زر المفضلة
                            IconButton(
                              onPressed:
                                  _translatedText.isEmpty
                                      ? null
                                      : _toggleFavorite,
                              icon: Icon(
                                _isFavorite
                                    ? Icons.favorite
                                    : Icons.favorite_border,
                                color: _isFavorite ? Colors.red : null,
                              ),
                              tooltip:
                                  _isFavorite
                                      ? 'إزالة من المفضلة'
                                      : 'إضافة إلى المفضلة',
                            ),
                          ],
                        ),
                      ],
                    ),

                    const SizedBox(height: 8),

                    // النص المترجم
                    Expanded(
                      child: SingleChildScrollView(
                        child: Align(
                          alignment: Alignment.topRight,
                          child: Text(
                            _translatedText.isEmpty
                                ? 'النص المترجم سيظهر هنا...'
                                : _translatedText,
                            style: TextStyle(
                              fontSize: 16,
                              color:
                                  _translatedText.isEmpty
                                      ? Colors.grey
                                      : Theme.of(context).brightness ==
                                          Brightness.dark
                                      ? Colors.white
                                      : Colors.black,
                            ),
                            // استخدام null بدلاً من TextDirection لأن Text يتوقع TextDirection؟
                            textDirection: null,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
