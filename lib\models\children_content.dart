import 'package:uuid/uuid.dart';

/// نموذج محتوى تعليم الأطفال
class ChildrenContent {
  final String id;
  final String title;
  final String type; // story, game, lesson, quiz
  final String category; // animals, colors, numbers, letters, etc.
  final Map<String, String> content; // محتوى بلغات مختلفة
  final String imageUrl;
  final String audioUrl;
  final int difficulty; // 1-5
  final int ageGroup; // 3-5, 6-8, 9-12
  final bool isCompleted;
  final int stars; // تقييم الطفل 1-5
  final DateTime? completedAt;
  final DateTime createdAt;

  ChildrenContent({
    String? id,
    required this.title,
    required this.type,
    required this.category,
    required this.content,
    this.imageUrl = '',
    this.audioUrl = '',
    this.difficulty = 1,
    this.ageGroup = 6,
    this.isCompleted = false,
    this.stars = 0,
    this.completedAt,
    DateTime? createdAt,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now();

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'type': type,
      'category': category,
      'content': content,
      'imageUrl': imageUrl,
      'audioUrl': audioUrl,
      'difficulty': difficulty,
      'ageGroup': ageGroup,
      'isCompleted': isCompleted,
      'stars': stars,
      'completedAt': completedAt?.millisecondsSinceEpoch,
      'createdAt': createdAt.millisecondsSinceEpoch,
    };
  }

  /// إنشاء من Map
  factory ChildrenContent.fromMap(Map<String, dynamic> map) {
    return ChildrenContent(
      id: map['id'],
      title: map['title'],
      type: map['type'],
      category: map['category'],
      content: Map<String, String>.from(map['content']),
      imageUrl: map['imageUrl'] ?? '',
      audioUrl: map['audioUrl'] ?? '',
      difficulty: map['difficulty'] ?? 1,
      ageGroup: map['ageGroup'] ?? 6,
      isCompleted: map['isCompleted'] ?? false,
      stars: map['stars'] ?? 0,
      completedAt: map['completedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['completedAt'])
          : null,
      createdAt: map['createdAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['createdAt'])
          : DateTime.now(),
    );
  }

  /// نسخ مع تعديلات
  ChildrenContent copyWith({
    String? id,
    String? title,
    String? type,
    String? category,
    Map<String, String>? content,
    String? imageUrl,
    String? audioUrl,
    int? difficulty,
    int? ageGroup,
    bool? isCompleted,
    int? stars,
    DateTime? completedAt,
    DateTime? createdAt,
  }) {
    return ChildrenContent(
      id: id ?? this.id,
      title: title ?? this.title,
      type: type ?? this.type,
      category: category ?? this.category,
      content: content ?? Map.from(this.content),
      imageUrl: imageUrl ?? this.imageUrl,
      audioUrl: audioUrl ?? this.audioUrl,
      difficulty: difficulty ?? this.difficulty,
      ageGroup: ageGroup ?? this.ageGroup,
      isCompleted: isCompleted ?? this.isCompleted,
      stars: stars ?? this.stars,
      completedAt: completedAt ?? this.completedAt,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

/// فئات محتوى الأطفال
class ChildrenCategories {
  static const String animals = 'animals';
  static const String colors = 'colors';
  static const String numbers = 'numbers';
  static const String letters = 'letters';
  static const String shapes = 'shapes';
  static const String family = 'family';
  static const String food = 'food';
  static const String transport = 'transport';
  static const String nature = 'nature';
  static const String emotions = 'emotions';

  static const Map<String, String> categoryNames = {
    animals: 'الحيوانات',
    colors: 'الألوان',
    numbers: 'الأرقام',
    letters: 'الحروف',
    shapes: 'الأشكال',
    family: 'العائلة',
    food: 'الطعام',
    transport: 'المواصلات',
    nature: 'الطبيعة',
    emotions: 'المشاعر',
  };

  static const Map<String, String> categoryIcons = {
    animals: '🐾',
    colors: '🌈',
    numbers: '🔢',
    letters: '📝',
    shapes: '🔷',
    family: '👨‍👩‍👧‍👦',
    food: '🍎',
    transport: '🚗',
    nature: '🌳',
    emotions: '😊',
  };

  static const Map<String, List<String>> categoryColors = {
    animals: ['#FF6B6B', '#4ECDC4'],
    colors: ['#FFE66D', '#FF6B6B'],
    numbers: ['#4ECDC4', '#45B7D1'],
    letters: ['#96CEB4', '#FFEAA7'],
    shapes: ['#DDA0DD', '#98D8C8'],
    family: ['#F7DC6F', '#BB8FCE'],
    food: ['#F8C471', '#82E0AA'],
    transport: ['#85C1E9', '#F8C471'],
    nature: ['#82E0AA', '#85C1E9'],
    emotions: ['#F1C40F', '#E74C3C'],
  };
}

/// أنواع المحتوى
class ContentTypes {
  static const String story = 'story';
  static const String game = 'game';
  static const String lesson = 'lesson';
  static const String quiz = 'quiz';
  static const String song = 'song';

  static const Map<String, String> typeNames = {
    story: 'قصة',
    game: 'لعبة',
    lesson: 'درس',
    quiz: 'اختبار',
    song: 'أغنية',
  };

  static const Map<String, String> typeIcons = {
    story: '📚',
    game: '🎮',
    lesson: '🎓',
    quiz: '❓',
    song: '🎵',
  };
}

/// نموذج تقدم الطفل
class ChildProgress {
  final String childId;
  final String contentId;
  final int attempts;
  final int bestScore;
  final int totalTime; // بالثواني
  final DateTime lastPlayed;
  final bool isUnlocked;

  ChildProgress({
    required this.childId,
    required this.contentId,
    this.attempts = 0,
    this.bestScore = 0,
    this.totalTime = 0,
    DateTime? lastPlayed,
    this.isUnlocked = true,
  }) : lastPlayed = lastPlayed ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'childId': childId,
      'contentId': contentId,
      'attempts': attempts,
      'bestScore': bestScore,
      'totalTime': totalTime,
      'lastPlayed': lastPlayed.millisecondsSinceEpoch,
      'isUnlocked': isUnlocked,
    };
  }

  factory ChildProgress.fromMap(Map<String, dynamic> map) {
    return ChildProgress(
      childId: map['childId'],
      contentId: map['contentId'],
      attempts: map['attempts'] ?? 0,
      bestScore: map['bestScore'] ?? 0,
      totalTime: map['totalTime'] ?? 0,
      lastPlayed: DateTime.fromMillisecondsSinceEpoch(map['lastPlayed']),
      isUnlocked: map['isUnlocked'] ?? true,
    );
  }
}

/// نموذج إنجاز
class Achievement {
  final String id;
  final String title;
  final String description;
  final String icon;
  final String color;
  final int requiredScore;
  final String category;
  final bool isUnlocked;
  final DateTime? unlockedAt;

  Achievement({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.requiredScore,
    required this.category,
    this.isUnlocked = false,
    this.unlockedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'icon': icon,
      'color': color,
      'requiredScore': requiredScore,
      'category': category,
      'isUnlocked': isUnlocked,
      'unlockedAt': unlockedAt?.millisecondsSinceEpoch,
    };
  }

  factory Achievement.fromMap(Map<String, dynamic> map) {
    return Achievement(
      id: map['id'],
      title: map['title'],
      description: map['description'],
      icon: map['icon'],
      color: map['color'],
      requiredScore: map['requiredScore'],
      category: map['category'],
      isUnlocked: map['isUnlocked'] ?? false,
      unlockedAt: map['unlockedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['unlockedAt'])
          : null,
    );
  }
}
