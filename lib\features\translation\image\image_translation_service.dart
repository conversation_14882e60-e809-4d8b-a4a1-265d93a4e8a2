import 'package:flutter/material.dart';
import 'package:google_ml_kit/google_ml_kit.dart';
import '../../../services/api/translation_service.dart';

/// خدمة ترجمة الصور
class ImageTranslationService {
  final TextRecognizer _textRecognizer = TextRecognizer();
  TranslationService? _translationService;

  /// استخراج النص من الصورة
  Future<String> extractTextFromImage(String imagePath) async {
    try {
      final inputImage = InputImage.fromFilePath(imagePath);
      final RecognizedText recognizedText = await _textRecognizer.processImage(
        inputImage,
      );

      String text = '';
      for (TextBlock block in recognizedText.blocks) {
        for (TextLine line in block.lines) {
          text += '${line.text}\n';
        }
      }

      return text.trim();
    } catch (e) {
      debugPrint('Error extracting text from image: $e');
      rethrow;
    }
  }

  /// تعيين خدمة الترجمة
  void setTranslationService(TranslationService translationService) {
    _translationService = translationService;
  }

  /// ترجمة النص
  Future<String> translateText(
    String text,
    String sourceLanguage,
    String targetLanguage,
  ) async {
    if (_translationService == null) {
      throw Exception(
        'TranslationService not initialized. Call setTranslationService.',
      );
    }
    return await _translationService!.translateText(
      text: text,
      sourceLanguage: sourceLanguage,
      targetLanguage: targetLanguage,
    );
  }

  /// التخلص من الموارد
  void dispose() {
    _textRecognizer.close();
  }
}
