import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../config/constants.dart';
import 'api_key_manager.dart';

/// خدمة الترجمة باستخدام واجهات برمجة التطبيقات المختلفة
class TranslationService {
  final FirebaseRemoteConfig _remoteConfig;
  late final ApiKeyManager _apiKeyManager;

  TranslationService(this._remoteConfig) {
    _initServices();
  }

  /// تهيئة الخدمات
  Future<void> _initServices() async {
    final prefs = await SharedPreferences.getInstance();
    _apiKeyManager = ApiKeyManager(_remoteConfig, prefs);
  }

  /// الحصول على مفتاح API من Firebase Remote Config
  String _getApiKey(String configKey) {
    return _remoteConfig.getString(configKey);
  }

  /// ترجمة نص باستخدام الذكاء الاصطناعي
  Future<String> translateWithAI({
    required String text,
    required String sourceLanguage,
    required String targetLanguage,
    String context = '',
  }) async {
    try {
      // استخدام الطريقة المباشرة
      return await _translateWithDirectAPI(
        text: text,
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        context: context,
      );
    } catch (e) {
      debugPrint('AI Translation error: $e');
      throw Exception('AI Translation service error: $e');
    }
  }

  /// ترجمة نص باستخدام الاتصال المباشر بواجهات برمجة التطبيقات
  Future<String> _translateWithDirectAPI({
    required String text,
    required String sourceLanguage,
    required String targetLanguage,
    String context = '',
  }) async {
    try {
      // الحصول على نوع الخدمة النشطة
      final serviceType = _apiKeyManager.getActiveServiceType();
      final apiKey = _apiKeyManager.getActiveApiKey();

      // استخدام ترجمة وهمية للت development إذا لم يكن هناك مفتاح API
      if (apiKey.isEmpty) {
        debugPrint('API key not found, using mock translation');
        return '⚠️ لا يوجد مفتاح Google Translate API مضاف. الرجاء إضافة المفتاح في إعدادات Firebase Remote Config أو ملف الإعدادات.';
      }

      // تسجيل استخدام API
      await _apiKeyManager.trackUsage(serviceType);

      try {
        switch (serviceType) {
          case AppConstants.apiServiceOpenAI:
            return await _translateWithOpenAI(
              text: text,
              sourceLanguage: sourceLanguage,
              targetLanguage: targetLanguage,
              context: context,
              apiKey: apiKey,
            );
          case AppConstants.apiServiceGemini:
            return await _translateWithGemini(
              text: text,
              sourceLanguage: sourceLanguage,
              targetLanguage: targetLanguage,
              context: context,
              apiKey: apiKey,
            );
          case AppConstants.apiServiceOpenRouter:
            return await _translateWithOpenRouter(
              text: text,
              sourceLanguage: sourceLanguage,
              targetLanguage: targetLanguage,
              context: context,
              apiKey: apiKey,
            );
          default:
            return await _translateWithOpenAI(
              text: text,
              sourceLanguage: sourceLanguage,
              targetLanguage: targetLanguage,
              context: context,
              apiKey: apiKey,
            );
        }
      } catch (e) {
        debugPrint('API call error with $serviceType: $e');

        // تسجيل فشل API
        await _apiKeyManager.recordFailure(serviceType);

        // محاولة استخدام الخدمة التالية
        final nextService = _apiKeyManager.getNextServiceAfterFailure(
          serviceType,
        );
        final nextApiKey = _remoteConfig.getString(
          _getConfigKeyForService(nextService),
        );

        if (nextApiKey.isNotEmpty) {
          debugPrint('Trying next service: $nextService');

          switch (nextService) {
            case AppConstants.apiServiceOpenAI:
              return await _translateWithOpenAI(
                text: text,
                sourceLanguage: sourceLanguage,
                targetLanguage: targetLanguage,
                context: context,
                apiKey: nextApiKey,
              );
            case AppConstants.apiServiceGemini:
              return await _translateWithGemini(
                text: text,
                sourceLanguage: sourceLanguage,
                targetLanguage: targetLanguage,
                context: context,
                apiKey: nextApiKey,
              );
            case AppConstants.apiServiceOpenRouter:
              return await _translateWithOpenRouter(
                text: text,
                sourceLanguage: sourceLanguage,
                targetLanguage: targetLanguage,
                context: context,
                apiKey: nextApiKey,
              );
            default:
              // استخدام ترجمة وهمية في حالة فشل جميع الخدمات
              return _mockTranslate(text, sourceLanguage, targetLanguage);
          }
        } else {
          // استخدام ترجمة وهمية في حالة عدم وجود مفتاح للخدمة التالية
          return _mockTranslate(text, sourceLanguage, targetLanguage);
        }
      }
    } catch (e) {
      debugPrint('Direct API Translation error: $e');
      throw Exception('Direct API Translation service error: $e');
    }
  }

  /// ترجمة وهمية للاستخدام في حالة فشل جميع الخدمات
  String _mockTranslate(
    String text,
    String sourceLanguage,
    String targetLanguage,
  ) {
    // إرجاع النص الأصلي مع إشعار أنه ترجمة وهمية
    return '(ترجمة وهمية) $text';
  }

  /// الحصول على مفتاح التكوين للخدمة
  String _getConfigKeyForService(String serviceType) {
    switch (serviceType) {
      case AppConstants.apiServiceOpenAI:
        return AppConstants.keyOpenAIApiKey;
      case AppConstants.apiServiceGemini:
        return AppConstants.keyGeminiApiKey;
      case AppConstants.apiServiceOpenRouter:
        return AppConstants.keyOpenRouterApiKey;
      default:
        return AppConstants.keyOpenAIApiKey;
    }
  }

  /// ترجمة نص باستخدام OpenAI API
  Future<String> _translateWithOpenAI({
    required String text,
    required String sourceLanguage,
    required String targetLanguage,
    required String apiKey,
    String context = '',
  }) async {
    final url = Uri.parse('https://api.openai.com/v1/chat/completions');

    // بناء سياق الترجمة
    String prompt =
        'Translate the following text from $sourceLanguage to $targetLanguage. Provide only the translated text without explanations.';

    if (context.isNotEmpty) {
      prompt += ' Context: $context.';
    }

    final response = await http.post(
      url,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $apiKey',
      },
      body: json.encode({
        'model': 'gpt-3.5-turbo',
        'messages': [
          {'role': 'system', 'content': prompt},
          {'role': 'user', 'content': text},
        ],
        'temperature': 0.3,
        'max_tokens': 1000,
      }),
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return data['choices'][0]['message']['content'].trim();
    } else {
      debugPrint('OpenAI API error: ${response.body}');
      throw Exception('OpenAI API error: ${response.statusCode}');
    }
  }

  /// ترجمة نص باستخدام Gemini API
  Future<String> _translateWithGemini({
    required String text,
    required String sourceLanguage,
    required String targetLanguage,
    required String apiKey,
    String context = '',
  }) async {
    final url = Uri.parse(
      'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=$apiKey',
    );

    // بناء سياق الترجمة
    String prompt =
        'Translate the following text from $sourceLanguage to $targetLanguage. Provide only the translated text without explanations.';

    if (context.isNotEmpty) {
      prompt += ' Context: $context.';
    }

    prompt += ' Text to translate: $text';

    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: json.encode({
        'contents': [
          {
            'parts': [
              {'text': prompt},
            ],
          },
        ],
        'generationConfig': {'temperature': 0.2},
      }),
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return data['candidates'][0]['content']['parts'][0]['text'].trim();
    } else {
      debugPrint('Gemini API error: ${response.body}');
      throw Exception('Gemini API error: ${response.statusCode}');
    }
  }

  /// ترجمة نص باستخدام OpenRouter API
  Future<String> _translateWithOpenRouter({
    required String text,
    required String sourceLanguage,
    required String targetLanguage,
    required String apiKey,
    String context = '',
  }) async {
    final url = Uri.parse('https://openrouter.ai/api/v1/chat/completions');

    // بناء سياق الترجمة
    String prompt =
        'Translate the following text from $sourceLanguage to $targetLanguage. Provide only the translated text without explanations.';

    if (context.isNotEmpty) {
      prompt += ' Context: $context.';
    }

    final response = await http.post(
      url,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $apiKey',
        'HTTP-Referer': 'https://translator-ai-8c4a4.web.app',
        'X-Title': 'AI Smart Translator',
      },
      body: json.encode({
        'model': 'openai/gpt-3.5-turbo',
        'messages': [
          {'role': 'system', 'content': prompt},
          {'role': 'user', 'content': text},
        ],
        'temperature': 0.3,
      }),
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return data['choices'][0]['message']['content'].trim();
    } else {
      debugPrint('OpenRouter API error: ${response.body}');
      throw Exception('OpenRouter API error: ${response.statusCode}');
    }
  }

  /// الحصول على قائمة اللغات المدعومة
  Future<List<Map<String, dynamic>>> getSupportedLanguages() async {
    try {
      final apiKey = _getApiKey('google_translate_api_key');

      if (apiKey.isEmpty) {
        // إرجاع قائمة افتراضية من اللغات
        return [
          {'code': 'ar', 'name': 'العربية'},
          {'code': 'en', 'name': 'English'},
          {'code': 'fr', 'name': 'Français'},
          {'code': 'es', 'name': 'Español'},
          {'code': 'de', 'name': 'Deutsch'},
          // يمكن إضافة المزيد من اللغات هنا
        ];
      }

      final url = Uri.parse(
        'https://translation.googleapis.com/language/translate/v2/languages?key=$apiKey&target=ar',
      );

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final languages = data['data']['languages'] as List;

        return languages.map((lang) {
          return {'code': lang['language'], 'name': lang['name']};
        }).toList();
      } else {
        debugPrint('Languages API error: ${response.body}');
        throw Exception('Failed to get supported languages');
      }
    } catch (e) {
      debugPrint('Get languages error: $e');
      throw Exception('Get languages service error: $e');
    }
  }

  /// اكتشاف لغة النص
  Future<String> detectLanguage(String text) async {
    try {
      final apiKey = _getApiKey('google_translate_api_key');

      // استخدام اكتشاف وهمي للغة إذا لم يكن هناك مفتاح API
      if (apiKey.isEmpty) {
        debugPrint('API key not found, using mock language detection');
        return _mockDetectLanguage(text);
      }

      try {
        final url = Uri.parse(
          'https://translation.googleapis.com/language/translate/v2/detect?key=$apiKey',
        );

        final response = await http.post(url, body: {'q': text});

        if (response.statusCode == 200) {
          final data = json.decode(response.body);
          return data['data']['detections'][0][0]['language'];
        } else {
          debugPrint('Language detection API error: ${response.body}');
          // استخدام اكتشاف وهمي للغة في حالة فشل الاتصال بالخدمة
          return _mockDetectLanguage(text);
        }
      } catch (e) {
        debugPrint('Language detection API call error: $e');
        // استخدام اكتشاف وهمي للغة في حالة حدوث خطأ
        return _mockDetectLanguage(text);
      }
    } catch (e) {
      debugPrint('Language detection error: $e');
      throw Exception('Language detection service error: $e');
    }
  }

  /// اكتشاف وهمي للغة النص
  String _mockDetectLanguage(String text) {
    // تحقق من وجود حروف عربية
    final arabicRegex = RegExp(r'[\u0600-\u06FF]');
    if (arabicRegex.hasMatch(text)) {
      return 'ar';
    }

    // تحقق من وجود حروف فرنسية
    final frenchRegex = RegExp(r'[éèêëàâäôöùûüÿçÉÈÊËÀÂÄÔÖÙÛÜŸÇ]');
    if (frenchRegex.hasMatch(text)) {
      return 'fr';
    }

    // تحقق من وجود حروف إسبانية
    final spanishRegex = RegExp(r'[áéíóúñÁÉÍÓÚÑ¿¡]');
    if (spanishRegex.hasMatch(text)) {
      return 'es';
    }

    // تحقق من وجود حروف ألمانية
    final germanRegex = RegExp(r'[äöüßÄÖÜ]');
    if (germanRegex.hasMatch(text)) {
      return 'de';
    }

    // افتراضيًا، نفترض أن النص بالإنجليزية
    return 'en';
  }

  /// ترجمة نص باستخدام Gemini API مباشرة
  Future<String> translateText({
    required String text,
    required String sourceLanguage,
    required String targetLanguage,
  }) async {
    final apiKey = _getApiKey(AppConstants.keyGeminiApiKey);
    if (apiKey.isEmpty) {
      throw Exception('مفتاح Gemini API غير متوفر في Remote Config.');
    }
    final url = Uri.parse(
      'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=$apiKey',
    );
    String prompt =
        'Translate the following text from $sourceLanguage to $targetLanguage. Provide only the translated text without explanations.';
    prompt += ' Text to translate: $text';
    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: json.encode({
        'contents': [
          {
            'parts': [
              {'text': prompt},
            ],
          },
        ],
        'generationConfig': {'temperature': 0.2},
      }),
    );
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return data['candidates'][0]['content']['parts'][0]['text'].trim();
    } else {
      debugPrint('Gemini API error: \\${response.body}');
      throw Exception('فشل الاتصال بخدمة Gemini API: ${response.body}');
    }
  }
}
