import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:flutter_tts/flutter_tts.dart';
import '../../../core/app_state.dart';
import '../../../config/app_theme.dart';
import '../../../services/speech/multi_speaker_recognition_service.dart';
import '../../../services/api/translation_service.dart';
import '../../../widgets/language_selector.dart';

/// شاشة الترجمة الصوتية المحسنة مع دعم تمييز المتحدثين
class EnhancedVoiceTranslationScreen extends StatefulWidget {
  const EnhancedVoiceTranslationScreen({super.key});

  @override
  State<EnhancedVoiceTranslationScreen> createState() =>
      _EnhancedVoiceTranslationScreenState();
}

class _EnhancedVoiceTranslationScreenState
    extends State<EnhancedVoiceTranslationScreen>
    with TickerProviderStateMixin {
  late MultiSpeakerRecognitionService _recognitionService;
  late TranslationService _translationService;
  final FlutterTts _flutterTts = FlutterTts();

  bool _isRecording = false;
  bool _isSpeaking = false;
  String _sourceLanguage = 'ar';
  String _targetLanguage = 'en';
  List<SpeechSegment> _segments = [];
  final List<Speaker> _speakers = [];

  // متحكمات الرسوم المتحركة
  late AnimationController _recordButtonAnimationController;
  late Animation<double> _recordButtonScaleAnimation;
  late Animation<Color?> _recordButtonColorAnimation;

  @override
  void initState() {
    super.initState();
    _initServices();
    _initAnimations();
  }

  /// تهيئة الخدمات
  void _initServices() async {
    final appState = Provider.of<AppState>(context, listen: false);
    _translationService = TranslationService(appState.remoteConfig);
    _recognitionService = MultiSpeakerRecognitionService(
      translationService: _translationService,
    );

    // إضافة متحدثين افتراضيين
    _speakers.add(_recognitionService.addSpeaker('المتحدث 1'));
    _speakers.add(_recognitionService.addSpeaker('المتحدث 2'));

    // الاستماع لتغييرات المقاطع
    _recognitionService.segmentsStream.listen((segments) {
      setState(() {
        _segments = segments;
      });
    });

    // الاستماع لتغييرات حالة التسجيل
    _recognitionService.recordingStatusStream.listen((isRecording) {
      setState(() {
        _isRecording = isRecording;
      });
      if (isRecording) {
        _recordButtonAnimationController.repeat(reverse: true);
      } else {
        _recordButtonAnimationController.stop();
        _recordButtonAnimationController.reset();
      }
    });

    // تهيئة خدمة تحويل النص إلى كلام
    await _flutterTts.setLanguage(_getLocaleId(_targetLanguage));
    await _flutterTts.setSpeechRate(0.5);
    await _flutterTts.setVolume(1.0);
    await _flutterTts.setPitch(1.0);
    _flutterTts.setCompletionHandler(() {
      setState(() {
        _isSpeaking = false;
      });
    });
  }

  /// تهيئة الرسوم المتحركة
  void _initAnimations() {
    _recordButtonAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _recordButtonScaleAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(
        parent: _recordButtonAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    _recordButtonColorAnimation = ColorTween(
      begin: AppTheme.primaryColor,
      end: Colors.red,
    ).animate(_recordButtonAnimationController);
  }

  @override
  void dispose() {
    _recognitionService.dispose();
    _recordButtonAnimationController.dispose();
    _flutterTts.stop();
    super.dispose();
  }

  /// بدء أو إيقاف التسجيل
  Future<void> _toggleRecording() async {
    HapticFeedback.mediumImpact();

    if (_isRecording) {
      final segments = await _recognitionService.stopRecordingAndRecognize();
      debugPrint('Recognized segments: ${segments.length}');
    } else {
      await _recognitionService.startRecording();
    }
  }

  /// نطق النص المترجم
  Future<void> _speakTranslatedText(SpeechSegment segment) async {
    if (_isSpeaking) {
      await _flutterTts.stop();
      setState(() {
        _isSpeaking = false;
      });
      return;
    }

    if (segment.translatedText == null || segment.translatedText!.isEmpty) {
      return;
    }

    setState(() {
      _isSpeaking = true;
    });

    await _flutterTts.setLanguage(_getLocaleId(_targetLanguage));
    await _flutterTts.speak(segment.translatedText!);
  }

  /// ترجمة مقطع
  Future<void> _translateSegment(SpeechSegment segment) async {
    await _recognitionService.translateSegment(segment, _targetLanguage);
  }

  /// الحصول على معرف اللغة المناسب
  String _getLocaleId(String languageCode) {
    switch (languageCode) {
      case 'ar':
        return 'ar-SA';
      case 'en':
        return 'en-US';
      case 'fr':
        return 'fr-FR';
      case 'es':
        return 'es-ES';
      case 'de':
        return 'de-DE';
      case 'it':
        return 'it-IT';
      case 'ru':
        return 'ru-RU';
      case 'zh':
        return 'zh-CN';
      case 'ja':
        return 'ja-JP';
      case 'ko':
        return 'ko-KR';
      default:
        return 'en-US';
    }
  }

  /// إضافة متحدث جديد
  void _addNewSpeaker() {
    final speaker = _recognitionService.addSpeaker(
      'المتحدث ${_speakers.length + 1}',
    );
    setState(() {
      _speakers.add(speaker);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الترجمة الصوتية المحسنة'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.person_add),
            onPressed: _addNewSpeaker,
            tooltip: 'إضافة متحدث',
          ),
        ],
      ),
      body: Column(
        children: [
          // محددات اللغة
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: LanguageSelector(
                    label: 'من',
                    selectedLanguage: _sourceLanguage,
                    onChanged: (value) {
                      setState(() {
                        _sourceLanguage = value;
                      });
                    },
                  ),
                ),
                const SizedBox(width: 16),
                IconButton(
                  icon: const Icon(Icons.swap_horiz),
                  onPressed: () {
                    setState(() {
                      final temp = _sourceLanguage;
                      _sourceLanguage = _targetLanguage;
                      _targetLanguage = temp;
                    });
                  },
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: LanguageSelector(
                    label: 'إلى',
                    selectedLanguage: _targetLanguage,
                    onChanged: (value) {
                      setState(() {
                        _targetLanguage = value;
                      });
                    },
                  ),
                ),
              ],
            ),
          ),

          // قائمة المقاطع
          Expanded(
            child:
                _segments.isEmpty ? _buildEmptyState() : _buildSegmentsList(),
          ),

          // زر التسجيل
          Padding(
            padding: const EdgeInsets.all(24),
            child: _buildRecordButton(),
          ),
        ],
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.record_voice_over, size: 80, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'اضغط على زر التسجيل للبدء',
            style: TextStyle(fontSize: 18, color: Colors.grey.shade600),
          ),
          const SizedBox(height: 8),
          Text(
            'سيتم التعرف على المتحدثين وترجمة كلامهم',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }

  /// بناء قائمة المقاطع
  Widget _buildSegmentsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _segments.length,
      itemBuilder: (context, index) {
        final segment = _segments[index];
        final speaker = _recognitionService.getSpeakerById(segment.speakerId);

        return _buildSegmentCard(segment, speaker);
      },
    );
  }

  /// بناء بطاقة المقطع
  Widget _buildSegmentCard(SpeechSegment segment, Speaker? speaker) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات المتحدث
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: speaker?.color ?? Colors.grey,
                  radius: 16,
                  child: Text(
                    speaker?.name.substring(0, 1) ?? '?',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  speaker?.name ?? 'غير معروف',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const Spacer(),
                Text(
                  '${segment.timestamp.hour}:${segment.timestamp.minute.toString().padLeft(2, '0')}',
                  style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // النص الأصلي
            Text(segment.text, style: const TextStyle(fontSize: 16)),

            // النص المترجم
            if (segment.translatedText != null) ...[
              const Divider(height: 24),
              Text(
                segment.translatedText!,
                style: TextStyle(
                  fontSize: 16,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],

            // أزرار الإجراءات
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // زر الترجمة
                if (segment.translatedText == null)
                  IconButton(
                    icon: const Icon(Icons.translate),
                    onPressed: () => _translateSegment(segment),
                    tooltip: 'ترجمة',
                  ),

                // زر النطق
                if (segment.translatedText != null)
                  IconButton(
                    icon: Icon(_isSpeaking ? Icons.stop : Icons.volume_up),
                    onPressed: () => _speakTranslatedText(segment),
                    tooltip: _isSpeaking ? 'إيقاف النطق' : 'نطق الترجمة',
                    color: _isSpeaking ? Colors.red : null,
                  ),

                // زر النسخ
                IconButton(
                  icon: const Icon(Icons.copy),
                  onPressed: () {
                    Clipboard.setData(
                      ClipboardData(
                        text: segment.translatedText ?? segment.text,
                      ),
                    );
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('تم نسخ النص')),
                    );
                  },
                  tooltip: 'نسخ النص',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء زر التسجيل
  Widget _buildRecordButton() {
    return AnimatedBuilder(
      animation: _recordButtonAnimationController,
      builder: (context, child) {
        return GestureDetector(
          onTap: _toggleRecording,
          child: Container(
            width: 80 * _recordButtonScaleAnimation.value,
            height: 80 * _recordButtonScaleAnimation.value,
            decoration: BoxDecoration(
              color:
                  _isRecording
                      ? _recordButtonColorAnimation.value
                      : AppTheme.primaryColor,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: (_isRecording ? Colors.red : AppTheme.primaryColor)
                      .withAlpha(100),
                  blurRadius: 10,
                  spreadRadius: 2,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Icon(
              _isRecording ? Icons.stop : Icons.mic,
              color: Colors.white,
              size: 40,
            ),
          ),
        );
      },
    );
  }
}
