import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج بيانات استخدام الخدمات
class UsageModel {
  /// معرف المستخدم
  final String userId;

  /// الفترة (عادة الشهر الحالي بتنسيق YYYY-MM)
  final String period;

  /// إجمالي الدقائق المستخدمة
  final double totalMinutes;

  /// دقائق الترجمة الصوتية
  final double voiceTranslationMinutes;

  /// دقائق تمييز المتحدثين
  final double speakerRecognitionMinutes;

  /// دقائق الترجمة الفورية
  final double realTimeTranslationMinutes;

  /// عدد الصور المترجمة
  final int imageTranslationCount;

  /// عدد المستندات المترجمة
  final int documentTranslationCount;

  /// عدد رسائل المحادثة الذكية
  final int aiChatMessageCount;

  /// تاريخ آخر تحديث
  final DateTime lastUpdated;

  UsageModel({
    required this.userId,
    required this.period,
    required this.totalMinutes,
    required this.voiceTranslationMinutes,
    required this.speakerRecognitionMinutes,
    required this.realTimeTranslationMinutes,
    required this.imageTranslationCount,
    required this.documentTranslationCount,
    required this.aiChatMessageCount,
    required this.lastUpdated,
  });

  /// إنشاء نموذج من بيانات Firestore
  factory UsageModel.fromFirestore(
    Map<String, dynamic> data,
    String userId,
    String period,
  ) {
    return UsageModel(
      userId: userId,
      period: period,
      totalMinutes: (data['totalMinutes'] ?? 0.0).toDouble(),
      voiceTranslationMinutes:
          (data['voiceTranslationMinutes'] ?? 0.0).toDouble(),
      speakerRecognitionMinutes:
          (data['speakerRecognitionMinutes'] ?? 0.0).toDouble(),
      realTimeTranslationMinutes:
          (data['realTimeTranslationMinutes'] ?? 0.0).toDouble(),
      imageTranslationCount: data['imageTranslationCount'] ?? 0,
      documentTranslationCount: data['documentTranslationCount'] ?? 0,
      aiChatMessageCount: data['aiChatMessageCount'] ?? 0,
      lastUpdated:
          data['lastUpdated'] != null
              ? (data['lastUpdated'] as Timestamp).toDate()
              : DateTime.now(),
    );
  }

  /// تحويل النموذج إلى بيانات Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'totalMinutes': totalMinutes,
      'voiceTranslationMinutes': voiceTranslationMinutes,
      'speakerRecognitionMinutes': speakerRecognitionMinutes,
      'realTimeTranslationMinutes': realTimeTranslationMinutes,
      'imageTranslationCount': imageTranslationCount,
      'documentTranslationCount': documentTranslationCount,
      'aiChatMessageCount': aiChatMessageCount,
      'lastUpdated': Timestamp.fromDate(lastUpdated),
    };
  }

  /// إنشاء نموذج فارغ
  factory UsageModel.empty(String userId) {
    final now = DateTime.now();
    final period = '${now.year}-${now.month.toString().padLeft(2, '0')}';

    return UsageModel(
      userId: userId,
      period: period,
      totalMinutes: 0.0,
      voiceTranslationMinutes: 0.0,
      speakerRecognitionMinutes: 0.0,
      realTimeTranslationMinutes: 0.0,
      imageTranslationCount: 0,
      documentTranslationCount: 0,
      aiChatMessageCount: 0,
      lastUpdated: now,
    );
  }

  /// نسخة محدثة من النموذج
  UsageModel copyWith({
    String? userId,
    String? period,
    double? totalMinutes,
    double? voiceTranslationMinutes,
    double? speakerRecognitionMinutes,
    double? realTimeTranslationMinutes,
    int? imageTranslationCount,
    int? documentTranslationCount,
    int? aiChatMessageCount,
    DateTime? lastUpdated,
  }) {
    return UsageModel(
      userId: userId ?? this.userId,
      period: period ?? this.period,
      totalMinutes: totalMinutes ?? this.totalMinutes,
      voiceTranslationMinutes:
          voiceTranslationMinutes ?? this.voiceTranslationMinutes,
      speakerRecognitionMinutes:
          speakerRecognitionMinutes ?? this.speakerRecognitionMinutes,
      realTimeTranslationMinutes:
          realTimeTranslationMinutes ?? this.realTimeTranslationMinutes,
      imageTranslationCount:
          imageTranslationCount ?? this.imageTranslationCount,
      documentTranslationCount:
          documentTranslationCount ?? this.documentTranslationCount,
      aiChatMessageCount: aiChatMessageCount ?? this.aiChatMessageCount,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  /// إضافة استخدام جديد
  UsageModel addUsage({
    double voiceMinutes = 0.0,
    double speakerMinutes = 0.0,
    double realTimeMinutes = 0.0,
    int imageCount = 0,
    int documentCount = 0,
    int aiChatCount = 0,
  }) {
    final newTotalMinutes =
        totalMinutes + voiceMinutes + speakerMinutes + realTimeMinutes;

    return copyWith(
      totalMinutes: newTotalMinutes,
      voiceTranslationMinutes: voiceTranslationMinutes + voiceMinutes,
      speakerRecognitionMinutes: speakerRecognitionMinutes + speakerMinutes,
      realTimeTranslationMinutes: realTimeTranslationMinutes + realTimeMinutes,
      imageTranslationCount: imageTranslationCount + imageCount,
      documentTranslationCount: documentTranslationCount + documentCount,
      aiChatMessageCount: aiChatMessageCount + aiChatCount,
      lastUpdated: DateTime.now(),
    );
  }
}

/// نموذج تفاصيل استخدام الخدمة
class UsageDetailModel {
  /// معرف المستخدم
  final String userId;

  /// نوع الخدمة
  final String serviceType;

  /// المدة بالدقائق (للخدمات الصوتية)
  final double? durationMinutes;

  /// العدد (للخدمات غير الصوتية)
  final int? count;

  /// تاريخ الاستخدام
  final DateTime timestamp;

  UsageDetailModel({
    required this.userId,
    required this.serviceType,
    this.durationMinutes,
    this.count,
    required this.timestamp,
  });

  /// إنشاء نموذج من بيانات Firestore
  factory UsageDetailModel.fromFirestore(Map<String, dynamic> data, String id) {
    return UsageDetailModel(
      userId: data['userId'] ?? '',
      serviceType: data['serviceType'] ?? '',
      durationMinutes: data['durationMinutes']?.toDouble(),
      count: data['count'],
      timestamp: (data['timestamp'] as Timestamp).toDate(),
    );
  }

  /// تحويل النموذج إلى بيانات Firestore
  Map<String, dynamic> toFirestore() {
    final data = {
      'userId': userId,
      'serviceType': serviceType,
      'timestamp': Timestamp.fromDate(timestamp),
    };

    if (durationMinutes != null) {
      data['durationMinutes'] = durationMinutes!;
    }

    if (count != null) {
      data['count'] = count!;
    }

    return data;
  }
}
