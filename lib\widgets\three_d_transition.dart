import 'package:flutter/material.dart';

/// انتقال ثلاثي الأبعاد
class Transition3D extends PageRouteBuilder {
  /// الصفحة المستهدفة
  final Widget page;

  /// نوع الانتقال
  final TransitionType type;

  /// مدة الانتقال
  final Duration duration;

  /// منحنى الانتقال
  final Curve curve;

  Transition3D({
    required this.page,
    this.type = TransitionType.rightToLeft,
    this.duration = const Duration(milliseconds: 300),
    this.curve = Curves.easeInOut,
    super.settings,
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          transitionsBuilder: (
            BuildContext context,
            Animation<double> animation,
            Animation<double> secondaryAnimation,
            Widget child,
          ) {
            animation = CurvedAnimation(parent: animation, curve: curve);

            // استخدام if-else بدلاً من switch لتجنب مشكلة الحالة الافتراضية
            if (type == TransitionType.rightToLeft) {
              return _buildRightToLeftTransition(animation, child);
            } else if (type == TransitionType.leftToRight) {
              return _buildLeftToRightTransition(animation, child);
            } else if (type == TransitionType.bottomToTop) {
              return _buildBottomToTopTransition(animation, child);
            } else if (type == TransitionType.topToBottom) {
              return _buildTopToBottomTransition(animation, child);
            } else if (type == TransitionType.scale) {
              return _buildScaleTransition(animation, child);
            } else if (type == TransitionType.fade) {
              return _buildFadeTransition(animation, child);
            } else if (type == TransitionType.rotation) {
              return _buildRotationTransition(animation, child);
            } else if (type == TransitionType.flip) {
              return _buildFlipTransition(animation, child);
            } else if (type == TransitionType.zoom) {
              return _buildZoomTransition(animation, child);
            } else if (type == TransitionType.slide3D) {
              return _buildSlide3DTransition(animation, child);
            } else {
              // الحالة الافتراضية
              return _buildRightToLeftTransition(animation, child);
            }
          },
        );

  /// انتقال من اليمين إلى اليسار
  static Widget _buildRightToLeftTransition(
    Animation<double> animation,
    Widget child,
  ) {
    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(1.0, 0.0),
        end: Offset.zero,
      ).animate(animation),
      child: child,
    );
  }

  /// انتقال من اليسار إلى اليمين
  static Widget _buildLeftToRightTransition(
    Animation<double> animation,
    Widget child,
  ) {
    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(-1.0, 0.0),
        end: Offset.zero,
      ).animate(animation),
      child: child,
    );
  }

  /// انتقال من الأسفل إلى الأعلى
  static Widget _buildBottomToTopTransition(
    Animation<double> animation,
    Widget child,
  ) {
    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(0.0, 1.0),
        end: Offset.zero,
      ).animate(animation),
      child: child,
    );
  }

  /// انتقال من الأعلى إلى الأسفل
  static Widget _buildTopToBottomTransition(
    Animation<double> animation,
    Widget child,
  ) {
    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(0.0, -1.0),
        end: Offset.zero,
      ).animate(animation),
      child: child,
    );
  }

  /// انتقال بالتكبير
  static Widget _buildScaleTransition(
    Animation<double> animation,
    Widget child,
  ) {
    return ScaleTransition(
      scale: Tween<double>(begin: 0.0, end: 1.0).animate(animation),
      child: child,
    );
  }

  /// انتقال بالتلاشي
  static Widget _buildFadeTransition(
    Animation<double> animation,
    Widget child,
  ) {
    return FadeTransition(
      opacity: Tween<double>(begin: 0.0, end: 1.0).animate(animation),
      child: child,
    );
  }

  /// انتقال بالدوران
  static Widget _buildRotationTransition(
    Animation<double> animation,
    Widget child,
  ) {
    return RotationTransition(
      turns: Tween<double>(begin: 0.5, end: 1.0).animate(animation),
      child: FadeTransition(opacity: animation, child: child),
    );
  }

  /// انتقال بالقلب
  static Widget _buildFlipTransition(
    Animation<double> animation,
    Widget child,
  ) {
    final rotateAnim = Tween<double>(begin: 0.0, end: 1.0).animate(animation);

    return AnimatedBuilder(
      animation: rotateAnim,
      child: child,
      builder: (context, widget) {
        final isUnder = rotateAnim.value < 0.5;
        final value =
            isUnder ? (1 - rotateAnim.value) * 2 : (rotateAnim.value - 0.5) * 2;
        final angle = isUnder ? -90.0 : 90.0;

        return Transform(
          transform:
              Matrix4.identity()
                ..setEntry(3, 2, 0.001)
                ..rotateY(value * angle * 3.1415927 / 180),
          alignment: Alignment.center,
          child: widget,
        );
      },
    );
  }

  /// انتقال بالتكبير والتصغير
  static Widget _buildZoomTransition(
    Animation<double> animation,
    Widget child,
  ) {
    final zoomAnim = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: animation, curve: Curves.fastOutSlowIn));

    return ScaleTransition(
      scale: zoomAnim,
      child: FadeTransition(
        opacity: animation,
        child: SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0.0, 0.5),
            end: Offset.zero,
          ).animate(animation),
          child: child,
        ),
      ),
    );
  }

  /// انتقال ثلاثي الأبعاد
  static Widget _buildSlide3DTransition(
    Animation<double> animation,
    Widget child,
  ) {
    final slideAnim = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(animation);

    final rotateAnim = Tween<double>(begin: 0.5, end: 0.0).animate(animation);

    return AnimatedBuilder(
      animation: animation,
      builder: (context, widget) {
        return Transform(
          transform:
              Matrix4.identity()
                ..setEntry(3, 2, 0.001)
                ..rotateY(rotateAnim.value * 3.1415927),
          alignment: Alignment.centerRight,
          child: SlideTransition(position: slideAnim, child: child),
        );
      },
    );
  }
}

/// أنواع الانتقالات
enum TransitionType {
  /// من اليمين إلى اليسار
  rightToLeft,

  /// من اليسار إلى اليمين
  leftToRight,

  /// من الأسفل إلى الأعلى
  bottomToTop,

  /// من الأعلى إلى الأسفل
  topToBottom,

  /// تكبير
  scale,

  /// تلاشي
  fade,

  /// دوران
  rotation,

  /// قلب
  flip,

  /// تكبير وتصغير
  zoom,

  /// انزلاق ثلاثي الأبعاد
  slide3D,
}
