import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../config/constants.dart';
import 'api_key_manager.dart';

/// خدمة محادثة الذكاء الاصطناعي
class AIChatService {
  final FirebaseRemoteConfig _remoteConfig;
  late final ApiKeyManager _apiKeyManager;

  AIChatService(this._remoteConfig) {
    _initServices();
  }

  /// تهيئة الخدمات
  Future<void> _initServices() async {
    final prefs = await SharedPreferences.getInstance();
    _apiKeyManager = ApiKeyManager(_remoteConfig, prefs);
  }

  /// الحصول على رد من الذكاء الاصطناعي
  Future<String> getChatResponse({
    required String message,
    required String responseType,
    List<Map<String, dynamic>>? chatHistory,
  }) async {
    try {
      // استخدام Gemini API مباشرة
      return await _getGeminiResponse(
        message: message,
        responseType: responseType,
        chatHistory: chatHistory,
      );
    } catch (e) {
      debugPrint('AI Chat Service error: $e');
      throw Exception('فشل في الحصول على رد من الذكاء الاصطناعي: $e');
    }
  }

  /// الحصول على رد من Gemini
  Future<String> _getGeminiResponse({
    required String message,
    required String responseType,
    List<Map<String, dynamic>>? chatHistory,
  }) async {
    try {
      final apiKey = _apiKeyManager.getApiKey(AppConstants.apiServiceGemini);
      if (apiKey.isEmpty) {
        throw Exception('مفتاح Gemini API غير متوفر');
      }

      // بناء prompt حسب نوع الاستجابة
      String prompt = _buildPrompt(message, responseType);

      // إضافة تاريخ المحادثة إذا كان متوفراً
      if (chatHistory != null && chatHistory.isNotEmpty) {
        String historyText = '';
        for (final msg in chatHistory) {
          final role = msg['role'] as String;
          final content = msg['content'] as String;
          historyText += '$role: $content\n';
        }
        prompt = '$historyText\nUser: $prompt';
      }

      final response = await http.post(
        Uri.parse(
          'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=$apiKey',
        ),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'contents': [
            {
              'parts': [
                {'text': prompt},
              ],
            },
          ],
          'generationConfig': {
            'temperature': 0.7,
            'topK': 40,
            'topP': 0.95,
            'maxOutputTokens': 1024,
          },
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final candidates = data['candidates'] as List;
        if (candidates.isNotEmpty) {
          final content = candidates[0]['content'];
          final parts = content['parts'] as List;
          if (parts.isNotEmpty) {
            return parts[0]['text'] as String;
          }
        }
        throw Exception('لم يتم العثور على محتوى في الاستجابة');
      } else {
        throw Exception('خطأ في API: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Gemini API error: $e');
      rethrow;
    }
  }

  /// بناء prompt حسب نوع الاستجابة
  String _buildPrompt(String message, String responseType) {
    switch (responseType) {
      case 'translation':
        return 'قم بترجمة النص التالي وقدم ترجمة دقيقة ومناسبة للسياق: $message';
      case 'explanation':
        return 'اشرح المفهوم التالي بطريقة واضحة ومفصلة: $message';
      case 'conversation':
        return 'تحدث معي حول الموضوع التالي بطريقة ودية ومفيدة: $message';
      case 'help':
        return 'ساعدني في حل المشكلة التالية أو الإجابة على السؤال: $message';
      default:
        return message;
    }
  }

  /// تنظيف الموارد
  void dispose() {
    // تنظيف أي موارد إذا لزم الأمر
  }
}
