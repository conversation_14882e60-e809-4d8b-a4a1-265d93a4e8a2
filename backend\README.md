# خادم وسيط لمفاتيح API

هذا المشروع يحتوي على خادم وسيط (Backend Proxy) لإدارة مفاتيح API الخاصة بخدمات الذكاء الاصطناعي (OpenAI، Google Gemini، وOpenRouter) بشكل آمن.

## الميزات

- تخزين مفاتيح API في بيئة آمنة على الخادم
- التحقق من المصادقة لكل طلب
- تتبع حدود الاستخدام اليومية
- التبديل التلقائي بين المفاتيح المدفوعة والمجانية
- تخزين مؤقت للطلبات المتكررة
- تسجيل الاستخدام والأخطاء

## الإعداد

### 1. تثبيت Firebase CLI

```bash
npm install -g firebase-tools
```

### 2. تسجيل الدخول إلى Firebase

```bash
firebase login
```

### 3. تهيئة المشروع

```bash
cd backend
firebase init functions
```

### 4. تعيين مفاتيح API كمتغيرات بيئية

```bash
firebase functions:config:set openai.key="YOUR_OPENAI_KEY" gemini.paid_key="YOUR_PAID_GEMINI_KEY" gemini.free_key="YOUR_FREE_GEMINI_KEY" openrouter.key="YOUR_OPENROUTER_KEY"
```

### 5. تعيين تواريخ انتهاء الصلاحية

```bash
firebase functions:config:set openai.end_date="2025-12-31" gemini.end_date="2025-12-31"
```

### 6. تعيين حدود الاستخدام اليومية

```bash
firebase functions:config:set limits.openai=100 limits.gemini=100 limits.openrouter=100
```

### 7. نشر الدوال

```bash
firebase deploy --only functions
```

## الدوال المتاحة

- `translateWithOpenAI`: ترجمة نص باستخدام OpenAI
- `translateWithGemini`: ترجمة نص باستخدام Gemini
- `translateWithOpenRouter`: ترجمة نص باستخدام OpenRouter
- `chatWithGemini`: محادثة مع Gemini

## الاستخدام في تطبيق Flutter

```dart
// استدعاء دالة ترجمة
final result = await FirebaseFunctions.instance.httpsCallable('translateWithGemini').call({
  'text': 'Hello, world!',
  'sourceLanguage': 'en',
  'targetLanguage': 'ar',
  'context': '',
});

final translatedText = result.data['translatedText'];
```

## الأمان

- المفاتيح لا تظهر أبدًا في التطبيق
- التحقق من المصادقة لكل طلب
- تتبع الاستخدام لكل مستخدم
- تخزين مؤقت للطلبات المتكررة
- تدوير المفاتيح تلقائيًا عند انتهاء صلاحيتها

## الصيانة

- مراقبة سجلات Firebase Functions للأخطاء
- تحديث المفاتيح عند الحاجة باستخدام `firebase functions:config:set`
- مراقبة استخدام API في Firestore
