import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_tts/flutter_tts.dart';
import '../models/tourism_phrase.dart';
import 'arabic_speech_service.dart';
import 'arabic_speech_test.dart';

/// خدمة إدارة العبارات السياحية
class TourismService {
  static const String _favoritesKey = 'tourism_favorites';
  static const String _usageKey = 'tourism_usage';

  final FlutterTts _tts = FlutterTts();
  final ArabicSpeechService _arabicSpeech = ArabicSpeechService();
  List<TourismPhrase> _allPhrases = [];
  List<TourismPhrase> _favoritePhrases = [];

  /// تهيئة الخدمة
  Future<void> initialize() async {
    await _initializeTTS();
    await _arabicSpeech.initialize();
    await _loadPhrases();
    await _loadFavorites();
  }

  /// تهيئة خدمة النطق مع إصلاح شامل للعربية
  Future<void> _initializeTTS() async {
    try {
      debugPrint('🔧 بدء تهيئة خدمة النطق...');

      // إعداد معالجات الأحداث
      _tts.setErrorHandler((message) {
        debugPrint('❌ خطأ في النطق: $message');
      });

      _tts.setStartHandler(() {
        debugPrint('🎤 بدء النطق');
      });

      _tts.setCompletionHandler(() {
        debugPrint('✅ انتهى النطق');
      });

      // التحقق من اللغات المتاحة أولاً
      final languages = await _tts.getLanguages;
      debugPrint('🌍 اللغات المتاحة: $languages');

      // البحث عن اللغة العربية المتاحة
      String? arabicLanguage;
      if (languages != null) {
        for (String lang in languages) {
          if (lang.toLowerCase().contains('ar') ||
              lang.toLowerCase().contains('arabic') ||
              lang.contains('ar-SA') ||
              lang.contains('ar_SA')) {
            arabicLanguage = lang;
            debugPrint('🇸🇦 تم العثور على اللغة العربية: $arabicLanguage');
            break;
          }
        }
      }

      // تعيين اللغة العربية
      if (arabicLanguage != null) {
        final langResult = await _tts.setLanguage(arabicLanguage);
        debugPrint('🔧 نتيجة تعيين اللغة العربية: $langResult');
      } else {
        debugPrint('⚠️ لم يتم العثور على اللغة العربية، استخدام ar-SA');
        await _tts.setLanguage('ar-SA');
      }

      // التحقق من الأصوات المتاحة
      final voices = await _tts.getVoices;
      debugPrint('🎵 عدد الأصوات المتاحة: ${voices?.length ?? 0}');

      // البحث عن أفضل صوت عربي
      if (voices != null && voices.isNotEmpty) {
        final arabicVoices =
            voices.where((voice) {
              final locale = voice['locale']?.toString().toLowerCase() ?? '';
              final name = voice['name']?.toString().toLowerCase() ?? '';
              return locale.contains('ar') ||
                  name.contains('arabic') ||
                  name.contains('عربي') ||
                  locale.contains('saudi') ||
                  locale.contains('egypt');
            }).toList();

        debugPrint('🇸🇦 الأصوات العربية المتاحة: ${arabicVoices.length}');

        if (arabicVoices.isNotEmpty) {
          // اختيار أفضل صوت عربي (تفضيل السعودي)
          final bestVoice = arabicVoices.firstWhere(
            (voice) => voice['locale']?.toString().contains('SA') == true,
            orElse: () => arabicVoices.first,
          );

          try {
            await _tts.setVoice({
              'name': bestVoice['name'],
              'locale': bestVoice['locale'],
            });
            debugPrint(
              '✅ تم تعيين الصوت العربي: ${bestVoice['name']} (${bestVoice['locale']})',
            );
          } catch (e) {
            debugPrint('❌ فشل في تعيين الصوت العربي: $e');
          }
        } else {
          debugPrint('⚠️ لم يتم العثور على أصوات عربية');
        }
      }

      // تعيين إعدادات النطق المحسنة للعربية
      await _configureArabicTTS();

      // اختبار النطق العربي الشامل
      await _comprehensiveArabicTest();

      debugPrint('✅ تم إكمال تهيئة خدمة النطق');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة النطق: $e');
      debugPrint('📋 تفاصيل الخطأ: ${e.toString()}');
    }
  }

  /// تكوين إعدادات النطق المحسنة للعربية
  Future<void> _configureArabicTTS() async {
    try {
      debugPrint('🔧 تكوين إعدادات النطق العربي...');

      // إعدادات محسنة للعربية
      await _tts.setSpeechRate(0.4); // سرعة أبطأ للوضوح
      await _tts.setVolume(1.0); // أقصى صوت
      await _tts.setPitch(1.0); // نبرة طبيعية

      debugPrint('✅ تم تكوين إعدادات النطق العربي');
    } catch (e) {
      debugPrint('❌ خطأ في تكوين إعدادات النطق العربي: $e');
    }
  }

  /// اختبار شامل للنطق العربي
  Future<void> _comprehensiveArabicTest() async {
    try {
      debugPrint('🧪 بدء الاختبار الشامل للنطق العربي...');

      // قائمة عبارات اختبار متنوعة
      final testPhrases = [
        'مرحبا',
        'أهلا وسهلا',
        'شكرا لك',
        'من فضلك',
        'أين المطار؟',
        'كم الساعة؟',
      ];

      for (int i = 0; i < testPhrases.length; i++) {
        final phrase = testPhrases[i];
        debugPrint('🎤 اختبار العبارة ${i + 1}: "$phrase"');

        try {
          final result = await _tts.speak(phrase);
          debugPrint('✅ نجح نطق العبارة: "$phrase" - النتيجة: $result');

          // انتظار قصير بين العبارات
          await Future.delayed(const Duration(milliseconds: 500));
        } catch (e) {
          debugPrint('❌ فشل نطق العبارة: "$phrase" - الخطأ: $e');
        }
      }

      debugPrint('✅ انتهى الاختبار الشامل للنطق العربي');
    } catch (e) {
      debugPrint('❌ خطأ في الاختبار الشامل للنطق العربي: $e');
    }
  }

  /// تحميل العبارات
  Future<void> _loadPhrases() async {
    _allPhrases = _generateTourismPhrases();
  }

  /// تحميل المفضلة
  Future<void> _loadFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson = prefs.getStringList(_favoritesKey) ?? [];

      _favoritePhrases =
          favoritesJson
              .map((json) => TourismPhrase.fromMap(jsonDecode(json)))
              .toList();
    } catch (e) {
      debugPrint('خطأ في تحميل المفضلة: $e');
    }
  }

  /// حفظ المفضلة
  Future<void> _saveFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson =
          _favoritePhrases.map((phrase) => jsonEncode(phrase.toMap())).toList();

      await prefs.setStringList(_favoritesKey, favoritesJson);
    } catch (e) {
      debugPrint('خطأ في حفظ المفضلة: $e');
    }
  }

  /// الحصول على جميع العبارات
  List<TourismPhrase> getAllPhrases() => _allPhrases;

  /// الحصول على العبارات حسب الفئة
  List<TourismPhrase> getPhrasesByCategory(String category) {
    return _allPhrases.where((phrase) => phrase.category == category).toList();
  }

  /// الحصول على العبارات حسب الفئة الفرعية
  List<TourismPhrase> getPhrasesBySubcategory(
    String category,
    String subcategory,
  ) {
    return _allPhrases
        .where(
          (phrase) =>
              phrase.category == category && phrase.subcategory == subcategory,
        )
        .toList();
  }

  /// البحث في العبارات
  List<TourismPhrase> searchPhrases(String query) {
    if (query.isEmpty) return _allPhrases;

    final lowerQuery = query.toLowerCase();
    return _allPhrases.where((phrase) {
      return phrase.translations.values.any(
        (text) => text.toLowerCase().contains(lowerQuery),
      );
    }).toList();
  }

  /// الحصول على المفضلة
  List<TourismPhrase> getFavoritePhrases() => _favoritePhrases;

  /// إضافة/إزالة من المفضلة
  Future<void> toggleFavorite(TourismPhrase phrase) async {
    final index = _favoritePhrases.indexWhere((p) => p.id == phrase.id);

    if (index >= 0) {
      _favoritePhrases.removeAt(index);
    } else {
      _favoritePhrases.add(
        phrase.copyWith(isFavorite: true, addedToFavoritesAt: DateTime.now()),
      );
    }

    await _saveFavorites();
  }

  /// التحقق من كون العبارة مفضلة
  bool isFavorite(String phraseId) {
    return _favoritePhrases.any((phrase) => phrase.id == phraseId);
  }

  /// نطق العبارة مع إصلاح شامل للعربية
  Future<void> speakPhrase(String text, String language) async {
    if (text.isEmpty) {
      debugPrint('❌ النص فارغ، لا يمكن النطق');
      return;
    }

    try {
      debugPrint('🎤 بدء نطق النص: "$text" باللغة: $language');

      // إيقاف أي نطق جاري
      await _tts.stop();
      await Future.delayed(const Duration(milliseconds: 100));

      // معالجة خاصة للعربية
      if (language == 'ar') {
        await _speakArabicText(text);
        return;
      }

      // الحصول على كود اللغة المناسب
      String languageCode = _getLanguageCode(language);
      debugPrint('🌍 كود اللغة المستخدم: $languageCode');

      // تعيين اللغة أولاً
      final languageResult = await _tts.setLanguage(languageCode);
      debugPrint('🔧 نتيجة تعيين اللغة: $languageResult');

      // تعيين إعدادات النطق المحسنة حسب اللغة
      await _configureLanguageSettings(language);

      // محاولة النطق الأساسية
      final speakResult = await _tts.speak(text);
      debugPrint('📢 نتيجة النطق: $speakResult');

      if (speakResult == 1) {
        debugPrint('✅ تم نطق النص بنجاح: $text');
      } else {
        debugPrint('⚠️ فشل النطق، النتيجة: $speakResult');
        await _fallbackSpeak(text, language);
      }
    } catch (e) {
      debugPrint('❌ خطأ في النطق الأساسي: $e');
      await _fallbackSpeak(text, language);
    }
  }

  /// نطق النص العربي مع حل جذري للمشكلة
  Future<void> _speakArabicText(String text) async {
    try {
      debugPrint('🇸🇦 بدء النطق العربي المحسن للنص: "$text"');

      // استخدام خدمة النطق العربي الجديدة المحسنة
      await _arabicSpeech.speakArabic(text);
    } catch (e) {
      debugPrint('❌ خطأ في النطق العربي المحسن: $e');

      // محاولة بديلة مع النظام القديم
      try {
        await _speakArabicTTS(text);
      } catch (e2) {
        debugPrint('❌ فشل النظام البديل أيضاً: $e2');
        // محاولة أخيرة
        await _fallbackArabicSpeak(text);
      }
    }
  }

  /// نطق عربي محسن باستخدام flutter_tts
  Future<void> _speakArabicTTS(String text) async {
    try {
      debugPrint('🔧 استخدام flutter_tts للنطق العربي');

      // تعيين اللغة العربية مع محاولات متعددة
      final arabicCodes = ['ar-SA', 'ar', 'ar_SA', 'ar-EG', 'ar_EG'];
      bool languageSet = false;

      for (final code in arabicCodes) {
        try {
          final result = await _tts.setLanguage(code);
          if (result == 1) {
            debugPrint('✅ تم تعيين اللغة العربية: $code');
            languageSet = true;
            break;
          }
        } catch (e) {
          debugPrint('⚠️ فشل في تعيين اللغة $code: $e');
          continue;
        }
      }

      if (!languageSet) {
        debugPrint('❌ فشل في تعيين أي لغة عربية');
        throw Exception('فشل في تعيين اللغة العربية');
      }

      // إعدادات محسنة للعربية
      await _tts.setSpeechRate(0.6); // سرعة محسنة
      await _tts.setVolume(1.0);
      await _tts.setPitch(1.0);

      // انتظار للتأكد من تطبيق الإعدادات
      await Future.delayed(const Duration(milliseconds: 300));

      // محاولة النطق
      final result = await _tts.speak(text);

      if (result == 1) {
        debugPrint('✅ نجح النطق العربي عبر flutter_tts');
      } else {
        debugPrint('⚠️ فشل النطق العربي، النتيجة: $result');
        throw Exception('فشل في النطق');
      }
    } catch (e) {
      debugPrint('❌ خطأ في flutter_tts: $e');
      rethrow;
    }
  }

  /// نطق بديل أخير للعربية
  Future<void> _fallbackArabicSpeak(String text) async {
    try {
      debugPrint('🆘 محاولة النطق البديل الأخيرة للعربية');

      // محاولة مع إعدادات أساسية
      await _tts.setLanguage('ar');
      await _tts.setSpeechRate(0.5);
      await _tts.setVolume(1.0);
      await _tts.setPitch(1.0);

      await Future.delayed(const Duration(milliseconds: 500));

      final result = await _tts.speak(text);

      if (result == 1) {
        debugPrint('✅ نجح النطق البديل للعربية');
      } else {
        debugPrint('❌ فشل النطق البديل أيضاً');
        // كحل أخير، محاولة النطق بالإنجليزية
        await _tts.setLanguage('en-US');
        await _tts.speak('Arabic text: $text');
      }
    } catch (e) {
      debugPrint('❌ فشل في النطق البديل: $e');
    }
  }

  /// تكوين إعدادات النطق حسب اللغة
  Future<void> _configureLanguageSettings(String language) async {
    double speechRate = 0.5;
    double pitch = 1.0;
    double volume = 1.0;

    switch (language) {
      case 'ar':
        speechRate = 0.4; // أبطأ للعربية لوضوح أكبر
        pitch = 1.15; // نبرة أعلى للعربية
        volume = 1.0;
        break;
      case 'ja':
      case 'zh':
        speechRate = 0.45; // أبطأ للغات الآسيوية
        pitch = 1.0;
        volume = 1.0;
        break;
      case 'ru':
        speechRate = 0.5;
        pitch = 0.9; // نبرة أقل للروسية
        volume = 1.0;
        break;
      case 'de':
        speechRate = 0.55; // أسرع قليلاً للألمانية
        pitch = 0.95;
        volume = 1.0;
        break;
      default:
        speechRate = 0.5;
        pitch = 1.0;
        volume = 1.0;
    }

    await _tts.setSpeechRate(speechRate);
    await _tts.setVolume(volume);
    await _tts.setPitch(pitch);

    debugPrint(
      'تم تكوين إعدادات النطق - السرعة: $speechRate، النبرة: $pitch، الصوت: $volume',
    );
  }

  /// الحصول على كود اللغة للنطق
  String _getLanguageCode(String language) {
    switch (language) {
      case 'ar':
        return 'ar-SA';
      case 'en':
        return 'en-US';
      case 'fr':
        return 'fr-FR';
      case 'de':
        return 'de-DE';
      case 'es':
        return 'es-ES';
      case 'it':
        return 'it-IT';
      case 'ja':
        return 'ja-JP';
      case 'zh':
        return 'zh-CN';
      case 'tr':
        return 'tr-TR';
      case 'ru':
        return 'ru-RU';
      default:
        return 'en-US';
    }
  }

  /// نطق بديل عند فشل النطق الأساسي
  Future<void> _fallbackSpeak(String text, String originalLanguage) async {
    debugPrint('محاولة النطق البديل للنص: $text');

    // قائمة اللغات البديلة للمحاولة
    final fallbackLanguages = ['en-US', 'en-GB', 'ar-SA', 'fr-FR'];

    for (final fallbackLang in fallbackLanguages) {
      if (fallbackLang == _getLanguageCode(originalLanguage)) {
        continue; // تخطي اللغة الأصلية
      }

      try {
        debugPrint('محاولة النطق البديل باللغة: $fallbackLang');

        await _tts.setLanguage(fallbackLang);
        await _tts.setSpeechRate(0.5);
        await _tts.setPitch(1.0);
        await _tts.setVolume(1.0);

        final result = await _tts.speak(text);

        if (result == 1) {
          debugPrint('نجح النطق البديل باللغة: $fallbackLang');
          return;
        }
      } catch (e) {
        debugPrint('فشل النطق البديل باللغة $fallbackLang: $e');
        continue;
      }
    }

    debugPrint('فشل في جميع محاولات النطق البديل');
  }

  /// تسجيل استخدام العبارة
  Future<void> recordUsage(String phraseId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final usageData = prefs.getString(_usageKey) ?? '{}';
      final usage = Map<String, int>.from(jsonDecode(usageData));

      usage[phraseId] = (usage[phraseId] ?? 0) + 1;

      await prefs.setString(_usageKey, jsonEncode(usage));
    } catch (e) {
      debugPrint('خطأ في تسجيل الاستخدام: $e');
    }
  }

  /// الحصول على العبارات الأكثر استخداماً
  Future<List<TourismPhrase>> getMostUsedPhrases({int limit = 10}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final usageData = prefs.getString(_usageKey) ?? '{}';
      final usage = Map<String, int>.from(jsonDecode(usageData));

      final sortedPhrases =
          _allPhrases.where((phrase) => usage.containsKey(phrase.id)).toList();

      sortedPhrases.sort(
        (a, b) => (usage[b.id] ?? 0).compareTo(usage[a.id] ?? 0),
      );

      return sortedPhrases.take(limit).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على العبارات الأكثر استخداماً: $e');
      return [];
    }
  }

  /// تشغيل اختبار شامل للنطق العربي
  Future<void> runArabicSpeechTest() async {
    await ArabicSpeechTest.runComprehensiveTest();
  }

  /// تشغيل اختبار سريع للنطق العربي
  Future<void> runQuickArabicTest() async {
    await ArabicSpeechTest.runQuickTest();
  }

  /// توليد العبارات السياحية
  List<TourismPhrase> _generateTourismPhrases() {
    final phrases = <TourismPhrase>[];

    // عبارات المطار
    phrases.addAll(_generateAirportPhrases());

    // عبارات الفندق
    phrases.addAll(_generateHotelPhrases());

    // عبارات المطعم
    phrases.addAll(_generateRestaurantPhrases());

    // عبارات المواصلات
    phrases.addAll(_generateTransportationPhrases());

    // عبارات التسوق
    phrases.addAll(_generateShoppingPhrases());

    // عبارات الطوارئ
    phrases.addAll(_generateEmergencyPhrases());

    // عبارات الاتجاهات
    phrases.addAll(_generateDirectionsPhrases());

    // عبارات المعالم السياحية
    phrases.addAll(_generateAttractionsPhrases());

    // عبارات عامة
    phrases.addAll(_generateGeneralPhrases());

    // عبارات الملابس والأزياء
    phrases.addAll(_generateClothingPhrases());

    // عبارات أنواع الطعام
    phrases.addAll(_generateFoodPhrases());

    // عبارات الأسواق والتسوق
    phrases.addAll(_generateMarketsPhrases());

    // عبارات التعارف والتعريف
    phrases.addAll(_generateIntroductionPhrases());

    return phrases;
  }

  /// عبارات المطار
  List<TourismPhrase> _generateAirportPhrases() {
    return [
      TourismPhrase(
        category: TourismCategories.airport,
        subcategory: 'تسجيل الوصول',
        translations: {
          'ar': 'أين مكتب تسجيل الوصول؟',
          'en': 'Where is the check-in counter?',
          'fr': 'Où est le comptoir d\'enregistrement?',
          'de': 'Wo ist der Check-in-Schalter?',
          'es': '¿Dónde está el mostrador de facturación?',
          'it': 'Dove è il banco del check-in?',
          'ja': 'チェックインカウンターはどこですか？',
          'zh': '登机柜台在哪里？',
          'tr': 'Check-in kontuarı nerede?',
          'ru': 'Где стойка регистрации?',
        },
        pronunciation: 'Where is the check-in counter?',
      ),
      TourismPhrase(
        category: TourismCategories.airport,
        subcategory: 'تسجيل الوصول',
        translations: {
          'ar': 'أريد تسجيل الوصول لرحلة رقم...',
          'en': 'I want to check in for flight number...',
          'fr': 'Je veux m\'enregistrer pour le vol numéro...',
          'de': 'Ich möchte für Flug Nummer... einchecken',
          'es': 'Quiero hacer el check-in para el vuelo número...',
          'it': 'Voglio fare il check-in per il volo numero...',
          'ja': '便番号...のチェックインをしたいです',
          'zh': '我想办理航班号...的登机手续',
          'tr': '... numaralı uçuş için check-in yapmak istiyorum',
          'ru': 'Я хочу зарегистрироваться на рейс номер...',
        },
        pronunciation: 'I want to check in for flight number...',
      ),
      TourismPhrase(
        category: TourismCategories.airport,
        subcategory: 'الأمتعة',
        translations: {
          'ar': 'أين يمكنني استلام أمتعتي؟',
          'en': 'Where can I collect my luggage?',
        },
        pronunciation: 'Where can I collect my luggage?',
      ),
      TourismPhrase(
        category: TourismCategories.airport,
        subcategory: 'الأمتعة',
        translations: {'ar': 'أمتعتي مفقودة', 'en': 'My luggage is lost'},
        pronunciation: 'My luggage is lost',
      ),
      TourismPhrase(
        category: TourismCategories.airport,
        subcategory: 'الأمان',
        translations: {
          'ar': 'أين نقطة التفتيش الأمني؟',
          'en': 'Where is the security checkpoint?',
        },
        pronunciation: 'Where is the security checkpoint?',
      ),

      // عبارات إضافية للمطار
      TourismPhrase(
        category: TourismCategories.airport,
        subcategory: 'تسجيل الوصول',
        translations: {
          'ar': 'هل يمكنني اختيار مقعدي؟',
          'en': 'Can I choose my seat?',
          'fr': 'Puis-je choisir mon siège?',
          'de': 'Kann ich meinen Sitzplatz wählen?',
          'es': '¿Puedo elegir mi asiento?',
          'it': 'Posso scegliere il mio posto?',
          'ja': '座席を選べますか？',
          'zh': '我可以选择座位吗？',
          'tr': 'Koltuğumu seçebilir miyim?',
          'ru': 'Могу ли я выбрать место?',
        },
        pronunciation: 'Can I choose my seat?',
      ),
      TourismPhrase(
        category: TourismCategories.airport,
        subcategory: 'تسجيل الوصول',
        translations: {
          'ar': 'أريد مقعد بجانب النافذة',
          'en': 'I want a window seat',
          'fr': 'Je veux un siège côté hublot',
          'de': 'Ich möchte einen Fensterplatz',
          'es': 'Quiero un asiento junto a la ventana',
          'it': 'Voglio un posto vicino al finestrino',
          'ja': '窓側の席をお願いします',
          'zh': '我想要靠窗的座位',
          'tr': 'Pencere kenarı koltuk istiyorum',
          'ru': 'Я хочу место у окна',
        },
        pronunciation: 'I want a window seat',
      ),
      TourismPhrase(
        category: TourismCategories.airport,
        subcategory: 'الأمتعة',
        translations: {
          'ar': 'كم الوزن المسموح للأمتعة؟',
          'en': 'What is the baggage weight limit?',
          'fr': 'Quelle est la limite de poids des bagages?',
          'de': 'Wie hoch ist das Gepäckgewichtslimit?',
          'es': '¿Cuál es el límite de peso del equipaje?',
          'it': 'Qual è il limite di peso dei bagagli?',
          'ja': '荷物の重量制限はいくらですか？',
          'zh': '行李重量限制是多少？',
          'tr': 'Bagaj ağırlık sınırı nedir?',
          'ru': 'Какой лимит веса багажа?',
        },
        pronunciation: 'What is the baggage weight limit?',
      ),
      TourismPhrase(
        category: TourismCategories.airport,
        subcategory: 'الأمتعة',
        translations: {
          'ar': 'أمتعتي تالفة',
          'en': 'My luggage is damaged',
          'fr': 'Mes bagages sont endommagés',
          'de': 'Mein Gepäck ist beschädigt',
          'es': 'Mi equipaje está dañado',
          'it': 'I miei bagagli sono danneggiati',
          'ja': '荷物が破損しています',
          'zh': '我的行李损坏了',
          'tr': 'Bagajım hasar görmüş',
          'ru': 'Мой багаж поврежден',
        },
        pronunciation: 'My luggage is damaged',
      ),
      TourismPhrase(
        category: TourismCategories.airport,
        subcategory: 'الأمان',
        translations: {
          'ar': 'هل يمكنني حمل هذا في الحقيبة اليدوية؟',
          'en': 'Can I carry this in my hand luggage?',
          'fr': 'Puis-je emporter ceci dans mon bagage à main?',
          'de': 'Kann ich das im Handgepäck mitnehmen?',
          'es': '¿Puedo llevar esto en mi equipaje de mano?',
          'it': 'Posso portare questo nel bagaglio a mano?',
          'ja': 'これを手荷物に入れて持ち込めますか？',
          'zh': '我可以把这个放在手提行李里吗？',
          'tr': 'Bunu el bagajımda taşıyabilir miyim?',
          'ru': 'Могу ли я взять это в ручную кладь?',
        },
        pronunciation: 'Can I carry this in my hand luggage?',
      ),
      TourismPhrase(
        category: TourismCategories.airport,
        subcategory: 'البوابات',
        translations: {
          'ar': 'أين البوابة رقم...؟',
          'en': 'Where is gate number...?',
          'fr': 'Où est la porte numéro...?',
          'de': 'Wo ist Gate Nummer...?',
          'es': '¿Dónde está la puerta número...?',
          'it': 'Dove è il gate numero...?',
          'ja': '...番ゲートはどこですか？',
          'zh': '...号登机口在哪里？',
          'tr': '... numaralı kapı nerede?',
          'ru': 'Где выход номер...?',
        },
        pronunciation: 'Where is gate number...?',
      ),
      TourismPhrase(
        category: TourismCategories.airport,
        subcategory: 'البوابات',
        translations: {
          'ar': 'متى يبدأ الصعود للطائرة؟',
          'en': 'When does boarding start?',
          'fr': 'Quand commence l\'embarquement?',
          'de': 'Wann beginnt das Boarding?',
          'es': '¿Cuándo comienza el embarque?',
          'it': 'Quando inizia l\'imbarco?',
          'ja': '搭乗はいつ始まりますか？',
          'zh': '什么时候开始登机？',
          'tr': 'Uçağa biniş ne zaman başlıyor?',
          'ru': 'Когда начинается посадка?',
        },
        pronunciation: 'When does boarding start?',
      ),
      TourismPhrase(
        category: TourismCategories.airport,
        subcategory: 'تأخير الرحلات',
        translations: {
          'ar': 'هل الرحلة متأخرة؟',
          'en': 'Is the flight delayed?',
          'fr': 'Le vol est-il retardé?',
          'de': 'Ist der Flug verspätet?',
          'es': '¿Está retrasado el vuelo?',
          'it': 'Il volo è in ritardo?',
          'ja': 'フライトは遅れていますか？',
          'zh': '航班延误了吗？',
          'tr': 'Uçuş gecikti mi?',
          'ru': 'Рейс задерживается?',
        },
        pronunciation: 'Is the flight delayed?',
      ),
      TourismPhrase(
        category: TourismCategories.airport,
        subcategory: 'تأخير الرحلات',
        translations: {
          'ar': 'كم مدة التأخير؟',
          'en': 'How long is the delay?',
          'fr': 'Combien de temps dure le retard?',
          'de': 'Wie lange ist die Verspätung?',
          'es': '¿Cuánto tiempo es el retraso?',
          'it': 'Quanto dura il ritardo?',
          'ja': 'どのくらい遅れますか？',
          'zh': '延误多长时间？',
          'tr': 'Gecikme ne kadar sürecek?',
          'ru': 'На сколько задерживается рейс?',
        },
        pronunciation: 'How long is the delay?',
      ),
      TourismPhrase(
        category: TourismCategories.airport,
        subcategory: 'الجمارك',
        translations: {
          'ar': 'أين الجمارك؟',
          'en': 'Where is customs?',
          'fr': 'Où est la douane?',
          'de': 'Wo ist der Zoll?',
          'es': '¿Dónde está la aduana?',
          'it': 'Dove è la dogana?',
          'ja': '税関はどこですか？',
          'zh': '海关在哪里？',
          'tr': 'Gümrük nerede?',
          'ru': 'Где таможня?',
        },
        pronunciation: 'Where is customs?',
      ),
      TourismPhrase(
        category: TourismCategories.airport,
        subcategory: 'الجمارك',
        translations: {
          'ar': 'ليس لدي شيء للتصريح عنه',
          'en': 'I have nothing to declare',
          'fr': 'Je n\'ai rien à déclarer',
          'de': 'Ich habe nichts zu verzollen',
          'es': 'No tengo nada que declarar',
          'it': 'Non ho nulla da dichiarare',
          'ja': '申告するものはありません',
          'zh': '我没有需要申报的物品',
          'tr': 'Beyan edecek bir şeyim yok',
          'ru': 'Мне нечего декларировать',
        },
        pronunciation: 'I have nothing to declare',
      ),
    ];
  }

  /// عبارات الفندق (15-20 عبارة)
  List<TourismPhrase> _generateHotelPhrases() {
    return [
      // الحجز
      TourismPhrase(
        category: TourismCategories.hotel,
        subcategory: 'الحجز',
        translations: {
          'ar': 'لدي حجز باسم...',
          'en': 'I have a reservation under the name...',
          'fr': 'J\'ai une réservation au nom de...',
          'de': 'Ich habe eine Reservierung unter dem Namen...',
          'es': 'Tengo una reserva a nombre de...',
          'it': 'Ho una prenotazione a nome di...',
          'ja': '...の名前で予約しています',
          'zh': '我有一个以...名字预订的房间',
          'tr': '... adına rezervasyonum var',
          'ru': 'У меня бронь на имя...',
        },
        pronunciation: 'I have a reservation under the name...',
      ),
      TourismPhrase(
        category: TourismCategories.hotel,
        subcategory: 'الحجز',
        translations: {
          'ar': 'أريد غرفة لليلة واحدة',
          'en': 'I need a room for one night',
          'fr': 'J\'ai besoin d\'une chambre pour une nuit',
          'de': 'Ich brauche ein Zimmer für eine Nacht',
          'es': 'Necesito una habitación por una noche',
          'it': 'Ho bisogno di una camera per una notte',
          'ja': '一泊の部屋が必要です',
          'zh': '我需要一间房住一晚',
          'tr': 'Bir gecelik oda istiyorum',
          'ru': 'Мне нужен номер на одну ночь',
        },
        pronunciation: 'I need a room for one night',
      ),
      TourismPhrase(
        category: TourismCategories.hotel,
        subcategory: 'الحجز',
        translations: {
          'ar': 'هل لديكم غرف متاحة؟',
          'en': 'Do you have any rooms available?',
          'fr': 'Avez-vous des chambres disponibles?',
          'de': 'Haben Sie freie Zimmer?',
          'es': '¿Tienen habitaciones disponibles?',
          'it': 'Avete camere disponibili?',
          'ja': '空室はありますか？',
          'zh': '你们有空房间吗？',
          'tr': 'Müsait odanız var mı?',
          'ru': 'У вас есть свободные номера?',
        },
        pronunciation: 'Do you have any rooms available?',
      ),
      TourismPhrase(
        category: TourismCategories.hotel,
        subcategory: 'الحجز',
        translations: {
          'ar': 'كم سعر الغرفة لليلة الواحدة؟',
          'en': 'How much is the room per night?',
          'fr': 'Combien coûte la chambre par nuit?',
          'de': 'Wie viel kostet das Zimmer pro Nacht?',
          'es': '¿Cuánto cuesta la habitación por noche?',
          'it': 'Quanto costa la camera per notte?',
          'ja': '一泊いくらですか？',
          'zh': '房间一晚多少钱？',
          'tr': 'Oda gecelik ne kadar?',
          'ru': 'Сколько стоит номер за ночь?',
        },
        pronunciation: 'How much is the room per night?',
      ),

      // تسجيل الدخول
      TourismPhrase(
        category: TourismCategories.hotel,
        subcategory: 'تسجيل الدخول',
        translations: {
          'ar': 'أريد تسجيل الدخول',
          'en': 'I would like to check in',
          'fr': 'Je voudrais m\'enregistrer',
          'de': 'Ich möchte einchecken',
          'es': 'Me gustaría hacer el check-in',
          'it': 'Vorrei fare il check-in',
          'ja': 'チェックインしたいです',
          'zh': '我想办理入住',
          'tr': 'Check-in yapmak istiyorum',
          'ru': 'Я хочу заселиться',
        },
        pronunciation: 'I would like to check in',
      ),
      TourismPhrase(
        category: TourismCategories.hotel,
        subcategory: 'تسجيل الدخول',
        translations: {
          'ar': 'هل يمكنني رؤية الغرفة أولاً؟',
          'en': 'Can I see the room first?',
          'fr': 'Puis-je voir la chambre d\'abord?',
          'de': 'Kann ich das Zimmer zuerst sehen?',
          'es': '¿Puedo ver la habitación primero?',
          'it': 'Posso vedere la camera prima?',
          'ja': '最初に部屋を見ることができますか？',
          'zh': '我可以先看看房间吗？',
          'tr': 'Önce odayı görebilir miyim?',
          'ru': 'Могу ли я сначала посмотреть номер?',
        },
        pronunciation: 'Can I see the room first?',
      ),

      // المرافق والخدمات
      TourismPhrase(
        category: TourismCategories.hotel,
        subcategory: 'المرافق',
        translations: {
          'ar': 'أين المصعد؟',
          'en': 'Where is the elevator?',
          'fr': 'Où est l\'ascenseur?',
          'de': 'Wo ist der Aufzug?',
          'es': '¿Dónde está el ascensor?',
          'it': 'Dove è l\'ascensore?',
          'ja': 'エレベーターはどこですか？',
          'zh': '电梯在哪里？',
          'tr': 'Asansör nerede?',
          'ru': 'Где лифт?',
        },
        pronunciation: 'Where is the elevator?',
      ),
      TourismPhrase(
        category: TourismCategories.hotel,
        subcategory: 'المرافق',
        translations: {
          'ar': 'هل يوجد واي فاي مجاني؟',
          'en': 'Is there free WiFi?',
          'fr': 'Y a-t-il du WiFi gratuit?',
          'de': 'Gibt es kostenloses WLAN?',
          'es': '¿Hay WiFi gratis?',
          'it': 'C\'è WiFi gratuito?',
          'ja': '無料WiFiはありますか？',
          'zh': '有免费WiFi吗？',
          'tr': 'Ücretsiz WiFi var mı?',
          'ru': 'Есть ли бесплатный WiFi?',
        },
        pronunciation: 'Is there free WiFi?',
      ),
      TourismPhrase(
        category: TourismCategories.hotel,
        subcategory: 'المرافق',
        translations: {
          'ar': 'أين حمام السباحة؟',
          'en': 'Where is the swimming pool?',
          'fr': 'Où est la piscine?',
          'de': 'Wo ist das Schwimmbad?',
          'es': '¿Dónde está la piscina?',
          'it': 'Dove è la piscina?',
          'ja': 'プールはどこですか？',
          'zh': '游泳池在哪里？',
          'tr': 'Yüzme havuzu nerede?',
          'ru': 'Где бассейн?',
        },
        pronunciation: 'Where is the swimming pool?',
      ),
      TourismPhrase(
        category: TourismCategories.hotel,
        subcategory: 'المرافق',
        translations: {
          'ar': 'أين صالة الألعاب الرياضية؟',
          'en': 'Where is the gym?',
          'fr': 'Où est la salle de sport?',
          'de': 'Wo ist das Fitnessstudio?',
          'es': '¿Dónde está el gimnasio?',
          'it': 'Dove è la palestra?',
          'ja': 'ジムはどこですか？',
          'zh': '健身房在哪里？',
          'tr': 'Spor salonu nerede?',
          'ru': 'Где спортзал?',
        },
        pronunciation: 'Where is the gym?',
      ),

      // خدمة الغرف
      TourismPhrase(
        category: TourismCategories.hotel,
        subcategory: 'خدمة الغرف',
        translations: {
          'ar': 'أريد طلب خدمة الغرف',
          'en': 'I would like to order room service',
          'fr': 'Je voudrais commander le service en chambre',
          'de': 'Ich möchte Zimmerservice bestellen',
          'es': 'Me gustaría pedir servicio a la habitación',
          'it': 'Vorrei ordinare il servizio in camera',
          'ja': 'ルームサービスを注文したいです',
          'zh': '我想要客房服务',
          'tr': 'Oda servisi sipariş etmek istiyorum',
          'ru': 'Я хочу заказать обслуживание в номере',
        },
        pronunciation: 'I would like to order room service',
      ),
      TourismPhrase(
        category: TourismCategories.hotel,
        subcategory: 'خدمة الغرف',
        translations: {
          'ar': 'هل يمكن تنظيف غرفتي؟',
          'en': 'Can you clean my room?',
          'fr': 'Pouvez-vous nettoyer ma chambre?',
          'de': 'Können Sie mein Zimmer reinigen?',
          'es': '¿Pueden limpiar mi habitación?',
          'it': 'Potete pulire la mia camera?',
          'ja': '部屋を掃除してもらえますか？',
          'zh': '你们能打扫我的房间吗？',
          'tr': 'Odamı temizleyebilir misiniz?',
          'ru': 'Можете ли вы убрать мой номер?',
        },
        pronunciation: 'Can you clean my room?',
      ),
      TourismPhrase(
        category: TourismCategories.hotel,
        subcategory: 'خدمة الغرف',
        translations: {
          'ar': 'أحتاج مناشف إضافية',
          'en': 'I need extra towels',
          'fr': 'J\'ai besoin de serviettes supplémentaires',
          'de': 'Ich brauche zusätzliche Handtücher',
          'es': 'Necesito toallas adicionales',
          'it': 'Ho bisogno di asciugamani extra',
          'ja': '追加のタオルが必要です',
          'zh': '我需要额外的毛巾',
          'tr': 'Ekstra havluya ihtiyacım var',
          'ru': 'Мне нужны дополнительные полотенца',
        },
        pronunciation: 'I need extra towels',
      ),

      // الشكاوى والمشاكل
      TourismPhrase(
        category: TourismCategories.hotel,
        subcategory: 'الشكاوى',
        translations: {
          'ar': 'التكييف لا يعمل',
          'en': 'The air conditioning is not working',
          'fr': 'La climatisation ne fonctionne pas',
          'de': 'Die Klimaanlage funktioniert nicht',
          'es': 'El aire acondicionado no funciona',
          'it': 'L\'aria condizionata non funziona',
          'ja': 'エアコンが動きません',
          'zh': '空调不工作',
          'tr': 'Klima çalışmıyor',
          'ru': 'Кондиционер не работает',
        },
        pronunciation: 'The air conditioning is not working',
      ),
      TourismPhrase(
        category: TourismCategories.hotel,
        subcategory: 'الشكاوى',
        translations: {
          'ar': 'هناك ضوضاء كثيرة',
          'en': 'There is too much noise',
          'fr': 'Il y a trop de bruit',
          'de': 'Es ist zu laut',
          'es': 'Hay demasiado ruido',
          'it': 'C\'è troppo rumore',
          'ja': '騒音がひどいです',
          'zh': '太吵了',
          'tr': 'Çok fazla gürültü var',
          'ru': 'Слишком много шума',
        },
        pronunciation: 'There is too much noise',
      ),

      // تسجيل الخروج والخدمات
      TourismPhrase(
        category: TourismCategories.hotel,
        subcategory: 'تسجيل الخروج',
        translations: {
          'ar': 'أريد تسجيل الخروج',
          'en': 'I would like to check out',
          'fr': 'Je voudrais faire le check-out',
          'de': 'Ich möchte auschecken',
          'es': 'Me gustaría hacer el check-out',
          'it': 'Vorrei fare il check-out',
          'ja': 'チェックアウトしたいです',
          'zh': '我想办理退房',
          'tr': 'Check-out yapmak istiyorum',
          'ru': 'Я хочу выселиться',
        },
        pronunciation: 'I would like to check out',
      ),
      TourismPhrase(
        category: TourismCategories.hotel,
        subcategory: 'تسجيل الخروج',
        translations: {
          'ar': 'هل يمكنني ترك أمتعتي هنا؟',
          'en': 'Can I leave my luggage here?',
          'fr': 'Puis-je laisser mes bagages ici?',
          'de': 'Kann ich mein Gepäck hier lassen?',
          'es': '¿Puedo dejar mi equipaje aquí?',
          'it': 'Posso lasciare i miei bagagli qui?',
          'ja': '荷物をここに預けられますか？',
          'zh': '我可以把行李放在这里吗？',
          'tr': 'Bagajımı burada bırakabilir miyim?',
          'ru': 'Могу ли я оставить багаж здесь?',
        },
        pronunciation: 'Can I leave my luggage here?',
      ),
      TourismPhrase(
        category: TourismCategories.hotel,
        subcategory: 'الخدمات',
        translations: {
          'ar': 'هل يمكنني الحصول على الفاتورة؟',
          'en': 'Can I get the bill?',
          'fr': 'Puis-je avoir la facture?',
          'de': 'Kann ich die Rechnung bekommen?',
          'es': '¿Puedo obtener la factura?',
          'it': 'Posso avere il conto?',
          'ja': '請求書をもらえますか？',
          'zh': '我可以要账单吗？',
          'tr': 'Faturayı alabilir miyim?',
          'ru': 'Могу ли я получить счет?',
        },
        pronunciation: 'Can I get the bill?',
      ),
      TourismPhrase(
        category: TourismCategories.hotel,
        subcategory: 'الخدمات',
        translations: {
          'ar': 'هل يمكنكم استدعاء سيارة أجرة؟',
          'en': 'Can you call a taxi?',
          'fr': 'Pouvez-vous appeler un taxi?',
          'de': 'Können Sie ein Taxi rufen?',
          'es': '¿Pueden llamar un taxi?',
          'it': 'Potete chiamare un taxi?',
          'ja': 'タクシーを呼んでもらえますか？',
          'zh': '你们能叫出租车吗？',
          'tr': 'Taksi çağırabilir misiniz?',
          'ru': 'Можете ли вы вызвать такси?',
        },
        pronunciation: 'Can you call a taxi?',
      ),
      TourismPhrase(
        category: TourismCategories.hotel,
        subcategory: 'الخدمات',
        translations: {
          'ar': 'أين يمكنني تبديل العملة؟',
          'en': 'Where can I exchange money?',
          'fr': 'Où puis-je changer de l\'argent?',
          'de': 'Wo kann ich Geld wechseln?',
          'es': '¿Dónde puedo cambiar dinero?',
          'it': 'Dove posso cambiare denaro?',
          'ja': 'どこで両替できますか？',
          'zh': '我在哪里可以换钱？',
          'tr': 'Nerede para bozdurabilirim?',
          'ru': 'Где я могу обменять деньги?',
        },
        pronunciation: 'Where can I exchange money?',
      ),
      TourismPhrase(
        category: TourismCategories.hotel,
        subcategory: 'الخدمات',
        translations: {
          'ar': 'هل يوجد خدمة غسيل الملابس؟',
          'en': 'Is there a laundry service?',
          'fr': 'Y a-t-il un service de blanchisserie?',
          'de': 'Gibt es einen Wäscheservice?',
          'es': '¿Hay servicio de lavandería?',
          'it': 'C\'è un servizio lavanderia?',
          'ja': 'ランドリーサービスはありますか？',
          'zh': '有洗衣服务吗？',
          'tr': 'Çamaşır yıkama servisi var mı?',
          'ru': 'Есть ли прачечная?',
        },
        pronunciation: 'Is there a laundry service?',
      ),
      TourismPhrase(
        category: TourismCategories.hotel,
        subcategory: 'خدمة الغرف',
        translations: {
          'ar': 'هل يمكن تنظيف غرفتي؟',
          'en': 'Can you clean my room?',
          'fr': 'Pouvez-vous nettoyer ma chambre?',
          'de': 'Können Sie mein Zimmer reinigen?',
          'es': '¿Pueden limpiar mi habitación?',
          'it': 'Potete pulire la mia camera?',
          'ja': '部屋を掃除してもらえますか？',
          'zh': '你们能打扫我的房间吗？',
          'tr': 'Odamı temizleyebilir misiniz?',
          'ru': 'Можете ли вы убрать мой номер?',
        },
        pronunciation: 'Can you clean my room?',
      ),
      TourismPhrase(
        category: TourismCategories.hotel,
        subcategory: 'خدمة الغرف',
        translations: {
          'ar': 'أحتاج مناشف إضافية',
          'en': 'I need extra towels',
          'fr': 'J\'ai besoin de serviettes supplémentaires',
          'de': 'Ich brauche zusätzliche Handtücher',
          'es': 'Necesito toallas adicionales',
          'it': 'Ho bisogno di asciugamani extra',
          'ja': '追加のタオルが必要です',
          'zh': '我需要额外的毛巾',
          'tr': 'Ekstra havluya ihtiyacım var',
          'ru': 'Мне нужны дополнительные полотенца',
        },
        pronunciation: 'I need extra towels',
      ),

      // الشكاوى
      TourismPhrase(
        category: TourismCategories.hotel,
        subcategory: 'الشكاوى',
        translations: {
          'ar': 'التكييف لا يعمل',
          'en': 'The air conditioning is not working',
          'fr': 'La climatisation ne fonctionne pas',
          'de': 'Die Klimaanlage funktioniert nicht',
          'es': 'El aire acondicionado no funciona',
          'it': 'L\'aria condizionata non funziona',
          'ja': 'エアコンが動きません',
          'zh': '空调不工作',
          'tr': 'Klima çalışmıyor',
          'ru': 'Кондиционер не работает',
        },
        pronunciation: 'The air conditioning is not working',
      ),
      TourismPhrase(
        category: TourismCategories.hotel,
        subcategory: 'الشكاوى',
        translations: {
          'ar': 'الغرفة صاخبة جداً',
          'en': 'The room is too noisy',
          'fr': 'La chambre est trop bruyante',
          'de': 'Das Zimmer ist zu laut',
          'es': 'La habitación es muy ruidosa',
          'it': 'La camera è troppo rumorosa',
          'ja': '部屋がうるさすぎます',
          'zh': '房间太吵了',
          'tr': 'Oda çok gürültülü',
          'ru': 'В номере слишком шумно',
        },
        pronunciation: 'The room is too noisy',
      ),

      // تسجيل الخروج
      TourismPhrase(
        category: TourismCategories.hotel,
        subcategory: 'تسجيل الخروج',
        translations: {
          'ar': 'أريد تسجيل الخروج',
          'en': 'I would like to check out',
          'fr': 'Je voudrais faire le check-out',
          'de': 'Ich möchte auschecken',
          'es': 'Me gustaría hacer el check-out',
          'it': 'Vorrei fare il check-out',
          'ja': 'チェックアウトしたいです',
          'zh': '我想退房',
          'tr': 'Check-out yapmak istiyorum',
          'ru': 'Я хочу выселиться',
        },
        pronunciation: 'I would like to check out',
      ),
      TourismPhrase(
        category: TourismCategories.hotel,
        subcategory: 'تسجيل الخروج',
        translations: {
          'ar': 'هل يمكنني ترك أمتعتي هنا؟',
          'en': 'Can I leave my luggage here?',
          'fr': 'Puis-je laisser mes bagages ici?',
          'de': 'Kann ich mein Gepäck hier lassen?',
          'es': '¿Puedo dejar mi equipaje aquí?',
          'it': 'Posso lasciare i miei bagagli qui?',
          'ja': '荷物をここに置いておけますか？',
          'zh': '我可以把行李放在这里吗？',
          'tr': 'Bagajımı burada bırakabilir miyim?',
          'ru': 'Могу ли я оставить багаж здесь?',
        },
        pronunciation: 'Can I leave my luggage here?',
      ),
      TourismPhrase(
        category: TourismCategories.hotel,
        subcategory: 'تسجيل الخروج',
        translations: {
          'ar': 'هل يمكنني الحصول على الفاتورة؟',
          'en': 'Can I get the bill?',
          'fr': 'Puis-je avoir la facture?',
          'de': 'Kann ich die Rechnung bekommen?',
          'es': '¿Puedo obtener la factura?',
          'it': 'Posso avere il conto?',
          'ja': '請求書をもらえますか？',
          'zh': '我可以拿到账单吗？',
          'tr': 'Faturayı alabilir miyim?',
          'ru': 'Могу ли я получить счет?',
        },
        pronunciation: 'Can I get the bill?',
      ),
    ];
  }

  /// عبارات المطعم (15-20 عبارة)
  List<TourismPhrase> _generateRestaurantPhrases() {
    return [
      // القائمة والطلب
      TourismPhrase(
        category: TourismCategories.restaurant,
        subcategory: 'القائمة',
        translations: {
          'ar': 'هل يمكنني رؤية القائمة؟',
          'en': 'Can I see the menu?',
          'fr': 'Puis-je voir le menu?',
          'de': 'Kann ich die Speisekarte sehen?',
          'es': '¿Puedo ver el menú?',
          'it': 'Posso vedere il menu?',
          'ja': 'メニューを見せてもらえますか？',
          'zh': '我可以看看菜单吗？',
          'tr': 'Menüyü görebilir miyim?',
          'ru': 'Могу ли я посмотреть меню?',
        },
        pronunciation: 'Can I see the menu?',
      ),
      TourismPhrase(
        category: TourismCategories.restaurant,
        subcategory: 'القائمة',
        translations: {
          'ar': 'هل لديكم قائمة باللغة الإنجليزية؟',
          'en': 'Do you have an English menu?',
          'fr': 'Avez-vous un menu en anglais?',
          'de': 'Haben Sie eine englische Speisekarte?',
          'es': '¿Tienen un menú en inglés?',
          'it': 'Avete un menu in inglese?',
          'ja': '英語のメニューはありますか？',
          'zh': '你们有英文菜单吗？',
          'tr': 'İngilizce menünüz var mı?',
          'ru': 'У вас есть меню на английском?',
        },
        pronunciation: 'Do you have an English menu?',
      ),
      TourismPhrase(
        category: TourismCategories.restaurant,
        subcategory: 'الطلب',
        translations: {
          'ar': 'أريد طلب هذا الطبق',
          'en': 'I would like to order this dish',
          'fr': 'Je voudrais commander ce plat',
          'de': 'Ich möchte dieses Gericht bestellen',
          'es': 'Me gustaría pedir este plato',
          'it': 'Vorrei ordinare questo piatto',
          'ja': 'この料理を注文したいです',
          'zh': '我想点这道菜',
          'tr': 'Bu yemeği sipariş etmek istiyorum',
          'ru': 'Я хочу заказать это блюдо',
        },
        pronunciation: 'I would like to order this dish',
      ),
      TourismPhrase(
        category: TourismCategories.restaurant,
        subcategory: 'الطلب',
        translations: {
          'ar': 'هل يمكنني الحصول على كوب من الماء؟',
          'en': 'Can I have a glass of water?',
          'fr': 'Puis-je avoir un verre d\'eau?',
          'de': 'Kann ich ein Glas Wasser haben?',
          'es': '¿Puedo tener un vaso de agua?',
          'it': 'Posso avere un bicchiere d\'acqua?',
          'ja': '水を一杯もらえますか？',
          'zh': '我可以要一杯水吗？',
          'tr': 'Bir bardak su alabilir miyim?',
          'ru': 'Можно стакан воды?',
        },
        pronunciation: 'Can I have a glass of water?',
      ),
      TourismPhrase(
        category: TourismCategories.restaurant,
        subcategory: 'الطلب',
        translations: {
          'ar': 'أريد طاولة لشخصين',
          'en': 'I want a table for two',
          'fr': 'Je veux une table pour deux',
          'de': 'Ich möchte einen Tisch für zwei',
          'es': 'Quiero una mesa para dos',
          'it': 'Voglio un tavolo per due',
          'ja': '二人用のテーブルをお願いします',
          'zh': '我要一张两人桌',
          'tr': 'İki kişilik masa istiyorum',
          'ru': 'Мне нужен столик на двоих',
        },
        pronunciation: 'I want a table for two',
      ),

      // التوصيات
      TourismPhrase(
        category: TourismCategories.restaurant,
        subcategory: 'التوصيات',
        translations: {
          'ar': 'ما هو الطبق المميز اليوم؟',
          'en': 'What is the special dish today?',
          'fr': 'Quel est le plat spécial aujourd\'hui?',
          'de': 'Was ist das Tagesgericht?',
          'es': '¿Cuál es el plato especial de hoy?',
          'it': 'Qual è il piatto speciale di oggi?',
          'ja': '今日のおすすめ料理は何ですか？',
          'zh': '今天的特色菜是什么？',
          'tr': 'Bugünün özel yemeği nedir?',
          'ru': 'Какое блюдо дня сегодня?',
        },
        pronunciation: 'What is the special dish today?',
      ),
      TourismPhrase(
        category: TourismCategories.restaurant,
        subcategory: 'التوصيات',
        translations: {
          'ar': 'ما تنصحونني به؟',
          'en': 'What do you recommend?',
          'fr': 'Que me recommandez-vous?',
          'de': 'Was empfehlen Sie?',
          'es': '¿Qué me recomienda?',
          'it': 'Cosa mi consiglia?',
          'ja': '何がおすすめですか？',
          'zh': '你推荐什么？',
          'tr': 'Ne tavsiye edersiniz?',
          'ru': 'Что вы рекомендуете?',
        },
        pronunciation: 'What do you recommend?',
      ),
      TourismPhrase(
        category: TourismCategories.restaurant,
        subcategory: 'التوصيات',
        translations: {
          'ar': 'هل هذا الطبق حار؟',
          'en': 'Is this dish spicy?',
          'fr': 'Ce plat est-il épicé?',
          'de': 'Ist dieses Gericht scharf?',
          'es': '¿Este plato es picante?',
          'it': 'Questo piatto è piccante?',
          'ja': 'この料理は辛いですか？',
          'zh': '这道菜辣吗？',
          'tr': 'Bu yemek acı mı?',
          'ru': 'Это блюдо острое?',
        },
        pronunciation: 'Is this dish spicy?',
      ),

      // الحساسية الغذائية
      TourismPhrase(
        category: TourismCategories.restaurant,
        subcategory: 'الحساسية الغذائية',
        translations: {
          'ar': 'لدي حساسية من المكسرات',
          'en': 'I am allergic to nuts',
          'fr': 'Je suis allergique aux noix',
          'de': 'Ich bin allergisch gegen Nüsse',
          'es': 'Soy alérgico a los frutos secos',
          'it': 'Sono allergico alle noci',
          'ja': 'ナッツアレルギーがあります',
          'zh': '我对坚果过敏',
          'tr': 'Fındık alerjim var',
          'ru': 'У меня аллергия на орехи',
        },
        pronunciation: 'I am allergic to nuts',
      ),
      TourismPhrase(
        category: TourismCategories.restaurant,
        subcategory: 'الحساسية الغذائية',
        translations: {
          'ar': 'لا آكل اللحم',
          'en': 'I don\'t eat meat',
          'fr': 'Je ne mange pas de viande',
          'de': 'Ich esse kein Fleisch',
          'es': 'No como carne',
          'it': 'Non mangio carne',
          'ja': '肉は食べません',
          'zh': '我不吃肉',
          'tr': 'Et yemem',
          'ru': 'Я не ем мясо',
        },
        pronunciation: 'I don\'t eat meat',
      ),
      TourismPhrase(
        category: TourismCategories.restaurant,
        subcategory: 'الحساسية الغذائية',
        translations: {
          'ar': 'هل يحتوي هذا على منتجات الألبان؟',
          'en': 'Does this contain dairy?',
          'fr': 'Cela contient-il des produits laitiers?',
          'de': 'Enthält das Milchprodukte?',
          'es': '¿Esto contiene lácteos?',
          'it': 'Questo contiene latticini?',
          'ja': 'これには乳製品が含まれていますか？',
          'zh': '这个含有乳制品吗？',
          'tr': 'Bu süt ürünü içeriyor mu?',
          'ru': 'Это содержит молочные продукты?',
        },
        pronunciation: 'Does this contain dairy?',
      ),

      // الدفع
      TourismPhrase(
        category: TourismCategories.restaurant,
        subcategory: 'الدفع',
        translations: {
          'ar': 'الحساب من فضلك',
          'en': 'The bill, please',
          'fr': 'L\'addition, s\'il vous plaît',
          'de': 'Die Rechnung, bitte',
          'es': 'La cuenta, por favor',
          'it': 'Il conto, per favore',
          'ja': 'お会計をお願いします',
          'zh': '请结账',
          'tr': 'Hesap lütfen',
          'ru': 'Счет, пожалуйста',
        },
        pronunciation: 'The bill, please',
      ),
      TourismPhrase(
        category: TourismCategories.restaurant,
        subcategory: 'الدفع',
        translations: {
          'ar': 'هل تقبلون البطاقة الائتمانية؟',
          'en': 'Do you accept credit cards?',
          'fr': 'Acceptez-vous les cartes de crédit?',
          'de': 'Nehmen Sie Kreditkarten?',
          'es': '¿Aceptan tarjetas de crédito?',
          'it': 'Accettate carte di credito?',
          'ja': 'クレジットカードは使えますか？',
          'zh': '你们接受信用卡吗？',
          'tr': 'Kredi kartı kabul ediyor musunuz?',
          'ru': 'Вы принимаете кредитные карты?',
        },
        pronunciation: 'Do you accept credit cards?',
      ),
      TourismPhrase(
        category: TourismCategories.restaurant,
        subcategory: 'الدفع',
        translations: {
          'ar': 'هل الخدمة مشمولة؟',
          'en': 'Is service included?',
          'fr': 'Le service est-il inclus?',
          'de': 'Ist die Bedienung inbegriffen?',
          'es': '¿Está incluido el servicio?',
          'it': 'Il servizio è incluso?',
          'ja': 'サービス料は含まれていますか？',
          'zh': '包含服务费吗？',
          'tr': 'Servis dahil mi?',
          'ru': 'Обслуживание включено?',
        },
        pronunciation: 'Is service included?',
      ),

      // الشكاوى
      TourismPhrase(
        category: TourismCategories.restaurant,
        subcategory: 'الشكاوى',
        translations: {
          'ar': 'هذا ليس ما طلبته',
          'en': 'This is not what I ordered',
          'fr': 'Ce n\'est pas ce que j\'ai commandé',
          'de': 'Das ist nicht das, was ich bestellt habe',
          'es': 'Esto no es lo que pedí',
          'it': 'Questo non è quello che ho ordinato',
          'ja': 'これは注文したものではありません',
          'zh': '这不是我点的',
          'tr': 'Bu sipariş ettiğim şey değil',
          'ru': 'Это не то, что я заказывал',
        },
        pronunciation: 'This is not what I ordered',
      ),
      TourismPhrase(
        category: TourismCategories.restaurant,
        subcategory: 'الشكاوى',
        translations: {
          'ar': 'الطعام بارد',
          'en': 'The food is cold',
          'fr': 'La nourriture est froide',
          'de': 'Das Essen ist kalt',
          'es': 'La comida está fría',
          'it': 'Il cibo è freddo',
          'ja': '料理が冷めています',
          'zh': '食物是冷的',
          'tr': 'Yemek soğuk',
          'ru': 'Еда холодная',
        },
        pronunciation: 'The food is cold',
      ),
      TourismPhrase(
        category: TourismCategories.restaurant,
        subcategory: 'الشكاوى',
        translations: {
          'ar': 'هل يمكنكم تسخين هذا؟',
          'en': 'Can you heat this up?',
          'fr': 'Pouvez-vous réchauffer ceci?',
          'de': 'Können Sie das aufwärmen?',
          'es': '¿Pueden calentar esto?',
          'it': 'Potete riscaldare questo?',
          'ja': 'これを温めてもらえますか？',
          'zh': '你们能把这个加热吗？',
          'tr': 'Bunu ısıtabilir misiniz?',
          'ru': 'Можете это разогреть?',
        },
        pronunciation: 'Can you heat this up?',
      ),
    ];
  }

  /// عبارات المواصلات (20 عبارة)
  List<TourismPhrase> _generateTransportationPhrases() {
    return [
      // التاكسي
      TourismPhrase(
        category: TourismCategories.transportation,
        subcategory: 'التاكسي',
        translations: {
          'ar': 'أريد تاكسي إلى المطار',
          'en': 'I need a taxi to the airport',
          'fr': 'J\'ai besoin d\'un taxi pour l\'aéroport',
          'de': 'Ich brauche ein Taxi zum Flughafen',
          'es': 'Necesito un taxi al aeropuerto',
          'it': 'Ho bisogno di un taxi per l\'aeroporto',
          'ja': '空港までタクシーが必要です',
          'zh': '我需要一辆出租车去机场',
          'tr': 'Havaalanına taksi istiyorum',
          'ru': 'Мне нужно такси в аэропорт',
        },
        pronunciation: 'I need a taxi to the airport',
      ),
      TourismPhrase(
        category: TourismCategories.transportation,
        subcategory: 'التاكسي',
        translations: {
          'ar': 'كم تكلفة الرحلة؟',
          'en': 'How much does the trip cost?',
          'fr': 'Combien coûte le voyage?',
          'de': 'Wie viel kostet die Fahrt?',
          'es': '¿Cuánto cuesta el viaje?',
          'it': 'Quanto costa il viaggio?',
          'ja': '旅行はいくらかかりますか？',
          'zh': '这次旅行要多少钱？',
          'tr': 'Yolculuk ne kadar tutuyor?',
          'ru': 'Сколько стоит поездка?',
        },
        pronunciation: 'How much does the trip cost?',
      ),
      TourismPhrase(
        category: TourismCategories.transportation,
        subcategory: 'التاكسي',
        translations: {
          'ar': 'هل يمكنك تشغيل العداد؟',
          'en': 'Can you turn on the meter?',
          'fr': 'Pouvez-vous allumer le compteur?',
          'de': 'Können Sie das Taxameter einschalten?',
          'es': '¿Puede encender el taxímetro?',
          'it': 'Può accendere il tassametro?',
          'ja': 'メーターをつけてもらえますか？',
          'zh': '你能打开计价器吗？',
          'tr': 'Taksimetreyi açabilir misiniz?',
          'ru': 'Можете включить счетчик?',
        },
        pronunciation: 'Can you turn on the meter?',
      ),
      TourismPhrase(
        category: TourismCategories.transportation,
        subcategory: 'التاكسي',
        translations: {
          'ar': 'توقف هنا من فضلك',
          'en': 'Stop here please',
          'fr': 'Arrêtez-vous ici s\'il vous plaît',
          'de': 'Halten Sie hier bitte an',
          'es': 'Pare aquí por favor',
          'it': 'Si fermi qui per favore',
          'ja': 'ここで止めてください',
          'zh': '请在这里停车',
          'tr': 'Lütfen burada durun',
          'ru': 'Остановитесь здесь, пожалуйста',
        },
        pronunciation: 'Stop here please',
      ),

      // الحافلة
      TourismPhrase(
        category: TourismCategories.transportation,
        subcategory: 'الحافلة',
        translations: {
          'ar': 'أين محطة الحافلات؟',
          'en': 'Where is the bus station?',
          'fr': 'Où est la gare routière?',
          'de': 'Wo ist der Busbahnhof?',
          'es': '¿Dónde está la estación de autobuses?',
          'it': 'Dove è la stazione degli autobus?',
          'ja': 'バス停はどこですか？',
          'zh': '公交车站在哪里？',
          'tr': 'Otobüs durağı nerede?',
          'ru': 'Где автобусная станция?',
        },
        pronunciation: 'Where is the bus station?',
      ),
      TourismPhrase(
        category: TourismCategories.transportation,
        subcategory: 'الحافلة',
        translations: {
          'ar': 'هل هذه الحافلة تذهب إلى وسط المدينة؟',
          'en': 'Does this bus go to the city center?',
          'fr': 'Ce bus va-t-il au centre-ville?',
          'de': 'Fährt dieser Bus ins Stadtzentrum?',
          'es': '¿Este autobús va al centro de la ciudad?',
          'it': 'Questo autobus va in centro?',
          'ja': 'このバスは市内中心部に行きますか？',
          'zh': '这辆公交车去市中心吗？',
          'tr': 'Bu otobüs şehir merkezine gidiyor mu?',
          'ru': 'Этот автобус идет в центр города?',
        },
        pronunciation: 'Does this bus go to the city center?',
      ),
      TourismPhrase(
        category: TourismCategories.transportation,
        subcategory: 'الحافلة',
        translations: {
          'ar': 'كم سعر التذكرة؟',
          'en': 'How much is the ticket?',
          'fr': 'Combien coûte le billet?',
          'de': 'Wie viel kostet das Ticket?',
          'es': '¿Cuánto cuesta el boleto?',
          'it': 'Quanto costa il biglietto?',
          'ja': 'チケットはいくらですか？',
          'zh': '票价多少？',
          'tr': 'Bilet ne kadar?',
          'ru': 'Сколько стоит билет?',
        },
        pronunciation: 'How much is the ticket?',
      ),
      TourismPhrase(
        category: TourismCategories.transportation,
        subcategory: 'الحافلة',
        translations: {
          'ar': 'متى تصل الحافلة التالية؟',
          'en': 'When does the next bus arrive?',
          'fr': 'Quand arrive le prochain bus?',
          'de': 'Wann kommt der nächste Bus?',
          'es': '¿Cuándo llega el próximo autobús?',
          'it': 'Quando arriva il prossimo autobus?',
          'ja': '次のバスはいつ来ますか？',
          'zh': '下一班公交车什么时候到？',
          'tr': 'Bir sonraki otobüs ne zaman geliyor?',
          'ru': 'Когда приедет следующий автобус?',
        },
        pronunciation: 'When does the next bus arrive?',
      ),

      // القطار
      TourismPhrase(
        category: TourismCategories.transportation,
        subcategory: 'القطار',
        translations: {
          'ar': 'متى يصل القطار التالي؟',
          'en': 'When does the next train arrive?',
          'fr': 'Quand arrive le prochain train?',
          'de': 'Wann kommt der nächste Zug?',
          'es': '¿Cuándo llega el próximo tren?',
          'it': 'Quando arriva il prossimo treno?',
          'ja': '次の電車はいつ来ますか？',
          'zh': '下一班火车什么时候到？',
          'tr': 'Bir sonraki tren ne zaman geliyor?',
          'ru': 'Когда приедет следующий поезд?',
        },
        pronunciation: 'When does the next train arrive?',
      ),
      TourismPhrase(
        category: TourismCategories.transportation,
        subcategory: 'القطار',
        translations: {
          'ar': 'أين يمكنني شراء تذكرة؟',
          'en': 'Where can I buy a ticket?',
          'fr': 'Où puis-je acheter un billet?',
          'de': 'Wo kann ich ein Ticket kaufen?',
          'es': '¿Dónde puedo comprar un boleto?',
          'it': 'Dove posso comprare un biglietto?',
          'ja': 'どこでチケットを買えますか？',
          'zh': '我在哪里可以买票？',
          'tr': 'Nereden bilet alabilirim?',
          'ru': 'Где я могу купить билет?',
        },
        pronunciation: 'Where can I buy a ticket?',
      ),
      TourismPhrase(
        category: TourismCategories.transportation,
        subcategory: 'القطار',
        translations: {
          'ar': 'أي رصيف للقطار المتجه إلى...؟',
          'en': 'Which platform for the train to...?',
          'fr': 'Quel quai pour le train vers...?',
          'de': 'Welcher Bahnsteig für den Zug nach...?',
          'es': '¿Qué andén para el tren a...?',
          'it': 'Quale binario per il treno per...?',
          'ja': '...行きの電車はどのホームですか？',
          'zh': '去...的火车在哪个站台？',
          'tr': '...\'a giden tren hangi perondan?',
          'ru': 'С какой платформы поезд до...?',
        },
        pronunciation: 'Which platform for the train to...?',
      ),
      TourismPhrase(
        category: TourismCategories.transportation,
        subcategory: 'القطار',
        translations: {
          'ar': 'هل هذا المقعد محجوز؟',
          'en': 'Is this seat reserved?',
          'fr': 'Cette place est-elle réservée?',
          'de': 'Ist dieser Platz reserviert?',
          'es': '¿Está reservado este asiento?',
          'it': 'Questo posto è riservato?',
          'ja': 'この席は予約されていますか？',
          'zh': '这个座位有人预订吗？',
          'tr': 'Bu koltuk rezerve mi?',
          'ru': 'Это место зарезервировано?',
        },
        pronunciation: 'Is this seat reserved?',
      ),

      // المترو
      TourismPhrase(
        category: TourismCategories.transportation,
        subcategory: 'المترو',
        translations: {
          'ar': 'أين مدخل المترو؟',
          'en': 'Where is the subway entrance?',
          'fr': 'Où est l\'entrée du métro?',
          'de': 'Wo ist der U-Bahn-Eingang?',
          'es': '¿Dónde está la entrada del metro?',
          'it': 'Dove è l\'ingresso della metropolitana?',
          'ja': '地下鉄の入口はどこですか？',
          'zh': '地铁入口在哪里？',
          'tr': 'Metro girişi nerede?',
          'ru': 'Где вход в метро?',
        },
        pronunciation: 'Where is the subway entrance?',
      ),
      TourismPhrase(
        category: TourismCategories.transportation,
        subcategory: 'المترو',
        translations: {
          'ar': 'أي خط للذهاب إلى...؟',
          'en': 'Which line to go to...?',
          'fr': 'Quelle ligne pour aller à...?',
          'de': 'Welche Linie nach...?',
          'es': '¿Qué línea para ir a...?',
          'it': 'Quale linea per andare a...?',
          'ja': '...に行くにはどの路線ですか？',
          'zh': '去...坐哪条线？',
          'tr': '...\'a gitmek için hangi hat?',
          'ru': 'Какая линия до...?',
        },
        pronunciation: 'Which line to go to...?',
      ),

      // تأجير السيارات
      TourismPhrase(
        category: TourismCategories.transportation,
        subcategory: 'تأجير السيارات',
        translations: {
          'ar': 'أريد استئجار سيارة',
          'en': 'I want to rent a car',
          'fr': 'Je veux louer une voiture',
          'de': 'Ich möchte ein Auto mieten',
          'es': 'Quiero alquilar un coche',
          'it': 'Voglio noleggiare un\'auto',
          'ja': '車をレンタルしたいです',
          'zh': '我想租一辆车',
          'tr': 'Araba kiralamak istiyorum',
          'ru': 'Я хочу арендовать машину',
        },
        pronunciation: 'I want to rent a car',
      ),
      TourismPhrase(
        category: TourismCategories.transportation,
        subcategory: 'تأجير السيارات',
        translations: {
          'ar': 'هل لديكم تأمين شامل؟',
          'en': 'Do you have full insurance?',
          'fr': 'Avez-vous une assurance complète?',
          'de': 'Haben Sie eine Vollkaskoversicherung?',
          'es': '¿Tienen seguro completo?',
          'it': 'Avete un\'assicurazione completa?',
          'ja': '完全保険はありますか？',
          'zh': '你们有全险吗？',
          'tr': 'Tam sigortanız var mı?',
          'ru': 'У вас есть полная страховка?',
        },
        pronunciation: 'Do you have full insurance?',
      ),
      TourismPhrase(
        category: TourismCategories.transportation,
        subcategory: 'تأجير السيارات',
        translations: {
          'ar': 'أين يمكنني إرجاع السيارة؟',
          'en': 'Where can I return the car?',
          'fr': 'Où puis-je rendre la voiture?',
          'de': 'Wo kann ich das Auto zurückgeben?',
          'es': '¿Dónde puedo devolver el coche?',
          'it': 'Dove posso restituire l\'auto?',
          'ja': 'どこで車を返却できますか？',
          'zh': '我在哪里可以还车？',
          'tr': 'Arabayı nerede iade edebilirim?',
          'ru': 'Где я могу вернуть машину?',
        },
        pronunciation: 'Where can I return the car?',
      ),

      // الطيران
      TourismPhrase(
        category: TourismCategories.transportation,
        subcategory: 'الطيران',
        translations: {
          'ar': 'أين بوابة المغادرة؟',
          'en': 'Where is the departure gate?',
          'fr': 'Où est la porte d\'embarquement?',
          'de': 'Wo ist das Abfluggate?',
          'es': '¿Dónde está la puerta de embarque?',
          'it': 'Dove è il gate di partenza?',
          'ja': '出発ゲートはどこですか？',
          'zh': '登机口在哪里？',
          'tr': 'Kalkış kapısı nerede?',
          'ru': 'Где выход на посадку?',
        },
        pronunciation: 'Where is the departure gate?',
      ),
      TourismPhrase(
        category: TourismCategories.transportation,
        subcategory: 'الطيران',
        translations: {
          'ar': 'هل الرحلة في الموعد؟',
          'en': 'Is the flight on time?',
          'fr': 'Le vol est-il à l\'heure?',
          'de': 'Ist der Flug pünktlich?',
          'es': '¿El vuelo está a tiempo?',
          'it': 'Il volo è in orario?',
          'ja': 'フライトは時間通りですか？',
          'zh': '航班准时吗？',
          'tr': 'Uçuş zamanında mı?',
          'ru': 'Рейс по расписанию?',
        },
        pronunciation: 'Is the flight on time?',
      ),
    ];
  }

  /// عبارات التسوق (20 عبارة)
  List<TourismPhrase> _generateShoppingPhrases() {
    return [
      // الأسعار
      TourismPhrase(
        category: TourismCategories.shopping,
        subcategory: 'الأسعار',
        translations: {
          'ar': 'كم سعر هذا؟',
          'en': 'How much is this?',
          'fr': 'Combien ça coûte?',
          'de': 'Wie viel kostet das?',
          'es': '¿Cuánto cuesta esto?',
          'it': 'Quanto costa questo?',
          'ja': 'これはいくらですか？',
          'zh': '这个多少钱？',
          'tr': 'Bu ne kadar?',
          'ru': 'Сколько это стоит?',
        },
        pronunciation: 'How much is this?',
      ),
      TourismPhrase(
        category: TourismCategories.shopping,
        subcategory: 'الأسعار',
        translations: {
          'ar': 'هل يمكنكم تخفيض السعر؟',
          'en': 'Can you reduce the price?',
          'fr': 'Pouvez-vous baisser le prix?',
          'de': 'Können Sie den Preis senken?',
          'es': '¿Pueden reducir el precio?',
          'it': 'Potete ridurre il prezzo?',
          'ja': '値段を下げてもらえますか？',
          'zh': '你们能降价吗？',
          'tr': 'Fiyatı düşürebilir misiniz?',
          'ru': 'Можете снизить цену?',
        },
        pronunciation: 'Can you reduce the price?',
      ),
      TourismPhrase(
        category: TourismCategories.shopping,
        subcategory: 'الأسعار',
        translations: {
          'ar': 'هل هذا أفضل سعر؟',
          'en': 'Is this your best price?',
          'fr': 'Est-ce votre meilleur prix?',
          'de': 'Ist das Ihr bester Preis?',
          'es': '¿Es su mejor precio?',
          'it': 'È il vostro miglior prezzo?',
          'ja': 'これが最安値ですか？',
          'zh': '这是你们的最低价吗？',
          'tr': 'Bu en iyi fiyatınız mı?',
          'ru': 'Это ваша лучшая цена?',
        },
        pronunciation: 'Is this your best price?',
      ),
      TourismPhrase(
        category: TourismCategories.shopping,
        subcategory: 'الأسعار',
        translations: {
          'ar': 'هل يوجد خصم؟',
          'en': 'Is there a discount?',
          'fr': 'Y a-t-il une remise?',
          'de': 'Gibt es einen Rabatt?',
          'es': '¿Hay descuento?',
          'it': 'C\'è uno sconto?',
          'ja': '割引はありますか？',
          'zh': '有折扣吗？',
          'tr': 'İndirim var mı?',
          'ru': 'Есть ли скидка?',
        },
        pronunciation: 'Is there a discount?',
      ),

      // المقاسات والألوان
      TourismPhrase(
        category: TourismCategories.shopping,
        subcategory: 'المقاسات',
        translations: {
          'ar': 'هل يوجد مقاس أكبر؟',
          'en': 'Do you have a larger size?',
          'fr': 'Avez-vous une taille plus grande?',
          'de': 'Haben Sie eine größere Größe?',
          'es': '¿Tienen una talla más grande?',
          'it': 'Avete una taglia più grande?',
          'ja': 'もっと大きいサイズはありますか？',
          'zh': '有更大的尺码吗？',
          'tr': 'Daha büyük beden var mı?',
          'ru': 'У вас есть размер побольше?',
        },
        pronunciation: 'Do you have a larger size?',
      ),
      TourismPhrase(
        category: TourismCategories.shopping,
        subcategory: 'المقاسات',
        translations: {
          'ar': 'هل يوجد مقاس أصغر؟',
          'en': 'Do you have a smaller size?',
          'fr': 'Avez-vous une taille plus petite?',
          'de': 'Haben Sie eine kleinere Größe?',
          'es': '¿Tienen una talla más pequeña?',
          'it': 'Avete una taglia più piccola?',
          'ja': 'もっと小さいサイズはありますか？',
          'zh': '有更小的尺码吗？',
          'tr': 'Daha küçük beden var mı?',
          'ru': 'У вас есть размер поменьше?',
        },
        pronunciation: 'Do you have a smaller size?',
      ),
      TourismPhrase(
        category: TourismCategories.shopping,
        subcategory: 'الألوان',
        translations: {
          'ar': 'هل يوجد لون آخر؟',
          'en': 'Do you have another color?',
          'fr': 'Avez-vous une autre couleur?',
          'de': 'Haben Sie eine andere Farbe?',
          'es': '¿Tienen otro color?',
          'it': 'Avete un altro colore?',
          'ja': '他の色はありますか？',
          'zh': '有其他颜色吗？',
          'tr': 'Başka renk var mı?',
          'ru': 'У вас есть другой цвет?',
        },
        pronunciation: 'Do you have another color?',
      ),
      TourismPhrase(
        category: TourismCategories.shopping,
        subcategory: 'الألوان',
        translations: {
          'ar': 'أريد هذا باللون الأسود',
          'en': 'I want this in black',
          'fr': 'Je veux ceci en noir',
          'de': 'Ich möchte das in Schwarz',
          'es': 'Quiero esto en negro',
          'it': 'Voglio questo in nero',
          'ja': 'これを黒で欲しいです',
          'zh': '我要黑色的',
          'tr': 'Bunu siyah renkte istiyorum',
          'ru': 'Я хочу это в черном цвете',
        },
        pronunciation: 'I want this in black',
      ),

      // التجربة والقياس
      TourismPhrase(
        category: TourismCategories.shopping,
        subcategory: 'التجربة',
        translations: {
          'ar': 'هل يمكنني تجربة هذا؟',
          'en': 'Can I try this on?',
          'fr': 'Puis-je essayer ceci?',
          'de': 'Kann ich das anprobieren?',
          'es': '¿Puedo probarme esto?',
          'it': 'Posso provare questo?',
          'ja': 'これを試着できますか？',
          'zh': '我可以试穿这个吗？',
          'tr': 'Bunu deneyebilir miyim?',
          'ru': 'Могу ли я это примерить?',
        },
        pronunciation: 'Can I try this on?',
      ),
      TourismPhrase(
        category: TourismCategories.shopping,
        subcategory: 'التجربة',
        translations: {
          'ar': 'أين غرفة القياس؟',
          'en': 'Where is the fitting room?',
          'fr': 'Où est la cabine d\'essayage?',
          'de': 'Wo ist die Umkleidekabine?',
          'es': '¿Dónde está el probador?',
          'it': 'Dove è il camerino?',
          'ja': '試着室はどこですか？',
          'zh': '试衣间在哪里？',
          'tr': 'Deneme kabini nerede?',
          'ru': 'Где примерочная?',
        },
        pronunciation: 'Where is the fitting room?',
      ),

      // الدفع والشراء
      TourismPhrase(
        category: TourismCategories.shopping,
        subcategory: 'الدفع',
        translations: {
          'ar': 'هل تقبلون البطاقة الائتمانية؟',
          'en': 'Do you accept credit cards?',
          'fr': 'Acceptez-vous les cartes de crédit?',
          'de': 'Nehmen Sie Kreditkarten?',
          'es': '¿Aceptan tarjetas de crédito?',
          'it': 'Accettate carte di credito?',
          'ja': 'クレジットカードは使えますか？',
          'zh': '你们接受信用卡吗？',
          'tr': 'Kredi kartı kabul ediyor musunuz?',
          'ru': 'Вы принимаете кредитные карты?',
        },
        pronunciation: 'Do you accept credit cards?',
      ),
      TourismPhrase(
        category: TourismCategories.shopping,
        subcategory: 'الدفع',
        translations: {
          'ar': 'أريد أن أدفع نقداً',
          'en': 'I want to pay cash',
          'fr': 'Je veux payer en espèces',
          'de': 'Ich möchte bar bezahlen',
          'es': 'Quiero pagar en efectivo',
          'it': 'Voglio pagare in contanti',
          'ja': '現金で支払いたいです',
          'zh': '我想付现金',
          'tr': 'Nakit ödemek istiyorum',
          'ru': 'Я хочу заплатить наличными',
        },
        pronunciation: 'I want to pay cash',
      ),
      TourismPhrase(
        category: TourismCategories.shopping,
        subcategory: 'الدفع',
        translations: {
          'ar': 'هل يمكنني الحصول على إيصال؟',
          'en': 'Can I get a receipt?',
          'fr': 'Puis-je avoir un reçu?',
          'de': 'Kann ich eine Quittung bekommen?',
          'es': '¿Puedo obtener un recibo?',
          'it': 'Posso avere una ricevuta?',
          'ja': 'レシートをもらえますか？',
          'zh': '我可以要收据吗？',
          'tr': 'Fiş alabilir miyim?',
          'ru': 'Могу ли я получить чек?',
        },
        pronunciation: 'Can I get a receipt?',
      ),

      // الخدمات والمساعدة
      TourismPhrase(
        category: TourismCategories.shopping,
        subcategory: 'الخدمات',
        translations: {
          'ar': 'هل يمكنكم تغليف هذا؟',
          'en': 'Can you wrap this?',
          'fr': 'Pouvez-vous emballer ceci?',
          'de': 'Können Sie das einpacken?',
          'es': '¿Pueden envolver esto?',
          'it': 'Potete incartare questo?',
          'ja': 'これを包んでもらえますか？',
          'zh': '你们能包装这个吗？',
          'tr': 'Bunu paketleyebilir misiniz?',
          'ru': 'Можете это упаковать?',
        },
        pronunciation: 'Can you wrap this?',
      ),
      TourismPhrase(
        category: TourismCategories.shopping,
        subcategory: 'الخدمات',
        translations: {
          'ar': 'هل يوجد ضمان؟',
          'en': 'Is there a warranty?',
          'fr': 'Y a-t-il une garantie?',
          'de': 'Gibt es eine Garantie?',
          'es': '¿Hay garantía?',
          'it': 'C\'è una garanzia?',
          'ja': '保証はありますか？',
          'zh': '有保修吗？',
          'tr': 'Garanti var mı?',
          'ru': 'Есть ли гарантия?',
        },
        pronunciation: 'Is there a warranty?',
      ),
      TourismPhrase(
        category: TourismCategories.shopping,
        subcategory: 'الخدمات',
        translations: {
          'ar': 'هل يمكنني إرجاع هذا؟',
          'en': 'Can I return this?',
          'fr': 'Puis-je retourner ceci?',
          'de': 'Kann ich das zurückgeben?',
          'es': '¿Puedo devolver esto?',
          'it': 'Posso restituire questo?',
          'ja': 'これを返品できますか？',
          'zh': '我可以退货吗？',
          'tr': 'Bunu iade edebilir miyim?',
          'ru': 'Могу ли я это вернуть?',
        },
        pronunciation: 'Can I return this?',
      ),
      TourismPhrase(
        category: TourismCategories.shopping,
        subcategory: 'الخدمات',
        translations: {
          'ar': 'أين خدمة العملاء؟',
          'en': 'Where is customer service?',
          'fr': 'Où est le service client?',
          'de': 'Wo ist der Kundenservice?',
          'es': '¿Dónde está el servicio al cliente?',
          'it': 'Dove è il servizio clienti?',
          'ja': 'カスタマーサービスはどこですか？',
          'zh': '客服在哪里？',
          'tr': 'Müşteri hizmetleri nerede?',
          'ru': 'Где служба поддержки клиентов?',
        },
        pronunciation: 'Where is customer service?',
      ),

      // البحث والاستفسار
      TourismPhrase(
        category: TourismCategories.shopping,
        subcategory: 'البحث',
        translations: {
          'ar': 'أبحث عن هدية',
          'en': 'I\'m looking for a gift',
          'fr': 'Je cherche un cadeau',
          'de': 'Ich suche ein Geschenk',
          'es': 'Busco un regalo',
          'it': 'Sto cercando un regalo',
          'ja': 'プレゼントを探しています',
          'zh': '我在找礼物',
          'tr': 'Hediye arıyorum',
          'ru': 'Я ищу подарок',
        },
        pronunciation: 'I\'m looking for a gift',
      ),
      TourismPhrase(
        category: TourismCategories.shopping,
        subcategory: 'البحث',
        translations: {
          'ar': 'هل لديكم هذا بجودة أفضل؟',
          'en': 'Do you have this in better quality?',
          'fr': 'Avez-vous ceci en meilleure qualité?',
          'de': 'Haben Sie das in besserer Qualität?',
          'es': '¿Tienen esto en mejor calidad?',
          'it': 'Avete questo di qualità migliore?',
          'ja': 'これのもっと良い品質はありますか？',
          'zh': '你们有质量更好的吗？',
          'tr': 'Bunun daha iyi kalitesi var mı?',
          'ru': 'У вас есть это лучшего качества?',
        },
        pronunciation: 'Do you have this in better quality?',
      ),
      TourismPhrase(
        category: TourismCategories.shopping,
        subcategory: 'البحث',
        translations: {
          'ar': 'أين قسم الإلكترونيات؟',
          'en': 'Where is the electronics section?',
          'fr': 'Où est le rayon électronique?',
          'de': 'Wo ist die Elektronikabteilung?',
          'es': '¿Dónde está la sección de electrónicos?',
          'it': 'Dove è la sezione elettronica?',
          'ja': '電子機器売り場はどこですか？',
          'zh': '电子产品区在哪里？',
          'tr': 'Elektronik bölümü nerede?',
          'ru': 'Где отдел электроники?',
        },
        pronunciation: 'Where is the electronics section?',
      ),
    ];
  }

  /// عبارات الطوارئ (15 عبارة)
  List<TourismPhrase> _generateEmergencyPhrases() {
    return [
      // الطوارئ الطبية
      TourismPhrase(
        category: TourismCategories.emergency,
        subcategory: 'الطوارئ الطبية',
        translations: {
          'ar': 'أحتاج طبيب',
          'en': 'I need a doctor',
          'fr': 'J\'ai besoin d\'un médecin',
          'de': 'Ich brauche einen Arzt',
          'es': 'Necesito un médico',
          'it': 'Ho bisogno di un dottore',
          'ja': '医者が必要です',
          'zh': '我需要医生',
          'tr': 'Doktora ihtiyacım var',
          'ru': 'Мне нужен врач',
        },
        pronunciation: 'I need a doctor',
      ),
      TourismPhrase(
        category: TourismCategories.emergency,
        subcategory: 'الطوارئ الطبية',
        translations: {
          'ar': 'اتصلوا بالإسعاف',
          'en': 'Call an ambulance',
          'fr': 'Appelez une ambulance',
          'de': 'Rufen Sie einen Krankenwagen',
          'es': 'Llamen una ambulancia',
          'it': 'Chiamate un\'ambulanza',
          'ja': '救急車を呼んでください',
          'zh': '叫救护车',
          'tr': 'Ambulans çağırın',
          'ru': 'Вызовите скорую помощь',
        },
        pronunciation: 'Call an ambulance',
      ),
      TourismPhrase(
        category: TourismCategories.emergency,
        subcategory: 'الطوارئ الطبية',
        translations: {
          'ar': 'أين أقرب مستشفى؟',
          'en': 'Where is the nearest hospital?',
          'fr': 'Où est l\'hôpital le plus proche?',
          'de': 'Wo ist das nächste Krankenhaus?',
          'es': '¿Dónde está el hospital más cercano?',
          'it': 'Dove è l\'ospedale più vicino?',
          'ja': '一番近い病院はどこですか？',
          'zh': '最近的医院在哪里？',
          'tr': 'En yakın hastane nerede?',
          'ru': 'Где ближайшая больница?',
        },
        pronunciation: 'Where is the nearest hospital?',
      ),
      TourismPhrase(
        category: TourismCategories.emergency,
        subcategory: 'الطوارئ الطبية',
        translations: {
          'ar': 'أنا مريض',
          'en': 'I am sick',
          'fr': 'Je suis malade',
          'de': 'Ich bin krank',
          'es': 'Estoy enfermo',
          'it': 'Sono malato',
          'ja': '病気です',
          'zh': '我生病了',
          'tr': 'Hastayım',
          'ru': 'Я болен',
        },
        pronunciation: 'I am sick',
      ),
      TourismPhrase(
        category: TourismCategories.emergency,
        subcategory: 'الطوارئ الطبية',
        translations: {
          'ar': 'أنا مصاب',
          'en': 'I am injured',
          'fr': 'Je suis blessé',
          'de': 'Ich bin verletzt',
          'es': 'Estoy herido',
          'it': 'Sono ferito',
          'ja': 'けがをしています',
          'zh': '我受伤了',
          'tr': 'Yaralıyım',
          'ru': 'Я ранен',
        },
        pronunciation: 'I am injured',
      ),

      // الأمن والشرطة
      TourismPhrase(
        category: TourismCategories.emergency,
        subcategory: 'الأمن والشرطة',
        translations: {
          'ar': 'اتصلوا بالشرطة',
          'en': 'Call the police',
          'fr': 'Appelez la police',
          'de': 'Rufen Sie die Polizei',
          'es': 'Llamen a la policía',
          'it': 'Chiamate la polizia',
          'ja': '警察を呼んでください',
          'zh': '叫警察',
          'tr': 'Polisi arayın',
          'ru': 'Вызовите полицию',
        },
        pronunciation: 'Call the police',
      ),
      TourismPhrase(
        category: TourismCategories.emergency,
        subcategory: 'الأمن والشرطة',
        translations: {
          'ar': 'ساعدوني!',
          'en': 'Help me!',
          'fr': 'Aidez-moi!',
          'de': 'Helfen Sie mir!',
          'es': '¡Ayúdenme!',
          'it': 'Aiutatemi!',
          'ja': '助けて！',
          'zh': '救命！',
          'tr': 'Yardım edin!',
          'ru': 'Помогите мне!',
        },
        pronunciation: 'Help me!',
      ),
      TourismPhrase(
        category: TourismCategories.emergency,
        subcategory: 'الأمن والشرطة',
        translations: {
          'ar': 'تم سرقتي',
          'en': 'I have been robbed',
          'fr': 'J\'ai été volé',
          'de': 'Ich wurde beraubt',
          'es': 'Me han robado',
          'it': 'Sono stato derubato',
          'ja': '強盗にあいました',
          'zh': '我被抢劫了',
          'tr': 'Soyuldum',
          'ru': 'Меня ограбили',
        },
        pronunciation: 'I have been robbed',
      ),
      TourismPhrase(
        category: TourismCategories.emergency,
        subcategory: 'الأمن والشرطة',
        translations: {
          'ar': 'فقدت جواز سفري',
          'en': 'I lost my passport',
          'fr': 'J\'ai perdu mon passeport',
          'de': 'Ich habe meinen Pass verloren',
          'es': 'Perdí mi pasaporte',
          'it': 'Ho perso il passaporto',
          'ja': 'パスポートを失くしました',
          'zh': '我丢了护照',
          'tr': 'Pasaportumu kaybettim',
          'ru': 'Я потерял паспорт',
        },
        pronunciation: 'I lost my passport',
      ),

      // الحرائق والكوارث
      TourismPhrase(
        category: TourismCategories.emergency,
        subcategory: 'الحرائق والكوارث',
        translations: {
          'ar': 'حريق!',
          'en': 'Fire!',
          'fr': 'Feu!',
          'de': 'Feuer!',
          'es': '¡Fuego!',
          'it': 'Fuoco!',
          'ja': '火事！',
          'zh': '着火了！',
          'tr': 'Yangın!',
          'ru': 'Пожар!',
        },
        pronunciation: 'Fire!',
      ),
      TourismPhrase(
        category: TourismCategories.emergency,
        subcategory: 'الحرائق والكوارث',
        translations: {
          'ar': 'اتصلوا بالإطفاء',
          'en': 'Call the fire department',
          'fr': 'Appelez les pompiers',
          'de': 'Rufen Sie die Feuerwehr',
          'es': 'Llamen a los bomberos',
          'it': 'Chiamate i vigili del fuoco',
          'ja': '消防署を呼んでください',
          'zh': '叫消防队',
          'tr': 'İtfaiyeyi arayın',
          'ru': 'Вызовите пожарную службу',
        },
        pronunciation: 'Call the fire department',
      ),

      // الاتصالات الطارئة
      TourismPhrase(
        category: TourismCategories.emergency,
        subcategory: 'الاتصالات',
        translations: {
          'ar': 'أحتاج للاتصال بسفارتي',
          'en': 'I need to call my embassy',
          'fr': 'Je dois appeler mon ambassade',
          'de': 'Ich muss meine Botschaft anrufen',
          'es': 'Necesito llamar a mi embajada',
          'it': 'Devo chiamare la mia ambasciata',
          'ja': '大使館に電話する必要があります',
          'zh': '我需要联系我的大使馆',
          'tr': 'Büyükelçiliğimi aramam gerekiyor',
          'ru': 'Мне нужно позвонить в посольство',
        },
        pronunciation: 'I need to call my embassy',
      ),
      TourismPhrase(
        category: TourismCategories.emergency,
        subcategory: 'الاتصالات',
        translations: {
          'ar': 'أين أقرب هاتف عمومي؟',
          'en': 'Where is the nearest public phone?',
          'fr': 'Où est le téléphone public le plus proche?',
          'de': 'Wo ist das nächste öffentliche Telefon?',
          'es': '¿Dónde está el teléfono público más cercano?',
          'it': 'Dove è il telefono pubblico più vicino?',
          'ja': '一番近い公衆電話はどこですか？',
          'zh': '最近的公用电话在哪里？',
          'tr': 'En yakın ankesörlü telefon nerede?',
          'ru': 'Где ближайший таксофон?',
        },
        pronunciation: 'Where is the nearest public phone?',
      ),

      // المساعدة العامة
      TourismPhrase(
        category: TourismCategories.emergency,
        subcategory: 'المساعدة العامة',
        translations: {
          'ar': 'أنا في خطر',
          'en': 'I am in danger',
          'fr': 'Je suis en danger',
          'de': 'Ich bin in Gefahr',
          'es': 'Estoy en peligro',
          'it': 'Sono in pericolo',
          'ja': '危険です',
          'zh': '我有危险',
          'tr': 'Tehlikedeyim',
          'ru': 'Я в опасности',
        },
        pronunciation: 'I am in danger',
      ),
      TourismPhrase(
        category: TourismCategories.emergency,
        subcategory: 'المساعدة العامة',
        translations: {
          'ar': 'أحتاج مساعدة فورية',
          'en': 'I need immediate help',
          'fr': 'J\'ai besoin d\'aide immédiate',
          'de': 'Ich brauche sofortige Hilfe',
          'es': 'Necesito ayuda inmediata',
          'it': 'Ho bisogno di aiuto immediato',
          'ja': '緊急に助けが必要です',
          'zh': '我需要立即帮助',
          'tr': 'Acil yardıma ihtiyacım var',
          'ru': 'Мне нужна немедленная помощь',
        },
        pronunciation: 'I need immediate help',
      ),
    ];
  }

  /// عبارات الاتجاهات (15 عبارة)
  List<TourismPhrase> _generateDirectionsPhrases() {
    return [
      // السؤال عن الأماكن
      TourismPhrase(
        category: TourismCategories.directions,
        subcategory: 'السؤال عن الأماكن',
        translations: {
          'ar': 'أين هذا المكان؟',
          'en': 'Where is this place?',
          'fr': 'Où est cet endroit?',
          'de': 'Wo ist dieser Ort?',
          'es': '¿Dónde está este lugar?',
          'it': 'Dove è questo posto?',
          'ja': 'この場所はどこですか？',
          'zh': '这个地方在哪里？',
          'tr': 'Bu yer nerede?',
          'ru': 'Где это место?',
        },
        pronunciation: 'Where is this place?',
      ),
      TourismPhrase(
        category: TourismCategories.directions,
        subcategory: 'السؤال عن الأماكن',
        translations: {
          'ar': 'كيف أصل إلى...؟',
          'en': 'How do I get to...?',
          'fr': 'Comment aller à...?',
          'de': 'Wie komme ich zu...?',
          'es': '¿Cómo llego a...?',
          'it': 'Come arrivo a...?',
          'ja': '...にはどう行けばいいですか？',
          'zh': '我怎么去...？',
          'tr': '...\'a nasıl giderim?',
          'ru': 'Как добраться до...?',
        },
        pronunciation: 'How do I get to...?',
      ),
      TourismPhrase(
        category: TourismCategories.directions,
        subcategory: 'السؤال عن الأماكن',
        translations: {
          'ar': 'أين وسط المدينة؟',
          'en': 'Where is the city center?',
          'fr': 'Où est le centre-ville?',
          'de': 'Wo ist das Stadtzentrum?',
          'es': '¿Dónde está el centro de la ciudad?',
          'it': 'Dove è il centro città?',
          'ja': '市内中心部はどこですか？',
          'zh': '市中心在哪里？',
          'tr': 'Şehir merkezi nerede?',
          'ru': 'Где центр города?',
        },
        pronunciation: 'Where is the city center?',
      ),
      TourismPhrase(
        category: TourismCategories.directions,
        subcategory: 'السؤال عن الأماكن',
        translations: {
          'ar': 'أين أقرب محطة مترو؟',
          'en': 'Where is the nearest metro station?',
          'fr': 'Où est la station de métro la plus proche?',
          'de': 'Wo ist die nächste U-Bahn-Station?',
          'es': '¿Dónde está la estación de metro más cercana?',
          'it': 'Dove è la stazione metro più vicina?',
          'ja': '一番近い地下鉄駅はどこですか？',
          'zh': '最近的地铁站在哪里？',
          'tr': 'En yakın metro istasyonu nerede?',
          'ru': 'Где ближайшая станция метро?',
        },
        pronunciation: 'Where is the nearest metro station?',
      ),

      // المسافات والوقت
      TourismPhrase(
        category: TourismCategories.directions,
        subcategory: 'المسافات والوقت',
        translations: {
          'ar': 'كم المسافة من هنا؟',
          'en': 'How far is it from here?',
          'fr': 'À quelle distance d\'ici?',
          'de': 'Wie weit ist es von hier?',
          'es': '¿Qué tan lejos está de aquí?',
          'it': 'Quanto dista da qui?',
          'ja': 'ここからどのくらい遠いですか？',
          'zh': '从这里有多远？',
          'tr': 'Buradan ne kadar uzak?',
          'ru': 'Как далеко это отсюда?',
        },
        pronunciation: 'How far is it from here?',
      ),
      TourismPhrase(
        category: TourismCategories.directions,
        subcategory: 'المسافات والوقت',
        translations: {
          'ar': 'كم يستغرق الوصول؟',
          'en': 'How long does it take to get there?',
          'fr': 'Combien de temps faut-il pour y arriver?',
          'de': 'Wie lange dauert es, dorthin zu gelangen?',
          'es': '¿Cuánto tiempo toma llegar?',
          'it': 'Quanto tempo ci vuole per arrivarci?',
          'ja': 'そこまでどのくらい時間がかかりますか？',
          'zh': '到那里需要多长时间？',
          'tr': 'Oraya varmak ne kadar sürer?',
          'ru': 'Сколько времени нужно, чтобы добраться?',
        },
        pronunciation: 'How long does it take to get there?',
      ),
      TourismPhrase(
        category: TourismCategories.directions,
        subcategory: 'المسافات والوقت',
        translations: {
          'ar': 'هل يمكنني المشي إلى هناك؟',
          'en': 'Can I walk there?',
          'fr': 'Puis-je y aller à pied?',
          'de': 'Kann ich dorthin laufen?',
          'es': '¿Puedo caminar hasta allí?',
          'it': 'Posso andarci a piedi?',
          'ja': 'そこまで歩いて行けますか？',
          'zh': '我可以走路去那里吗？',
          'tr': 'Oraya yürüyerek gidebilir miyim?',
          'ru': 'Могу ли я дойти туда пешком?',
        },
        pronunciation: 'Can I walk there?',
      ),

      // الاتجاهات الأساسية
      TourismPhrase(
        category: TourismCategories.directions,
        subcategory: 'الاتجاهات الأساسية',
        translations: {
          'ar': 'اذهب مباشرة',
          'en': 'Go straight',
          'fr': 'Allez tout droit',
          'de': 'Gehen Sie geradeaus',
          'es': 'Vaya derecho',
          'it': 'Vada dritto',
          'ja': 'まっすぐ行ってください',
          'zh': '直走',
          'tr': 'Düz gidin',
          'ru': 'Идите прямо',
        },
        pronunciation: 'Go straight',
      ),
      TourismPhrase(
        category: TourismCategories.directions,
        subcategory: 'الاتجاهات الأساسية',
        translations: {
          'ar': 'انعطف يميناً',
          'en': 'Turn right',
          'fr': 'Tournez à droite',
          'de': 'Biegen Sie rechts ab',
          'es': 'Gire a la derecha',
          'it': 'Giri a destra',
          'ja': '右に曲がってください',
          'zh': '向右转',
          'tr': 'Sağa dönün',
          'ru': 'Поверните направо',
        },
        pronunciation: 'Turn right',
      ),
      TourismPhrase(
        category: TourismCategories.directions,
        subcategory: 'الاتجاهات الأساسية',
        translations: {
          'ar': 'انعطف يساراً',
          'en': 'Turn left',
          'fr': 'Tournez à gauche',
          'de': 'Biegen Sie links ab',
          'es': 'Gire a la izquierda',
          'it': 'Giri a sinistra',
          'ja': '左に曲がってください',
          'zh': '向左转',
          'tr': 'Sola dönün',
          'ru': 'Поверните налево',
        },
        pronunciation: 'Turn left',
      ),
      TourismPhrase(
        category: TourismCategories.directions,
        subcategory: 'الاتجاهات الأساسية',
        translations: {
          'ar': 'ارجع للخلف',
          'en': 'Go back',
          'fr': 'Retournez',
          'de': 'Gehen Sie zurück',
          'es': 'Regrese',
          'it': 'Torni indietro',
          'ja': '戻ってください',
          'zh': '往回走',
          'tr': 'Geri dönün',
          'ru': 'Вернитесь назад',
        },
        pronunciation: 'Go back',
      ),

      // المعالم والنقاط المرجعية
      TourismPhrase(
        category: TourismCategories.directions,
        subcategory: 'المعالم',
        translations: {
          'ar': 'هل هو قريب من...؟',
          'en': 'Is it near...?',
          'fr': 'Est-ce près de...?',
          'de': 'Ist es in der Nähe von...?',
          'es': '¿Está cerca de...?',
          'it': 'È vicino a...?',
          'ja': '...の近くですか？',
          'zh': '它在...附近吗？',
          'tr': '...\'ın yakınında mı?',
          'ru': 'Это рядом с...?',
        },
        pronunciation: 'Is it near...?',
      ),
      TourismPhrase(
        category: TourismCategories.directions,
        subcategory: 'المعالم',
        translations: {
          'ar': 'أين الشارع الرئيسي؟',
          'en': 'Where is the main street?',
          'fr': 'Où est la rue principale?',
          'de': 'Wo ist die Hauptstraße?',
          'es': '¿Dónde está la calle principal?',
          'it': 'Dove è la strada principale?',
          'ja': 'メインストリートはどこですか？',
          'zh': '主街在哪里？',
          'tr': 'Ana cadde nerede?',
          'ru': 'Где главная улица?',
        },
        pronunciation: 'Where is the main street?',
      ),

      // طلب المساعدة
      TourismPhrase(
        category: TourismCategories.directions,
        subcategory: 'طلب المساعدة',
        translations: {
          'ar': 'هل يمكنك مساعدتي؟',
          'en': 'Can you help me?',
          'fr': 'Pouvez-vous m\'aider?',
          'de': 'Können Sie mir helfen?',
          'es': '¿Puede ayudarme?',
          'it': 'Può aiutarmi?',
          'ja': '手伝ってもらえますか？',
          'zh': '你能帮助我吗？',
          'tr': 'Bana yardım edebilir misiniz?',
          'ru': 'Можете ли вы мне помочь?',
        },
        pronunciation: 'Can you help me?',
      ),
      TourismPhrase(
        category: TourismCategories.directions,
        subcategory: 'طلب المساعدة',
        translations: {
          'ar': 'أنا تائه',
          'en': 'I am lost',
          'fr': 'Je suis perdu',
          'de': 'Ich habe mich verlaufen',
          'es': 'Estoy perdido',
          'it': 'Mi sono perso',
          'ja': '道に迷いました',
          'zh': '我迷路了',
          'tr': 'Kayboldum',
          'ru': 'Я заблудился',
        },
        pronunciation: 'I am lost',
      ),
    ];
  }

  /// عبارات المعالم السياحية (15 عبارة)
  List<TourismPhrase> _generateAttractionsPhrases() {
    return [
      // المتاحف والمعارض
      TourismPhrase(
        category: TourismCategories.attractions,
        subcategory: 'المتاحف والمعارض',
        translations: {
          'ar': 'متى يفتح المتحف؟',
          'en': 'When does the museum open?',
          'fr': 'Quand le musée ouvre-t-il?',
          'de': 'Wann öffnet das Museum?',
          'es': '¿Cuándo abre el museo?',
          'it': 'Quando apre il museo?',
          'ja': '博物館はいつ開きますか？',
          'zh': '博物馆什么时候开门？',
          'tr': 'Müze ne zaman açılıyor?',
          'ru': 'Когда открывается музей?',
        },
        pronunciation: 'When does the museum open?',
      ),
      TourismPhrase(
        category: TourismCategories.attractions,
        subcategory: 'المتاحف والمعارض',
        translations: {
          'ar': 'كم سعر تذكرة الدخول؟',
          'en': 'How much is the entrance ticket?',
          'fr': 'Combien coûte le billet d\'entrée?',
          'de': 'Wie viel kostet die Eintrittskarte?',
          'es': '¿Cuánto cuesta la entrada?',
          'it': 'Quanto costa il biglietto d\'ingresso?',
          'ja': '入場券はいくらですか？',
          'zh': '门票多少钱？',
          'tr': 'Giriş bileti ne kadar?',
          'ru': 'Сколько стоит входной билет?',
        },
        pronunciation: 'How much is the entrance ticket?',
      ),
      TourismPhrase(
        category: TourismCategories.attractions,
        subcategory: 'المتاحف والمعارض',
        translations: {
          'ar': 'هل يوجد خصم للطلاب؟',
          'en': 'Is there a student discount?',
          'fr': 'Y a-t-il une réduction étudiante?',
          'de': 'Gibt es einen Studentenrabatt?',
          'es': '¿Hay descuento para estudiantes?',
          'it': 'C\'è uno sconto per studenti?',
          'ja': '学生割引はありますか？',
          'zh': '有学生折扣吗？',
          'tr': 'Öğrenci indirimi var mı?',
          'ru': 'Есть ли скидка для студентов?',
        },
        pronunciation: 'Is there a student discount?',
      ),
      TourismPhrase(
        category: TourismCategories.attractions,
        subcategory: 'المتاحف والمعارض',
        translations: {
          'ar': 'هل يوجد جولة إرشادية؟',
          'en': 'Is there a guided tour?',
          'fr': 'Y a-t-il une visite guidée?',
          'de': 'Gibt es eine Führung?',
          'es': '¿Hay una visita guiada?',
          'it': 'C\'è una visita guidata?',
          'ja': 'ガイドツアーはありますか？',
          'zh': '有导游吗？',
          'tr': 'Rehberli tur var mı?',
          'ru': 'Есть ли экскурсия с гидом?',
        },
        pronunciation: 'Is there a guided tour?',
      ),

      // الحدائق والمنتزهات
      TourismPhrase(
        category: TourismCategories.attractions,
        subcategory: 'الحدائق والمنتزهات',
        translations: {
          'ar': 'أين أقرب حديقة؟',
          'en': 'Where is the nearest park?',
          'fr': 'Où est le parc le plus proche?',
          'de': 'Wo ist der nächste Park?',
          'es': '¿Dónde está el parque más cercano?',
          'it': 'Dove è il parco più vicino?',
          'ja': '一番近い公園はどこですか？',
          'zh': '最近的公园在哪里？',
          'tr': 'En yakın park nerede?',
          'ru': 'Где ближайший парк?',
        },
        pronunciation: 'Where is the nearest park?',
      ),
      TourismPhrase(
        category: TourismCategories.attractions,
        subcategory: 'الحدائق والمنتزهات',
        translations: {
          'ar': 'هل الحديقة مفتوحة؟',
          'en': 'Is the park open?',
          'fr': 'Le parc est-il ouvert?',
          'de': 'Ist der Park geöffnet?',
          'es': '¿Está abierto el parque?',
          'it': 'Il parco è aperto?',
          'ja': '公園は開いていますか？',
          'zh': '公园开放吗？',
          'tr': 'Park açık mı?',
          'ru': 'Парк открыт?',
        },
        pronunciation: 'Is the park open?',
      ),
      TourismPhrase(
        category: TourismCategories.attractions,
        subcategory: 'الحدائق والمنتزهات',
        translations: {
          'ar': 'هل يمكنني التنزه هنا؟',
          'en': 'Can I walk here?',
          'fr': 'Puis-je me promener ici?',
          'de': 'Kann ich hier spazieren gehen?',
          'es': '¿Puedo caminar aquí?',
          'it': 'Posso camminare qui?',
          'ja': 'ここを歩けますか？',
          'zh': '我可以在这里散步吗？',
          'tr': 'Burada yürüyebilir miyim?',
          'ru': 'Могу ли я здесь гулять?',
        },
        pronunciation: 'Can I walk here?',
      ),

      // المعالم التاريخية
      TourismPhrase(
        category: TourismCategories.attractions,
        subcategory: 'المعالم التاريخية',
        translations: {
          'ar': 'ما عمر هذا المبنى؟',
          'en': 'How old is this building?',
          'fr': 'Quel âge a ce bâtiment?',
          'de': 'Wie alt ist dieses Gebäude?',
          'es': '¿Qué edad tiene este edificio?',
          'it': 'Quanti anni ha questo edificio?',
          'ja': 'この建物は何年前のものですか？',
          'zh': '这座建筑有多少年历史？',
          'tr': 'Bu bina kaç yaşında?',
          'ru': 'Сколько лет этому зданию?',
        },
        pronunciation: 'How old is this building?',
      ),
      TourismPhrase(
        category: TourismCategories.attractions,
        subcategory: 'المعالم التاريخية',
        translations: {
          'ar': 'هل يمكنني التقاط صور؟',
          'en': 'Can I take photos?',
          'fr': 'Puis-je prendre des photos?',
          'de': 'Kann ich Fotos machen?',
          'es': '¿Puedo tomar fotos?',
          'it': 'Posso fare foto?',
          'ja': '写真を撮ってもいいですか？',
          'zh': '我可以拍照吗？',
          'tr': 'Fotoğraf çekebilir miyim?',
          'ru': 'Могу ли я фотографировать?',
        },
        pronunciation: 'Can I take photos?',
      ),
      TourismPhrase(
        category: TourismCategories.attractions,
        subcategory: 'المعالم التاريخية',
        translations: {
          'ar': 'ما قصة هذا المكان؟',
          'en': 'What is the story of this place?',
          'fr': 'Quelle est l\'histoire de cet endroit?',
          'de': 'Was ist die Geschichte dieses Ortes?',
          'es': '¿Cuál es la historia de este lugar?',
          'it': 'Qual è la storia di questo posto?',
          'ja': 'この場所の歴史は何ですか？',
          'zh': '这个地方的历史是什么？',
          'tr': 'Bu yerin hikayesi nedir?',
          'ru': 'Какова история этого места?',
        },
        pronunciation: 'What is the story of this place?',
      ),

      // الأنشطة والفعاليات
      TourismPhrase(
        category: TourismCategories.attractions,
        subcategory: 'الأنشطة والفعاليات',
        translations: {
          'ar': 'ما الأنشطة المتاحة؟',
          'en': 'What activities are available?',
          'fr': 'Quelles activités sont disponibles?',
          'de': 'Welche Aktivitäten sind verfügbar?',
          'es': '¿Qué actividades están disponibles?',
          'it': 'Quali attività sono disponibili?',
          'ja': 'どんな活動ができますか？',
          'zh': '有什么活动？',
          'tr': 'Hangi aktiviteler mevcut?',
          'ru': 'Какие мероприятия доступны?',
        },
        pronunciation: 'What activities are available?',
      ),
      TourismPhrase(
        category: TourismCategories.attractions,
        subcategory: 'الأنشطة والفعاليات',
        translations: {
          'ar': 'هل يوجد عروض اليوم؟',
          'en': 'Are there shows today?',
          'fr': 'Y a-t-il des spectacles aujourd\'hui?',
          'de': 'Gibt es heute Shows?',
          'es': '¿Hay espectáculos hoy?',
          'it': 'Ci sono spettacoli oggi?',
          'ja': '今日ショーはありますか？',
          'zh': '今天有表演吗？',
          'tr': 'Bugün gösteri var mı?',
          'ru': 'Есть ли сегодня представления?',
        },
        pronunciation: 'Are there shows today?',
      ),
      TourismPhrase(
        category: TourismCategories.attractions,
        subcategory: 'الأنشطة والفعاليات',
        translations: {
          'ar': 'متى العرض التالي؟',
          'en': 'When is the next show?',
          'fr': 'Quand est le prochain spectacle?',
          'de': 'Wann ist die nächste Show?',
          'es': '¿Cuándo es el próximo espectáculo?',
          'it': 'Quando è il prossimo spettacolo?',
          'ja': '次のショーはいつですか？',
          'zh': '下一场表演是什么时候？',
          'tr': 'Bir sonraki gösteri ne zaman?',
          'ru': 'Когда следующее представление?',
        },
        pronunciation: 'When is the next show?',
      ),

      // المرافق والخدمات
      TourismPhrase(
        category: TourismCategories.attractions,
        subcategory: 'المرافق والخدمات',
        translations: {
          'ar': 'أين دورة المياه؟',
          'en': 'Where is the restroom?',
          'fr': 'Où sont les toilettes?',
          'de': 'Wo ist die Toilette?',
          'es': '¿Dónde está el baño?',
          'it': 'Dove è il bagno?',
          'ja': 'トイレはどこですか？',
          'zh': '洗手间在哪里？',
          'tr': 'Tuvalet nerede?',
          'ru': 'Где туалет?',
        },
        pronunciation: 'Where is the restroom?',
      ),
      TourismPhrase(
        category: TourismCategories.attractions,
        subcategory: 'المرافق والخدمات',
        translations: {
          'ar': 'هل يوجد مقهى هنا؟',
          'en': 'Is there a cafe here?',
          'fr': 'Y a-t-il un café ici?',
          'de': 'Gibt es hier ein Café?',
          'es': '¿Hay una cafetería aquí?',
          'it': 'C\'è un caffè qui?',
          'ja': 'ここにカフェはありますか？',
          'zh': '这里有咖啡厅吗？',
          'tr': 'Burada kafe var mı?',
          'ru': 'Есть ли здесь кафе?',
        },
        pronunciation: 'Is there a cafe here?',
      ),
    ];
  }

  /// عبارات عامة (30 عبارة)
  List<TourismPhrase> _generateGeneralPhrases() {
    return [
      // التحيات والمجاملات
      TourismPhrase(
        category: TourismCategories.general,
        subcategory: 'التحيات والمجاملات',
        translations: {
          'ar': 'مرحبا',
          'en': 'Hello',
          'fr': 'Bonjour',
          'de': 'Hallo',
          'es': 'Hola',
          'it': 'Ciao',
          'ja': 'こんにちは',
          'zh': '你好',
          'tr': 'Merhaba',
          'ru': 'Привет',
        },
        pronunciation: 'Hello',
      ),
      TourismPhrase(
        category: TourismCategories.general,
        subcategory: 'التحيات والمجاملات',
        translations: {
          'ar': 'صباح الخير',
          'en': 'Good morning',
          'fr': 'Bonjour',
          'de': 'Guten Morgen',
          'es': 'Buenos días',
          'it': 'Buongiorno',
          'ja': 'おはようございます',
          'zh': '早上好',
          'tr': 'Günaydın',
          'ru': 'Доброе утро',
        },
        pronunciation: 'Good morning',
      ),
      TourismPhrase(
        category: TourismCategories.general,
        subcategory: 'التحيات والمجاملات',
        translations: {
          'ar': 'مساء الخير',
          'en': 'Good evening',
          'fr': 'Bonsoir',
          'de': 'Guten Abend',
          'es': 'Buenas tardes',
          'it': 'Buonasera',
          'ja': 'こんばんは',
          'zh': '晚上好',
          'tr': 'İyi akşamlar',
          'ru': 'Добрый вечер',
        },
        pronunciation: 'Good evening',
      ),
      TourismPhrase(
        category: TourismCategories.general,
        subcategory: 'التحيات والمجاملات',
        translations: {
          'ar': 'تصبح على خير',
          'en': 'Good night',
          'fr': 'Bonne nuit',
          'de': 'Gute Nacht',
          'es': 'Buenas noches',
          'it': 'Buonanotte',
          'ja': 'おやすみなさい',
          'zh': '晚安',
          'tr': 'İyi geceler',
          'ru': 'Спокойной ночи',
        },
        pronunciation: 'Good night',
      ),
      TourismPhrase(
        category: TourismCategories.general,
        subcategory: 'التحيات والمجاملات',
        translations: {
          'ar': 'مع السلامة',
          'en': 'Goodbye',
          'fr': 'Au revoir',
          'de': 'Auf Wiedersehen',
          'es': 'Adiós',
          'it': 'Arrivederci',
          'ja': 'さようなら',
          'zh': '再见',
          'tr': 'Hoşça kalın',
          'ru': 'До свидания',
        },
        pronunciation: 'Goodbye',
      ),

      // الشكر والاعتذار
      TourismPhrase(
        category: TourismCategories.general,
        subcategory: 'الشكر والاعتذار',
        translations: {
          'ar': 'شكرا لك',
          'en': 'Thank you',
          'fr': 'Merci',
          'de': 'Danke',
          'es': 'Gracias',
          'it': 'Grazie',
          'ja': 'ありがとう',
          'zh': '谢谢',
          'tr': 'Teşekkürler',
          'ru': 'Спасибо',
        },
        pronunciation: 'Thank you',
      ),
      TourismPhrase(
        category: TourismCategories.general,
        subcategory: 'الشكر والاعتذار',
        translations: {
          'ar': 'شكرا جزيلا',
          'en': 'Thank you very much',
          'fr': 'Merci beaucoup',
          'de': 'Vielen Dank',
          'es': 'Muchas gracias',
          'it': 'Grazie mille',
          'ja': 'どうもありがとうございます',
          'zh': '非常感谢',
          'tr': 'Çok teşekkürler',
          'ru': 'Большое спасибо',
        },
        pronunciation: 'Thank you very much',
      ),
      TourismPhrase(
        category: TourismCategories.general,
        subcategory: 'الشكر والاعتذار',
        translations: {
          'ar': 'عذرا',
          'en': 'Excuse me',
          'fr': 'Excusez-moi',
          'de': 'Entschuldigung',
          'es': 'Disculpe',
          'it': 'Scusi',
          'ja': 'すみません',
          'zh': '对不起',
          'tr': 'Affedersiniz',
          'ru': 'Извините',
        },
        pronunciation: 'Excuse me',
      ),
      TourismPhrase(
        category: TourismCategories.general,
        subcategory: 'الشكر والاعتذار',
        translations: {
          'ar': 'أنا آسف',
          'en': 'I am sorry',
          'fr': 'Je suis désolé',
          'de': 'Es tut mir leid',
          'es': 'Lo siento',
          'it': 'Mi dispiace',
          'ja': 'ごめんなさい',
          'zh': '我很抱歉',
          'tr': 'Özür dilerim',
          'ru': 'Мне жаль',
        },
        pronunciation: 'I am sorry',
      ),
      TourismPhrase(
        category: TourismCategories.general,
        subcategory: 'الشكر والاعتذار',
        translations: {
          'ar': 'لا شكر على واجب',
          'en': 'You\'re welcome',
          'fr': 'De rien',
          'de': 'Bitte schön',
          'es': 'De nada',
          'it': 'Prego',
          'ja': 'どういたしまして',
          'zh': '不客气',
          'tr': 'Rica ederim',
          'ru': 'Пожалуйста',
        },
        pronunciation: 'You\'re welcome',
      ),

      // التواصل واللغة
      TourismPhrase(
        category: TourismCategories.general,
        subcategory: 'التواصل واللغة',
        translations: {
          'ar': 'هل تتحدث الإنجليزية؟',
          'en': 'Do you speak English?',
          'fr': 'Parlez-vous anglais?',
          'de': 'Sprechen Sie Englisch?',
          'es': '¿Habla inglés?',
          'it': 'Parla inglese?',
          'ja': '英語を話しますか？',
          'zh': '你会说英语吗？',
          'tr': 'İngilizce konuşuyor musunuz?',
          'ru': 'Вы говорите по-английски?',
        },
        pronunciation: 'Do you speak English?',
      ),
      TourismPhrase(
        category: TourismCategories.general,
        subcategory: 'التواصل واللغة',
        translations: {
          'ar': 'أنا لا أتحدث العربية جيداً',
          'en': 'I don\'t speak Arabic well',
          'fr': 'Je ne parle pas bien arabe',
          'de': 'Ich spreche nicht gut Arabisch',
          'es': 'No hablo bien árabe',
          'it': 'Non parlo bene l\'arabo',
          'ja': 'アラビア語があまり話せません',
          'zh': '我阿拉伯语说得不好',
          'tr': 'Arapça iyi konuşamıyorum',
          'ru': 'Я плохо говорю по-арабски',
        },
        pronunciation: 'I don\'t speak Arabic well',
      ),
      TourismPhrase(
        category: TourismCategories.general,
        subcategory: 'التواصل واللغة',
        translations: {
          'ar': 'هل يمكنك مساعدتي؟',
          'en': 'Can you help me?',
          'fr': 'Pouvez-vous m\'aider?',
          'de': 'Können Sie mir helfen?',
          'es': '¿Puede ayudarme?',
          'it': 'Può aiutarmi?',
          'ja': '手伝ってもらえますか？',
          'zh': '你能帮助我吗？',
          'tr': 'Bana yardım edebilir misiniz?',
          'ru': 'Можете ли вы мне помочь?',
        },
        pronunciation: 'Can you help me?',
      ),
      TourismPhrase(
        category: TourismCategories.general,
        subcategory: 'التواصل واللغة',
        translations: {
          'ar': 'لا أفهم',
          'en': 'I don\'t understand',
          'fr': 'Je ne comprends pas',
          'de': 'Ich verstehe nicht',
          'es': 'No entiendo',
          'it': 'Non capisco',
          'ja': 'わかりません',
          'zh': '我不明白',
          'tr': 'Anlamıyorum',
          'ru': 'Я не понимаю',
        },
        pronunciation: 'I don\'t understand',
      ),
      TourismPhrase(
        category: TourismCategories.general,
        subcategory: 'التواصل واللغة',
        translations: {
          'ar': 'هل يمكنك التكرار؟',
          'en': 'Can you repeat?',
          'fr': 'Pouvez-vous répéter?',
          'de': 'Können Sie das wiederholen?',
          'es': '¿Puede repetir?',
          'it': 'Può ripetere?',
          'ja': 'もう一度言ってもらえますか？',
          'zh': '你能重复一遍吗？',
          'tr': 'Tekrar edebilir misiniz?',
          'ru': 'Можете повторить?',
        },
        pronunciation: 'Can you repeat?',
      ),

      // الأساسيات والضروريات
      TourismPhrase(
        category: TourismCategories.general,
        subcategory: 'الأساسيات',
        translations: {
          'ar': 'نعم',
          'en': 'Yes',
          'fr': 'Oui',
          'de': 'Ja',
          'es': 'Sí',
          'it': 'Sì',
          'ja': 'はい',
          'zh': '是的',
          'tr': 'Evet',
          'ru': 'Да',
        },
        pronunciation: 'Yes',
      ),
      TourismPhrase(
        category: TourismCategories.general,
        subcategory: 'الأساسيات',
        translations: {
          'ar': 'لا',
          'en': 'No',
          'fr': 'Non',
          'de': 'Nein',
          'es': 'No',
          'it': 'No',
          'ja': 'いいえ',
          'zh': '不',
          'tr': 'Hayır',
          'ru': 'Нет',
        },
        pronunciation: 'No',
      ),
      TourismPhrase(
        category: TourismCategories.general,
        subcategory: 'الأساسيات',
        translations: {
          'ar': 'من فضلك',
          'en': 'Please',
          'fr': 'S\'il vous plaît',
          'de': 'Bitte',
          'es': 'Por favor',
          'it': 'Per favore',
          'ja': 'お願いします',
          'zh': '请',
          'tr': 'Lütfen',
          'ru': 'Пожалуйста',
        },
        pronunciation: 'Please',
      ),
      TourismPhrase(
        category: TourismCategories.general,
        subcategory: 'الأساسيات',
        translations: {
          'ar': 'أين؟',
          'en': 'Where?',
          'fr': 'Où?',
          'de': 'Wo?',
          'es': '¿Dónde?',
          'it': 'Dove?',
          'ja': 'どこ？',
          'zh': '哪里？',
          'tr': 'Nerede?',
          'ru': 'Где?',
        },
        pronunciation: 'Where?',
      ),
      TourismPhrase(
        category: TourismCategories.general,
        subcategory: 'الأساسيات',
        translations: {
          'ar': 'متى؟',
          'en': 'When?',
          'fr': 'Quand?',
          'de': 'Wann?',
          'es': '¿Cuándo?',
          'it': 'Quando?',
          'ja': 'いつ？',
          'zh': '什么时候？',
          'tr': 'Ne zaman?',
          'ru': 'Когда?',
        },
        pronunciation: 'When?',
      ),

      // الوقت والتاريخ
      TourismPhrase(
        category: TourismCategories.general,
        subcategory: 'الوقت والتاريخ',
        translations: {
          'ar': 'كم الساعة؟',
          'en': 'What time is it?',
          'fr': 'Quelle heure est-il?',
          'de': 'Wie spät ist es?',
          'es': '¿Qué hora es?',
          'it': 'Che ore sono?',
          'ja': '何時ですか？',
          'zh': '几点了？',
          'tr': 'Saat kaç?',
          'ru': 'Который час?',
        },
        pronunciation: 'What time is it?',
      ),
      TourismPhrase(
        category: TourismCategories.general,
        subcategory: 'الوقت والتاريخ',
        translations: {
          'ar': 'اليوم',
          'en': 'Today',
          'fr': 'Aujourd\'hui',
          'de': 'Heute',
          'es': 'Hoy',
          'it': 'Oggi',
          'ja': '今日',
          'zh': '今天',
          'tr': 'Bugün',
          'ru': 'Сегодня',
        },
        pronunciation: 'Today',
      ),
      TourismPhrase(
        category: TourismCategories.general,
        subcategory: 'الوقت والتاريخ',
        translations: {
          'ar': 'غداً',
          'en': 'Tomorrow',
          'fr': 'Demain',
          'de': 'Morgen',
          'es': 'Mañana',
          'it': 'Domani',
          'ja': '明日',
          'zh': '明天',
          'tr': 'Yarın',
          'ru': 'Завтра',
        },
        pronunciation: 'Tomorrow',
      ),
      TourismPhrase(
        category: TourismCategories.general,
        subcategory: 'الوقت والتاريخ',
        translations: {
          'ar': 'أمس',
          'en': 'Yesterday',
          'fr': 'Hier',
          'de': 'Gestern',
          'es': 'Ayer',
          'it': 'Ieri',
          'ja': '昨日',
          'zh': '昨天',
          'tr': 'Dün',
          'ru': 'Вчера',
        },
        pronunciation: 'Yesterday',
      ),

      // الأرقام الأساسية
      TourismPhrase(
        category: TourismCategories.general,
        subcategory: 'الأرقام',
        translations: {
          'ar': 'واحد',
          'en': 'One',
          'fr': 'Un',
          'de': 'Eins',
          'es': 'Uno',
          'it': 'Uno',
          'ja': '一',
          'zh': '一',
          'tr': 'Bir',
          'ru': 'Один',
        },
        pronunciation: 'One',
      ),
      TourismPhrase(
        category: TourismCategories.general,
        subcategory: 'الأرقام',
        translations: {
          'ar': 'اثنان',
          'en': 'Two',
          'fr': 'Deux',
          'de': 'Zwei',
          'es': 'Dos',
          'it': 'Due',
          'ja': '二',
          'zh': '二',
          'tr': 'İki',
          'ru': 'Два',
        },
        pronunciation: 'Two',
      ),
      TourismPhrase(
        category: TourismCategories.general,
        subcategory: 'الأرقام',
        translations: {
          'ar': 'ثلاثة',
          'en': 'Three',
          'fr': 'Trois',
          'de': 'Drei',
          'es': 'Tres',
          'it': 'Tre',
          'ja': '三',
          'zh': '三',
          'tr': 'Üç',
          'ru': 'Три',
        },
        pronunciation: 'Three',
      ),

      // المشاعر والحالة
      TourismPhrase(
        category: TourismCategories.general,
        subcategory: 'المشاعر والحالة',
        translations: {
          'ar': 'أنا بخير',
          'en': 'I am fine',
          'fr': 'Je vais bien',
          'de': 'Mir geht es gut',
          'es': 'Estoy bien',
          'it': 'Sto bene',
          'ja': '元気です',
          'zh': '我很好',
          'tr': 'İyiyim',
          'ru': 'Я в порядке',
        },
        pronunciation: 'I am fine',
      ),
      TourismPhrase(
        category: TourismCategories.general,
        subcategory: 'المشاعر والحالة',
        translations: {
          'ar': 'كيف حالك؟',
          'en': 'How are you?',
          'fr': 'Comment allez-vous?',
          'de': 'Wie geht es Ihnen?',
          'es': '¿Cómo está?',
          'it': 'Come sta?',
          'ja': 'お元気ですか？',
          'zh': '你好吗？',
          'tr': 'Nasılsınız?',
          'ru': 'Как дела?',
        },
        pronunciation: 'How are you?',
      ),
      TourismPhrase(
        category: TourismCategories.general,
        subcategory: 'المشاعر والحالة',
        translations: {
          'ar': 'أنا سعيد',
          'en': 'I am happy',
          'fr': 'Je suis heureux',
          'de': 'Ich bin glücklich',
          'es': 'Estoy feliz',
          'it': 'Sono felice',
          'ja': '嬉しいです',
          'zh': '我很高兴',
          'tr': 'Mutluyum',
          'ru': 'Я счастлив',
        },
        pronunciation: 'I am happy',
      ),
    ];
  }

  /// عبارات الملابس والأزياء (25 عبارة)
  List<TourismPhrase> _generateClothingPhrases() {
    return [
      // الأسعار والمساومة
      TourismPhrase(
        category: TourismCategories.clothing,
        subcategory: 'الأسعار والمساومة',
        translations: {
          'ar': 'كم سعر هذا القميص؟',
          'en': 'How much is this shirt?',
          'fr': 'Combien coûte cette chemise?',
          'de': 'Wie viel kostet dieses Hemd?',
          'es': '¿Cuánto cuesta esta camisa?',
          'it': 'Quanto costa questa camicia?',
          'ja': 'このシャツはいくらですか？',
          'zh': '这件衬衫多少钱？',
          'tr': 'Bu gömlek ne kadar?',
          'ru': 'Сколько стоит эта рубашка?',
        },
        pronunciation: 'How much is this shirt?',
      ),
      TourismPhrase(
        category: TourismCategories.clothing,
        subcategory: 'الأسعار والمساومة',
        translations: {
          'ar': 'هل يمكنك تخفيض السعر؟',
          'en': 'Can you reduce the price?',
          'fr': 'Pouvez-vous baisser le prix?',
          'de': 'Können Sie den Preis senken?',
          'es': '¿Puede reducir el precio?',
          'it': 'Può ridurre il prezzo?',
          'ja': '値段を下げてもらえますか？',
          'zh': '你能降价吗？',
          'tr': 'Fiyatı düşürebilir misiniz?',
          'ru': 'Можете ли вы снизить цену?',
        },
        pronunciation: 'Can you reduce the price?',
      ),
      TourismPhrase(
        category: TourismCategories.clothing,
        subcategory: 'الأسعار والمساومة',
        translations: {
          'ar': 'هذا غالي جداً',
          'en': 'This is too expensive',
          'fr': 'C\'est trop cher',
          'de': 'Das ist zu teuer',
          'es': 'Esto es muy caro',
          'it': 'È troppo caro',
          'ja': 'これは高すぎます',
          'zh': '这太贵了',
          'tr': 'Bu çok pahalı',
          'ru': 'Это слишком дорого',
        },
        pronunciation: 'This is too expensive',
      ),
      TourismPhrase(
        category: TourismCategories.clothing,
        subcategory: 'الأسعار والمساومة',
        translations: {
          'ar': 'ما هو أفضل سعر؟',
          'en': 'What is your best price?',
          'fr': 'Quel est votre meilleur prix?',
          'de': 'Was ist Ihr bester Preis?',
          'es': '¿Cuál es su mejor precio?',
          'it': 'Qual è il vostro miglior prezzo?',
          'ja': '最安値はいくらですか？',
          'zh': '你们的最低价是多少？',
          'tr': 'En iyi fiyatınız nedir?',
          'ru': 'Какая ваша лучшая цена?',
        },
        pronunciation: 'What is your best price?',
      ),
      TourismPhrase(
        category: TourismCategories.clothing,
        subcategory: 'الأسعار والمساومة',
        translations: {
          'ar': 'هل يوجد خصم؟',
          'en': 'Is there a discount?',
          'fr': 'Y a-t-il une remise?',
          'de': 'Gibt es einen Rabatt?',
          'es': '¿Hay descuento?',
          'it': 'C\'è uno sconto?',
          'ja': '割引はありますか？',
          'zh': '有折扣吗？',
          'tr': 'İndirim var mı?',
          'ru': 'Есть ли скидка?',
        },
        pronunciation: 'Is there a discount?',
      ),

      // الألوان والمقاسات
      TourismPhrase(
        category: TourismCategories.clothing,
        subcategory: 'الألوان والمقاسات',
        translations: {
          'ar': 'هل يوجد هذا باللون الأزرق؟',
          'en': 'Do you have this in blue?',
          'fr': 'Avez-vous ceci en bleu?',
          'de': 'Haben Sie das in Blau?',
          'es': '¿Tienen esto en azul?',
          'it': 'Avete questo in blu?',
          'ja': 'これの青色はありますか？',
          'zh': '你们有蓝色的吗？',
          'tr': 'Bunun mavi rengi var mı?',
          'ru': 'У вас есть это в синем цвете?',
        },
        pronunciation: 'Do you have this in blue?',
      ),
      TourismPhrase(
        category: TourismCategories.clothing,
        subcategory: 'الألوان والمقاسات',
        translations: {
          'ar': 'ما هو مقاسي؟',
          'en': 'What is my size?',
          'fr': 'Quelle est ma taille?',
          'de': 'Was ist meine Größe?',
          'es': '¿Cuál es mi talla?',
          'it': 'Qual è la mia taglia?',
          'ja': '私のサイズは何ですか？',
          'zh': '我的尺码是什么？',
          'tr': 'Benim bedenim nedir?',
          'ru': 'Какой у меня размер?',
        },
        pronunciation: 'What is my size?',
      ),
      TourismPhrase(
        category: TourismCategories.clothing,
        subcategory: 'الألوان والمقاسات',
        translations: {
          'ar': 'أريد مقاس كبير',
          'en': 'I want a large size',
          'fr': 'Je veux une grande taille',
          'de': 'Ich möchte eine große Größe',
          'es': 'Quiero una talla grande',
          'it': 'Voglio una taglia grande',
          'ja': '大きいサイズが欲しいです',
          'zh': '我要大号的',
          'tr': 'Büyük beden istiyorum',
          'ru': 'Я хочу большой размер',
        },
        pronunciation: 'I want a large size',
      ),
      TourismPhrase(
        category: TourismCategories.clothing,
        subcategory: 'الألوان والمقاسات',
        translations: {
          'ar': 'هل يوجد ألوان أخرى؟',
          'en': 'Are there other colors?',
          'fr': 'Y a-t-il d\'autres couleurs?',
          'de': 'Gibt es andere Farben?',
          'es': '¿Hay otros colores?',
          'it': 'Ci sono altri colori?',
          'ja': '他の色はありますか？',
          'zh': '有其他颜色吗？',
          'tr': 'Başka renkler var mı?',
          'ru': 'Есть ли другие цвета?',
        },
        pronunciation: 'Are there other colors?',
      ),
      TourismPhrase(
        category: TourismCategories.clothing,
        subcategory: 'الألوان والمقاسات',
        translations: {
          'ar': 'أريد مقاس صغير',
          'en': 'I want a small size',
          'fr': 'Je veux une petite taille',
          'de': 'Ich möchte eine kleine Größe',
          'es': 'Quiero una talla pequeña',
          'it': 'Voglio una taglia piccola',
          'ja': '小さいサイズが欲しいです',
          'zh': '我要小号的',
          'tr': 'Küçük beden istiyorum',
          'ru': 'Я хочу маленький размер',
        },
        pronunciation: 'I want a small size',
      ),

      // غرف القياس
      TourismPhrase(
        category: TourismCategories.clothing,
        subcategory: 'غرف القياس',
        translations: {
          'ar': 'أين غرفة القياس؟',
          'en': 'Where is the fitting room?',
          'fr': 'Où est la cabine d\'essayage?',
          'de': 'Wo ist die Umkleidekabine?',
          'es': '¿Dónde está el probador?',
          'it': 'Dove è il camerino?',
          'ja': '試着室はどこですか？',
          'zh': '试衣间在哪里？',
          'tr': 'Deneme kabini nerede?',
          'ru': 'Где примерочная?',
        },
        pronunciation: 'Where is the fitting room?',
      ),
      TourismPhrase(
        category: TourismCategories.clothing,
        subcategory: 'غرف القياس',
        translations: {
          'ar': 'هل يمكنني تجربة هذا؟',
          'en': 'Can I try this on?',
          'fr': 'Puis-je essayer ceci?',
          'de': 'Kann ich das anprobieren?',
          'es': '¿Puedo probarme esto?',
          'it': 'Posso provare questo?',
          'ja': 'これを試着できますか？',
          'zh': '我可以试穿这个吗？',
          'tr': 'Bunu deneyebilir miyim?',
          'ru': 'Могу ли я это примерить?',
        },
        pronunciation: 'Can I try this on?',
      ),
      TourismPhrase(
        category: TourismCategories.clothing,
        subcategory: 'غرف القياس',
        translations: {
          'ar': 'هذا لا يناسبني',
          'en': 'This doesn\'t fit me',
          'fr': 'Cela ne me va pas',
          'de': 'Das passt mir nicht',
          'es': 'Esto no me queda bien',
          'it': 'Questo non mi sta bene',
          'ja': 'これは私に合いません',
          'zh': '这个不适合我',
          'tr': 'Bu bana uymuyor',
          'ru': 'Это мне не подходит',
        },
        pronunciation: 'This doesn\'t fit me',
      ),
      TourismPhrase(
        category: TourismCategories.clothing,
        subcategory: 'غرف القياس',
        translations: {
          'ar': 'هذا مناسب تماماً',
          'en': 'This fits perfectly',
          'fr': 'Cela me va parfaitement',
          'de': 'Das passt perfekt',
          'es': 'Esto me queda perfecto',
          'it': 'Questo mi sta perfetto',
          'ja': 'これはぴったりです',
          'zh': '这个很合身',
          'tr': 'Bu mükemmel uyuyor',
          'ru': 'Это идеально подходит',
        },
        pronunciation: 'This fits perfectly',
      ),
      TourismPhrase(
        category: TourismCategories.clothing,
        subcategory: 'غرف القياس',
        translations: {
          'ar': 'أحتاج مقاس أكبر',
          'en': 'I need a bigger size',
          'fr': 'J\'ai besoin d\'une taille plus grande',
          'de': 'Ich brauche eine größere Größe',
          'es': 'Necesito una talla más grande',
          'it': 'Ho bisogno di una taglia più grande',
          'ja': 'もっと大きいサイズが必要です',
          'zh': '我需要更大的尺码',
          'tr': 'Daha büyük bedene ihtiyacım var',
          'ru': 'Мне нужен размер побольше',
        },
        pronunciation: 'I need a bigger size',
      ),

      // الأقمشة والجودة
      TourismPhrase(
        category: TourismCategories.clothing,
        subcategory: 'الأقمشة والجودة',
        translations: {
          'ar': 'ما نوع القماش؟',
          'en': 'What type of fabric is this?',
          'fr': 'Quel type de tissu est-ce?',
          'de': 'Was für ein Stoff ist das?',
          'es': '¿Qué tipo de tela es esta?',
          'it': 'Che tipo di tessuto è questo?',
          'ja': 'これはどんな生地ですか？',
          'zh': '这是什么面料？',
          'tr': 'Bu ne tür kumaş?',
          'ru': 'Какой это тип ткани?',
        },
        pronunciation: 'What type of fabric is this?',
      ),
      TourismPhrase(
        category: TourismCategories.clothing,
        subcategory: 'الأقمشة والجودة',
        translations: {
          'ar': 'هل هذا قطن؟',
          'en': 'Is this cotton?',
          'fr': 'Est-ce du coton?',
          'de': 'Ist das Baumwolle?',
          'es': '¿Esto es algodón?',
          'it': 'È cotone?',
          'ja': 'これはコットンですか？',
          'zh': '这是棉的吗？',
          'tr': 'Bu pamuk mu?',
          'ru': 'Это хлопок?',
        },
        pronunciation: 'Is this cotton?',
      ),
      TourismPhrase(
        category: TourismCategories.clothing,
        subcategory: 'الأقمشة والجودة',
        translations: {
          'ar': 'هل الجودة جيدة؟',
          'en': 'Is the quality good?',
          'fr': 'La qualité est-elle bonne?',
          'de': 'Ist die Qualität gut?',
          'es': '¿Es buena la calidad?',
          'it': 'La qualità è buona?',
          'ja': '品質は良いですか？',
          'zh': '质量好吗？',
          'tr': 'Kalite iyi mi?',
          'ru': 'Качество хорошее?',
        },
        pronunciation: 'Is the quality good?',
      ),
      TourismPhrase(
        category: TourismCategories.clothing,
        subcategory: 'الأقمشة والجودة',
        translations: {
          'ar': 'هل يتقلص عند الغسيل؟',
          'en': 'Does it shrink when washed?',
          'fr': 'Est-ce que ça rétrécit au lavage?',
          'de': 'Läuft es beim Waschen ein?',
          'es': '¿Se encoge al lavarlo?',
          'it': 'Si restringe quando si lava?',
          'ja': '洗濯すると縮みますか？',
          'zh': '洗的时候会缩水吗？',
          'tr': 'Yıkandığında küçülür mü?',
          'ru': 'Садится ли при стирке?',
        },
        pronunciation: 'Does it shrink when washed?',
      ),
      TourismPhrase(
        category: TourismCategories.clothing,
        subcategory: 'الأقمشة والجودة',
        translations: {
          'ar': 'هل هذا مقاوم للماء؟',
          'en': 'Is this waterproof?',
          'fr': 'Est-ce imperméable?',
          'de': 'Ist das wasserdicht?',
          'es': '¿Esto es impermeable?',
          'it': 'È impermeabile?',
          'ja': 'これは防水ですか？',
          'zh': '这个防水吗？',
          'tr': 'Bu su geçirmez mi?',
          'ru': 'Это водонепроницаемое?',
        },
        pronunciation: 'Is this waterproof?',
      ),

      // الدفع والضمان
      TourismPhrase(
        category: TourismCategories.clothing,
        subcategory: 'الدفع والضمان',
        translations: {
          'ar': 'هل يمكنني إرجاع هذا؟',
          'en': 'Can I return this?',
          'fr': 'Puis-je retourner ceci?',
          'de': 'Kann ich das zurückgeben?',
          'es': '¿Puedo devolver esto?',
          'it': 'Posso restituire questo?',
          'ja': 'これを返品できますか？',
          'zh': '我可以退货吗？',
          'tr': 'Bunu iade edebilir miyim?',
          'ru': 'Могу ли я это вернуть?',
        },
        pronunciation: 'Can I return this?',
      ),
      TourismPhrase(
        category: TourismCategories.clothing,
        subcategory: 'الدفع والضمان',
        translations: {
          'ar': 'كم مدة الضمان؟',
          'en': 'How long is the warranty?',
          'fr': 'Combien de temps dure la garantie?',
          'de': 'Wie lange ist die Garantie?',
          'es': '¿Cuánto dura la garantía?',
          'it': 'Quanto dura la garanzia?',
          'ja': '保証期間はどのくらいですか？',
          'zh': '保修期多长？',
          'tr': 'Garanti süresi ne kadar?',
          'ru': 'Какой срок гарантии?',
        },
        pronunciation: 'How long is the warranty?',
      ),
      TourismPhrase(
        category: TourismCategories.clothing,
        subcategory: 'الدفع والضمان',
        translations: {
          'ar': 'هل تقبلون البطاقة الائتمانية؟',
          'en': 'Do you accept credit cards?',
          'fr': 'Acceptez-vous les cartes de crédit?',
          'de': 'Nehmen Sie Kreditkarten?',
          'es': '¿Aceptan tarjetas de crédito?',
          'it': 'Accettate carte di credito?',
          'ja': 'クレジットカードは使えますか？',
          'zh': '你们接受信用卡吗？',
          'tr': 'Kredi kartı kabul ediyor musunuz?',
          'ru': 'Вы принимаете кредитные карты?',
        },
        pronunciation: 'Do you accept credit cards?',
      ),
      TourismPhrase(
        category: TourismCategories.clothing,
        subcategory: 'الدفع والضمان',
        translations: {
          'ar': 'هل يمكنني الحصول على إيصال؟',
          'en': 'Can I get a receipt?',
          'fr': 'Puis-je avoir un reçu?',
          'de': 'Kann ich eine Quittung bekommen?',
          'es': '¿Puedo obtener un recibo?',
          'it': 'Posso avere una ricevuta?',
          'ja': 'レシートをもらえますか？',
          'zh': '我可以要收据吗？',
          'tr': 'Fiş alabilir miyim?',
          'ru': 'Могу ли я получить чек?',
        },
        pronunciation: 'Can I get a receipt?',
      ),
      TourismPhrase(
        category: TourismCategories.clothing,
        subcategory: 'الدفع والضمان',
        translations: {
          'ar': 'سآخذ هذا',
          'en': 'I\'ll take this',
          'fr': 'Je vais prendre ceci',
          'de': 'Ich nehme das',
          'es': 'Me llevo esto',
          'it': 'Prendo questo',
          'ja': 'これにします',
          'zh': '我要这个',
          'tr': 'Bunu alacağım',
          'ru': 'Я возьму это',
        },
        pronunciation: 'I\'ll take this',
      ),
    ];
  }

  /// عبارات أنواع الطعام والمشروبات (25 عبارة)
  List<TourismPhrase> _generateFoodPhrases() {
    return [
      // اللحوم والدواجن
      TourismPhrase(
        category: TourismCategories.food,
        subcategory: 'اللحوم والدواجن',
        translations: {
          'ar': 'أريد لحم بقر',
          'en': 'I want beef',
          'fr': 'Je veux du bœuf',
          'de': 'Ich möchte Rindfleisch',
          'es': 'Quiero carne de res',
          'it': 'Voglio manzo',
          'ja': '牛肉が欲しいです',
          'zh': '我要牛肉',
          'tr': 'Sığır eti istiyorum',
          'ru': 'Я хочу говядину',
        },
        pronunciation: 'I want beef',
      ),
      TourismPhrase(
        category: TourismCategories.food,
        subcategory: 'اللحوم والدواجن',
        translations: {
          'ar': 'هل لديكم دجاج؟',
          'en': 'Do you have chicken?',
          'fr': 'Avez-vous du poulet?',
          'de': 'Haben Sie Hähnchen?',
          'es': '¿Tienen pollo?',
          'it': 'Avete pollo?',
          'ja': 'チキンはありますか？',
          'zh': '你们有鸡肉吗？',
          'tr': 'Tavuğunuz var mı?',
          'ru': 'У вас есть курица?',
        },
        pronunciation: 'Do you have chicken?',
      ),
      TourismPhrase(
        category: TourismCategories.food,
        subcategory: 'اللحوم والدواجن',
        translations: {
          'ar': 'أريد سمك طازج',
          'en': 'I want fresh fish',
          'fr': 'Je veux du poisson frais',
          'de': 'Ich möchte frischen Fisch',
          'es': 'Quiero pescado fresco',
          'it': 'Voglio pesce fresco',
          'ja': '新鮮な魚が欲しいです',
          'zh': '我要新鲜的鱼',
          'tr': 'Taze balık istiyorum',
          'ru': 'Я хочу свежую рыбу',
        },
        pronunciation: 'I want fresh fish',
      ),
      TourismPhrase(
        category: TourismCategories.food,
        subcategory: 'اللحوم والدواجن',
        translations: {
          'ar': 'هل هذا اللحم حلال؟',
          'en': 'Is this meat halal?',
          'fr': 'Cette viande est-elle halal?',
          'de': 'Ist dieses Fleisch halal?',
          'es': '¿Esta carne es halal?',
          'it': 'Questa carne è halal?',
          'ja': 'この肉はハラルですか？',
          'zh': '这肉是清真的吗？',
          'tr': 'Bu et helal mi?',
          'ru': 'Это мясо халяль?',
        },
        pronunciation: 'Is this meat halal?',
      ),
      TourismPhrase(
        category: TourismCategories.food,
        subcategory: 'اللحوم والدواجن',
        translations: {
          'ar': 'لا آكل لحم الخنزير',
          'en': 'I don\'t eat pork',
          'fr': 'Je ne mange pas de porc',
          'de': 'Ich esse kein Schweinefleisch',
          'es': 'No como cerdo',
          'it': 'Non mangio maiale',
          'ja': '豚肉は食べません',
          'zh': '我不吃猪肉',
          'tr': 'Domuz eti yemem',
          'ru': 'Я не ем свинину',
        },
        pronunciation: 'I don\'t eat pork',
      ),

      // الخضروات والفواكه
      TourismPhrase(
        category: TourismCategories.food,
        subcategory: 'الخضروات والفواكه',
        translations: {
          'ar': 'أريد خضروات طازجة',
          'en': 'I want fresh vegetables',
          'fr': 'Je veux des légumes frais',
          'de': 'Ich möchte frisches Gemüse',
          'es': 'Quiero verduras frescas',
          'it': 'Voglio verdure fresche',
          'ja': '新鮮な野菜が欲しいです',
          'zh': '我要新鲜蔬菜',
          'tr': 'Taze sebze istiyorum',
          'ru': 'Я хочу свежие овощи',
        },
        pronunciation: 'I want fresh vegetables',
      ),
      TourismPhrase(
        category: TourismCategories.food,
        subcategory: 'الخضروات والفواكه',
        translations: {
          'ar': 'هل لديكم فواكه موسمية؟',
          'en': 'Do you have seasonal fruits?',
          'fr': 'Avez-vous des fruits de saison?',
          'de': 'Haben Sie saisonale Früchte?',
          'es': '¿Tienen frutas de temporada?',
          'it': 'Avete frutta di stagione?',
          'ja': '季節の果物はありますか？',
          'zh': '你们有时令水果吗？',
          'tr': 'Mevsim meyveleriniz var mı?',
          'ru': 'У вас есть сезонные фрукты?',
        },
        pronunciation: 'Do you have seasonal fruits?',
      ),
      TourismPhrase(
        category: TourismCategories.food,
        subcategory: 'الخضروات والفواكه',
        translations: {
          'ar': 'أريد سلطة خضراء',
          'en': 'I want a green salad',
          'fr': 'Je veux une salade verte',
          'de': 'Ich möchte einen grünen Salat',
          'es': 'Quiero una ensalada verde',
          'it': 'Voglio un\'insalata verde',
          'ja': 'グリーンサラダが欲しいです',
          'zh': '我要绿色沙拉',
          'tr': 'Yeşil salata istiyorum',
          'ru': 'Я хочу зеленый салат',
        },
        pronunciation: 'I want a green salad',
      ),
      TourismPhrase(
        category: TourismCategories.food,
        subcategory: 'الخضروات والفواكه',
        translations: {
          'ar': 'هل هذه الفواكه عضوية؟',
          'en': 'Are these fruits organic?',
          'fr': 'Ces fruits sont-ils biologiques?',
          'de': 'Sind diese Früchte biologisch?',
          'es': '¿Estas frutas son orgánicas?',
          'it': 'Questi frutti sono biologici?',
          'ja': 'この果物はオーガニックですか？',
          'zh': '这些水果是有机的吗？',
          'tr': 'Bu meyveler organik mi?',
          'ru': 'Эти фрукты органические?',
        },
        pronunciation: 'Are these fruits organic?',
      ),
      TourismPhrase(
        category: TourismCategories.food,
        subcategory: 'الخضروات والفواكه',
        translations: {
          'ar': 'كم سعر الكيلو؟',
          'en': 'How much per kilogram?',
          'fr': 'Combien le kilogramme?',
          'de': 'Wie viel pro Kilogramm?',
          'es': '¿Cuánto por kilogramo?',
          'it': 'Quanto al chilogrammo?',
          'ja': '1キロいくらですか？',
          'zh': '一公斤多少钱？',
          'tr': 'Kilosu ne kadar?',
          'ru': 'Сколько за килограмм?',
        },
        pronunciation: 'How much per kilogram?',
      ),

      // المشروبات
      TourismPhrase(
        category: TourismCategories.food,
        subcategory: 'المشروبات',
        translations: {
          'ar': 'أريد قهوة عربية',
          'en': 'I want Arabic coffee',
          'fr': 'Je veux du café arabe',
          'de': 'Ich möchte arabischen Kaffee',
          'es': 'Quiero café árabe',
          'it': 'Voglio caffè arabo',
          'ja': 'アラビアコーヒーが欲しいです',
          'zh': '我要阿拉伯咖啡',
          'tr': 'Arap kahvesi istiyorum',
          'ru': 'Я хочу арабский кофе',
        },
        pronunciation: 'I want Arabic coffee',
      ),
      TourismPhrase(
        category: TourismCategories.food,
        subcategory: 'المشروبات',
        translations: {
          'ar': 'هل لديكم شاي أخضر؟',
          'en': 'Do you have green tea?',
          'fr': 'Avez-vous du thé vert?',
          'de': 'Haben Sie grünen Tee?',
          'es': '¿Tienen té verde?',
          'it': 'Avete tè verde?',
          'ja': '緑茶はありますか？',
          'zh': '你们有绿茶吗？',
          'tr': 'Yeşil çayınız var mı?',
          'ru': 'У вас есть зеленый чай?',
        },
        pronunciation: 'Do you have green tea?',
      ),
      TourismPhrase(
        category: TourismCategories.food,
        subcategory: 'المشروبات',
        translations: {
          'ar': 'أريد عصير طازج',
          'en': 'I want fresh juice',
          'fr': 'Je veux du jus frais',
          'de': 'Ich möchte frischen Saft',
          'es': 'Quiero jugo fresco',
          'it': 'Voglio succo fresco',
          'ja': 'フレッシュジュースが欲しいです',
          'zh': '我要新鲜果汁',
          'tr': 'Taze meyve suyu istiyorum',
          'ru': 'Я хочу свежий сок',
        },
        pronunciation: 'I want fresh juice',
      ),
      TourismPhrase(
        category: TourismCategories.food,
        subcategory: 'المشروبات',
        translations: {
          'ar': 'هل هذا بدون كحول؟',
          'en': 'Is this alcohol-free?',
          'fr': 'Est-ce sans alcool?',
          'de': 'Ist das alkoholfrei?',
          'es': '¿Esto es sin alcohol?',
          'it': 'È senza alcol?',
          'ja': 'これはアルコールフリーですか？',
          'zh': '这个不含酒精吗？',
          'tr': 'Bu alkolsüz mü?',
          'ru': 'Это безалкогольное?',
        },
        pronunciation: 'Is this alcohol-free?',
      ),
      TourismPhrase(
        category: TourismCategories.food,
        subcategory: 'المشروبات',
        translations: {
          'ar': 'أريد ماء معدني',
          'en': 'I want mineral water',
          'fr': 'Je veux de l\'eau minérale',
          'de': 'Ich möchte Mineralwasser',
          'es': 'Quiero agua mineral',
          'it': 'Voglio acqua minerale',
          'ja': 'ミネラルウォーターが欲しいです',
          'zh': '我要矿泉水',
          'tr': 'Maden suyu istiyorum',
          'ru': 'Я хочу минеральную воду',
        },
        pronunciation: 'I want mineral water',
      ),

      // الحلويات
      TourismPhrase(
        category: TourismCategories.food,
        subcategory: 'الحلويات',
        translations: {
          'ar': 'أريد حلويات شرقية',
          'en': 'I want Middle Eastern sweets',
          'fr': 'Je veux des pâtisseries orientales',
          'de': 'Ich möchte orientalische Süßigkeiten',
          'es': 'Quiero dulces orientales',
          'it': 'Voglio dolci orientali',
          'ja': '中東のお菓子が欲しいです',
          'zh': '我要中东甜点',
          'tr': 'Doğu tatlıları istiyorum',
          'ru': 'Я хочу восточные сладости',
        },
        pronunciation: 'I want Middle Eastern sweets',
      ),
      TourismPhrase(
        category: TourismCategories.food,
        subcategory: 'الحلويات',
        translations: {
          'ar': 'هل لديكم آيس كريم؟',
          'en': 'Do you have ice cream?',
          'fr': 'Avez-vous de la glace?',
          'de': 'Haben Sie Eis?',
          'es': '¿Tienen helado?',
          'it': 'Avete gelato?',
          'ja': 'アイスクリームはありますか？',
          'zh': '你们有冰淇淋吗？',
          'tr': 'Dondurmanız var mı?',
          'ru': 'У вас есть мороженое?',
        },
        pronunciation: 'Do you have ice cream?',
      ),
      TourismPhrase(
        category: TourismCategories.food,
        subcategory: 'الحلويات',
        translations: {
          'ar': 'أريد كعك بدون سكر',
          'en': 'I want sugar-free cake',
          'fr': 'Je veux un gâteau sans sucre',
          'de': 'Ich möchte zuckerfreien Kuchen',
          'es': 'Quiero pastel sin azúcar',
          'it': 'Voglio torta senza zucchero',
          'ja': '砂糖なしのケーキが欲しいです',
          'zh': '我要无糖蛋糕',
          'tr': 'Şekersiz pasta istiyorum',
          'ru': 'Я хочу торт без сахара',
        },
        pronunciation: 'I want sugar-free cake',
      ),
      TourismPhrase(
        category: TourismCategories.food,
        subcategory: 'الحلويات',
        translations: {
          'ar': 'هل هذا يحتوي على مكسرات؟',
          'en': 'Does this contain nuts?',
          'fr': 'Est-ce que cela contient des noix?',
          'de': 'Enthält das Nüsse?',
          'es': '¿Esto contiene nueces?',
          'it': 'Questo contiene noci?',
          'ja': 'これにナッツは入っていますか？',
          'zh': '这个含有坚果吗？',
          'tr': 'Bunun içinde fındık var mı?',
          'ru': 'Это содержит орехи?',
        },
        pronunciation: 'Does this contain nuts?',
      ),
      TourismPhrase(
        category: TourismCategories.food,
        subcategory: 'الحلويات',
        translations: {
          'ar': 'أريد شوكولاتة داكنة',
          'en': 'I want dark chocolate',
          'fr': 'Je veux du chocolat noir',
          'de': 'Ich möchte dunkle Schokolade',
          'es': 'Quiero chocolate negro',
          'it': 'Voglio cioccolato fondente',
          'ja': 'ダークチョコレートが欲しいです',
          'zh': '我要黑巧克力',
          'tr': 'Bitter çikolata istiyorum',
          'ru': 'Я хочу темный шоколад',
        },
        pronunciation: 'I want dark chocolate',
      ),

      // الطعام الحلال
      TourismPhrase(
        category: TourismCategories.food,
        subcategory: 'الطعام الحلال',
        translations: {
          'ar': 'أين أجد طعام حلال؟',
          'en': 'Where can I find halal food?',
          'fr': 'Où puis-je trouver de la nourriture halal?',
          'de': 'Wo finde ich Halal-Essen?',
          'es': '¿Dónde puedo encontrar comida halal?',
          'it': 'Dove posso trovare cibo halal?',
          'ja': 'ハラルフードはどこで見つけられますか？',
          'zh': '我在哪里能找到清真食品？',
          'tr': 'Helal yemek nerede bulabilirim?',
          'ru': 'Где я могу найти халяльную еду?',
        },
        pronunciation: 'Where can I find halal food?',
      ),
      TourismPhrase(
        category: TourismCategories.food,
        subcategory: 'الطعام الحلال',
        translations: {
          'ar': 'هل هذا المطعم حلال؟',
          'en': 'Is this restaurant halal?',
          'fr': 'Ce restaurant est-il halal?',
          'de': 'Ist dieses Restaurant halal?',
          'es': '¿Este restaurante es halal?',
          'it': 'Questo ristorante è halal?',
          'ja': 'このレストランはハラルですか？',
          'zh': '这家餐厅是清真的吗？',
          'tr': 'Bu restoran helal mi?',
          'ru': 'Этот ресторан халяльный?',
        },
        pronunciation: 'Is this restaurant halal?',
      ),
      TourismPhrase(
        category: TourismCategories.food,
        subcategory: 'الطعام الحلال',
        translations: {
          'ar': 'لا آكل لحم الخنزير أو الكحول',
          'en': 'I don\'t eat pork or alcohol',
          'fr': 'Je ne mange pas de porc ni d\'alcool',
          'de': 'Ich esse kein Schweinefleisch und trinke keinen Alkohol',
          'es': 'No como cerdo ni alcohol',
          'it': 'Non mangio maiale né alcol',
          'ja': '豚肉もアルコールも摂取しません',
          'zh': '我不吃猪肉也不喝酒',
          'tr': 'Domuz eti ve alkol kullanmam',
          'ru': 'Я не ем свинину и не пью алкоголь',
        },
        pronunciation: 'I don\'t eat pork or alcohol',
      ),
      TourismPhrase(
        category: TourismCategories.food,
        subcategory: 'الطعام الحلال',
        translations: {
          'ar': 'هل لديكم شهادة حلال؟',
          'en': 'Do you have halal certification?',
          'fr': 'Avez-vous une certification halal?',
          'de': 'Haben Sie eine Halal-Zertifizierung?',
          'es': '¿Tienen certificación halal?',
          'it': 'Avete certificazione halal?',
          'ja': 'ハラル認証はありますか？',
          'zh': '你们有清真认证吗？',
          'tr': 'Helal sertifikanız var mı?',
          'ru': 'У вас есть халяльная сертификация?',
        },
        pronunciation: 'Do you have halal certification?',
      ),
      TourismPhrase(
        category: TourismCategories.food,
        subcategory: 'الطعام الحلال',
        translations: {
          'ar': 'أريد وجبة نباتية',
          'en': 'I want a vegetarian meal',
          'fr': 'Je veux un repas végétarien',
          'de': 'Ich möchte ein vegetarisches Essen',
          'es': 'Quiero una comida vegetariana',
          'it': 'Voglio un pasto vegetariano',
          'ja': 'ベジタリアンの食事が欲しいです',
          'zh': '我要素食',
          'tr': 'Vejetaryen yemek istiyorum',
          'ru': 'Я хочу вегетарианскую еду',
        },
        pronunciation: 'I want a vegetarian meal',
      ),
    ];
  }

  /// عبارات الأسواق والتسوق العام (20 عبارة)
  List<TourismPhrase> _generateMarketsPhrases() {
    return [
      // البحث عن المحلات
      TourismPhrase(
        category: TourismCategories.markets,
        subcategory: 'البحث عن المحلات',
        translations: {
          'ar': 'أين أقرب سوق؟',
          'en': 'Where is the nearest market?',
          'fr': 'Où est le marché le plus proche?',
          'de': 'Wo ist der nächste Markt?',
          'es': '¿Dónde está el mercado más cercano?',
          'it': 'Dove è il mercato più vicino?',
          'ja': '一番近い市場はどこですか？',
          'zh': '最近的市场在哪里？',
          'tr': 'En yakın pazar nerede?',
          'ru': 'Где ближайший рынок?',
        },
        pronunciation: 'Where is the nearest market?',
      ),
      TourismPhrase(
        category: TourismCategories.markets,
        subcategory: 'البحث عن المحلات',
        translations: {
          'ar': 'أين مركز التسوق؟',
          'en': 'Where is the shopping mall?',
          'fr': 'Où est le centre commercial?',
          'de': 'Wo ist das Einkaufszentrum?',
          'es': '¿Dónde está el centro comercial?',
          'it': 'Dove è il centro commerciale?',
          'ja': 'ショッピングモールはどこですか？',
          'zh': '购物中心在哪里？',
          'tr': 'Alışveriş merkezi nerede?',
          'ru': 'Где торговый центр?',
        },
        pronunciation: 'Where is the shopping mall?',
      ),
      TourismPhrase(
        category: TourismCategories.markets,
        subcategory: 'البحث عن المحلات',
        translations: {
          'ar': 'هل يوجد سوق شعبي هنا؟',
          'en': 'Is there a traditional market here?',
          'fr': 'Y a-t-il un marché traditionnel ici?',
          'de': 'Gibt es hier einen traditionellen Markt?',
          'es': '¿Hay un mercado tradicional aquí?',
          'it': 'C\'è un mercato tradizionale qui?',
          'ja': 'ここに伝統的な市場はありますか？',
          'zh': '这里有传统市场吗？',
          'tr': 'Burada geleneksel bir pazar var mı?',
          'ru': 'Есть ли здесь традиционный рынок?',
        },
        pronunciation: 'Is there a traditional market here?',
      ),
      TourismPhrase(
        category: TourismCategories.markets,
        subcategory: 'البحث عن المحلات',
        translations: {
          'ar': 'أين أجد محلات الهدايا؟',
          'en': 'Where can I find gift shops?',
          'fr': 'Où puis-je trouver des magasins de cadeaux?',
          'de': 'Wo finde ich Geschäfteläden?',
          'es': '¿Dónde puedo encontrar tiendas de regalos?',
          'it': 'Dove posso trovare negozi di regali?',
          'ja': 'ギフトショップはどこで見つけられますか？',
          'zh': '我在哪里能找到礼品店？',
          'tr': 'Hediyelik eşya dükkanlarını nerede bulabilirim?',
          'ru': 'Где я могу найти магазины подарков?',
        },
        pronunciation: 'Where can I find gift shops?',
      ),

      // ساعات العمل
      TourismPhrase(
        category: TourismCategories.markets,
        subcategory: 'ساعات العمل',
        translations: {
          'ar': 'متى يفتح السوق؟',
          'en': 'When does the market open?',
          'fr': 'Quand ouvre le marché?',
          'de': 'Wann öffnet der Markt?',
          'es': '¿Cuándo abre el mercado?',
          'it': 'Quando apre il mercato?',
          'ja': '市場はいつ開きますか？',
          'zh': '市场什么时候开门？',
          'tr': 'Pazar ne zaman açılıyor?',
          'ru': 'Когда открывается рынок?',
        },
        pronunciation: 'When does the market open?',
      ),
      TourismPhrase(
        category: TourismCategories.markets,
        subcategory: 'ساعات العمل',
        translations: {
          'ar': 'متى يغلق المحل؟',
          'en': 'When does the shop close?',
          'fr': 'Quand ferme le magasin?',
          'de': 'Wann schließt der Laden?',
          'es': '¿Cuándo cierra la tienda?',
          'it': 'Quando chiude il negozio?',
          'ja': '店はいつ閉まりますか？',
          'zh': '商店什么时候关门？',
          'tr': 'Dükkan ne zaman kapanıyor?',
          'ru': 'Когда закрывается магазин?',
        },
        pronunciation: 'When does the shop close?',
      ),
      TourismPhrase(
        category: TourismCategories.markets,
        subcategory: 'ساعات العمل',
        translations: {
          'ar': 'هل تفتحون يوم الجمعة؟',
          'en': 'Are you open on Friday?',
          'fr': 'Êtes-vous ouvert le vendredi?',
          'de': 'Haben Sie freitags geöffnet?',
          'es': '¿Abren los viernes?',
          'it': 'Siete aperti il venerdì?',
          'ja': '金曜日は営業していますか？',
          'zh': '你们星期五营业吗？',
          'tr': 'Cuma günü açık mısınız?',
          'ru': 'Вы работаете в пятницу?',
        },
        pronunciation: 'Are you open on Friday?',
      ),
      TourismPhrase(
        category: TourismCategories.markets,
        subcategory: 'ساعات العمل',
        translations: {
          'ar': 'هل يوجد عروض اليوم؟',
          'en': 'Are there any offers today?',
          'fr': 'Y a-t-il des offres aujourd\'hui?',
          'de': 'Gibt es heute Angebote?',
          'es': '¿Hay ofertas hoy?',
          'it': 'Ci sono offerte oggi?',
          'ja': '今日は特売がありますか？',
          'zh': '今天有优惠吗？',
          'tr': 'Bugün teklif var mı?',
          'ru': 'Есть ли предложения сегодня?',
        },
        pronunciation: 'Are there any offers today?',
      ),

      // التفاوض
      TourismPhrase(
        category: TourismCategories.markets,
        subcategory: 'التفاوض',
        translations: {
          'ar': 'هل يمكن المساومة؟',
          'en': 'Can I bargain?',
          'fr': 'Puis-je marchander?',
          'de': 'Kann ich handeln?',
          'es': '¿Puedo regatear?',
          'it': 'Posso contrattare?',
          'ja': '値切ることはできますか？',
          'zh': '我可以讨价还价吗？',
          'tr': 'Pazarlık yapabilir miyim?',
          'ru': 'Могу ли я торговаться?',
        },
        pronunciation: 'Can I bargain?',
      ),
      TourismPhrase(
        category: TourismCategories.markets,
        subcategory: 'التفاوض',
        translations: {
          'ar': 'هذا السعر عالي جداً',
          'en': 'This price is too high',
          'fr': 'Ce prix est trop élevé',
          'de': 'Dieser Preis ist zu hoch',
          'es': 'Este precio es muy alto',
          'it': 'Questo prezzo è troppo alto',
          'ja': 'この値段は高すぎます',
          'zh': '这个价格太高了',
          'tr': 'Bu fiyat çok yüksek',
          'ru': 'Эта цена слишком высокая',
        },
        pronunciation: 'This price is too high',
      ),
      TourismPhrase(
        category: TourismCategories.markets,
        subcategory: 'التفاوض',
        translations: {
          'ar': 'ما هو آخر سعر؟',
          'en': 'What is your final price?',
          'fr': 'Quel est votre prix final?',
          'de': 'Was ist Ihr Endpreis?',
          'es': '¿Cuál es su precio final?',
          'it': 'Qual è il vostro prezzo finale?',
          'ja': '最終価格はいくらですか？',
          'zh': '你们的最终价格是多少？',
          'tr': 'Son fiyatınız nedir?',
          'ru': 'Какая ваша окончательная цена?',
        },
        pronunciation: 'What is your final price?',
      ),
      TourismPhrase(
        category: TourismCategories.markets,
        subcategory: 'التفاوض',
        translations: {
          'ar': 'هل تقبل هذا السعر؟',
          'en': 'Do you accept this price?',
          'fr': 'Acceptez-vous ce prix?',
          'de': 'Akzeptieren Sie diesen Preis?',
          'es': '¿Acepta este precio?',
          'it': 'Accettate questo prezzo?',
          'ja': 'この値段で受けてもらえますか？',
          'zh': '你接受这个价格吗？',
          'tr': 'Bu fiyatı kabul ediyor musunuz?',
          'ru': 'Вы принимаете эту цену?',
        },
        pronunciation: 'Do you accept this price?',
      ),

      // طرق الدفع
      TourismPhrase(
        category: TourismCategories.markets,
        subcategory: 'طرق الدفع',
        translations: {
          'ar': 'هل تقبلون الدفع بالبطاقة؟',
          'en': 'Do you accept card payment?',
          'fr': 'Acceptez-vous le paiement par carte?',
          'de': 'Nehmen Sie Kartenzahlung?',
          'es': '¿Aceptan pago con tarjeta?',
          'it': 'Accettate pagamento con carta?',
          'ja': 'カード払いは可能ですか？',
          'zh': '你们接受刷卡吗？',
          'tr': 'Kart ile ödeme kabul ediyor musunuz?',
          'ru': 'Вы принимаете оплату картой?',
        },
        pronunciation: 'Do you accept card payment?',
      ),
      TourismPhrase(
        category: TourismCategories.markets,
        subcategory: 'طرق الدفع',
        translations: {
          'ar': 'أريد الدفع نقداً',
          'en': 'I want to pay cash',
          'fr': 'Je veux payer en espèces',
          'de': 'Ich möchte bar bezahlen',
          'es': 'Quiero pagar en efectivo',
          'it': 'Voglio pagare in contanti',
          'ja': '現金で支払いたいです',
          'zh': '我要付现金',
          'tr': 'Nakit ödeme yapmak istiyorum',
          'ru': 'Я хочу заплатить наличными',
        },
        pronunciation: 'I want to pay cash',
      ),
      TourismPhrase(
        category: TourismCategories.markets,
        subcategory: 'طرق الدفع',
        translations: {
          'ar': 'هل لديكم تطبيق دفع؟',
          'en': 'Do you have a payment app?',
          'fr': 'Avez-vous une application de paiement?',
          'de': 'Haben Sie eine Zahlungs-App?',
          'es': '¿Tienen aplicación de pago?',
          'it': 'Avete un\'app di pagamento?',
          'ja': '決済アプリはありますか？',
          'zh': '你们有支付应用吗？',
          'tr': 'Ödeme uygulamanız var mı?',
          'ru': 'У вас есть приложение для оплаты?',
        },
        pronunciation: 'Do you have a payment app?',
      ),
      TourismPhrase(
        category: TourismCategories.markets,
        subcategory: 'طرق الدفع',
        translations: {
          'ar': 'كم المبلغ الإجمالي؟',
          'en': 'What is the total amount?',
          'fr': 'Quel est le montant total?',
          'de': 'Wie hoch ist der Gesamtbetrag?',
          'es': '¿Cuál es el monto total?',
          'it': 'Qual è l\'importo totale?',
          'ja': '合計金額はいくらですか？',
          'zh': '总金额是多少？',
          'tr': 'Toplam tutar ne kadar?',
          'ru': 'Какая общая сумма?',
        },
        pronunciation: 'What is the total amount?',
      ),

      // التوصيل
      TourismPhrase(
        category: TourismCategories.markets,
        subcategory: 'التوصيل',
        translations: {
          'ar': 'هل يمكن التوصيل للفندق؟',
          'en': 'Can you deliver to the hotel?',
          'fr': 'Pouvez-vous livrer à l\'hôtel?',
          'de': 'Können Sie ins Hotel liefern?',
          'es': '¿Pueden entregar al hotel?',
          'it': 'Potete consegnare all\'hotel?',
          'ja': 'ホテルまで配達できますか？',
          'zh': '你们能送到酒店吗？',
          'tr': 'Otele teslimat yapabilir misiniz?',
          'ru': 'Можете ли вы доставить в отель?',
        },
        pronunciation: 'Can you deliver to the hotel?',
      ),
      TourismPhrase(
        category: TourismCategories.markets,
        subcategory: 'التوصيل',
        translations: {
          'ar': 'كم تكلفة التوصيل؟',
          'en': 'How much is delivery?',
          'fr': 'Combien coûte la livraison?',
          'de': 'Wie viel kostet die Lieferung?',
          'es': '¿Cuánto cuesta la entrega?',
          'it': 'Quanto costa la consegna?',
          'ja': '配達料金はいくらですか？',
          'zh': '送货费多少钱？',
          'tr': 'Teslimat ücreti ne kadar?',
          'ru': 'Сколько стоит доставка?',
        },
        pronunciation: 'How much is delivery?',
      ),
      TourismPhrase(
        category: TourismCategories.markets,
        subcategory: 'التوصيل',
        translations: {
          'ar': 'متى سيصل الطلب؟',
          'en': 'When will the order arrive?',
          'fr': 'Quand la commande arrivera-t-elle?',
          'de': 'Wann wird die Bestellung ankommen?',
          'es': '¿Cuándo llegará el pedido?',
          'it': 'Quando arriverà l\'ordine?',
          'ja': '注文はいつ届きますか？',
          'zh': '订单什么时候到？',
          'tr': 'Sipariş ne zaman gelecek?',
          'ru': 'Когда прибудет заказ?',
        },
        pronunciation: 'When will the order arrive?',
      ),
      TourismPhrase(
        category: TourismCategories.markets,
        subcategory: 'التوصيل',
        translations: {
          'ar': 'هل يمكن تغليف الهدايا؟',
          'en': 'Can you wrap the gifts?',
          'fr': 'Pouvez-vous emballer les cadeaux?',
          'de': 'Können Sie die Geschenke einpacken?',
          'es': '¿Pueden envolver los regalos?',
          'it': 'Potete incartare i regali?',
          'ja': 'プレゼントを包装してもらえますか？',
          'zh': '你们能包装礼品吗？',
          'tr': 'Hediyeleri paketleyebilir misiniz?',
          'ru': 'Можете ли вы упаковать подарки?',
        },
        pronunciation: 'Can you wrap the gifts?',
      ),
    ];
  }

  /// عبارات التعارف والتعريف (30 عبارة)
  List<TourismPhrase> _generateIntroductionPhrases() {
    return [
      // التعريف الشخصي
      TourismPhrase(
        category: TourismCategories.introduction,
        subcategory: 'التعريف الشخصي',
        translations: {
          'ar': 'اسمي أحمد',
          'en': 'My name is Ahmed',
          'fr': 'Je m\'appelle Ahmed',
          'de': 'Mein Name ist Ahmed',
          'es': 'Mi nombre es Ahmed',
          'it': 'Il mio nome è Ahmed',
          'ja': '私の名前はアハメドです',
          'zh': '我叫艾哈迈德',
          'tr': 'Benim adım Ahmed',
          'ru': 'Меня зовут Ахмед',
        },
        pronunciation: 'My name is Ahmed',
      ),
      TourismPhrase(
        category: TourismCategories.introduction,
        subcategory: 'التعريف الشخصي',
        translations: {
          'ar': 'أنا من السعودية',
          'en': 'I am from Saudi Arabia',
          'fr': 'Je viens d\'Arabie Saoudite',
          'de': 'Ich komme aus Saudi-Arabien',
          'es': 'Soy de Arabia Saudita',
          'it': 'Vengo dall\'Arabia Saudita',
          'ja': '私はサウジアラビア出身です',
          'zh': '我来自沙特阿拉伯',
          'tr': 'Suudi Arabistan\'danım',
          'ru': 'Я из Саудовской Аравии',
        },
        pronunciation: 'I am from Saudi Arabia',
      ),
      TourismPhrase(
        category: TourismCategories.introduction,
        subcategory: 'التعريف الشخصي',
        translations: {
          'ar': 'أنا طالب',
          'en': 'I am a student',
          'fr': 'Je suis étudiant',
          'de': 'Ich bin Student',
          'es': 'Soy estudiante',
          'it': 'Sono uno studente',
          'ja': '私は学生です',
          'zh': '我是学生',
          'tr': 'Ben öğrenciyim',
          'ru': 'Я студент',
        },
        pronunciation: 'I am a student',
      ),
      TourismPhrase(
        category: TourismCategories.introduction,
        subcategory: 'التعريف الشخصي',
        translations: {
          'ar': 'أنا مهندس',
          'en': 'I am an engineer',
          'fr': 'Je suis ingénieur',
          'de': 'Ich bin Ingenieur',
          'es': 'Soy ingeniero',
          'it': 'Sono un ingegnere',
          'ja': '私はエンジニアです',
          'zh': '我是工程师',
          'tr': 'Ben mühendisim',
          'ru': 'Я инженер',
        },
        pronunciation: 'I am an engineer',
      ),
      TourismPhrase(
        category: TourismCategories.introduction,
        subcategory: 'التعريف الشخصي',
        translations: {
          'ar': 'عمري 25 سنة',
          'en': 'I am 25 years old',
          'fr': 'J\'ai 25 ans',
          'de': 'Ich bin 25 Jahre alt',
          'es': 'Tengo 25 años',
          'it': 'Ho 25 anni',
          'ja': '私は25歳です',
          'zh': '我25岁',
          'tr': '25 yaşındayım',
          'ru': 'Мне 25 лет',
        },
        pronunciation: 'I am 25 years old',
      ),

      // السؤال عن الآخرين
      TourismPhrase(
        category: TourismCategories.introduction,
        subcategory: 'السؤال عن الآخرين',
        translations: {
          'ar': 'ما اسمك؟',
          'en': 'What is your name?',
          'fr': 'Comment vous appelez-vous?',
          'de': 'Wie heißen Sie?',
          'es': '¿Cómo te llamas?',
          'it': 'Come ti chiami?',
          'ja': 'お名前は何ですか？',
          'zh': '你叫什么名字？',
          'tr': 'Adınız nedir?',
          'ru': 'Как вас зовут?',
        },
        pronunciation: 'What is your name?',
      ),
      TourismPhrase(
        category: TourismCategories.introduction,
        subcategory: 'السؤال عن الآخرين',
        translations: {
          'ar': 'من أين أنت؟',
          'en': 'Where are you from?',
          'fr': 'D\'où venez-vous?',
          'de': 'Woher kommen Sie?',
          'es': '¿De dónde eres?',
          'it': 'Da dove vieni?',
          'ja': 'どちらの出身ですか？',
          'zh': '你来自哪里？',
          'tr': 'Nerelisiniz?',
          'ru': 'Откуда вы?',
        },
        pronunciation: 'Where are you from?',
      ),
      TourismPhrase(
        category: TourismCategories.introduction,
        subcategory: 'السؤال عن الآخرين',
        translations: {
          'ar': 'ما عملك؟',
          'en': 'What is your job?',
          'fr': 'Quel est votre travail?',
          'de': 'Was ist Ihr Beruf?',
          'es': '¿Cuál es tu trabajo?',
          'it': 'Qual è il tuo lavoro?',
          'ja': 'お仕事は何ですか？',
          'zh': '你的工作是什么？',
          'tr': 'İşiniz nedir?',
          'ru': 'Какая у вас работа?',
        },
        pronunciation: 'What is your job?',
      ),
      TourismPhrase(
        category: TourismCategories.introduction,
        subcategory: 'السؤال عن الآخرين',
        translations: {
          'ar': 'كم عمرك؟',
          'en': 'How old are you?',
          'fr': 'Quel âge avez-vous?',
          'de': 'Wie alt sind Sie?',
          'es': '¿Cuántos años tienes?',
          'it': 'Quanti anni hai?',
          'ja': '何歳ですか？',
          'zh': '你多大了？',
          'tr': 'Kaç yaşındasınız?',
          'ru': 'Сколько вам лет?',
        },
        pronunciation: 'How old are you?',
      ),
      TourismPhrase(
        category: TourismCategories.introduction,
        subcategory: 'السؤال عن الآخرين',
        translations: {
          'ar': 'هل أنت متزوج؟',
          'en': 'Are you married?',
          'fr': 'Êtes-vous marié?',
          'de': 'Sind Sie verheiratet?',
          'es': '¿Estás casado?',
          'it': 'Sei sposato?',
          'ja': '結婚していますか？',
          'zh': '你结婚了吗？',
          'tr': 'Evli misiniz?',
          'ru': 'Вы женаты?',
        },
        pronunciation: 'Are you married?',
      ),

      // بدء المحادثة
      TourismPhrase(
        category: TourismCategories.introduction,
        subcategory: 'بدء المحادثة',
        translations: {
          'ar': 'تشرفنا بلقائك',
          'en': 'Nice to meet you',
          'fr': 'Ravi de vous rencontrer',
          'de': 'Freut mich, Sie kennenzulernen',
          'es': 'Encantado de conocerte',
          'it': 'Piacere di conoscerti',
          'ja': 'お会いできて嬉しいです',
          'zh': '很高兴认识你',
          'tr': 'Tanıştığımıza memnun oldum',
          'ru': 'Приятно познакомиться',
        },
        pronunciation: 'Nice to meet you',
      ),
      TourismPhrase(
        category: TourismCategories.introduction,
        subcategory: 'بدء المحادثة',
        translations: {
          'ar': 'هل يمكنني التعرف عليك؟',
          'en': 'Can I get to know you?',
          'fr': 'Puis-je faire votre connaissance?',
          'de': 'Darf ich Sie kennenlernen?',
          'es': '¿Puedo conocerte?',
          'it': 'Posso conoscerti?',
          'ja': 'お知り合いになれますか？',
          'zh': '我可以认识你吗？',
          'tr': 'Sizi tanıyabilir miyim?',
          'ru': 'Могу ли я с вами познакомиться?',
        },
        pronunciation: 'Can I get to know you?',
      ),
      TourismPhrase(
        category: TourismCategories.introduction,
        subcategory: 'بدء المحادثة',
        translations: {
          'ar': 'هل تتحدث العربية؟',
          'en': 'Do you speak Arabic?',
          'fr': 'Parlez-vous arabe?',
          'de': 'Sprechen Sie Arabisch?',
          'es': '¿Hablas árabe?',
          'it': 'Parli arabo?',
          'ja': 'アラビア語を話しますか？',
          'zh': '你会说阿拉伯语吗？',
          'tr': 'Arapça konuşuyor musunuz?',
          'ru': 'Вы говорите по-арабски?',
        },
        pronunciation: 'Do you speak Arabic?',
      ),
      TourismPhrase(
        category: TourismCategories.introduction,
        subcategory: 'بدء المحادثة',
        translations: {
          'ar': 'هل هذه زيارتك الأولى هنا؟',
          'en': 'Is this your first visit here?',
          'fr': 'Est-ce votre première visite ici?',
          'de': 'Ist das Ihr erster Besuch hier?',
          'es': '¿Es tu primera visita aquí?',
          'it': 'È la tua prima visita qui?',
          'ja': 'ここは初めてですか？',
          'zh': '这是你第一次来这里吗？',
          'tr': 'Buraya ilk ziyaretiniz mi?',
          'ru': 'Это ваш первый визит сюда?',
        },
        pronunciation: 'Is this your first visit here?',
      ),
      TourismPhrase(
        category: TourismCategories.introduction,
        subcategory: 'بدء المحادثة',
        translations: {
          'ar': 'كم ستبقى هنا؟',
          'en': 'How long will you stay here?',
          'fr': 'Combien de temps allez-vous rester ici?',
          'de': 'Wie lange werden Sie hier bleiben?',
          'es': '¿Cuánto tiempo te quedarás aquí?',
          'it': 'Quanto tempo rimarrai qui?',
          'ja': 'どのくらいここにいる予定ですか？',
          'zh': '你会在这里待多久？',
          'tr': 'Burada ne kadar kalacaksınız?',
          'ru': 'Как долго вы здесь пробудете?',
        },
        pronunciation: 'How long will you stay here?',
      ),

      // الجنسية والمهنة
      TourismPhrase(
        category: TourismCategories.introduction,
        subcategory: 'الجنسية والمهنة',
        translations: {
          'ar': 'أنا طبيب',
          'en': 'I am a doctor',
          'fr': 'Je suis médecin',
          'de': 'Ich bin Arzt',
          'es': 'Soy médico',
          'it': 'Sono un dottore',
          'ja': '私は医者です',
          'zh': '我是医生',
          'tr': 'Ben doktorum',
          'ru': 'Я врач',
        },
        pronunciation: 'I am a doctor',
      ),
      TourismPhrase(
        category: TourismCategories.introduction,
        subcategory: 'الجنسية والمهنة',
        translations: {
          'ar': 'أنا معلم',
          'en': 'I am a teacher',
          'fr': 'Je suis professeur',
          'de': 'Ich bin Lehrer',
          'es': 'Soy profesor',
          'it': 'Sono un insegnante',
          'ja': '私は教師です',
          'zh': '我是老师',
          'tr': 'Ben öğretmenim',
          'ru': 'Я учитель',
        },
        pronunciation: 'I am a teacher',
      ),
      TourismPhrase(
        category: TourismCategories.introduction,
        subcategory: 'الجنسية والمهنة',
        translations: {
          'ar': 'أنا من مصر',
          'en': 'I am from Egypt',
          'fr': 'Je viens d\'Égypte',
          'de': 'Ich komme aus Ägypten',
          'es': 'Soy de Egipto',
          'it': 'Vengo dall\'Egitto',
          'ja': '私はエジプト出身です',
          'zh': '我来自埃及',
          'tr': 'Mısır\'danım',
          'ru': 'Я из Египта',
        },
        pronunciation: 'I am from Egypt',
      ),
      TourismPhrase(
        category: TourismCategories.introduction,
        subcategory: 'الجنسية والمهنة',
        translations: {
          'ar': 'أنا من الإمارات',
          'en': 'I am from UAE',
          'fr': 'Je viens des Émirats',
          'de': 'Ich komme aus den VAE',
          'es': 'Soy de los Emiratos',
          'it': 'Vengo dagli Emirati',
          'ja': '私はUAE出身です',
          'zh': '我来自阿联酋',
          'tr': 'BAE\'denim',
          'ru': 'Я из ОАЭ',
        },
        pronunciation: 'I am from UAE',
      ),
      TourismPhrase(
        category: TourismCategories.introduction,
        subcategory: 'الجنسية والمهنة',
        translations: {
          'ar': 'أعمل في شركة',
          'en': 'I work in a company',
          'fr': 'Je travaille dans une entreprise',
          'de': 'Ich arbeite in einem Unternehmen',
          'es': 'Trabajo en una empresa',
          'it': 'Lavoro in un\'azienda',
          'ja': '私は会社で働いています',
          'zh': '我在公司工作',
          'tr': 'Bir şirkette çalışıyorum',
          'ru': 'Я работаю в компании',
        },
        pronunciation: 'I work in a company',
      ),

      // الهوايات والاهتمامات
      TourismPhrase(
        category: TourismCategories.introduction,
        subcategory: 'الهوايات والاهتمامات',
        translations: {
          'ar': 'أحب السفر',
          'en': 'I love traveling',
          'fr': 'J\'aime voyager',
          'de': 'Ich liebe das Reisen',
          'es': 'Me encanta viajar',
          'it': 'Amo viaggiare',
          'ja': '私は旅行が大好きです',
          'zh': '我喜欢旅行',
          'tr': 'Seyahat etmeyi seviyorum',
          'ru': 'Я люблю путешествовать',
        },
        pronunciation: 'I love traveling',
      ),
      TourismPhrase(
        category: TourismCategories.introduction,
        subcategory: 'الهوايات والاهتمامات',
        translations: {
          'ar': 'أحب القراءة',
          'en': 'I love reading',
          'fr': 'J\'aime lire',
          'de': 'Ich liebe das Lesen',
          'es': 'Me encanta leer',
          'it': 'Amo leggere',
          'ja': '私は読書が大好きです',
          'zh': '我喜欢阅读',
          'tr': 'Okumayı seviyorum',
          'ru': 'Я люблю читать',
        },
        pronunciation: 'I love reading',
      ),
      TourismPhrase(
        category: TourismCategories.introduction,
        subcategory: 'الهوايات والاهتمامات',
        translations: {
          'ar': 'أحب الرياضة',
          'en': 'I love sports',
          'fr': 'J\'aime le sport',
          'de': 'Ich liebe Sport',
          'es': 'Me encanta el deporte',
          'it': 'Amo lo sport',
          'ja': '私はスポーツが大好きです',
          'zh': '我喜欢运动',
          'tr': 'Sporu seviyorum',
          'ru': 'Я люблю спорт',
        },
        pronunciation: 'I love sports',
      ),
      TourismPhrase(
        category: TourismCategories.introduction,
        subcategory: 'الهوايات والاهتمامات',
        translations: {
          'ar': 'ما هواياتك؟',
          'en': 'What are your hobbies?',
          'fr': 'Quels sont vos loisirs?',
          'de': 'Was sind Ihre Hobbys?',
          'es': '¿Cuáles son tus aficiones?',
          'it': 'Quali sono i tuoi hobby?',
          'ja': 'あなたの趣味は何ですか？',
          'zh': '你的爱好是什么？',
          'tr': 'Hobileriniz nelerdir?',
          'ru': 'Какие у вас хобби?',
        },
        pronunciation: 'What are your hobbies?',
      ),
      TourismPhrase(
        category: TourismCategories.introduction,
        subcategory: 'الهوايات والاهتمامات',
        translations: {
          'ar': 'أحب الموسيقى',
          'en': 'I love music',
          'fr': 'J\'aime la musique',
          'de': 'Ich liebe Musik',
          'es': 'Me encanta la música',
          'it': 'Amo la musica',
          'ja': '私は音楽が大好きです',
          'zh': '我喜欢音乐',
          'tr': 'Müziği seviyorum',
          'ru': 'Я люблю музыку',
        },
        pronunciation: 'I love music',
      ),

      // التواصل الاجتماعي
      TourismPhrase(
        category: TourismCategories.introduction,
        subcategory: 'التواصل الاجتماعي',
        translations: {
          'ar': 'هل يمكنني الحصول على رقمك؟',
          'en': 'Can I get your number?',
          'fr': 'Puis-je avoir votre numéro?',
          'de': 'Kann ich Ihre Nummer haben?',
          'es': '¿Puedo tener tu número?',
          'it': 'Posso avere il tuo numero?',
          'ja': '電話番号を教えてもらえますか？',
          'zh': '我可以要你的电话号码吗？',
          'tr': 'Numaranızı alabilir miyim?',
          'ru': 'Могу ли я получить ваш номер?',
        },
        pronunciation: 'Can I get your number?',
      ),
      TourismPhrase(
        category: TourismCategories.introduction,
        subcategory: 'التواصل الاجتماعي',
        translations: {
          'ar': 'هل لديك واتساب؟',
          'en': 'Do you have WhatsApp?',
          'fr': 'Avez-vous WhatsApp?',
          'de': 'Haben Sie WhatsApp?',
          'es': '¿Tienes WhatsApp?',
          'it': 'Hai WhatsApp?',
          'ja': 'WhatsAppはありますか？',
          'zh': '你有WhatsApp吗？',
          'tr': 'WhatsApp\'ınız var mı?',
          'ru': 'У вас есть WhatsApp?',
        },
        pronunciation: 'Do you have WhatsApp?',
      ),
      TourismPhrase(
        category: TourismCategories.introduction,
        subcategory: 'التواصل الاجتماعي',
        translations: {
          'ar': 'دعنا نبقى على تواصل',
          'en': 'Let\'s stay in touch',
          'fr': 'Restons en contact',
          'de': 'Lass uns in Kontakt bleiben',
          'es': 'Mantengámonos en contacto',
          'it': 'Rimaniamo in contatto',
          'ja': '連絡を取り合いましょう',
          'zh': '让我们保持联系',
          'tr': 'İletişimde kalalım',
          'ru': 'Давайте поддерживать связь',
        },
        pronunciation: 'Let\'s stay in touch',
      ),
      TourismPhrase(
        category: TourismCategories.introduction,
        subcategory: 'التواصل الاجتماعي',
        translations: {
          'ar': 'أتمنى أن نلتقي مرة أخرى',
          'en': 'I hope we meet again',
          'fr': 'J\'espère qu\'on se reverra',
          'de': 'Ich hoffe, wir sehen uns wieder',
          'es': 'Espero que nos volvamos a ver',
          'it': 'Spero che ci incontreremo di nuovo',
          'ja': 'また会えることを願っています',
          'zh': '我希望我们能再次见面',
          'tr': 'Umarım tekrar görüşürüz',
          'ru': 'Надеюсь, мы встретимся снова',
        },
        pronunciation: 'I hope we meet again',
      ),
      TourismPhrase(
        category: TourismCategories.introduction,
        subcategory: 'التواصل الاجتماعي',
        translations: {
          'ar': 'كان من دواعي سروري لقاؤك',
          'en': 'It was a pleasure meeting you',
          'fr': 'Ce fut un plaisir de vous rencontrer',
          'de': 'Es war mir eine Freude, Sie kennenzulernen',
          'es': 'Fue un placer conocerte',
          'it': 'È stato un piacere conoscerti',
          'ja': 'お会いできて光栄でした',
          'zh': '很高兴认识你',
          'tr': 'Sizinle tanışmak bir zevkti',
          'ru': 'Было приятно познакомиться с вами',
        },
        pronunciation: 'It was a pleasure meeting you',
      ),
    ];
  }
}
