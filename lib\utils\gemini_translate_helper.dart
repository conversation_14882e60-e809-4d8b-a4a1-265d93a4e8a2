import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../core/app_state.dart';
import '../services/api/translation_service.dart';

/// دالة ترجمة نص باستخدام Gemini فقط (للاستخدام في أي قائمة أو عنصر)
Future<String> geminiTranslate({
  required BuildContext context,
  required String text,
  required String sourceLanguage,
  required String targetLanguage,
}) async {
  final appState = Provider.of<AppState>(context, listen: false);
  final remoteConfig = appState.remoteConfig;
  final translationService = TranslationService(remoteConfig);
  return await translationService.translateText(
    text: text,
    sourceLanguage: sourceLanguage,
    targetLanguage: targetLanguage,
  );
}
