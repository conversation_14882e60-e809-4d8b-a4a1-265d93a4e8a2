import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'dart:math';
import '../../../services/educational_service.dart';

/// لعبة المطابقة السريعة
class QuickMatchGame extends StatefulWidget {
  final String category;
  final EducationalService educationalService;

  const QuickMatchGame({
    super.key,
    required this.category,
    required this.educationalService,
  });

  @override
  State<QuickMatchGame> createState() => _QuickMatchGameState();
}

class _QuickMatchGameState extends State<QuickMatchGame>
    with TickerProviderStateMixin {
  late AnimationController _timerController;
  late AnimationController _scoreController;
  
  Timer? _gameTimer;
  int _timeLeft = 60; // 60 ثانية
  int _score = 0;
  int _streak = 0;
  int _currentQuestionIndex = 0;
  
  List<String> _words = [];
  String _currentWord = '';
  List<String> _options = [];
  String _correctAnswer = '';
  bool _isGameActive = false;
  bool _showResult = false;

  @override
  void initState() {
    super.initState();
    _timerController = AnimationController(
      duration: const Duration(seconds: 60),
      vsync: this,
    );
    _scoreController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _initializeGame();
  }

  @override
  void dispose() {
    _timerController.dispose();
    _scoreController.dispose();
    _gameTimer?.cancel();
    super.dispose();
  }

  /// تهيئة اللعبة
  void _initializeGame() {
    _words = _getWordsForCategory();
    _generateQuestion();
  }

  /// الحصول على الكلمات حسب الفئة
  List<String> _getWordsForCategory() {
    switch (widget.category) {
      case 'animals':
        return ['قطة', 'كلب', 'بقرة', 'حصان', 'فيل', 'أسد', 'نمر', 'دب', 'أرنب', 'طائر'];
      case 'colors':
        return ['أحمر', 'أزرق', 'أخضر', 'أصفر', 'بنفسجي', 'برتقالي', 'وردي', 'أسود', 'أبيض', 'بني'];
      case 'family':
        return ['أب', 'أم', 'أخ', 'أخت', 'جد', 'جدة', 'عم', 'عمة', 'خال', 'خالة'];
      case 'food':
        return ['تفاح', 'موز', 'خبز', 'ماء', 'حليب', 'لحم', 'سمك', 'أرز', 'خضار', 'فاكهة'];
      default:
        return ['قطة', 'كلب', 'بقرة', 'حصان', 'فيل', 'أسد'];
    }
  }

  /// إنشاء سؤال جديد
  void _generateQuestion() {
    if (_words.isEmpty) return;
    
    final random = Random();
    _currentWord = _words[random.nextInt(_words.length)];
    
    final translations = widget.educationalService.getWordTranslations(_currentWord);
    _correctAnswer = translations['en'] ?? _currentWord;
    
    // إنشاء خيارات خاطئة
    final wrongOptions = <String>[];
    while (wrongOptions.length < 3) {
      final randomWord = _words[random.nextInt(_words.length)];
      final randomTranslations = widget.educationalService.getWordTranslations(randomWord);
      final wrongAnswer = randomTranslations['en'] ?? randomWord;
      
      if (wrongAnswer != _correctAnswer && !wrongOptions.contains(wrongAnswer)) {
        wrongOptions.add(wrongAnswer);
      }
    }
    
    _options = [_correctAnswer, ...wrongOptions];
    _options.shuffle();
    
    setState(() {
      _showResult = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text('المطابقة السريعة'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
      body: _isGameActive ? _buildGameContent() : _buildStartScreen(),
    );
  }

  /// شاشة البداية
  Widget _buildStartScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.flash_on,
            size: 100,
            color: Colors.orange,
          ),
          const SizedBox(height: 20),
          const Text(
            'المطابقة السريعة',
            style: TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: Colors.orange,
            ),
          ),
          const SizedBox(height: 10),
          const Text(
            'اطبق الكلمة العربية مع ترجمتها الإنجليزية',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 30),
          Container(
            padding: const EdgeInsets.all(20),
            margin: const EdgeInsets.symmetric(horizontal: 40),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  spreadRadius: 2,
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                const Text(
                  'قواعد اللعبة:',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 10),
                const Text('• لديك 60 ثانية'),
                const Text('• اختر الترجمة الصحيحة'),
                const Text('• كل إجابة صحيحة = 10 نقاط'),
                const Text('• الإجابات المتتالية تضاعف النقاط'),
              ],
            ),
          ),
          const SizedBox(height: 40),
          ElevatedButton(
            onPressed: _startGame,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 16),
              textStyle: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            child: const Text('ابدأ اللعبة'),
          ),
        ],
      ),
    );
  }

  /// محتوى اللعبة
  Widget _buildGameContent() {
    return Column(
      children: [
        _buildGameHeader(),
        Expanded(
          child: _buildQuestionArea(),
        ),
        _buildOptionsArea(),
      ],
    );
  }

  /// رأس اللعبة
  Widget _buildGameHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.orange.shade400, Colors.red.shade400],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Row(
        children: [
          // المؤقت
          Expanded(
            child: Column(
              children: [
                const Text(
                  'الوقت',
                  style: TextStyle(color: Colors.white, fontSize: 14),
                ),
                Text(
                  '$_timeLeft',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 4,
                  child: AnimatedBuilder(
                    animation: _timerController,
                    builder: (context, child) {
                      return LinearProgressIndicator(
                        value: 1 - _timerController.value,
                        backgroundColor: Colors.white.withValues(alpha: 0.3),
                        valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(width: 20),
          
          // النقاط
          Expanded(
            child: Column(
              children: [
                const Text(
                  'النقاط',
                  style: TextStyle(color: Colors.white, fontSize: 14),
                ),
                AnimatedBuilder(
                  animation: _scoreController,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: 1 + (_scoreController.value * 0.2),
                      child: Text(
                        '$_score',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          
          const SizedBox(width: 20),
          
          // السلسلة
          Expanded(
            child: Column(
              children: [
                const Text(
                  'السلسلة',
                  style: TextStyle(color: Colors.white, fontSize: 14),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.local_fire_department,
                      color: _streak > 0 ? Colors.yellow : Colors.white.withValues(alpha: 0.5),
                      size: 20,
                    ),
                    Text(
                      '$_streak',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// منطقة السؤال
  Widget _buildQuestionArea() {
    return Container(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(30),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 2,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Text(
            'ما معنى هذه الكلمة؟',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 20),
          GestureDetector(
            onTap: () => _speakWord(_currentWord, 'ar'),
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    _currentWord,
                    style: const TextStyle(
                      fontSize: 36,
                      fontWeight: FontWeight.bold,
                      color: Colors.orange,
                    ),
                  ),
                  const SizedBox(width: 10),
                  Icon(
                    Icons.volume_up,
                    color: Colors.orange,
                    size: 28,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// منطقة الخيارات
  Widget _buildOptionsArea() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 2.5,
          crossAxisSpacing: 10,
          mainAxisSpacing: 10,
        ),
        itemCount: _options.length,
        itemBuilder: (context, index) {
          return _buildOptionButton(_options[index]);
        },
      ),
    );
  }

  /// زر الخيار
  Widget _buildOptionButton(String option) {
    return GestureDetector(
      onTap: () => _selectOption(option),
      child: Container(
        decoration: BoxDecoration(
          color: _showResult
              ? option == _correctAnswer
                  ? Colors.green.shade100
                  : Colors.red.shade100
              : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: _showResult
                ? option == _correctAnswer
                    ? Colors.green
                    : Colors.red
                : Colors.orange.shade300,
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: Text(
            option,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _showResult
                  ? option == _correctAnswer
                      ? Colors.green.shade700
                      : Colors.red.shade700
                  : Colors.orange.shade700,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  /// بدء اللعبة
  void _startGame() {
    setState(() {
      _isGameActive = true;
      _timeLeft = 60;
      _score = 0;
      _streak = 0;
      _currentQuestionIndex = 0;
    });

    _timerController.forward();
    _startTimer();
    _generateQuestion();
  }

  /// بدء المؤقت
  void _startTimer() {
    _gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _timeLeft--;
      });

      if (_timeLeft <= 0) {
        _endGame();
      }
    });
  }

  /// اختيار خيار
  void _selectOption(String option) {
    if (_showResult) return;

    HapticFeedback.lightImpact();
    
    setState(() {
      _showResult = true;
    });

    if (option == _correctAnswer) {
      // إجابة صحيحة
      _streak++;
      final points = 10 * _streak; // مضاعفة النقاط
      _score += points;
      
      _scoreController.forward().then((_) => _scoreController.reverse());
      _speakWord(option, 'en');
      
      HapticFeedback.mediumImpact();
    } else {
      // إجابة خاطئة
      _streak = 0;
      HapticFeedback.heavyImpact();
    }

    // الانتقال للسؤال التالي
    Future.delayed(const Duration(milliseconds: 1500), () {
      _currentQuestionIndex++;
      _generateQuestion();
    });
  }

  /// نطق الكلمة
  Future<void> _speakWord(String word, String languageCode) async {
    await widget.educationalService.speakInLanguage(word, languageCode);
  }

  /// انتهاء اللعبة
  void _endGame() {
    _gameTimer?.cancel();
    _timerController.stop();
    
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('🎉 انتهت اللعبة!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('النقاط النهائية: $_score'),
            Text('الأسئلة المجابة: $_currentQuestionIndex'),
            Text('أفضل سلسلة: ${_getBestStreak()}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
            child: const Text('العودة'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _restartGame();
            },
            child: const Text('لعب مرة أخرى'),
          ),
        ],
      ),
    );
  }

  /// إعادة بدء اللعبة
  void _restartGame() {
    setState(() {
      _isGameActive = false;
    });
    _timerController.reset();
  }

  /// الحصول على أفضل سلسلة
  int _getBestStreak() {
    // يمكن حفظ هذا في SharedPreferences لاحقاً
    return _streak;
  }
}
