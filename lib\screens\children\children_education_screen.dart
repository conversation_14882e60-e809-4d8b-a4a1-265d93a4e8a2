import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/children_content.dart';
import '../../services/children_education_service.dart';
import '../../utils/app_helpers.dart';
import 'activities/animal_sounds_activity.dart';
import 'activities/colors_matching_activity.dart';
import 'activities/numbers_counting_activity.dart';
import 'activities/letters_learning_activity.dart';

/// شاشة تعليم الأطفال
class ChildrenEducationScreen extends StatefulWidget {
  const ChildrenEducationScreen({super.key});

  @override
  State<ChildrenEducationScreen> createState() =>
      _ChildrenEducationScreenState();
}

class _ChildrenEducationScreenState extends State<ChildrenEducationScreen>
    with TickerProviderStateMixin {
  final ChildrenEducationService _educationService = ChildrenEducationService();

  late AnimationController _animationController;
  late AnimationController _bounceController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _bounceAnimation;

  List<ChildrenContent> _allContent = [];
  List<ChildrenContent> _filteredContent = [];
  Map<String, dynamic> _stats = {};

  String _selectedCategory = '';
  final String _selectedType = '';
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _initializeEducationService();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _bounceController.dispose();
    super.dispose();
  }

  /// تهيئة الرسوم المتحركة
  void _initAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutQuart),
    );

    _bounceAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _bounceController, curve: Curves.elasticOut),
    );

    _animationController.forward();
    _bounceController.repeat(reverse: true);
  }

  /// تهيئة خدمة التعليم
  Future<void> _initializeEducationService() async {
    try {
      await _educationService.initialize();
      setState(() {
        _allContent = _educationService.getAllContent();
        _filteredContent = _allContent;
        _stats = _educationService.getChildStats();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ في تحميل المحتوى التعليمي',
          isError: true,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.purple.shade100,
              Colors.blue.shade100,
              Colors.pink.shade100,
            ],
          ),
        ),
        child: SafeArea(
          child:
              _isLoading
                  ? _buildLoadingState()
                  : FadeTransition(
                    opacity: _fadeAnimation,
                    child: Column(
                      children: [
                        _buildHeader(),
                        _buildStatsSection(),
                        _buildCategoryTabs(),
                        Expanded(child: _buildContentGrid()),
                      ],
                    ),
                  ),
        ),
      ),
    );
  }

  /// بناء حالة التحميل
  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ScaleTransition(
            scale: _bounceAnimation,
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.purple.withValues(alpha: 0.3),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: const Text('🎓', style: TextStyle(fontSize: 48)),
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            'جاري تحضير المحتوى التعليمي...',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.purple,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'انتظر قليلاً، سنبدأ المتعة قريباً! 🌟',
            style: TextStyle(fontSize: 14, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  /// بناء الرأس
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.purple),
            onPressed: () => Navigator.pop(context),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'مرحباً بك في عالم التعلم! 🌟',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.purple,
                  ),
                ),
                Text(
                  'اختر ما تريد تعلمه اليوم',
                  style: TextStyle(fontSize: 14, color: Colors.purple.shade600),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(15),
              boxShadow: [
                BoxShadow(
                  color: Colors.purple.withValues(alpha: 0.2),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: const Text('🏆', style: TextStyle(fontSize: 24)),
          ),
        ],
      ),
    );
  }

  /// بناء قسم الإحصائيات
  Widget _buildStatsSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.purple.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem(
            '📚',
            '${_stats['completedContent'] ?? 0}',
            'مكتمل',
            Colors.green,
          ),
          _buildStatItem(
            '⭐',
            '${_stats['averageScore'] ?? 0}',
            'النقاط',
            Colors.orange,
          ),
          _buildStatItem(
            '🏆',
            '${_stats['unlockedAchievements'] ?? 0}',
            'إنجازات',
            Colors.purple,
          ),
          _buildStatItem(
            '📈',
            '${_stats['completionRate'] ?? 0}%',
            'التقدم',
            Colors.blue,
          ),
        ],
      ),
    );
  }

  /// بناء عنصر إحصائي
  Widget _buildStatItem(String emoji, String value, String label, Color color) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(emoji, style: const TextStyle(fontSize: 20)),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(fontSize: 10, color: color.withValues(alpha: 0.7)),
        ),
      ],
    );
  }

  /// بناء تبويبات الفئات
  Widget _buildCategoryTabs() {
    return Container(
      height: 80,
      margin: const EdgeInsets.symmetric(vertical: 20),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 20),
        itemCount: ChildrenCategories.categoryNames.length + 1,
        itemBuilder: (context, index) {
          if (index == 0) {
            return _buildCategoryTab('', 'الكل', '🌟', ['#FF6B6B', '#4ECDC4']);
          }

          final category = ChildrenCategories.categoryNames.keys.elementAt(
            index - 1,
          );
          final name = ChildrenCategories.categoryNames[category]!;
          final icon = ChildrenCategories.categoryIcons[category]!;
          final colors = ChildrenCategories.categoryColors[category]!;

          return _buildCategoryTab(category, name, icon, colors);
        },
      ),
    );
  }

  /// بناء تبويب فئة
  Widget _buildCategoryTab(
    String category,
    String name,
    String icon,
    List<String> colors,
  ) {
    final isSelected = _selectedCategory == category;

    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        setState(() {
          _selectedCategory = category;
          _filterContent();
        });
      },
      child: Container(
        width: 70,
        margin: const EdgeInsets.only(right: 12),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors:
                isSelected
                    ? [
                      Color(int.parse('0xFF${colors[0].substring(1)}')),
                      Color(int.parse('0xFF${colors[1].substring(1)}')),
                    ]
                    : [Colors.white, Colors.white],
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color:
                isSelected
                    ? Colors.transparent
                    : Colors.grey.withValues(alpha: 0.3),
            width: 2,
          ),
          boxShadow:
              isSelected
                  ? [
                    BoxShadow(
                      color: Color(
                        int.parse('0xFF${colors[0].substring(1)}'),
                      ).withValues(alpha: 0.3),
                      blurRadius: 15,
                      offset: const Offset(0, 5),
                    ),
                  ]
                  : [],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(icon, style: const TextStyle(fontSize: 24)),
            const SizedBox(height: 4),
            Text(
              name,
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.bold,
                color: isSelected ? Colors.white : Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء شبكة المحتوى
  Widget _buildContentGrid() {
    if (_filteredContent.isEmpty) {
      return _buildEmptyState();
    }

    return GridView.builder(
      padding: const EdgeInsets.all(20),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.85,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: _filteredContent.length,
      itemBuilder: (context, index) {
        return _buildContentCard(_filteredContent[index]);
      },
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.purple.withValues(alpha: 0.2),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: const Text('🔍', style: TextStyle(fontSize: 48)),
          ),
          const SizedBox(height: 24),
          const Text(
            'لا يوجد محتوى في هذه الفئة',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.purple,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'جرب فئة أخرى للعثور على محتوى ممتع!',
            style: TextStyle(color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// فلترة المحتوى
  void _filterContent() {
    setState(() {
      _filteredContent =
          _allContent.where((content) {
            if (_selectedCategory.isNotEmpty &&
                content.category != _selectedCategory) {
              return false;
            }

            if (_selectedType.isNotEmpty && content.type != _selectedType) {
              return false;
            }

            return true;
          }).toList();
    });
  }

  /// فتح المحتوى
  void _openContent(ChildrenContent content) {
    HapticFeedback.lightImpact();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            height: MediaQuery.of(context).size.height * 0.6,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(25)),
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  Text(
                    content.title,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(content.content['ar'] ?? ''),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      _startContent(content);
                    },
                    child: const Text('ابدأ التعلم'),
                  ),
                ],
              ),
            ),
          ),
    );
  }

  /// بدء المحتوى
  void _startContent(ChildrenContent content) async {
    HapticFeedback.mediumImpact();

    // تحديد النشاط المناسب حسب المحتوى
    Widget? activityScreen;

    if (content.title.contains('أصوات الحيوانات')) {
      activityScreen = AnimalSoundsActivity(
        onComplete: (score) => _onActivityComplete(content, score),
      );
    } else if (content.title.contains('الألوان') ||
        content.category == 'colors') {
      activityScreen = ColorsMatchingActivity(
        onComplete: (score) => _onActivityComplete(content, score),
      );
    } else if (content.title.contains('العد') ||
        content.category == 'numbers') {
      activityScreen = NumbersCountingActivity(
        onComplete: (score) => _onActivityComplete(content, score),
      );
    } else if (content.title.contains('الحروف') ||
        content.category == 'letters') {
      activityScreen = LettersLearningActivity(
        onComplete: (score) => _onActivityComplete(content, score),
      );
    }

    if (activityScreen != null && mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => activityScreen!),
      );
    } else {
      // للمحتوى الذي لا يحتوي على نشاط تفاعلي بعد
      final score = 50 + (content.difficulty * 10);
      await _educationService.recordCompletion(content.id, score);

      setState(() {
        _stats = _educationService.getChildStats();
      });

      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'أحسنت! لقد أكملت "${content.title}" وحصلت على $score نقطة! 🌟',
        );
      }
    }
  }

  /// معالجة إكمال النشاط
  void _onActivityComplete(ChildrenContent content, int score) async {
    await _educationService.recordCompletion(content.id, score);

    setState(() {
      _stats = _educationService.getChildStats();
    });

    if (mounted) {
      AppHelpers.showSnackBar(
        context,
        'أحسنت! لقد أكملت "${content.title}" وحصلت على $score نقطة! 🌟',
      );
    }
  }

  /// بناء بطاقة المحتوى
  Widget _buildContentCard(ChildrenContent content) {
    final categoryColors =
        ChildrenCategories.categoryColors[content.category] ??
        ['#FF6B6B', '#4ECDC4'];

    return GestureDetector(
      onTap: () => _openContent(content),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(int.parse('0xFF${categoryColors[0].substring(1)}')),
              Color(int.parse('0xFF${categoryColors[1].substring(1)}')),
            ],
          ),
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: Color(
                int.parse('0xFF${categoryColors[0].substring(1)}'),
              ).withValues(alpha: 0.4),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      ContentTypes.typeIcons[content.type] ?? '📚',
                      style: const TextStyle(fontSize: 20),
                    ),
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${content.difficulty}⭐',
                      style: const TextStyle(
                        fontSize: 10,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              Center(
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    shape: BoxShape.circle,
                  ),
                  child: Text(
                    ChildrenCategories.categoryIcons[content.category] ?? '📚',
                    style: const TextStyle(fontSize: 32),
                  ),
                ),
              ),

              const SizedBox(height: 16),

              Text(
                content.title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 4),

              Text(
                content.content['ar'] ?? '',
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.white.withValues(alpha: 0.8),
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              const Spacer(),

              Row(
                children: [
                  Text(
                    'العمر: ${content.ageGroup}+',
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.white.withValues(alpha: 0.8),
                    ),
                  ),
                  const Spacer(),
                  if (content.isCompleted)
                    Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Colors.green,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.check,
                        size: 12,
                        color: Colors.white,
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
