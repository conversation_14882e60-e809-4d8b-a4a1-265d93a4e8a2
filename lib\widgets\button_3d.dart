import 'package:flutter/material.dart';
import '../config/app_theme.dart';

/// زر ثلاثي الأبعاد مخصص
class Button3D extends StatefulWidget {
  /// الإجراء الذي سيتم تنفيذه عند النقر على الزر
  final Function()? onPressed;

  /// لون الزر الرئيسي
  final Color? color;

  /// لون النص
  final Color? textColor;

  /// عرض الزر (اختياري)
  final double? width;

  /// ارتفاع الزر (اختياري)
  final double? height;

  /// حجم النص (اختياري)
  final double? fontSize;

  /// ما إذا كان الزر معطلاً
  final bool isDisabled;

  /// ما إذا كان الزر قيد التحميل
  final bool isLoading;

  /// نصف قطر الحواف
  final double borderRadius;

  /// عمق تأثير ثلاثي الأبعاد
  final double depth;

  /// محتوى الزر
  final Widget child;

  const Button3D({
    super.key,
    required this.onPressed,
    required this.child,
    this.color,
    this.textColor,
    this.width,
    this.height = 56.0,
    this.fontSize = 16.0,
    this.isDisabled = false,
    this.isLoading = false,
    this.borderRadius = 16.0,
    this.depth = 4.0,
  });

  @override
  State<Button3D> createState() => _Button3DState();
}

class _Button3DState extends State<Button3D>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _pressedAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 100),
    );
    _pressedAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (!widget.isDisabled && !widget.isLoading) {
      _animationController.forward();
    }
  }

  void _onTapUp(TapUpDetails details) {
    if (!widget.isDisabled && !widget.isLoading) {
      _animationController.reverse();
    }
  }

  void _onTapCancel() {
    if (!widget.isDisabled && !widget.isLoading) {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final Color buttonColor = widget.color ?? AppTheme.primaryColor;
    final Color textColor = widget.textColor ?? Colors.white;
    final Color shadowColor = Color.fromRGBO(0, 122, 255, 0.5);
    final Color disabledColor = Colors.grey.shade300;

    return GestureDetector(
      onTapDown: _onTapDown,
      onTapUp: _onTapUp,
      onTapCancel: _onTapCancel,
      onTap:
          widget.isDisabled || widget.isLoading || widget.onPressed == null
              ? null
              : widget.onPressed,
      child: AnimatedBuilder(
        animation: _pressedAnimation,
        builder: (context, child) {
          return Container(
            width: widget.width,
            height: widget.height,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(widget.borderRadius),
              boxShadow: [
                // الظل السفلي (تأثير العمق)
                BoxShadow(
                  color: widget.isDisabled ? Colors.grey.shade400 : shadowColor,
                  offset: Offset(
                    0,
                    widget.depth * (1 - _pressedAnimation.value),
                  ),
                  blurRadius: widget.depth,
                ),
              ],
            ),
            child: Transform.translate(
              offset: Offset(0, widget.depth * _pressedAnimation.value),
              child: Container(
                decoration: BoxDecoration(
                  color: widget.isDisabled ? disabledColor : buttonColor,
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                  boxShadow: [
                    // الظل العلوي (تأثير الإضاءة)
                    BoxShadow(
                      color: Color.fromRGBO(255, 255, 255, 0.3),
                      offset: const Offset(0, 2),
                      blurRadius: 5,
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Center(
                  child:
                      widget.isLoading
                          ? SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              color: textColor,
                              strokeWidth: 2,
                            ),
                          )
                          : widget.child,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
