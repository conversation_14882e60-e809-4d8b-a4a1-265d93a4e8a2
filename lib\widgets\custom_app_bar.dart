import 'package:flutter/material.dart';
import '../config/app_theme.dart';

/// شريط التطبيق المخصص
class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  /// عنوان شريط التطبيق
  final String title;
  
  /// إجراءات شريط التطبيق
  final List<Widget>? actions;
  
  /// زر الرجوع
  final bool showBackButton;
  
  /// لون الخلفية
  final Color? backgroundColor;
  
  /// لون النص
  final Color? foregroundColor;
  
  /// ارتفاع شريط التطبيق
  final double height;
  
  /// إجراء الضغط على زر الرجوع
  final VoidCallback? onBackPressed;
  
  /// الأيقونة المخصصة
  final Widget? leading;
  
  /// المحتوى المخصص
  final Widget? flexibleSpace;
  
  /// عنوان مخصص
  final Widget? titleWidget;
  
  /// الارتفاع
  final double? elevation;

  const CustomAppBar({
    super.key,
    required this.title,
    this.actions,
    this.showBackButton = true,
    this.backgroundColor,
    this.foregroundColor,
    this.height = kToolbarHeight,
    this.onBackPressed,
    this.leading,
    this.flexibleSpace,
    this.titleWidget,
    this.elevation,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: titleWidget ?? Text(title),
      actions: actions,
      backgroundColor: backgroundColor ?? AppTheme.primaryColor,
      foregroundColor: foregroundColor ?? Colors.white,
      elevation: elevation ?? 0,
      automaticallyImplyLeading: showBackButton,
      leading: leading ?? (showBackButton
          ? IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
            )
          : null),
      flexibleSpace: flexibleSpace,
      centerTitle: true,
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(height);
}
