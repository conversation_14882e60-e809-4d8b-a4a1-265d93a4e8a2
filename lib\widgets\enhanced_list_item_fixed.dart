import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../config/app_theme.dart';

/// عنصر قائمة محسن مع تأثيرات تفاعلية
class EnhancedListItem extends StatefulWidget {
  final Widget? leading;
  final String title;
  final Widget? subtitle;
  final Widget? trailing;
  final bool isThreeLine;
  final bool dense;
  final VisualDensity? visualDensity;
  final ShapeBorder? shape;
  final EdgeInsetsGeometry? contentPadding;
  final bool enabled;
  final bool enableHapticFeedback;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final Color? backgroundColor;
  final Color? selectedColor;
  final bool selected;
  final double elevation;
  final double selectedElevation;

  const EnhancedListItem({
    super.key,
    this.leading,
    required this.title,
    this.subtitle,
    this.trailing,
    this.isThreeLine = false,
    this.dense = false,
    this.visualDensity,
    this.shape,
    this.contentPadding,
    this.enabled = true,
    this.enableHapticFeedback = true,
    this.onTap,
    this.onLongPress,
    this.backgroundColor,
    this.selectedColor,
    this.selected = false,
    this.elevation = 0.0,
    this.selectedElevation = 2.0,
  });

  @override
  State<EnhancedListItem> createState() => _EnhancedListItemState();
}

class _EnhancedListItemState extends State<EnhancedListItem>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 100),
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (!widget.enabled) return;
    _controller.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    if (!widget.enabled) return;
    _controller.reverse();
  }

  void _handleTapCancel() {
    if (!widget.enabled) return;
    _controller.reverse();
  }

  void _handleTap() {
    if (!widget.enabled || widget.onTap == null) return;

    if (widget.enableHapticFeedback) {
      HapticFeedback.selectionClick();
    }

    widget.onTap!();
  }

  void _handleLongPress() {
    if (!widget.enabled || widget.onLongPress == null) return;

    if (widget.enableHapticFeedback) {
      HapticFeedback.mediumImpact();
    }

    widget.onLongPress!();
  }

  @override
  Widget build(BuildContext context) {
    final Color effectiveBackgroundColor =
        widget.selected
            ? widget.selectedColor ?? AppTheme.primaryColor.withAlpha(26)
            : widget.backgroundColor ?? Colors.white;

    final double effectiveElevation =
        widget.selected ? widget.selectedElevation : widget.elevation;

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Card(
            elevation: effectiveElevation,
            shape:
                widget.shape ??
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            color: effectiveBackgroundColor,
            child: InkWell(
              onTapDown: _handleTapDown,
              onTapUp: _handleTapUp,
              onTapCancel: _handleTapCancel,
              onTap: widget.onTap != null ? _handleTap : null,
              onLongPress: widget.onLongPress != null ? _handleLongPress : null,
              borderRadius: BorderRadius.circular(12),
              splashColor: AppTheme.primaryColor.withAlpha(51),
              highlightColor: AppTheme.primaryColor.withAlpha(26),
              child: Padding(
                padding:
                    widget.contentPadding ??
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                child: Row(
                  children: [
                    if (widget.leading != null) ...[
                      widget.leading!,
                      const SizedBox(width: 16),
                    ],
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            widget.title,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          if (widget.subtitle != null) ...[
                            const SizedBox(height: 4),
                            widget.subtitle!,
                          ],
                        ],
                      ),
                    ),
                    if (widget.trailing != null) widget.trailing!,
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
