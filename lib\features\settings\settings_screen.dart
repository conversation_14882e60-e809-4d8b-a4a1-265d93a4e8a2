import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../../core/app_state.dart';
import '../../config/app_theme.dart';
import '../../config/constants.dart';
import '../../utils/helpers.dart';
import '../../widgets/enhanced_list_item.dart';
import '../subscription/subscription_screen.dart';

/// شاشة الإعدادات
class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  String _appVersion = '';

  @override
  void initState() {
    super.initState();
    _getAppVersion();
  }

  /// الحصول على إصدار التطبيق
  Future<void> _getAppVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      setState(() {
        _appVersion = packageInfo.version;
      });
    } catch (e) {
      debugPrint('Error getting app version: $e');
    }
  }

  /// الحصول على نص وضع السمة
  String _getThemeModeText(String themeMode) {
    switch (themeMode) {
      case AppConstants.themeModeLight:
        return 'الوضع الفاتح';
      case AppConstants.themeModeDark:
        return 'الوضع الداكن';
      case AppConstants.themeModeAmoled:
        return 'وضع AMOLED الداكن';
      default:
        return 'الوضع الفاتح';
    }
  }

  /// عرض مربع حوار اختيار وضع السمة
  Future<void> _showThemeModeDialog(AppState appState) async {
    final selectedThemeMode = await showModalBottomSheet<String>(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => _buildThemeModeSelector(appState),
    );

    if (selectedThemeMode != null) {
      appState.setThemeMode(selectedThemeMode);
    }
  }

  /// بناء مربع حوار اختيار وضع السمة
  Widget _buildThemeModeSelector(AppState appState) {
    final themeModes = [
      {
        'code': AppConstants.themeModeLight,
        'name': 'الوضع الفاتح',
        'icon': Icons.light_mode,
      },
      {
        'code': AppConstants.themeModeDark,
        'name': 'الوضع الداكن',
        'icon': Icons.dark_mode,
      },
      {
        'code': AppConstants.themeModeAmoled,
        'name': 'وضع AMOLED الداكن',
        'icon': Icons.nights_stay,
        'description': 'أسود خالص لشاشات AMOLED',
      },
    ];

    final currentThemeMode = appState.themeMode;

    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'اختر وضع السمة',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
          const SizedBox(height: 20),
          ListView.builder(
            shrinkWrap: true,
            itemCount: themeModes.length,
            itemBuilder: (context, index) {
              final themeMode = themeModes[index];
              final isSelected = themeMode['code'] == currentThemeMode;

              return EnhancedListItem(
                leading: Icon(
                  themeMode['icon'] as IconData,
                  color: isSelected ? AppTheme.primaryColor : null,
                ),
                title: themeMode['name'] as String,
                subtitle: themeMode['description'] as String?,
                isSelected: isSelected,
                trailing:
                    isSelected
                        ? const Icon(
                          Icons.check_circle,
                          color: AppTheme.primaryColor,
                        )
                        : null,
                onTap: () {
                  HapticFeedback.selectionClick();
                  Navigator.pop(context, themeMode['code'] as String);
                },
                borderRadius: 12,
                backgroundColor:
                    isSelected ? AppTheme.primaryColor.withAlpha(26) : null,
              );
            },
          ),
        ],
      ),
    );
  }

  /// تغيير اللغة
  Future<void> _changeLanguage(AppState appState) async {
    final selectedLanguage = await showModalBottomSheet<String>(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => _buildLanguageSelector(),
    );

    if (selectedLanguage != null) {
      appState.setLanguage(selectedLanguage);
    }
  }

  /// بناء مربع حوار اختيار اللغة
  Widget _buildLanguageSelector() {
    // نستخدم فقط العربية والإنجليزية للتبسيط
    final languages = [
      {'code': 'ar', 'name': 'العربية', 'flag': '🇸🇦'},
      {'code': 'en', 'name': 'English', 'flag': '🇺🇸'},
    ];

    final appState = Provider.of<AppState>(context, listen: false);
    final currentLanguage = appState.selectedLanguage;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text(
            'اختر اللغة',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),
          ListView.builder(
            shrinkWrap: true,
            itemCount: languages.length,
            itemBuilder: (context, index) {
              final language = languages[index];
              final isSelected = language['code'] == currentLanguage;

              return EnhancedListItem(
                leading: Text(
                  language['flag'] as String,
                  style: const TextStyle(fontSize: 24),
                ),
                title: language['name'] as String,
                isSelected: isSelected,
                trailing:
                    isSelected
                        ? const Icon(
                          Icons.check_circle,
                          color: AppTheme.primaryColor,
                        )
                        : null,
                onTap: () {
                  HapticFeedback.selectionClick();
                  Navigator.pop(context, language['code'] as String);
                },
                borderRadius: 12,
                backgroundColor:
                    isSelected ? AppTheme.primaryColor.withAlpha(26) : null,
              );
            },
          ),
        ],
      ),
    );
  }

  /// فتح صفحة الاشتراكات
  void _openSubscriptions() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (_) => const SubscriptionScreen()));
  }

  /// فتح صفحة سياسة الخصوصية
  void _openPrivacyPolicy() {
    AppHelpers.launchURL(AppConstants.privacyPolicyUrl);
  }

  /// فتح صفحة شروط الاستخدام
  void _openTermsOfService() {
    AppHelpers.launchURL(AppConstants.termsOfServiceUrl);
  }

  /// فتح صفحة الدعم الفني
  void _openSupport() {
    AppHelpers.launchURL(AppConstants.supportUrl);
  }

  /// فتح صفحة اتصل بنا
  void _openContactUs() {
    AppHelpers.launchURL(AppConstants.contactUsUrl);
  }

  /// تقييم التطبيق
  void _rateApp() {
    // تنفيذ فتح صفحة التطبيق في متجر التطبيقات
    final snackBar = SnackBar(
      content: const Text('سيتم فتح صفحة التطبيق في متجر التطبيقات قريباً'),
      backgroundColor: AppTheme.primaryColor,
      behavior: SnackBarBehavior.floating,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      action: SnackBarAction(
        label: 'حسناً',
        textColor: Colors.white,
        onPressed: () {},
      ),
    );
    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }

  /// مشاركة التطبيق
  void _shareApp() {
    // تنفيذ مشاركة رابط التطبيق
    final snackBar = SnackBar(
      content: const Text('سيتم مشاركة رابط التطبيق قريباً'),
      backgroundColor: AppTheme.primaryColor,
      behavior: SnackBarBehavior.floating,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      action: SnackBarAction(
        label: 'حسناً',
        textColor: Colors.white,
        onPressed: () {},
      ),
    );
    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }

  /// تسجيل الخروج
  Future<void> _signOut(AppState appState) async {
    final confirm = await AppHelpers.showConfirmDialog(
      context,
      title: 'تسجيل الخروج',
      message: 'هل أنت متأكد من رغبتك في تسجيل الخروج؟',
    );

    if (confirm) {
      await appState.signOut();
      if (mounted) {
        Navigator.of(context).popUntil((route) => route.isFirst);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppState>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('الإعدادات'),
        centerTitle: true,
        backgroundColor: AppTheme.primaryColor,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // قسم المظهر
          _buildSectionTitle('المظهر'),

          // وضع السمة (فاتح/داكن)
          _buildSettingItem(
            icon: Icons.dark_mode,
            title: 'وضع السمة',
            subtitle: _getThemeModeText(appState.themeMode),
            onTap: () => _showThemeModeDialog(appState),
          ),

          // اللغة
          _buildSettingItem(
            icon: Icons.language,
            title: 'اللغة',
            subtitle: appState.selectedLanguage == 'ar' ? 'العربية' : 'English',
            onTap: () => _changeLanguage(appState),
          ),

          const Divider(),

          // قسم الاشتراك
          _buildSectionTitle('الاشتراك'),

          // حالة الاشتراك
          _buildSettingItem(
            icon: Icons.workspace_premium,
            title: 'حالة الاشتراك',
            subtitle:
                appState.isPremium
                    ? 'مشترك في الخطة المميزة'
                    : 'الخطة المجانية',
            onTap: _openSubscriptions,
          ),

          // إزالة الإعلانات
          _buildSettingItem(
            icon: Icons.block,
            title: 'الإعلانات',
            subtitle:
                appState.isAdsRemoved
                    ? 'تم إزالة الإعلانات'
                    : 'الإعلانات مفعلة',
            onTap: _openSubscriptions,
          ),

          const Divider(),

          // قسم المعلومات
          _buildSectionTitle('معلومات'),

          // سياسة الخصوصية
          _buildSettingItem(
            icon: Icons.privacy_tip,
            title: 'سياسة الخصوصية',
            onTap: _openPrivacyPolicy,
          ),

          // شروط الاستخدام
          _buildSettingItem(
            icon: Icons.description,
            title: 'شروط الاستخدام',
            onTap: _openTermsOfService,
          ),

          // الدعم الفني
          _buildSettingItem(
            icon: Icons.support_agent,
            title: 'الدعم الفني',
            onTap: _openSupport,
          ),

          // اتصل بنا
          _buildSettingItem(
            icon: Icons.email,
            title: 'اتصل بنا',
            onTap: _openContactUs,
          ),

          const Divider(),

          // قسم التطبيق
          _buildSectionTitle('التطبيق'),

          // تقييم التطبيق
          _buildSettingItem(
            icon: Icons.star,
            title: 'تقييم التطبيق',
            onTap: _rateApp,
          ),

          // مشاركة التطبيق
          _buildSettingItem(
            icon: Icons.share,
            title: 'مشاركة التطبيق',
            onTap: _shareApp,
          ),

          // إصدار التطبيق
          _buildSettingItem(
            icon: Icons.info,
            title: 'إصدار التطبيق',
            subtitle: _appVersion,
          ),

          const Divider(),

          // تسجيل الخروج
          _buildSettingItem(
            icon: Icons.logout,
            title: 'تسجيل الخروج',
            iconColor: Colors.red,
            titleColor: Colors.red,
            onTap: () => _signOut(appState),
          ),
        ],
      ),
    );
  }

  /// بناء عنوان القسم
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppTheme.primaryColor,
        ),
      ),
    );
  }

  /// بناء عنصر الإعدادات
  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    String? subtitle,
    Widget? trailing,
    Color iconColor = AppTheme.primaryColor,
    Color titleColor = AppTheme.onBackground,
    VoidCallback? onTap,
  }) {
    return EnhancedListItem(
      icon: icon,
      title: title,
      subtitle: subtitle,
      trailing:
          trailing ??
          (onTap != null
              ? const Icon(Icons.arrow_forward_ios, size: 16)
              : null),
      onTap:
          onTap != null
              ? () {
                HapticFeedback.selectionClick();
                onTap();
              }
              : null,
      iconColor: iconColor,
      titleColor: titleColor,
      borderRadius: 12,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      backgroundColor:
          Theme.of(context).brightness == Brightness.dark
              ? Colors.black12
              : Colors.white,
    );
  }
}
