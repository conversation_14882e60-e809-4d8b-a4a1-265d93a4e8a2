import 'package:flutter/material.dart';
import 'enhanced_list_item.dart';

/// قائمة محسنة مع تأثيرات بصرية ولمسية
class EnhancedMenu extends StatelessWidget {
  /// عناصر القائمة
  final List<EnhancedMenuItem> items;

  /// عنوان القائمة (اختياري)
  final String? title;

  /// تباعد داخلي
  final EdgeInsetsGeometry padding;

  /// تباعد بين العناصر
  final double itemSpacing;

  /// نصف قطر الحواف
  final double borderRadius;

  /// لون الخلفية
  final Color? backgroundColor;

  /// ارتفاع العنصر
  final double? itemHeight;

  /// منشئ القائمة المحسنة
  const EnhancedMenu({
    super.key,
    required this.items,
    this.title,
    this.padding = const EdgeInsets.all(16),
    this.itemSpacing = 8,
    this.borderRadius = 12,
    this.backgroundColor,
    this.itemHeight,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: padding,
      decoration: BoxDecoration(
        color: backgroundColor ?? theme.cardColor,
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // عنوان القائمة (إذا كان موجوداً)
          if (title != null) ...[
            Padding(
              padding: const EdgeInsets.only(bottom: 16, right: 8),
              child: Text(
                title!,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: theme.textTheme.titleLarge?.color,
                ),
              ),
            ),
          ],

          // عناصر القائمة
          ...List.generate(items.length, (index) {
            final item = items[index];

            // إضافة مسافة بين العناصر
            if (index > 0) {
              return Padding(
                padding: EdgeInsets.only(top: itemSpacing),
                child: _buildMenuItem(item, theme),
              );
            }

            return _buildMenuItem(item, theme);
          }),
        ],
      ),
    );
  }

  /// بناء عنصر القائمة
  Widget _buildMenuItem(EnhancedMenuItem item, ThemeData theme) {
    return EnhancedListItem(
      icon: item.icon,
      leading: item.leading,
      title: item.title,
      subtitle: item.subtitle,
      trailing: item.trailing,
      onTap: item.onTap,
      onLongPress: item.onLongPress,
      backgroundColor: item.backgroundColor,
      splashColor: item.splashColor,
      iconColor: item.iconColor,
      titleColor: item.titleColor,
      subtitleColor: item.subtitleColor,
      iconSize: item.iconSize,
      titleSize: item.titleSize,
      subtitleSize: item.subtitleSize,
      height: itemHeight,
      borderRadius: borderRadius,
      isDisabled: item.isDisabled,
      isSelected: item.isSelected,
    );
  }
}

/// عنصر القائمة المحسنة
class EnhancedMenuItem {
  /// أيقونة العنصر
  final IconData? icon;

  /// صورة العنصر (بديل للأيقونة)
  final Widget? leading;

  /// عنوان العنصر
  final String title;

  /// وصف العنصر (اختياري)
  final String? subtitle;

  /// عنصر في نهاية العنصر (اختياري)
  final Widget? trailing;

  /// دالة يتم تنفيذها عند الضغط على العنصر
  final VoidCallback? onTap;

  /// دالة يتم تنفيذها عند الضغط المطول على العنصر
  final VoidCallback? onLongPress;

  /// لون خلفية العنصر
  final Color? backgroundColor;

  /// لون العنصر عند الضغط عليه
  final Color? splashColor;

  /// لون الأيقونة
  final Color? iconColor;

  /// لون العنوان
  final Color? titleColor;

  /// لون الوصف
  final Color? subtitleColor;

  /// حجم الأيقونة
  final double? iconSize;

  /// حجم خط العنوان
  final double? titleSize;

  /// حجم خط الوصف
  final double? subtitleSize;

  /// ما إذا كان العنصر معطلاً
  final bool isDisabled;

  /// ما إذا كان العنصر محدداً
  final bool isSelected;

  /// منشئ عنصر القائمة المحسنة
  const EnhancedMenuItem({
    this.icon,
    this.leading,
    required this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.onLongPress,
    this.backgroundColor,
    this.splashColor,
    this.iconColor,
    this.titleColor,
    this.subtitleColor,
    this.iconSize,
    this.titleSize,
    this.subtitleSize,
    this.isDisabled = false,
    this.isSelected = false,
  });
}

/// عرض قائمة منبثقة محسنة
Future<T?> showEnhancedMenu<T>({
  required BuildContext context,
  required List<EnhancedMenuItem> items,
  String? title,
  double? height,
  double? width,
  Color? backgroundColor,
  double borderRadius = 20,
  bool isDismissible = true,
  bool enableDrag = true,
}) {
  return showModalBottomSheet<T>(
    context: context,
    backgroundColor: Colors.transparent,
    isScrollControlled: true,
    isDismissible: isDismissible,
    enableDrag: enableDrag,
    builder: (context) {
      return Container(
        height: height,
        width: width,
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        child: EnhancedMenu(
          items: items,
          title: title,
          borderRadius: borderRadius,
          backgroundColor: backgroundColor,
        ),
      );
    },
  );
}
