import 'package:uuid/uuid.dart';

/// نموذج عنصر الترجمة
class TranslationItem {
  final String id;
  final String sourceText;
  final String translatedText;
  final String sourceLanguage;
  final String targetLanguage;
  final String type;
  final DateTime timestamp;
  final bool isFavorite;
  final Map<String, dynamic>? metadata;

  TranslationItem({
    String? id,
    required this.sourceText,
    required this.translatedText,
    required this.sourceLanguage,
    required this.targetLanguage,
    required this.type,
    DateTime? timestamp,
    this.isFavorite = false,
    this.metadata,
  })  : id = id ?? const Uuid().v4(),
        timestamp = timestamp ?? DateTime.now();

  /// إنشاء نسخة جديدة مع تعديل بعض الخصائص
  TranslationItem copyWith({
    String? id,
    String? sourceText,
    String? translatedText,
    String? sourceLanguage,
    String? targetLanguage,
    String? type,
    DateTime? timestamp,
    bool? isFavorite,
    Map<String, dynamic>? metadata,
  }) {
    return TranslationItem(
      id: id ?? this.id,
      sourceText: sourceText ?? this.sourceText,
      translatedText: translatedText ?? this.translatedText,
      sourceLanguage: sourceLanguage ?? this.sourceLanguage,
      targetLanguage: targetLanguage ?? this.targetLanguage,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      isFavorite: isFavorite ?? this.isFavorite,
      metadata: metadata ?? this.metadata,
    );
  }

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'sourceText': sourceText,
      'translatedText': translatedText,
      'sourceLanguage': sourceLanguage,
      'targetLanguage': targetLanguage,
      'type': type,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'isFavorite': isFavorite,
      'metadata': metadata,
    };
  }

  /// إنشاء نموذج من Map
  factory TranslationItem.fromMap(Map<String, dynamic> map) {
    return TranslationItem(
      id: map['id'],
      sourceText: map['sourceText'],
      translatedText: map['translatedText'],
      sourceLanguage: map['sourceLanguage'],
      targetLanguage: map['targetLanguage'],
      type: map['type'],
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['timestamp']),
      isFavorite: map['isFavorite'] ?? false,
      metadata: map['metadata'],
    );
  }

  /// تحويل النموذج إلى JSON
  Map<String, dynamic> toJson() => toMap();

  /// إنشاء نموذج من JSON
  factory TranslationItem.fromJson(Map<String, dynamic> json) => TranslationItem.fromMap(json);

  @override
  String toString() {
    return 'TranslationItem(id: $id, sourceText: $sourceText, translatedText: $translatedText, sourceLanguage: $sourceLanguage, targetLanguage: $targetLanguage, type: $type, timestamp: $timestamp, isFavorite: $isFavorite)';
  }
}
