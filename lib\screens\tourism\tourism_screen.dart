import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../config/app_theme.dart';
import '../../models/tourism_phrase.dart';
import '../../services/tourism_service.dart';
import '../../utils/app_helpers.dart';

/// شاشة العبارات السياحية
class TourismScreen extends StatefulWidget {
  const TourismScreen({super.key});

  @override
  State<TourismScreen> createState() => _TourismScreenState();
}

class _TourismScreenState extends State<TourismScreen>
    with TickerProviderStateMixin {
  final TourismService _tourismService = TourismService();
  final TextEditingController _searchController = TextEditingController();

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  List<TourismPhrase> _allPhrases = [];
  List<TourismPhrase> _filteredPhrases = [];
  List<TourismPhrase> _favoritePhrases = [];

  String _selectedCategory = '';
  String _selectedSubcategory = '';
  String _sourceLanguage = 'ar'; // اللغة المصدر
  String _targetLanguage = 'en'; // اللغة الهدف
  bool _isLoading = true;
  bool _showFavoritesOnly = false;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _initializeTourismService();
    _loadLanguagePreferences();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  /// تهيئة الرسوم المتحركة
  void _initAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutQuart),
    );

    _animationController.forward();
  }

  /// تهيئة خدمة السياحة
  Future<void> _initializeTourismService() async {
    try {
      await _tourismService.initialize();
      setState(() {
        _allPhrases = _tourismService.getAllPhrases();
        _filteredPhrases = _allPhrases;
        _favoritePhrases = _tourismService.getFavoritePhrases();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ في تحميل العبارات السياحية',
          isError: true,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body:
          _isLoading
              ? _buildLoadingState()
              : FadeTransition(
                opacity: _fadeAnimation,
                child: Column(
                  children: [
                    _buildSearchAndFilters(),
                    _buildLanguageSelector(),
                    _buildCategoryTabs(),
                    Expanded(child: _buildPhrasesList()),
                  ],
                ),
              ),
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text('العبارات السياحية'),
      backgroundColor: AppTheme.primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        IconButton(
          icon: Icon(
            _showFavoritesOnly ? Icons.favorite : Icons.favorite_border,
            color: _showFavoritesOnly ? Colors.red : Colors.white,
          ),
          onPressed: () {
            HapticFeedback.lightImpact();
            setState(() {
              _showFavoritesOnly = !_showFavoritesOnly;
              _filterPhrases();
            });
          },
          tooltip: _showFavoritesOnly ? 'عرض الكل' : 'عرض المفضلة فقط',
        ),
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: () {
            HapticFeedback.lightImpact();
            _refreshData();
          },
          tooltip: 'تحديث',
        ),
      ],
    );
  }

  /// بناء حالة التحميل
  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
          const SizedBox(height: 16),
          const Text(
            'جاري تحميل العبارات السياحية...',
            style: TextStyle(fontSize: 16),
          ),
        ],
      ),
    );
  }

  /// بناء البحث والفلاتر
  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withValues(alpha: 0.05),
        border: Border(
          bottom: BorderSide(color: Colors.grey.withValues(alpha: 0.2)),
        ),
      ),
      child: Column(
        children: [
          // شريط البحث
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'ابحث في العبارات السياحية...',
                prefixIcon: Icon(Icons.search, color: AppTheme.primaryColor),
                suffixIcon:
                    _searchController.text.isNotEmpty
                        ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            _searchController.clear();
                            _filterPhrases();
                          },
                        )
                        : null,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 15,
                ),
              ),
              onChanged: (_) => _filterPhrases(),
            ),
          ),

          const SizedBox(height: 12),

          // إحصائيات سريعة
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatCard(
                'إجمالي العبارات',
                _allPhrases.length.toString(),
                Icons.translate,
                Colors.blue,
              ),
              _buildStatCard(
                'المفضلة',
                _favoritePhrases.length.toString(),
                Icons.favorite,
                Colors.red,
              ),
              _buildStatCard(
                'الفئات',
                TourismCategories.categoryNames.length.toString(),
                Icons.category,
                Colors.green,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(fontSize: 10, color: color.withValues(alpha: 0.8)),
          ),
        ],
      ),
    );
  }

  /// بناء قائمة اختيار اللغات المحسنة
  Widget _buildLanguageSelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // عنوان القسم
          Row(
            children: [
              Icon(Icons.translate, color: AppTheme.primaryColor, size: 20),
              const SizedBox(width: 8),
              const Text(
                'اختيار اللغات',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // صف اختيار اللغات
          Row(
            children: [
              // اللغة المصدر
              Expanded(
                child: _buildLanguageDropdown(
                  title: 'من',
                  value: _sourceLanguage,
                  onChanged: (value) {
                    setState(() {
                      _sourceLanguage = value!;
                      _saveLanguagePreferences();
                    });
                  },
                ),
              ),

              const SizedBox(width: 16),

              // زر التبديل
              GestureDetector(
                onTap: () {
                  HapticFeedback.lightImpact();
                  setState(() {
                    final temp = _sourceLanguage;
                    _sourceLanguage = _targetLanguage;
                    _targetLanguage = temp;
                    _saveLanguagePreferences();
                  });
                },
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.swap_horiz,
                    color: AppTheme.primaryColor,
                    size: 20,
                  ),
                ),
              ),

              const SizedBox(width: 16),

              // اللغة الهدف
              Expanded(
                child: _buildLanguageDropdown(
                  title: 'إلى',
                  value: _targetLanguage,
                  onChanged: (value) {
                    setState(() {
                      _targetLanguage = value!;
                      _saveLanguagePreferences();
                    });
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء قائمة منسدلة للغة
  Widget _buildLanguageDropdown({
    required String title,
    required String value,
    required ValueChanged<String?> onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
            borderRadius: BorderRadius.circular(8),
            color: Colors.grey.withValues(alpha: 0.05),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: value,
              isExpanded: true,
              icon: Icon(Icons.arrow_drop_down, color: AppTheme.primaryColor),
              style: const TextStyle(fontSize: 14, color: Colors.black87),
              onChanged: onChanged,
              items:
                  SupportedLanguages.getAllLanguages().map((code) {
                    return DropdownMenuItem<String>(
                      value: code,
                      child: Row(
                        children: [
                          Text(
                            SupportedLanguages.getLanguageFlag(code),
                            style: const TextStyle(fontSize: 16),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              SupportedLanguages.getLanguageName(code),
                              style: const TextStyle(fontSize: 13),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  /// حفظ تفضيلات اللغة
  Future<void> _saveLanguagePreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('source_language', _sourceLanguage);
      await prefs.setString('target_language', _targetLanguage);
      debugPrint('تم حفظ تفضيلات اللغة: $_sourceLanguage -> $_targetLanguage');
    } catch (e) {
      debugPrint('خطأ في حفظ تفضيلات اللغة: $e');
    }
  }

  /// تحميل تفضيلات اللغة
  Future<void> _loadLanguagePreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      setState(() {
        _sourceLanguage = prefs.getString('source_language') ?? 'ar';
        _targetLanguage = prefs.getString('target_language') ?? 'en';
      });
      debugPrint(
        'تم تحميل تفضيلات اللغة: $_sourceLanguage -> $_targetLanguage',
      );
    } catch (e) {
      debugPrint('خطأ في تحميل تفضيلات اللغة: $e');
    }
  }

  /// بناء تبويبات الفئات
  Widget _buildCategoryTabs() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: TourismCategories.categoryNames.length + 1,
        itemBuilder: (context, index) {
          if (index == 0) {
            return _buildCategoryTab('', 'الكل', '🌍');
          }

          final category = TourismCategories.categoryNames.keys.elementAt(
            index - 1,
          );
          final name = TourismCategories.categoryNames[category]!;
          final icon = TourismCategories.categoryIcons[category]!;

          return _buildCategoryTab(category, name, icon);
        },
      ),
    );
  }

  /// بناء تبويب فئة
  Widget _buildCategoryTab(String category, String name, String icon) {
    final isSelected = _selectedCategory == category;

    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        setState(() {
          _selectedCategory = category;
          _selectedSubcategory = '';
          _filterPhrases();
        });
      },
      child: Container(
        margin: const EdgeInsets.only(right: 8),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          gradient:
              isSelected
                  ? LinearGradient(
                    colors: [
                      AppTheme.primaryColor,
                      AppTheme.primaryColor.withValues(alpha: 0.8),
                    ],
                  )
                  : null,
          color: isSelected ? null : Colors.grey.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color:
                isSelected
                    ? AppTheme.primaryColor
                    : Colors.grey.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(icon, style: const TextStyle(fontSize: 16)),
            const SizedBox(width: 6),
            Text(
              name,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.black87,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قائمة العبارات
  Widget _buildPhrasesList() {
    if (_filteredPhrases.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredPhrases.length,
      itemBuilder: (context, index) {
        return _buildPhraseCard(_filteredPhrases[index]);
      },
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              _showFavoritesOnly ? Icons.favorite_border : Icons.search_off,
              size: 64,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            _showFavoritesOnly
                ? 'لا توجد عبارات مفضلة'
                : 'لم يتم العثور على عبارات',
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            _showFavoritesOnly
                ? 'أضف بعض العبارات إلى المفضلة لتظهر هنا'
                : 'جرب البحث بكلمات مختلفة أو اختر فئة أخرى',
            style: const TextStyle(color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة العبارة
  Widget _buildPhraseCard(TourismPhrase phrase) {
    final isFavorite = _tourismService.isFavorite(phrase.id);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _showPhraseDetails(phrase),
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // رأس البطاقة
                Row(
                  children: [
                    // أيقونة الفئة
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        TourismCategories.categoryIcons[phrase.category] ??
                            '💬',
                        style: const TextStyle(fontSize: 16),
                      ),
                    ),

                    const SizedBox(width: 12),

                    // معلومات الفئة
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            TourismCategories.categoryNames[phrase.category] ??
                                phrase.category,
                            style: TextStyle(
                              fontSize: 12,
                              color: AppTheme.primaryColor,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          if (phrase.subcategory.isNotEmpty)
                            Text(
                              phrase.subcategory,
                              style: const TextStyle(
                                fontSize: 10,
                                color: Colors.grey,
                              ),
                            ),
                        ],
                      ),
                    ),

                    // زر المفضلة
                    IconButton(
                      icon: Icon(
                        isFavorite ? Icons.favorite : Icons.favorite_border,
                        color: isFavorite ? Colors.red : Colors.grey,
                      ),
                      onPressed: () => _toggleFavorite(phrase),
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                // النص باللغة الهدف
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.green.withValues(alpha: 0.2),
                    ),
                  ),
                  child: Row(
                    children: [
                      Text(
                        SupportedLanguages.getLanguageFlag(_targetLanguage),
                        style: const TextStyle(fontSize: 16),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          phrase.translations[_targetLanguage] ??
                              phrase.translations['en'] ??
                              '',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.volume_up, color: Colors.green),
                        onPressed:
                            () => _speakPhrase(
                              phrase.translations[_targetLanguage] ??
                                  phrase.translations['en'] ??
                                  '',
                              _targetLanguage,
                            ),
                        tooltip: 'استمع للنطق',
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // النص العربي
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.blue.withValues(alpha: 0.2),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          phrase.translations['ar'] ?? '',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.volume_up, color: Colors.blue),
                        onPressed:
                            () => _speakPhrase(
                              phrase.translations['ar'] ?? '',
                              'ar',
                            ),
                        tooltip: 'استمع للنطق العربي',
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 8),

                // النص باللغة المصدر
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.green.withValues(alpha: 0.2),
                    ),
                  ),
                  child: Row(
                    children: [
                      Text(
                        SupportedLanguages.getLanguageFlag(_sourceLanguage),
                        style: const TextStyle(fontSize: 16),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          phrase.translations[_sourceLanguage] ??
                              phrase.translations['ar'] ??
                              '',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.volume_up, color: Colors.blue),
                        onPressed:
                            () => _speakPhrase(
                              phrase.translations[_sourceLanguage] ??
                                  phrase.translations['ar'] ??
                                  '',
                              _sourceLanguage,
                            ),
                        tooltip: 'استمع للنطق',
                      ),
                    ],
                  ),
                ),

                // النطق الصوتي
                if (phrase.pronunciation.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.05),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      'النطق: ${phrase.pronunciation}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.orange.shade700,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// فلترة العبارات
  void _filterPhrases() {
    setState(() {
      _filteredPhrases =
          _allPhrases.where((phrase) {
            // فلترة حسب المفضلة
            if (_showFavoritesOnly && !_tourismService.isFavorite(phrase.id)) {
              return false;
            }

            // فلترة حسب الفئة
            if (_selectedCategory.isNotEmpty &&
                phrase.category != _selectedCategory) {
              return false;
            }

            // فلترة حسب الفئة الفرعية
            if (_selectedSubcategory.isNotEmpty &&
                phrase.subcategory != _selectedSubcategory) {
              return false;
            }

            // فلترة حسب البحث
            if (_searchController.text.isNotEmpty) {
              final query = _searchController.text.toLowerCase();
              return phrase.translations.values.any(
                (text) => text.toLowerCase().contains(query),
              );
            }

            return true;
          }).toList();
    });
  }

  /// تبديل المفضلة
  Future<void> _toggleFavorite(TourismPhrase phrase) async {
    HapticFeedback.lightImpact();

    try {
      await _tourismService.toggleFavorite(phrase);
      setState(() {
        _favoritePhrases = _tourismService.getFavoritePhrases();
      });

      final isFavorite = _tourismService.isFavorite(phrase.id);
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          isFavorite
              ? 'تم إضافة العبارة للمفضلة'
              : 'تم إزالة العبارة من المفضلة',
        );
      }

      _filterPhrases();
    } catch (e) {
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ في تحديث المفضلة',
          isError: true,
        );
      }
    }
  }

  /// نطق العبارة
  Future<void> _speakPhrase(String text, String language) async {
    HapticFeedback.lightImpact();

    try {
      await _tourismService.speakPhrase(text, language);
    } catch (e) {
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ في تشغيل الصوت',
          isError: true,
        );
      }
    }
  }

  /// عرض تفاصيل العبارة
  void _showPhraseDetails(TourismPhrase phrase) {
    HapticFeedback.lightImpact();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildPhraseDetailsSheet(phrase),
    );
  }

  /// بناء ورقة تفاصيل العبارة
  Widget _buildPhraseDetailsSheet(TourismPhrase phrase) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // مقبض السحب
          Container(
            margin: const EdgeInsets.symmetric(vertical: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // محتوى التفاصيل
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // العنوان
                  Row(
                    children: [
                      Text(
                        TourismCategories.categoryIcons[phrase.category] ??
                            '💬',
                        style: const TextStyle(fontSize: 24),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              TourismCategories.categoryNames[phrase
                                      .category] ??
                                  phrase.category,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            if (phrase.subcategory.isNotEmpty)
                              Text(
                                phrase.subcategory,
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey,
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // النصوص والنطق
                  _buildLanguageSection(
                    'العربية',
                    phrase.translations['ar'] ?? '',
                    'ar',
                  ),
                  const SizedBox(height: 16),
                  _buildLanguageSection(
                    'English',
                    phrase.translations['en'] ?? '',
                    'en',
                  ),

                  if (phrase.pronunciation.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    _buildPronunciationSection(phrase.pronunciation),
                  ],

                  const Spacer(),

                  // أزرار الإجراءات
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _toggleFavorite(phrase),
                          icon: Icon(
                            _tourismService.isFavorite(phrase.id)
                                ? Icons.favorite
                                : Icons.favorite_border,
                          ),
                          label: Text(
                            _tourismService.isFavorite(phrase.id)
                                ? 'إزالة من المفضلة'
                                : 'إضافة للمفضلة',
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor:
                                _tourismService.isFavorite(phrase.id)
                                    ? Colors.red
                                    : AppTheme.primaryColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      ElevatedButton.icon(
                        onPressed: () {
                          // نسخ النص
                          Clipboard.setData(
                            ClipboardData(
                              text:
                                  '${phrase.translations['ar']}\n${phrase.translations['en']}',
                            ),
                          );
                          AppHelpers.showSnackBar(context, 'تم نسخ العبارة');
                        },
                        icon: const Icon(Icons.copy),
                        label: const Text('نسخ'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.grey.shade600,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قسم اللغة
  Widget _buildLanguageSection(String language, String text, String langCode) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 6),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient:
            langCode == 'ar'
                ? const LinearGradient(
                  colors: [Color(0xFFe3f0ff), Color(0xFFf7fbff)],
                )
                : const LinearGradient(
                  colors: [Color(0xFFeaffea), Color(0xFFf7fff7)],
                ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color:
              langCode == 'ar'
                  ? Colors.blue.withOpacity(0.15)
                  : Colors.green.withOpacity(0.15),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(
            langCode == 'ar' ? Icons.language : Icons.translate,
            color: langCode == 'ar' ? Colors.blue : Colors.green,
            size: 22,
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              text,
              textDirection:
                  langCode == 'ar' ? TextDirection.rtl : TextDirection.ltr,
              style: TextStyle(
                fontFamily: langCode == 'ar' ? 'Cairo' : null,
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color:
                    langCode == 'ar'
                        ? Colors.blue.shade900
                        : Colors.green.shade900,
              ),
            ),
          ),
          IconButton(
            icon: Icon(Icons.copy, color: Colors.grey.shade600),
            onPressed: () => _copyText(text),
            tooltip: 'نسخ',
          ),
          IconButton(
            icon: Icon(
              Icons.volume_up,
              color: langCode == 'ar' ? Colors.blue : Colors.green,
            ),
            onPressed: () => _speakPhrase(text, langCode),
            tooltip: 'استمع للنطق',
          ),
        ],
      ),
    );
  }

  /// بناء قسم النطق
  Widget _buildPronunciationSection(String pronunciation) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'النطق الصوتي',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.orange.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            pronunciation,
            style: TextStyle(
              fontSize: 16,
              fontStyle: FontStyle.italic,
              color: Colors.orange.shade800,
            ),
          ),
        ],
      ),
    );
  }

  /// تحديث البيانات
  Future<void> _refreshData() async {
    setState(() {
      _isLoading = true;
    });

    await _initializeTourismService();

    if (mounted) {
      AppHelpers.showSnackBar(context, 'تم تحديث البيانات بنجاح');
    }
  }

  void _copyText(String text) {
    Clipboard.setData(ClipboardData(text: text));
    AppHelpers.showSnackBar(context, 'تم نسخ العبارة');
  }
}
