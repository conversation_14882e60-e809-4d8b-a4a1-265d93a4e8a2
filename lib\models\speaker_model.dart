/// نموذج المتحدث
class Speaker {
  final String id;
  final String name;
  final String? profileId;
  final DateTime createdAt;
  final Map<String, dynamic>? metadata;

  Speaker({
    required this.id,
    required this.name,
    this.profileId,
    DateTime? createdAt,
    this.metadata,
  }) : createdAt = createdAt ?? DateTime.now();

  /// إنشاء نسخة جديدة مع تعديل بعض الخصائص
  Speaker copyWith({
    String? id,
    String? name,
    String? profileId,
    DateTime? createdAt,
    Map<String, dynamic>? metadata,
  }) {
    return Speaker(
      id: id ?? this.id,
      name: name ?? this.name,
      profileId: profileId ?? this.profileId,
      createdAt: createdAt ?? this.createdAt,
      metadata: metadata ?? this.metadata,
    );
  }

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'profileId': profileId,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'metadata': metadata,
    };
  }

  /// إنشاء نموذج من Map
  factory Speaker.fromMap(Map<String, dynamic> map) {
    return Speaker(
      id: map['id'],
      name: map['name'],
      profileId: map['profileId'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      metadata: map['metadata'],
    );
  }

  /// تحويل النموذج إلى JSON
  Map<String, dynamic> toJson() => toMap();

  /// إنشاء نموذج من JSON
  factory Speaker.fromJson(Map<String, dynamic> json) => Speaker.fromMap(json);

  @override
  String toString() {
    return 'Speaker(id: $id, name: $name, profileId: $profileId, createdAt: $createdAt)';
  }
}
