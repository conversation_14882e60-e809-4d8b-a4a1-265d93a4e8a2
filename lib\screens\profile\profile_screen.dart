import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../providers/app_state.dart';
import '../../config/app_theme.dart';
import '../../config/constants.dart';
import '../../utils/app_helpers.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/custom_button.dart';
import '../auth/simple_login_screen.dart';
import '../subscription/subscription_screen.dart';
import '../legal/terms_of_service_screen.dart';
import '../legal/privacy_policy_screen.dart';

/// شاشة الملف الشخصي
class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  // مثيل Firebase Auth
  final _auth = FirebaseAuth.instance;

  // حالة تحميل تسجيل الخروج
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppState>(context);
    final user = appState.currentUser;

    return Scaffold(
      appBar: const CustomAppBar(title: 'الملف الشخصي'),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // صورة المستخدم
            CircleAvatar(
              radius: 50,
              backgroundColor: AppTheme.primaryColor.withAlpha(26),
              backgroundImage:
                  user?.photoUrl != null ? NetworkImage(user!.photoUrl!) : null,
              child:
                  user?.photoUrl == null
                      ? Icon(
                        Icons.person,
                        size: 50,
                        color: AppTheme.primaryColor,
                      )
                      : null,
            ),
            const SizedBox(height: 16),

            // اسم المستخدم
            Text(
              user?.name ?? 'مستخدم',
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),

            // البريد الإلكتروني
            Text(
              user?.email ?? '<EMAIL>',
              style: const TextStyle(fontSize: 16, color: Colors.grey),
            ),
            const SizedBox(height: 24),

            // حالة الاشتراك
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color:
                    appState.isSubscribed
                        ? Colors.green.withAlpha(26)
                        : Colors.orange.withAlpha(26),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: appState.isSubscribed ? Colors.green : Colors.orange,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    appState.isSubscribed ? Icons.verified : Icons.info_outline,
                    color: appState.isSubscribed ? Colors.green : Colors.orange,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          appState.isSubscribed ? 'مشترك' : 'نسخة مجانية',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color:
                                appState.isSubscribed
                                    ? Colors.green
                                    : Colors.orange,
                          ),
                        ),
                        if (appState.isSubscribed) ...[
                          const SizedBox(height: 4),
                          Text(
                            'ينتهي في: ${_formatDate(appState.currentUser?.premiumExpiry)}',
                            style: const TextStyle(fontSize: 14),
                          ),
                        ],
                      ],
                    ),
                  ),
                  if (!appState.isSubscribed)
                    CustomButton(
                      text: 'ترقية',
                      height: 36,
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const SubscriptionScreen(),
                          ),
                        );
                      },
                    ),
                ],
              ),
            ),
            const SizedBox(height: 32),

            // قائمة الإعدادات
            _buildSettingsItem(
              icon: Icons.person_outline,
              title: 'تعديل الملف الشخصي',
              onTap: () {
                // سيتم تنفيذ هذه الدالة لاحقًا
              },
            ),
            _buildSettingsItem(
              icon: Icons.language,
              title: 'تغيير اللغة',
              onTap: () {
                _showLanguageDialog(context, appState);
              },
            ),
            _buildSettingsItem(
              icon: Icons.brightness_6,
              title: 'وضع السمة',
              subtitle: _getThemeModeText(appState.themeMode),
              onTap: () {
                _showThemeModeDialog(context, appState);
              },
            ),
            _buildSettingsItem(
              icon: Icons.notifications_outlined,
              title: 'الإشعارات',
              onTap: () {
                // سيتم تنفيذ هذه الدالة لاحقًا
              },
            ),
            _buildSettingsItem(
              icon: Icons.security,
              title: 'الخصوصية والأمان',
              onTap: () {
                // سيتم تنفيذ هذه الدالة لاحقًا
              },
            ),
            _buildSettingsItem(
              icon: Icons.description_outlined,
              title: 'شروط الاستخدام',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const TermsOfServiceScreen(),
                  ),
                );
              },
            ),
            _buildSettingsItem(
              icon: Icons.privacy_tip_outlined,
              title: 'سياسة الخصوصية',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const PrivacyPolicyScreen(),
                  ),
                );
              },
            ),
            _buildSettingsItem(
              icon: Icons.help_outline,
              title: 'المساعدة والدعم',
              onTap: () {
                // سيتم تنفيذ هذه الدالة لاحقًا
              },
            ),
            _buildSettingsItem(
              icon: Icons.info_outline,
              title: 'عن التطبيق',
              onTap: () {
                // سيتم تنفيذ هذه الدالة لاحقًا
              },
            ),
            const SizedBox(height: 16),

            // زر تسجيل الخروج
            CustomButton(
              text: 'تسجيل الخروج',
              type: ButtonType.outlined,
              color: Colors.red,
              icon: Icons.logout,
              onPressed: _isLoading ? null : _logout,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر الإعدادات
  Widget _buildSettingsItem({
    required IconData icon,
    required String title,
    String? subtitle,
    Widget? trailing,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon),
      title: Text(title),
      subtitle: subtitle != null ? Text(subtitle) : null,
      trailing: trailing ?? const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }

  /// الحصول على نص وضع السمة
  String _getThemeModeText(String themeMode) {
    switch (themeMode) {
      case AppConstants.themeModeLight:
        return 'الوضع الفاتح';
      case AppConstants.themeModeDark:
        return 'الوضع الداكن';
      case AppConstants.themeModeAmoled:
        return 'وضع AMOLED الداكن';
      default:
        return 'الوضع الفاتح';
    }
  }

  /// عرض مربع حوار اختيار وضع السمة
  void _showThemeModeDialog(BuildContext context, AppState appState) {
    final themeModes = [
      {
        'code': AppConstants.themeModeLight,
        'name': 'الوضع الفاتح',
        'icon': Icons.light_mode,
      },
      {
        'code': AppConstants.themeModeDark,
        'name': 'الوضع الداكن',
        'icon': Icons.dark_mode,
      },
      {
        'code': AppConstants.themeModeAmoled,
        'name': 'وضع AMOLED الداكن',
        'icon': Icons.nights_stay,
        'description': 'أسود خالص لشاشات AMOLED',
      },
    ];

    final currentThemeMode = appState.themeMode;

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('اختر وضع السمة'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children:
                themeModes.map((mode) {
                  final isSelected = mode['code'] == currentThemeMode;
                  return ListTile(
                    leading: Icon(
                      mode['icon'] as IconData,
                      color: isSelected ? AppTheme.primaryColor : null,
                    ),
                    title: Text(mode['name'] as String),
                    subtitle:
                        mode['description'] != null
                            ? Text(mode['description'] as String)
                            : null,
                    selected: isSelected,
                    trailing:
                        isSelected
                            ? const Icon(
                              Icons.check_circle,
                              color: AppTheme.primaryColor,
                            )
                            : null,
                    onTap: () {
                      appState.setThemeMode(mode['code'] as String);
                      Navigator.pop(context);
                    },
                  );
                }).toList(),
          ),
        );
      },
    );
  }

  /// عرض حوار تغيير اللغة
  void _showLanguageDialog(BuildContext context, AppState appState) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('اختر اللغة'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                title: const Text('العربية'),
                leading: const Text('🇸🇦'),
                selected: appState.selectedLanguage == 'ar',
                onTap: () {
                  appState.setSelectedLanguage('ar');
                  Navigator.pop(context);
                },
              ),
              ListTile(
                title: const Text('English'),
                leading: const Text('🇺🇸'),
                selected: appState.selectedLanguage == 'en',
                onTap: () {
                  appState.setSelectedLanguage('en');
                  Navigator.pop(context);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime? date) {
    if (date == null) {
      return 'غير محدد';
    }

    return '${date.year}/${date.month}/${date.day}';
  }

  /// تسجيل الخروج
  Future<void> _logout() async {
    final confirmed = await AppHelpers.showConfirmDialog(
      context,
      title: 'تسجيل الخروج',
      message: 'هل أنت متأكد من رغبتك في تسجيل الخروج؟',
    );

    if (!confirmed) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      await _auth.signOut();

      if (mounted) {
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const SimpleLoginScreen()),
          (route) => false,
        );
      }
    } catch (e) {
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء تسجيل الخروج: $e',
          isError: true,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
