import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../config/app_theme.dart';
import '../../../models/tourism_phrase.dart';
import '../../../utils/helpers.dart';
import 'tourism_phrases_service.dart';

/// شاشة العبارات السياحية
class TourismPhrasesScreen extends StatefulWidget {
  const TourismPhrasesScreen({super.key});

  @override
  State<TourismPhrasesScreen> createState() => _TourismPhrasesScreenState();
}

class _TourismPhrasesScreenState extends State<TourismPhrasesScreen>
    with SingleTickerProviderStateMixin {
  final TourismPhrasesService _phrasesService = TourismPhrasesService();
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  String _sourceLanguage = 'ar';
  String _targetLanguage = 'en';
  bool _isSpeaking = false;
  bool _isLoading = true;
  String _selectedCategory = 'all';
  String _searchQuery = '';
  bool _showFavoritesOnly = false;

  // قائمة الفئات
  final List<Map<String, dynamic>> _categories = [
    {'id': 'all', 'name': 'الكل', 'icon': Icons.language},
    {'id': 'common', 'name': 'عبارات شائعة', 'icon': Icons.chat_bubble_outline},
    {'id': 'hotels', 'name': 'الفنادق', 'icon': Icons.hotel},
    {'id': 'restaurants', 'name': 'المطاعم', 'icon': Icons.restaurant},
    {'id': 'transportation', 'name': 'المواصلات', 'icon': Icons.directions_bus},
    {
      'id': 'attractions',
      'name': 'الأماكن السياحية',
      'icon': Icons.photo_camera,
    },
    {'id': 'shopping', 'name': 'التسوق', 'icon': Icons.shopping_bag},
    {'id': 'emergency', 'name': 'الطوارئ', 'icon': Icons.emergency},
  ];

  List<TourismPhrase> _filteredPhrases = [];

  @override
  void initState() {
    super.initState();

    // تهيئة خدمة العبارات السياحية
    _initPhrasesService();

    // إضافة مستمع لحقل البحث
    _searchController.addListener(_filterPhrases);
  }

  @override
  void dispose() {
    _searchController.removeListener(_filterPhrases);
    _searchController.dispose();
    _scrollController.dispose();
    _phrasesService.dispose();
    super.dispose();
  }

  /// تهيئة خدمة العبارات السياحية
  Future<void> _initPhrasesService() async {
    try {
      await _phrasesService.initialize();

      // الاشتراك في تدفق العبارات
      _phrasesService.phrasesStream.listen((phrases) {
        if (mounted) {
          setState(() {
            _filterPhrases();
            _isLoading = false;
          });
        }
      });

      // تحميل العبارات
      _filterPhrases();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء تحميل العبارات: $e',
          isError: true,
        );
      }
    }
  }

  /// تصفية العبارات
  void _filterPhrases() {
    setState(() {
      _searchQuery = _searchController.text.trim();

      // تصفية حسب الفئة والبحث
      if (_selectedCategory == 'all') {
        _filteredPhrases = _phrasesService.searchPhrases(_searchQuery);
      } else {
        _filteredPhrases = _phrasesService.searchPhrases(
          _searchQuery,
          category: _selectedCategory,
        );
      }

      // تصفية حسب المفضلة
      if (_showFavoritesOnly) {
        _filteredPhrases = _filteredPhrases.where((p) => p.isFavorite).toList();
      }
    });
  }

  /// تغيير الفئة المحددة
  void _changeCategory(String categoryId) {
    if (_selectedCategory != categoryId) {
      setState(() {
        _selectedCategory = categoryId;
        _filterPhrases();
      });
    }
  }

  /// تبديل عرض المفضلة فقط
  void _toggleFavoritesOnly() {
    setState(() {
      _showFavoritesOnly = !_showFavoritesOnly;
      _filterPhrases();
    });
  }

  /// نسخ العبارة
  void _copyPhrase(String text) {
    Clipboard.setData(ClipboardData(text: text));
    AppHelpers.showSnackBar(context, 'تم نسخ النص');
  }

  /// نطق العبارة
  Future<void> _speakPhrase(String phrase, String languageCode) async {
    if (_isSpeaking) return;

    setState(() {
      _isSpeaking = true;
    });

    try {
      await _phrasesService.speakText(
        text: phrase,
        languageCode: languageCode,
        onDone: () {
          if (mounted) {
            setState(() {
              _isSpeaking = false;
            });
          }
        },
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _isSpeaking = false;
        });
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء النطق: $e',
          isError: true,
        );
      }
    }
  }

  /// إضافة/إزالة من المفضلة
  Future<void> _toggleFavorite(TourismPhrase phrase) async {
    try {
      bool success;
      if (phrase.isFavorite) {
        success = await _phrasesService.removeFromFavorites(phrase.id);
      } else {
        success = await _phrasesService.addToFavorites(phrase);
      }

      if (success && mounted) {
        AppHelpers.showSnackBar(
          context,
          phrase.isFavorite
              ? 'تمت إزالة العبارة من المفضلة'
              : 'تمت إضافة العبارة إلى المفضلة',
        );
      }
    } catch (e) {
      if (mounted) {
        AppHelpers.showSnackBar(context, 'حدث خطأ: $e', isError: true);
      }
    }
  }

  /// الحصول على اسم اللغة
  String _getLanguageName(String code) {
    switch (code) {
      case 'ar':
        return 'العربية';
      case 'en':
        return 'الإنجليزية';
      case 'fr':
        return 'الفرنسية';
      case 'es':
        return 'الإسبانية';
      case 'de':
        return 'الألمانية';
      case 'it':
        return 'الإيطالية';
      case 'ru':
        return 'الروسية';
      case 'zh':
        return 'الصينية';
      case 'ja':
        return 'اليابانية';
      case 'ko':
        return 'الكورية';
      default:
        return code;
    }
  }

  /// عرض مربع حوار اختيار اللغة
  void _showLanguageSelector(bool isSource) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                isSource ? 'اختر لغة المصدر' : 'اختر لغة الهدف',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView(
                  children: [
                    _buildLanguageItem(context, 'ar', isSource),
                    _buildLanguageItem(context, 'en', isSource),
                    _buildLanguageItem(context, 'fr', isSource),
                    _buildLanguageItem(context, 'es', isSource),
                    _buildLanguageItem(context, 'de', isSource),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// بناء عنصر اللغة
  Widget _buildLanguageItem(BuildContext context, String code, bool isSource) {
    final isSelected =
        isSource ? _sourceLanguage == code : _targetLanguage == code;

    return ListTile(
      title: Text(_getLanguageName(code)),
      trailing:
          isSelected
              ? const Icon(Icons.check, color: AppTheme.primaryColor)
              : null,
      onTap: () {
        setState(() {
          if (isSource) {
            _sourceLanguage = code;
          } else {
            _targetLanguage = code;
          }
        });
        Navigator.pop(context);
      },
    );
  }

  /// بناء عنصر العبارة
  Widget _buildPhraseItem(TourismPhrase phrase) {
    return Card(
          elevation: 2,
          margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 0),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // العبارة باللغة المصدر
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        phrase.translations[_sourceLanguage] ?? '',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.volume_up),
                      onPressed:
                          () => _speakPhrase(
                            phrase.translations[_sourceLanguage] ?? '',
                            _sourceLanguage,
                          ),
                      tooltip: 'نطق',
                    ),
                    IconButton(
                      icon: const Icon(Icons.copy),
                      onPressed:
                          () => _copyPhrase(
                            phrase.translations[_sourceLanguage] ?? '',
                          ),
                      tooltip: 'نسخ',
                    ),
                  ],
                ),

                const Divider(),

                // العبارة باللغة الهدف
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        phrase.translations[_targetLanguage] ?? '',
                        style: TextStyle(
                          fontSize: 18,
                          color: AppTheme.primaryColor,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.volume_up),
                      onPressed:
                          () => _speakPhrase(
                            phrase.translations[_targetLanguage] ?? '',
                            _targetLanguage,
                          ),
                      tooltip: 'نطق',
                    ),
                    IconButton(
                      icon: const Icon(Icons.copy),
                      onPressed:
                          () => _copyPhrase(
                            phrase.translations[_targetLanguage] ?? '',
                          ),
                      tooltip: 'نسخ',
                    ),
                    IconButton(
                      icon: Icon(
                        phrase.isFavorite
                            ? Icons.favorite
                            : Icons.favorite_border,
                        color: phrase.isFavorite ? Colors.red : null,
                      ),
                      onPressed: () => _toggleFavorite(phrase),
                      tooltip:
                          phrase.isFavorite
                              ? 'إزالة من المفضلة'
                              : 'إضافة إلى المفضلة',
                    ),
                  ],
                ),
              ],
            ),
          ),
        )
        .animate()
        .fadeIn(duration: 300.ms)
        .slideY(begin: 0.2, end: 0, duration: 300.ms);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('العبارات السياحية'),
        backgroundColor: AppTheme.primaryColor,
        actions: [
          // زر المفضلة
          IconButton(
            icon: Icon(
              _showFavoritesOnly ? Icons.favorite : Icons.favorite_border,
              color: _showFavoritesOnly ? Colors.red : null,
            ),
            onPressed: _toggleFavoritesOnly,
            tooltip: _showFavoritesOnly ? 'عرض الكل' : 'المفضلة فقط',
          ),
        ],
      ),
      body: Column(
        children: [
          // اختيار اللغات
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(10),
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                // لغة المصدر
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _showLanguageSelector(true),
                    icon: const Icon(Icons.language),
                    label: Text(_getLanguageName(_sourceLanguage)),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),

                // زر التبديل
                IconButton(
                  icon: const Icon(Icons.swap_horiz),
                  onPressed: () {
                    setState(() {
                      final temp = _sourceLanguage;
                      _sourceLanguage = _targetLanguage;
                      _targetLanguage = temp;
                    });
                  },
                  tooltip: 'تبديل اللغات',
                ),

                // لغة الهدف
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _showLanguageSelector(false),
                    icon: const Icon(Icons.language),
                    label: Text(_getLanguageName(_targetLanguage)),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // حقل البحث
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'ابحث عن عبارة...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon:
                    _searchQuery.isNotEmpty
                        ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            _searchController.clear();
                          },
                        )
                        : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),

          // قائمة الفئات
          SizedBox(
            height: 50,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _categories.length,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemBuilder: (context, index) {
                final category = _categories[index];
                final isSelected = _selectedCategory == category['id'];

                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: ChoiceChip(
                    label: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          category['icon'] as IconData,
                          size: 18,
                          color: isSelected ? Colors.white : null,
                        ),
                        const SizedBox(width: 4),
                        Text(category['name'] as String),
                      ],
                    ),
                    selected: isSelected,
                    onSelected: (selected) {
                      if (selected) {
                        _changeCategory(category['id'] as String);
                      }
                    },
                    backgroundColor: Colors.grey.shade200,
                    selectedColor: AppTheme.primaryColor,
                    labelStyle: TextStyle(
                      color: isSelected ? Colors.white : Colors.black,
                    ),
                  ),
                );
              },
            ),
          ),

          // قائمة العبارات
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _filteredPhrases.isEmpty
                    ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.search_off,
                            size: 64,
                            color: Colors.grey,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            _showFavoritesOnly
                                ? 'لا توجد عبارات في المفضلة'
                                : 'لا توجد عبارات مطابقة للبحث',
                            style: const TextStyle(
                              fontSize: 18,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    )
                    : ListView.builder(
                      controller: _scrollController,
                      padding: const EdgeInsets.all(16),
                      itemCount: _filteredPhrases.length,
                      itemBuilder: (context, index) {
                        return _buildPhraseItem(_filteredPhrases[index]);
                      },
                    ),
          ),
        ],
      ),
    );
  }
}
