import 'package:flutter/material.dart';
import '../config/app_theme.dart';

/// مؤشر التحميل
class LoadingIndicator extends StatelessWidget {
  /// حجم المؤشر
  final double size;
  
  /// سماكة المؤشر
  final double strokeWidth;
  
  /// لون المؤشر
  final Color? color;
  
  /// نص المؤشر
  final String? text;
  
  /// حجم النص
  final double? fontSize;
  
  /// لون النص
  final Color? textColor;
  
  /// المسافة بين المؤشر والنص
  final double spacing;
  
  /// اتجاه المؤشر
  final Axis direction;

  const LoadingIndicator({
    super.key,
    this.size = 36,
    this.strokeWidth = 4,
    this.color,
    this.text,
    this.fontSize,
    this.textColor,
    this.spacing = 16,
    this.direction = Axis.vertical,
  });

  @override
  Widget build(BuildContext context) {
    final indicatorColor = color ?? AppTheme.primaryColor;
    final indicatorTextColor = textColor ?? Theme.of(context).textTheme.bodyLarge?.color;
    
    final indicator = SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        strokeWidth: strokeWidth,
        valueColor: AlwaysStoppedAnimation<Color>(indicatorColor),
      ),
    );
    
    if (text == null) {
      return indicator;
    }
    
    final textWidget = Text(
      text!,
      style: TextStyle(
        fontSize: fontSize,
        color: indicatorTextColor,
      ),
      textAlign: TextAlign.center,
    );
    
    if (direction == Axis.vertical) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          indicator,
          SizedBox(height: spacing),
          textWidget,
        ],
      );
    } else {
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          indicator,
          SizedBox(width: spacing),
          textWidget,
        ],
      );
    }
  }
}
