import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// فئة لإدارة ترجمة التطبيق
class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  static final Map<String, Map<String, String>> _localizedValues = {
    'ar': {
      // عام
      'app_name': 'المترجم الذكي AI',
      'ok': 'موافق',
      'cancel': 'إلغاء',
      'save': 'حفظ',
      'delete': 'حذف',
      'edit': 'تعديل',
      'loading': 'جاري التحميل...',
      'error': 'حدث خطأ',
      'success': 'تمت العملية بنجاح',
      'retry': 'إعادة المحاولة',
      'next': 'التالي',
      'back': 'السابق',
      'finish': 'إنهاء',
      'skip': 'تخطي',

      // الترحيب والتسجيل
      'welcome': 'مرحباً بك في المترجم الذكي',
      'welcome_subtitle': 'ترجمة ذكية بتقنيات الذكاء الاصطناعي',
      'onboarding_title_1': 'ترجمة فورية',
      'onboarding_desc_1': 'ترجم النصوص والصوت والصور بشكل فوري',
      'onboarding_title_2': 'دعم متعدد اللغات',
      'onboarding_desc_2': 'أكثر من 50 لغة مدعومة بما فيها اللغات النادرة',
      'onboarding_title_3': 'ذكاء اصطناعي متقدم',
      'onboarding_desc_3':
          'استخدام أحدث تقنيات الذكاء الاصطناعي للترجمة الدقيقة',
      'get_started': 'لنبدأ',

      // تسجيل الدخول
      'login': 'تسجيل الدخول',
      'signup': 'إنشاء حساب',
      'logout': 'تسجيل الخروج',
      'email': 'البريد الإلكتروني',
      'password': 'كلمة المرور',
      'confirm_password': 'تأكيد كلمة المرور',
      'forgot_password': 'نسيت كلمة المرور؟',
      'login_with_google': 'تسجيل الدخول باستخدام Google',
      'continue_as_guest': 'المتابعة كضيف',
      'already_have_account': 'لديك حساب بالفعل؟',
      'dont_have_account': 'ليس لديك حساب؟',

      // الصفحة الرئيسية
      'home': 'الرئيسية',
      'favorites': 'المفضلة',
      'history': 'السجل',
      'settings': 'الإعدادات',
      'voice_translation': 'ترجمة صوتية',
      'text_translation': 'ترجمة نصوص',
      'image_translation': 'ترجمة صور',
      'document_translation': 'ترجمة مستندات',
      'conversation_translation': 'ترجمة محادثات',
      'kids_mode': 'وضع الأطفال',
      'tourism_mode': 'وضع السياحة',
      'ai_chat': 'محادثة ذكية',

      // الترجمة
      'translate': 'ترجم',
      'translation': 'الترجمة',
      'source_language': 'اللغة المصدر',
      'target_language': 'اللغة الهدف',
      'swap_languages': 'تبديل اللغات',
      'tap_to_speak': 'انقر للتحدث',
      'listening': 'جاري الاستماع...',
      'copy_translation': 'نسخ الترجمة',
      'share_translation': 'مشاركة الترجمة',
      'save_to_favorites': 'حفظ في المفضلة',
      'remove_from_favorites': 'إزالة من المفضلة',
      'clear_text': 'مسح النص',
      'take_photo': 'التقاط صورة',
      'select_from_gallery': 'اختيار من المعرض',
      'select_document': 'اختيار مستند',
      'processing_document': 'جاري معالجة المستند...',
      'start_conversation': 'بدء المحادثة',
      'end_conversation': 'إنهاء المحادثة',

      // الإعدادات
      'appearance': 'المظهر',
      'dark_mode': 'الوضع الداكن',
      'language': 'اللغة',
      'notifications': 'الإشعارات',
      'about': 'حول التطبيق',
      'privacy_policy': 'سياسة الخصوصية',
      'terms_of_service': 'شروط الاستخدام',
      'contact_us': 'اتصل بنا',
      'support': 'الدعم الفني',
      'rate_app': 'تقييم التطبيق',
      'share_app': 'مشاركة التطبيق',
      'version': 'الإصدار',

      // الاشتراكات
      'subscription': 'الاشتراك',
      'subscriptions': 'الاشتراكات',
      'free_plan': 'الخطة المجانية',
      'premium_plan': 'الخطة المميزة',
      'remove_ads': 'إزالة الإعلانات',
      'unlimited_translations': 'ترجمات غير محدودة',
      'subscribe_now': 'اشترك الآن',
      'restore_purchases': 'استعادة المشتريات',
      'subscription_details': 'تفاصيل الاشتراك',
      'monthly': 'شهري',
      'yearly': 'سنوي',
      'lifetime': 'مدى الحياة',

      // الذكاء الاصطناعي
      'ai_assistant': 'المساعد الذكي',
      'ask_ai': 'اسأل الذكاء الاصطناعي',
      'ai_thinking': 'جاري التفكير...',
      'ai_response_type': 'نوع الاستجابة',
      'general': 'عام',
      'islamic': 'إسلامي',
      'kids': 'أطفال',
      'tourism': 'سياحة',
    },
    'en': {
      // General
      'app_name': 'AI Smart Translator',
      'ok': 'OK',
      'cancel': 'Cancel',
      'save': 'Save',
      'delete': 'Delete',
      'edit': 'Edit',
      'loading': 'Loading...',
      'error': 'Error',
      'success': 'Success',
      'retry': 'Retry',
      'next': 'Next',
      'back': 'Back',
      'finish': 'Finish',
      'skip': 'Skip',

      // Welcome & Registration
      'welcome': 'Welcome to AI Smart Translator',
      'welcome_subtitle': 'Smart translation with AI technologies',
      'onboarding_title_1': 'Instant Translation',
      'onboarding_desc_1': 'Translate text, voice, and images instantly',
      'onboarding_title_2': 'Multi-language Support',
      'onboarding_desc_2': 'Over 50 languages supported including rare ones',
      'onboarding_title_3': 'Advanced AI',
      'onboarding_desc_3':
          'Using the latest AI technologies for accurate translation',
      'get_started': 'Get Started',

      // Login
      'login': 'Login',
      'signup': 'Sign Up',
      'logout': 'Logout',
      'email': 'Email',
      'password': 'Password',
      'confirm_password': 'Confirm Password',
      'forgot_password': 'Forgot Password?',
      'login_with_google': 'Login with Google',
      'continue_as_guest': 'Continue as Guest',
      'already_have_account': 'Already have an account?',
      'dont_have_account': 'Don\'t have an account?',

      // Home
      'home': 'Home',
      'favorites': 'Favorites',
      'history': 'History',
      'settings': 'Settings',
      'voice_translation': 'Voice Translation',
      'text_translation': 'Text Translation',
      'image_translation': 'Image Translation',
      'document_translation': 'Document Translation',
      'conversation_translation': 'Conversation Translation',
      'kids_mode': 'Kids Mode',
      'tourism_mode': 'Tourism Mode',
      'ai_chat': 'AI Chat',

      // Translation
      'translate': 'Translate',
      'translation': 'Translation',
      'source_language': 'Source Language',
      'target_language': 'Target Language',
      'swap_languages': 'Swap Languages',
      'tap_to_speak': 'Tap to Speak',
      'listening': 'Listening...',
      'copy_translation': 'Copy Translation',
      'share_translation': 'Share Translation',
      'save_to_favorites': 'Save to Favorites',
      'remove_from_favorites': 'Remove from Favorites',
      'clear_text': 'Clear Text',
      'take_photo': 'Take Photo',
      'select_from_gallery': 'Select from Gallery',
      'select_document': 'Select Document',
      'processing_document': 'Processing Document...',
      'start_conversation': 'Start Conversation',
      'end_conversation': 'End Conversation',

      // Settings
      'appearance': 'Appearance',
      'dark_mode': 'Dark Mode',
      'language': 'Language',
      'notifications': 'Notifications',
      'about': 'About',
      'privacy_policy': 'Privacy Policy',
      'terms_of_service': 'Terms of Service',
      'contact_us': 'Contact Us',
      'support': 'Support',
      'rate_app': 'Rate App',
      'share_app': 'Share App',
      'version': 'Version',

      // Subscriptions
      'subscription': 'Subscription',
      'subscriptions': 'Subscriptions',
      'free_plan': 'Free Plan',
      'premium_plan': 'Premium Plan',
      'remove_ads': 'Remove Ads',
      'unlimited_translations': 'Unlimited Translations',
      'subscribe_now': 'Subscribe Now',
      'restore_purchases': 'Restore Purchases',
      'subscription_details': 'Subscription Details',
      'monthly': 'Monthly',
      'yearly': 'Yearly',
      'lifetime': 'Lifetime',

      // AI
      'ai_assistant': 'AI Assistant',
      'ask_ai': 'Ask AI',
      'ai_thinking': 'AI is thinking...',
      'ai_response_type': 'Response Type',
      'general': 'General',
      'islamic': 'Islamic',
      'kids': 'Kids',
      'tourism': 'Tourism',
    },
  };

  String translate(String key) {
    return _localizedValues[locale.languageCode]?[key] ?? key;
  }
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['ar', 'en', 'fr', 'es'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    Intl.defaultLocale = locale.languageCode;
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
