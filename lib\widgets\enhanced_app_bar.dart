import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../config/app_theme.dart';

/// شريط تطبيق محسن مع تأثيرات تفاعلية
class EnhancedAppBar extends StatelessWidget implements PreferredSizeWidget {
  final Widget? title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double elevation;
  final bool centerTitle;
  final SystemUiOverlayStyle? systemOverlayStyle;
  final PreferredSizeWidget? bottom;
  final double? titleSpacing;
  final double? toolbarHeight;
  final bool enableHapticFeedback;

  const EnhancedAppBar({
    super.key,
    this.title,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation = 4.0,
    this.centerTitle = true,
    this.systemOverlayStyle,
    this.bottom,
    this.titleSpacing,
    this.toolbarHeight,
    this.enableHapticFeedback = true,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: title,
      actions:
          actions?.map((action) {
            if (action is IconButton) {
              return _buildEnhancedIconButton(action);
            }
            return action;
          }).toList(),
      leading:
          leading != null
              ? (leading is IconButton
                  ? _buildEnhancedIconButton(leading as IconButton)
                  : leading)
              : automaticallyImplyLeading
              ? _buildEnhancedBackButton(context)
              : null,
      automaticallyImplyLeading: automaticallyImplyLeading,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      elevation: elevation,
      centerTitle: centerTitle,
      systemOverlayStyle: systemOverlayStyle,
      bottom: bottom,
      titleSpacing: titleSpacing,
      toolbarHeight: toolbarHeight,
    );
  }

  /// بناء زر رجوع محسن
  Widget _buildEnhancedBackButton(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () {
        if (enableHapticFeedback) {
          HapticFeedback.lightImpact();
        }
        Navigator.of(context).pop();
      },
      splashColor: AppTheme.primaryColor.withAlpha(76),
      highlightColor: AppTheme.primaryColor.withAlpha(26),
    );
  }

  /// بناء زر أيقونة محسن
  Widget _buildEnhancedIconButton(IconButton button) {
    return IconButton(
      icon: button.icon,
      onPressed:
          button.onPressed != null
              ? () {
                if (enableHapticFeedback) {
                  HapticFeedback.lightImpact();
                }
                button.onPressed!();
              }
              : null,
      tooltip: button.tooltip,
      splashColor: AppTheme.primaryColor.withAlpha(76),
      highlightColor: AppTheme.primaryColor.withAlpha(26),
      padding: button.padding,
      iconSize: button.iconSize,
      color: button.color,
      focusColor: button.focusColor,
      hoverColor: button.hoverColor,
      disabledColor: button.disabledColor,
      splashRadius: button.splashRadius,
      alignment: button.alignment,
      constraints: button.constraints,
      visualDensity: button.visualDensity,
      enableFeedback: button.enableFeedback,
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(
    toolbarHeight ?? kToolbarHeight + (bottom?.preferredSize.height ?? 0.0),
  );
}
