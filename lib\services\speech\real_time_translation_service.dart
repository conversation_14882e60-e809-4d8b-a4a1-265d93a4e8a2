import 'dart:async';
import 'package:flutter/material.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:speech_to_text/speech_recognition_result.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import '../api/translation_service.dart';

/// نموذج نتيجة الترجمة الفورية
class RealTimeTranslationResult {
  final String originalText;
  final String translatedText;
  final String sourceLanguage;
  final String targetLanguage;
  final DateTime timestamp;

  RealTimeTranslationResult({
    required this.originalText,
    required this.translatedText,
    required this.sourceLanguage,
    required this.targetLanguage,
    required this.timestamp,
  });
}

/// خدمة الترجمة الفورية
class RealTimeTranslationService {
  final SpeechToText _speechToText = SpeechToText();
  final FlutterTts _flutterTts = FlutterTts();
  final TranslationService _translationService;

  // تدفق نتائج الترجمة الفورية
  final _translationResultController =
      StreamController<RealTimeTranslationResult>.broadcast();

  // حالة الاستماع
  bool _isListening = false;

  // النص المتراكم
  String _accumulatedText = '';

  // اللغات
  String _sourceLanguage = 'ar';
  String _targetLanguage = 'en';

  // مؤقت للترجمة الفورية
  Timer? _translationTimer;

  // الفاصل الزمني للترجمة (بالمللي ثانية)
  final int _translationInterval = 2000; // 2 ثانية

  RealTimeTranslationService(FirebaseRemoteConfig remoteConfig)
    : _translationService = TranslationService(remoteConfig) {
    _initializeSpeechRecognition();
    _initializeTextToSpeech();
  }

  /// تهيئة خدمة التعرف على الكلام
  Future<void> _initializeSpeechRecognition() async {
    try {
      bool available = await _speechToText.initialize(
        onStatus: (status) {
          debugPrint('Speech recognition status: $status');

          if (status == 'done' || status == 'notListening') {
            _isListening = false;
          }
        },
        onError: (error) {
          debugPrint('Speech recognition error: $error');
          _isListening = false;
        },
      );

      if (!available) {
        debugPrint('Speech recognition not available on this device');
      }
    } catch (e) {
      debugPrint('Error initializing speech recognition: $e');
    }
  }

  /// تهيئة خدمة تحويل النص إلى كلام
  Future<void> _initializeTextToSpeech() async {
    try {
      await _flutterTts.setLanguage(_getLocaleId(_targetLanguage));
      await _flutterTts.setSpeechRate(0.5);
      await _flutterTts.setVolume(1.0);
      await _flutterTts.setPitch(1.0);

      _flutterTts.setErrorHandler((error) {
        debugPrint('TTS error: $error');
      });
    } catch (e) {
      debugPrint('Error initializing text to speech: $e');
    }
  }

  /// الحصول على معرف اللغة المحلي
  String _getLocaleId(String languageCode) {
    final Map<String, String> localeMap = {
      'ar': 'ar-SA',
      'en': 'en-US',
      'fr': 'fr-FR',
      'es': 'es-ES',
      'de': 'de-DE',
      'it': 'it-IT',
      'ru': 'ru-RU',
      'zh': 'zh-CN',
      'ja': 'ja-JP',
      'ko': 'ko-KR',
      'tr': 'tr-TR',
      'pt': 'pt-BR',
      'hi': 'hi-IN',
      'ur': 'ur-PK',
      'fa': 'fa-IR',
      'nl': 'nl-NL',
      'sv': 'sv-SE',
      'da': 'da-DK',
      'no': 'no-NO',
      'fi': 'fi-FI',
      'pl': 'pl-PL',
      'cs': 'cs-CZ',
      'sk': 'sk-SK',
      'hu': 'hu-HU',
      'ro': 'ro-RO',
      'bg': 'bg-BG',
      'el': 'el-GR',
      'he': 'he-IL',
      'th': 'th-TH',
      'vi': 'vi-VN',
      'id': 'id-ID',
      'ms': 'ms-MY',
      'fil': 'fil-PH',
      'uk': 'uk-UA',
      'sw': 'sw-KE',
      'am': 'am-ET',
    };

    return localeMap[languageCode] ?? languageCode;
  }

  /// الحصول على تدفق نتائج الترجمة
  Stream<RealTimeTranslationResult> get translationResultStream =>
      _translationResultController.stream;

  /// تعيين حساسية الميكروفون
  void setMicrophoneSensitivity(double sensitivity) {
    // تطبيق الإعدادات على خدمة التعرف على الكلام
    if (_isListening) {
      // إعادة تشغيل الاستماع بالإعدادات الجديدة
      _speechToText.stop().then((_) {
        startListening(
          sourceLanguage: _sourceLanguage,
          targetLanguage: _targetLanguage,
        );
      });
    }

    // ملاحظة: في التطبيق الفعلي، يمكن استخدام هذه القيمة لضبط حساسية الميكروفون
    // لكن مكتبة speech_to_text الحالية لا توفر واجهة برمجة تطبيقات لضبط الحساسية
  }

  /// بدء الاستماع والترجمة الفورية
  Future<bool> startListening({
    required String sourceLanguage,
    required String targetLanguage,
  }) async {
    if (_isListening) {
      return true;
    }

    _sourceLanguage = sourceLanguage;
    _targetLanguage = targetLanguage;
    _accumulatedText = '';

    try {
      // تهيئة خدمة تحويل النص إلى كلام للغة الهدف
      await _flutterTts.setLanguage(_getLocaleId(_targetLanguage));

      // ضبط حساسية الميكروفون ومستوى الضوضاء
      // هذه الخيارات قد تختلف حسب مكتبة التعرف على الكلام المستخدمة
      // في التطبيق الفعلي، ستحتاج إلى استخدام واجهة برمجة تطبيقات تدعم هذه الإعدادات

      // بدء الاستماع
      _isListening = await _speechToText.listen(
        onResult: _onSpeechResult,
        localeId: _getLocaleId(_sourceLanguage),
        // استخدام وضع الإملاء للاستماع المستمر
        // في التطبيق الفعلي، ستحتاج إلى استخدام خيارات أكثر تقدمًا
        // لضبط حساسية الميكروفون ومستوى الضوضاء
      );

      // بدء مؤقت الترجمة الفورية
      _startTranslationTimer();

      return _isListening;
    } catch (e) {
      debugPrint('Error starting listening: $e');
      _isListening = false;
      return false;
    }
  }

  /// إيقاف الاستماع
  Future<void> stopListening() async {
    if (!_isListening) {
      return;
    }

    try {
      await _speechToText.stop();
      _isListening = false;

      // إيقاف مؤقت الترجمة
      _translationTimer?.cancel();

      // ترجمة النص المتراكم النهائي
      if (_accumulatedText.isNotEmpty) {
        await _translateAndPublish(_accumulatedText);
      }
    } catch (e) {
      debugPrint('Error stopping listening: $e');
    }
  }

  /// معالجة نتيجة التعرف على الكلام
  void _onSpeechResult(SpeechRecognitionResult result) {
    if (result.finalResult) {
      // النتيجة النهائية
      _accumulatedText = result.recognizedWords;

      // ترجمة النص المتراكم
      _translateAndPublish(_accumulatedText);
    } else {
      // النتيجة الجزئية
      _accumulatedText = result.recognizedWords;
    }
  }

  /// بدء مؤقت الترجمة الفورية
  void _startTranslationTimer() {
    _translationTimer?.cancel();

    _translationTimer = Timer.periodic(
      Duration(milliseconds: _translationInterval),
      (timer) {
        if (_accumulatedText.isNotEmpty) {
          _translateAndPublish(_accumulatedText);
        }
      },
    );
  }

  /// ترجمة النص ونشر النتيجة
  Future<void> _translateAndPublish(String text) async {
    try {
      final translatedText = await _translationService.translateWithAI(
        text: text,
        sourceLanguage: _sourceLanguage,
        targetLanguage: _targetLanguage,
      );

      // نشر نتيجة الترجمة
      _translationResultController.add(
        RealTimeTranslationResult(
          originalText: text,
          translatedText: translatedText,
          sourceLanguage: _sourceLanguage,
          targetLanguage: _targetLanguage,
          timestamp: DateTime.now(),
        ),
      );

      // نطق النص المترجم
      await _flutterTts.speak(translatedText);
    } catch (e) {
      debugPrint('Error translating text: $e');
    }
  }

  /// التخلص من الموارد
  void dispose() {
    _translationTimer?.cancel();
    _translationResultController.close();
    _speechToText.cancel();
    _flutterTts.stop();
  }
}
