import 'package:flutter/material.dart';
import '../../config/app_theme.dart';
import '../../widgets/enhanced_app_bar.dart';

/// شاشة سياسة الخصوصية
class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: EnhancedAppBar(
        title: const Text('سياسة الخصوصية'),
        backgroundColor: AppTheme.primaryColor,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'سياسة الخصوصية',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const Text(
              'آخر تحديث: 1 يونيو 2024',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
            const SizedBox(height: 24),
            _buildSection(
              'مقدمة',
              'نحن في تطبيق المترجم الذكي AI نقدر خصوصيتك ونلتزم بحماية بياناتك الشخصية. تصف سياسة الخصوصية هذه كيفية جمع واستخدام وحماية معلوماتك عند استخدام تطبيقنا.',
            ),
            _buildSection(
              'المعلومات التي نجمعها',
              '''نحن نجمع المعلومات التالية:
• معلومات الحساب: عنوان البريد الإلكتروني، الاسم، صورة الملف الشخصي (اختياري).
• معلومات الاستخدام: بيانات حول كيفية استخدامك للتطبيق، مثل الميزات التي تستخدمها وعدد مرات استخدامها.
• المحتوى: النصوص والتسجيلات الصوتية والصور التي تقوم بترجمتها.
• معلومات الجهاز: نوع الجهاز، إصدار نظام التشغيل، اللغة المفضلة.''',
            ),
            _buildSection(
              'كيفية استخدام المعلومات',
              '''نستخدم المعلومات التي نجمعها للأغراض التالية:
• توفير خدمات الترجمة وتحسينها.
• إدارة حسابك واشتراكك.
• تخصيص تجربتك في التطبيق.
• تحليل استخدام التطبيق وتحسين أدائه.
• الاتصال بك بخصوص التحديثات والعروض الخاصة.''',
            ),
            _buildSection('مشاركة المعلومات', '''قد نشارك معلوماتك مع:
• مزودي الخدمات: شركات تساعدنا في تقديم خدماتنا (مثل خدمات الترجمة والتعرف على الكلام).
• شركاء الدفع: لمعالجة المدفوعات والاشتراكات.
• الجهات القانونية: عندما يكون ذلك مطلوبًا بموجب القانون.

لن نبيع معلوماتك الشخصية لأطراف ثالثة لأغراض تسويقية.'''),
            _buildSection(
              'أمان البيانات',
              'نتخذ تدابير أمنية معقولة لحماية معلوماتك من الوصول غير المصرح به أو الإفصاح عنها. ومع ذلك، لا يمكن ضمان أمان البيانات المرسلة عبر الإنترنت بنسبة 100%.',
            ),
            _buildSection('حقوقك', '''لديك الحق في:
• الوصول إلى بياناتك الشخصية.
• تصحيح بياناتك غير الدقيقة.
• حذف بياناتك (في ظروف معينة).
• الاعتراض على معالجة بياناتك.
• سحب موافقتك في أي وقت.'''),
            _buildSection(
              'التغييرات على سياسة الخصوصية',
              'قد نقوم بتحديث سياسة الخصوصية هذه من وقت لآخر. سنخطرك بأي تغييرات جوهرية من خلال إشعار في التطبيق أو عبر البريد الإلكتروني.',
            ),
            _buildSection(
              'اتصل بنا',
              'إذا كانت لديك أي أسئلة حول سياسة الخصوصية هذه، يرجى الاتصال بنا على: <EMAIL>',
            ),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  /// بناء قسم من سياسة الخصوصية
  Widget _buildSection(String title, String content) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(content, style: const TextStyle(fontSize: 16, height: 1.5)),
        ],
      ),
    );
  }
}
