import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';

/// نموذج رسالة المحادثة
class ChatMessage {
  final String role;
  final String content;

  ChatMessage({required this.role, required this.content});

  Map<String, dynamic> toJson() {
    return {'role': role, 'content': content};
  }
}

/// خدمة محادثة Gemini AI
class GeminiChatService {
  final String apiKey;

  GeminiChatService({required this.apiKey});

  /// الحصول على رد من Gemini AI
  Future<String> getChatResponse({
    required List<ChatMessage> messages,
    String? systemPrompt,
  }) async {
    try {
      // إنشاء محتوى الطلب
      String prompt = '';

      // إضافة رسالة النظام إذا كانت موجودة
      if (systemPrompt != null && systemPrompt.isNotEmpty) {
        prompt += 'Instructions: $systemPrompt\n\n';
      }

      // إضافة رسائل المحادثة
      for (final message in messages) {
        if (message.role == 'user') {
          prompt += 'User: ${message.content}\n\n';
        } else if (message.role == 'assistant') {
          prompt += 'Assistant: ${message.content}\n\n';
        }
      }

      // إنشاء طلب API
      final url = Uri.parse(
        'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=$apiKey',
      );

      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'contents': [
            {
              'parts': [
                {'text': prompt},
              ],
            },
          ],
          'generationConfig': {'temperature': 0.7, 'maxOutputTokens': 1000},
        }),
      );

      // معالجة الاستجابة
      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['candidates'] != null &&
            data['candidates'].isNotEmpty &&
            data['candidates'][0]['content'] != null &&
            data['candidates'][0]['content']['parts'] != null &&
            data['candidates'][0]['content']['parts'].isNotEmpty) {
          return data['candidates'][0]['content']['parts'][0]['text'].trim();
        } else {
          throw Exception('Invalid response format from Gemini API');
        }
      } else {
        debugPrint('Gemini API error: ${response.body}');
        throw Exception('Gemini API error: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error getting chat response: $e');
      throw Exception('Failed to get response from Gemini: $e');
    }
  }

  /// الحصول على رد من Gemini AI مع سياق محدد
  Future<String> getChatResponseWithContext({
    required String message,
    required String responseType,
    List<ChatMessage>? chatHistory,
  }) async {
    try {
      // بناء سياق المحادثة
      String systemPrompt = _getSystemPrompt(responseType);

      // بناء رسائل المحادثة
      final List<ChatMessage> messages = [];

      // إضافة سجل المحادثة السابق
      if (chatHistory != null && chatHistory.isNotEmpty) {
        // إضافة آخر 10 رسائل فقط لتجنب تجاوز الحد الأقصى للرموز
        final recentHistory =
            chatHistory.length > 10
                ? chatHistory.sublist(chatHistory.length - 10)
                : chatHistory;

        messages.addAll(recentHistory);
      }

      // إضافة رسالة المستخدم الحالية
      messages.add(ChatMessage(role: 'user', content: message));

      // الحصول على الرد
      return await getChatResponse(
        messages: messages,
        systemPrompt: systemPrompt,
      );
    } catch (e) {
      debugPrint('Error getting chat response with context: $e');
      throw Exception('Failed to get response: $e');
    }
  }

  /// الحصول على سياق النظام حسب نوع الاستجابة
  String _getSystemPrompt(String responseType) {
    switch (responseType) {
      case 'general':
        return 'أنت مساعد ذكي ومفيد. قدم إجابات دقيقة ومفيدة على أسئلة المستخدم. استخدم اللغة العربية في إجاباتك.';
      case 'academic':
        return 'أنت مساعد أكاديمي متخصص. قدم إجابات علمية دقيقة ومفصلة مع ذكر المصادر إن أمكن. استخدم اللغة العربية الفصحى في إجاباتك.';
      case 'creative':
        return 'أنت مساعد إبداعي. قدم إجابات إبداعية وملهمة. يمكنك استخدام الأسلوب الأدبي والشعري عند الحاجة. استخدم اللغة العربية في إجاباتك.';
      case 'professional':
        return 'أنت مساعد محترف. قدم إجابات موجزة ودقيقة بأسلوب رسمي ومهني. استخدم اللغة العربية الفصحى في إجاباتك.';
      case 'friendly':
        return 'أنت مساعد ودود. قدم إجابات بأسلوب ودي وغير رسمي. استخدم اللغة العربية البسيطة والمفهومة في إجاباتك.';
      default:
        return 'أنت مساعد ذكي ومفيد. قدم إجابات دقيقة ومفيدة على أسئلة المستخدم. استخدم اللغة العربية في إجاباتك.';
    }
  }
}
