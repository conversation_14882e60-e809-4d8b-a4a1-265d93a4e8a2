import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../config/constants.dart';

/// خدمة إدارة مفاتيح API
class ApiKeyManager {
  final FirebaseRemoteConfig _remoteConfig;
  final SharedPreferences _prefs;

  ApiKeyManager(this._remoteConfig, this._prefs);

  /// الحصول على مفتاح API النشط حسب الترتيب المحدد
  String getActiveApiKey() {
    // الحصول على نوع الخدمة النشطة من Remote Config
    final activeService = _remoteConfig.getString(
      AppConstants.keyActiveApiService,
    );

    // إذا تم تحديد خدمة معينة، استخدمها
    if (activeService.isNotEmpty) {
      return _getApiKeyByService(activeService);
    }

    // ترتيب الأولوية: Gemini (مدفوع) ثم OpenAI ثم OpenRouter
    // استخدام Gemini المدفوع أولاً
    if (!isServiceTemporarilyDisabled(AppConstants.apiServiceGemini)) {
      final geminiKey = _remoteConfig.getString(AppConstants.keyGeminiApiKey);
      if (geminiKey.isNotEmpty) {
        return geminiKey;
      }
      // استخدام المفتاح المدفوع المباشر
      return AppConstants.keyGeminiApiKeyPaid;
    }

    final openaiKey = _remoteConfig.getString(AppConstants.keyOpenAIApiKey);
    if (openaiKey.isNotEmpty &&
        !isServiceTemporarilyDisabled(AppConstants.apiServiceOpenAI)) {
      return openaiKey;
    }

    final openrouterKey = _remoteConfig.getString(
      AppConstants.keyOpenRouterApiKey,
    );
    if (openrouterKey.isNotEmpty &&
        !isServiceTemporarilyDisabled(AppConstants.apiServiceOpenRouter)) {
      return openrouterKey;
    }

    // إذا لم يتم العثور على أي مفتاح، استخدم المفتاح المجاني
    return AppConstants.keyGeminiApiKeyFree;
  }

  /// الحصول على نوع الخدمة النشطة
  String getActiveServiceType() {
    // الحصول على نوع الخدمة النشطة من Remote Config
    final activeService = _remoteConfig.getString(
      AppConstants.keyActiveApiService,
    );

    // إذا تم تحديد خدمة معينة، أرجعها
    if (activeService.isNotEmpty) {
      return activeService;
    }

    // ترتيب الأولوية: Gemini ثم OpenAI ثم OpenRouter
    if (!isServiceTemporarilyDisabled(AppConstants.apiServiceGemini)) {
      return AppConstants.apiServiceGemini;
    }

    final openaiKey = _remoteConfig.getString(AppConstants.keyOpenAIApiKey);
    if (openaiKey.isNotEmpty &&
        !isServiceTemporarilyDisabled(AppConstants.apiServiceOpenAI)) {
      return AppConstants.apiServiceOpenAI;
    }

    final openrouterKey = _remoteConfig.getString(
      AppConstants.keyOpenRouterApiKey,
    );
    if (openrouterKey.isNotEmpty &&
        !isServiceTemporarilyDisabled(AppConstants.apiServiceOpenRouter)) {
      return AppConstants.apiServiceOpenRouter;
    }

    // إذا لم يتم العثور على أي خدمة، أرجع Gemini كافتراضي
    return AppConstants.apiServiceGemini;
  }

  /// الحصول على مفتاح API حسب نوع الخدمة (عام)
  String getApiKey(String serviceType) {
    return _getApiKeyByService(serviceType);
  }

  /// الحصول على مفتاح API حسب نوع الخدمة
  String _getApiKeyByService(String serviceType) {
    switch (serviceType) {
      case AppConstants.apiServiceOpenAI:
        return _remoteConfig.getString(AppConstants.keyOpenAIApiKey);
      case AppConstants.apiServiceGemini:
        // استخدام المفتاح المدفوع أولاً، ثم المفتاح المجاني إذا كان المدفوع معطلاً
        final geminiKey = _remoteConfig.getString(AppConstants.keyGeminiApiKey);
        if (geminiKey.isNotEmpty) {
          return geminiKey; // المفتاح من Remote Config
        } else {
          // التحقق من تاريخ انتهاء صلاحية المفتاح المدفوع
          final endDateStr = _remoteConfig.getString(
            AppConstants.keyGeminiEndDate,
          );
          if (endDateStr.isNotEmpty) {
            try {
              final endDate = DateTime.parse(endDateStr);
              if (DateTime.now().isBefore(endDate)) {
                return AppConstants.keyGeminiApiKeyPaid; // المفتاح المدفوع
              }
            } catch (e) {
              // في حالة حدوث خطأ في تحليل التاريخ
            }
          }
          return AppConstants.keyGeminiApiKeyFree; // المفتاح المجاني
        }
      case AppConstants.apiServiceOpenRouter:
        return _remoteConfig.getString(AppConstants.keyOpenRouterApiKey);
      case AppConstants.apiServiceGoogleTranslate:
        return _remoteConfig.getString(AppConstants.keyGoogleTranslateApiKey);
      default:
        return '';
    }
  }

  /// تسجيل استخدام API
  Future<void> trackUsage(String serviceType) async {
    final today = DateTime.now().toIso8601String().split('T')[0];
    final key = 'usage_${serviceType}_$today';

    int currentUsage = _prefs.getInt(key) ?? 0;
    await _prefs.setInt(key, currentUsage + 1);

    // التحقق من تجاوز الحد اليومي
    int limit = 0;
    switch (serviceType) {
      case AppConstants.apiServiceOpenAI:
        limit = _remoteConfig.getInt(AppConstants.keyDailyLimitOpenAI);
        break;
      case AppConstants.apiServiceGemini:
        limit = _remoteConfig.getInt(AppConstants.keyDailyLimitGemini);
        break;
      case AppConstants.apiServiceOpenRouter:
        limit = _remoteConfig.getInt(AppConstants.keyDailyLimitOpenRouter);
        break;
    }

    if (limit > 0 && currentUsage + 1 >= limit) {
      await _prefs.setBool('disabled_$serviceType', true);
      await _prefs.setString(
        'disabled_until_$serviceType',
        DateTime.now().add(const Duration(hours: 24)).toIso8601String(),
      );
    }
  }

  /// التحقق مما إذا كانت الخدمة معطلة مؤقتًا
  bool isServiceTemporarilyDisabled(String serviceType) {
    bool isDisabled = _prefs.getBool('disabled_$serviceType') ?? false;

    if (isDisabled) {
      String disabledUntilStr =
          _prefs.getString('disabled_until_$serviceType') ?? '';

      if (disabledUntilStr.isNotEmpty) {
        DateTime disabledUntil = DateTime.parse(disabledUntilStr);

        // إذا انتهت فترة التعطيل، قم بإعادة تفعيل الخدمة
        if (DateTime.now().isAfter(disabledUntil)) {
          _prefs.setBool('disabled_$serviceType', false);
          return false;
        }
      }
    }

    return isDisabled;
  }

  /// اختيار الخدمة التالية في حالة الفشل
  String getNextServiceAfterFailure(String currentService) {
    switch (currentService) {
      case AppConstants.apiServiceOpenAI:
        return isServiceTemporarilyDisabled(AppConstants.apiServiceGemini)
            ? AppConstants.apiServiceOpenRouter
            : AppConstants.apiServiceGemini;
      case AppConstants.apiServiceGemini:
        return isServiceTemporarilyDisabled(AppConstants.apiServiceOpenRouter)
            ? AppConstants.apiServiceOpenAI
            : AppConstants.apiServiceOpenRouter;
      case AppConstants.apiServiceOpenRouter:
        return isServiceTemporarilyDisabled(AppConstants.apiServiceOpenAI)
            ? AppConstants.apiServiceGemini
            : AppConstants.apiServiceOpenAI;
      default:
        return AppConstants.apiServiceOpenAI;
    }
  }

  /// تسجيل فشل API
  Future<void> recordFailure(String serviceType) async {
    final today = DateTime.now().toIso8601String().split('T')[0];
    final key = 'failures_${serviceType}_$today';

    int failures = _prefs.getInt(key) ?? 0;
    await _prefs.setInt(key, failures + 1);

    // إذا تجاوز عدد مرات الفشل الحد المسموح، قم بتعطيل الخدمة مؤقتًا
    if (failures >= 5) {
      await _prefs.setBool('disabled_$serviceType', true);
      await _prefs.setString(
        'disabled_until_$serviceType',
        DateTime.now().add(const Duration(hours: 1)).toIso8601String(),
      );
    }
  }

  /// الحصول على إحصائيات الاستخدام
  Map<String, int> getUsageStats() {
    final today = DateTime.now().toIso8601String().split('T')[0];
    final Map<String, int> stats = {};

    stats[AppConstants.apiServiceOpenAI] =
        _prefs.getInt('usage_${AppConstants.apiServiceOpenAI}_$today') ?? 0;
    stats[AppConstants.apiServiceGemini] =
        _prefs.getInt('usage_${AppConstants.apiServiceGemini}_$today') ?? 0;
    stats[AppConstants.apiServiceOpenRouter] =
        _prefs.getInt('usage_${AppConstants.apiServiceOpenRouter}_$today') ?? 0;

    return stats;
  }
}
