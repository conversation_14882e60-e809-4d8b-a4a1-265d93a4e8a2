name: ai_smart_translator_new
description: "AI Smart Translator New - Advanced translation app with multiple features and AI assistance."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # UI and Design
  cupertino_icons: ^1.0.8
  google_fonts: ^6.1.0
  flutter_svg: ^2.0.9
  lottie: ^3.0.0
  flutter_animate: ^4.5.0
  flutter_staggered_animations: ^1.1.1
  simple_animations: ^5.0.2
  shimmer: ^3.0.0
  cached_network_image: ^3.3.1

  # State Management
  provider: ^6.1.1
  flutter_bloc: ^8.1.3

  # Firebase
  firebase_core: ^2.24.2
  firebase_auth: ^4.15.3
  firebase_storage: ^11.5.6
  cloud_firestore: ^4.13.6
  firebase_remote_config: ^4.3.8
  firebase_analytics: ^10.7.4

  # Authentication
  google_sign_in: ^6.2.1

  # Translation and AI
  google_ml_kit: ^0.16.3
  speech_to_text: ^6.5.1
  flutter_tts: ^4.2.2
  camera: ^0.10.5+9
  image_picker: ^1.0.7
  file_picker: ^6.1.1
  path_provider: ^2.1.1
  http: ^1.1.2
  dio: ^5.4.0
  # Advanced Speech Recognition
  audio_session: ^0.1.18
  record: ^5.0.4
  audioplayers: ^5.2.1

  # PDF and Document
  syncfusion_flutter_pdf: ^24.1.41
  pdf: ^3.10.7
  flutter_pdfview: ^1.3.2

  # Ads
  google_mobile_ads: ^4.0.0

  # In-App Purchase
  in_app_purchase: ^3.1.11
  in_app_purchase_android: ^0.3.0+15
  in_app_purchase_storekit: ^0.3.6+7

  # Storage and Preferences
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Utilities
  intl: ^0.19.0
  url_launcher: ^6.2.2
  permission_handler: ^11.1.0
  connectivity_plus: ^5.0.2
  device_info_plus: ^9.1.1
  package_info_plus: ^5.0.1
  share_plus: ^7.2.1
  uuid: ^4.3.3
  logger: ^2.0.2+1
  flutter_dotenv: ^5.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  build_runner: ^2.4.7
  hive_generator: ^2.0.1
  flutter_launcher_icons: ^0.13.1
  flutter_native_splash: ^2.3.8

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # App localization
  generate: true

  # Assets
  assets:
    # App images
    - assets/images/
    - assets/images/logo/
    - assets/images/onboarding/
    - assets/images/icons/
    - assets/images/backgrounds/
    - assets/images/kids/

    # Animations
    - assets/animations/

    # Fonts
    - assets/fonts/

    # Audio files
    - assets/audio/
    - assets/audio/effects/
    - assets/audio/background/

    # Other
    - assets/data/

  # Fonts - تم تعطيل خطوط Cairo مؤقتاً لحل مشكلة الملفات المفقودة
  # fonts:
  #   - family: Cairo
  #     fonts:
  #       - asset: assets/fonts/Cairo-Regular.ttf
  #       - asset: assets/fonts/Cairo-Bold.ttf
  #         weight: 700
  #       - asset: assets/fonts/Cairo-SemiBold.ttf
  #         weight: 600
  #       - asset: assets/fonts/Cairo-Medium.ttf
  #         weight: 500
  #       - asset: assets/fonts/Cairo-Light.ttf
  #         weight: 300
