import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:share_plus/share_plus.dart';
import 'package:provider/provider.dart';
import '../../../config/app_theme.dart';
import '../../../utils/helpers.dart';
import 'image_translation_service.dart';
import '../../../providers/app_state.dart';
import '../../../services/api/translation_service.dart';

/// شاشة ترجمة الصور
class ImageTranslationScreen extends StatefulWidget {
  const ImageTranslationScreen({super.key});

  @override
  State<ImageTranslationScreen> createState() => _ImageTranslationScreenState();
}

class _ImageTranslationScreenState extends State<ImageTranslationScreen> {
  File? _selectedImage;
  String _extractedText = '';
  String _translatedText = '';
  String _sourceLanguage = 'auto';
  String _targetLanguage = 'ar';
  bool _isProcessing = false;
  bool _isTranslating = false;
  bool _isFavorite = false;
  final ImagePicker _imagePicker = ImagePicker();
  final ImageTranslationService _imageTranslationService =
      ImageTranslationService();

  /// التقاط صورة من الكاميرا
  Future<void> _captureImage() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
      );

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
          _extractedText = '';
          _translatedText = '';
          _isFavorite = false;
        });

        _processImage();
      }
    } catch (e) {
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء التقاط الصورة: $e',
          isError: true,
        );
      }
    }
  }

  /// اختيار صورة من المعرض
  Future<void> _pickImage() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
      );

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
          _extractedText = '';
          _translatedText = '';
          _isFavorite = false;
        });

        _processImage();
      }
    } catch (e) {
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء اختيار الصورة: $e',
          isError: true,
        );
      }
    }
  }

  /// معالجة الصورة واستخراج النص
  Future<void> _processImage() async {
    if (_selectedImage == null) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      final extractedText = await _imageTranslationService.extractTextFromImage(
        _selectedImage!.path,
      );

      if (mounted) {
        setState(() {
          _extractedText = extractedText;
          _isProcessing = false;
        });

        // ترجمة النص المستخرج تلقائيًا
        if (_extractedText.isNotEmpty) {
          _translateExtractedText();
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });

        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء معالجة الصورة: $e',
          isError: true,
        );
      }
    }
  }

  /// ترجمة النص المستخرج
  Future<void> _translateExtractedText() async {
    if (_extractedText.isEmpty) return;

    setState(() {
      _isTranslating = true;
    });

    try {
      final appState = Provider.of<AppState>(context, listen: false);
      final remoteConfig = appState.remoteConfig;
      final translationService = TranslationService(remoteConfig);
      _imageTranslationService.setTranslationService(translationService);
      final translatedText = await _imageTranslationService.translateText(
        _extractedText,
        _sourceLanguage,
        _targetLanguage,
      );

      if (mounted) {
        setState(() {
          _translatedText = translatedText;
          _isTranslating = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isTranslating = false;
        });

        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء ترجمة النص: $e',
          isError: true,
        );
      }
    }
  }

  /// تبديل اللغات
  void _swapLanguages() {
    if (_sourceLanguage == 'auto') {
      // إذا كانت اللغة المصدر تلقائية، نقوم بتعيينها إلى اللغة الهدف
      setState(() {
        _sourceLanguage = _targetLanguage;
        _targetLanguage = 'en'; // تعيين اللغة الهدف إلى الإنجليزية كافتراضي
      });
    } else {
      setState(() {
        final temp = _sourceLanguage;
        _sourceLanguage = _targetLanguage;
        _targetLanguage = temp;
      });
    }

    // إعادة ترجمة النص بعد تبديل اللغات
    if (_extractedText.isNotEmpty) {
      _translateExtractedText();
    }
  }

  /// نسخ النص المترجم
  void _copyTranslatedText() {
    if (_translatedText.isNotEmpty) {
      Clipboard.setData(ClipboardData(text: _translatedText));
      AppHelpers.showSnackBar(context, 'تم نسخ النص المترجم');
    }
  }

  /// مشاركة النص المترجم
  void _shareTranslatedText() {
    if (_translatedText.isNotEmpty) {
      Share.share(_translatedText);
    }
  }

  /// إضافة إلى المفضلة
  void _toggleFavorite() {
    if (_translatedText.isNotEmpty) {
      setState(() {
        _isFavorite = !_isFavorite;
      });

      AppHelpers.showSnackBar(
        context,
        _isFavorite ? 'تمت الإضافة إلى المفضلة' : 'تمت الإزالة من المفضلة',
      );

      // هنا يمكن إضافة كود لحفظ الترجمة في المفضلة
    }
  }

  /// الحصول على اسم اللغة
  String _getLanguageName(String code) {
    switch (code) {
      case 'auto':
        return '🔍 تلقائي';
      case 'ar':
        return '🇸🇦 العربية';
      case 'en':
        return '🇺🇸 English';
      case 'fr':
        return '🇫🇷 Français';
      case 'es':
        return '🇪🇸 Español';
      case 'de':
        return '🇩🇪 Deutsch';
      case 'it':
        return '🇮🇹 Italiano';
      case 'ru':
        return '🇷🇺 Русский';
      case 'zh':
        return '🇨🇳 中文';
      case 'ja':
        return '🇯🇵 日本語';
      case 'ko':
        return '🇰🇷 한국어';
      default:
        return code;
    }
  }

  /// عرض مربع حوار اختيار اللغة
  void _showLanguageSelector(bool isSource) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                isSource ? 'اختر لغة المصدر' : 'اختر لغة الهدف',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              Expanded(
                child: ListView(
                  children: [
                    if (isSource) _buildLanguageItem(context, 'auto', isSource),
                    _buildLanguageItem(context, 'ar', isSource),
                    _buildLanguageItem(context, 'en', isSource),
                    _buildLanguageItem(context, 'fr', isSource),
                    _buildLanguageItem(context, 'es', isSource),
                    _buildLanguageItem(context, 'de', isSource),
                    _buildLanguageItem(context, 'it', isSource),
                    _buildLanguageItem(context, 'ru', isSource),
                    _buildLanguageItem(context, 'zh', isSource),
                    _buildLanguageItem(context, 'ja', isSource),
                    _buildLanguageItem(context, 'ko', isSource),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// بناء عنصر اللغة
  Widget _buildLanguageItem(BuildContext context, String code, bool isSource) {
    final isSelected =
        isSource ? _sourceLanguage == code : _targetLanguage == code;

    return ListTile(
      title: Text(_getLanguageName(code)),
      trailing:
          isSelected
              ? Icon(Icons.check, color: Theme.of(context).colorScheme.primary)
              : null,
      onTap: () {
        setState(() {
          if (isSource) {
            _sourceLanguage = code;
          } else {
            _targetLanguage = code;
          }
        });
        Navigator.pop(context);

        // إعادة ترجمة النص بعد تغيير اللغة
        if (_extractedText.isNotEmpty) {
          _translateExtractedText();
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('ترجمة الصور'),
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // اختيار اللغات
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Color.fromRGBO(0, 0, 0, 0.04),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Row(
                children: [
                  // اللغة المصدر
                  Expanded(
                    child: InkWell(
                      onTap: () => _showLanguageSelector(true),
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          _getLanguageName(_sourceLanguage),
                          textAlign: TextAlign.center,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                  ),

                  // زر تبديل اللغات
                  IconButton(
                    icon: const Icon(Icons.swap_horiz),
                    onPressed: _swapLanguages,
                    color: AppTheme.primaryColor,
                  ),

                  // اللغة الهدف
                  Expanded(
                    child: InkWell(
                      onTap: () => _showLanguageSelector(false),
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          _getLanguageName(_targetLanguage),
                          textAlign: TextAlign.center,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // أزرار التقاط/اختيار الصورة
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _captureImage,
                    icon: const Icon(Icons.camera_alt),
                    label: const Text('التقاط صورة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _pickImage,
                    icon: const Icon(Icons.photo_library),
                    label: const Text('اختيار صورة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.secondaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // عرض الصورة المختارة
            if (_selectedImage != null)
              Container(
                width: double.infinity,
                height: 250,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Color.fromRGBO(0, 0, 0, 0.08),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16),
                  child: Image.file(_selectedImage!, fit: BoxFit.cover),
                ),
              ),

            const SizedBox(height: 20),

            // مؤشر التحميل أثناء المعالجة
            if (_isProcessing)
              const Center(
                child: Column(
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 10),
                    Text('جاري معالجة الصورة...'),
                  ],
                ),
              ),

            // النص المستخرج
            if (_extractedText.isNotEmpty && !_isProcessing)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'النص المستخرج:',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: Text(
                      _extractedText,
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                ],
              ),

            const SizedBox(height: 20),

            // مؤشر التحميل أثناء الترجمة
            if (_isTranslating)
              const Center(
                child: Column(
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 10),
                    Text('جاري ترجمة النص...'),
                  ],
                ),
              ),

            // النص المترجم
            if (_translatedText.isNotEmpty && !_isTranslating)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'الترجمة:',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Row(
                        children: [
                          // زر النسخ
                          IconButton(
                            icon: const Icon(Icons.copy, size: 20),
                            onPressed: _copyTranslatedText,
                            tooltip: 'نسخ',
                          ),
                          // زر المشاركة
                          IconButton(
                            icon: const Icon(Icons.share, size: 20),
                            onPressed: _shareTranslatedText,
                            tooltip: 'مشاركة',
                          ),
                          // زر المفضلة
                          IconButton(
                            icon: Icon(
                              _isFavorite
                                  ? Icons.favorite
                                  : Icons.favorite_border,
                              size: 20,
                              color: _isFavorite ? Colors.red : null,
                            ),
                            onPressed: _toggleFavorite,
                            tooltip: 'إضافة إلى المفضلة',
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Color.fromRGBO(0, 122, 255, 0.4),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Color.fromRGBO(0, 0, 0, 0.04),
                          blurRadius: 5,
                          offset: const Offset(0, 3),
                        ),
                      ],
                    ),
                    child: Text(
                      _translatedText,
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }
}
