import 'package:flutter/material.dart';
import '../../../models/duolingo_models.dart';
import '../../../utils/responsive_helper.dart';

/// ويدجت رأس التقدم مثل Duolingo
class ProgressHeader extends StatelessWidget {
  final UserProfile? userProfile;
  final LearningStats? stats;
  final String languageCode;

  const ProgressHeader({
    super.key,
    this.userProfile,
    this.stats,
    required this.languageCode,
  });

  @override
  Widget build(BuildContext context) {
    final margin = ResponsiveHelper.getResponsivePadding(
      context,
      mobile: 16,
      tablet: 20,
      desktop: 24,
    );

    final padding = ResponsiveHelper.getResponsivePadding(
      context,
      mobile: 16,
      tablet: 20,
      desktop: 24,
    );

    final borderRadius = ResponsiveHelper.getResponsiveBorderRadius(context);

    return Container(
      margin: EdgeInsets.all(margin),
      padding: EdgeInsets.all(padding),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.white, Colors.grey.shade50],
        ),
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 2,
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildUserInfo(context),
          SizedBox(
            height: ResponsiveHelper.getResponsivePadding(
              context,
              mobile: 16,
              tablet: 20,
              desktop: 24,
            ),
          ),
          _buildStatsRow(context),
          SizedBox(
            height: ResponsiveHelper.getResponsivePadding(
              context,
              mobile: 16,
              tablet: 20,
              desktop: 24,
            ),
          ),
          _buildProgressBar(context),
        ],
      ),
    );
  }

  /// معلومات المستخدم
  Widget _buildUserInfo(BuildContext context) {
    final avatarSize = ResponsiveHelper.getResponsiveIconSize(
      context,
      mobile: 50,
      tablet: 60,
      desktop: 70,
    );

    return Row(
      children: [
        // صورة المستخدم
        Container(
          width: avatarSize,
          height: avatarSize,
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color(0xFF58CC02), Color(0xFF89E219)],
            ),
            borderRadius: BorderRadius.circular(avatarSize / 2),
            border: Border.all(color: Colors.white, width: 3),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.2),
                spreadRadius: 2,
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Icon(
            Icons.person,
            color: Colors.white,
            size: avatarSize * 0.5,
          ),
        ),

        SizedBox(
          width: ResponsiveHelper.getResponsivePadding(
            context,
            mobile: 12,
            tablet: 15,
            desktop: 18,
          ),
        ),

        // اسم المستخدم والمستوى
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ResponsiveText(
                userProfile?.name ?? 'مستخدم',
                mobileFontSize: 16,
                tabletFontSize: 18,
                desktopFontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              SizedBox(
                height: ResponsiveHelper.getResponsivePadding(
                  context,
                  mobile: 2,
                  tablet: 4,
                  desktop: 6,
                ),
              ),
              Row(
                children: [
                  Icon(
                    Icons.local_fire_department,
                    color: Colors.orange,
                    size: ResponsiveHelper.getResponsiveIconSize(
                      context,
                      mobile: 14,
                      tablet: 16,
                      desktop: 18,
                    ),
                  ),
                  SizedBox(
                    width: ResponsiveHelper.getResponsivePadding(
                      context,
                      mobile: 3,
                      tablet: 4,
                      desktop: 5,
                    ),
                  ),
                  ResponsiveText(
                    '${userProfile?.currentStreak ?? 0} يوم متتالي',
                    mobileFontSize: 12,
                    tabletFontSize: 14,
                    desktopFontSize: 16,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ],
              ),
            ],
          ),
        ),

        // مستوى المستخدم
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: ResponsiveHelper.getResponsivePadding(
              context,
              mobile: 10,
              tablet: 12,
              desktop: 14,
            ),
            vertical: ResponsiveHelper.getResponsivePadding(
              context,
              mobile: 4,
              tablet: 6,
              desktop: 8,
            ),
          ),
          decoration: BoxDecoration(
            color: const Color(0xFFFF9600),
            borderRadius: BorderRadius.circular(
              ResponsiveHelper.getResponsiveBorderRadius(context),
            ),
          ),
          child: ResponsiveText(
            'المستوى ${_calculateLevel()}',
            mobileFontSize: 10,
            tabletFontSize: 12,
            desktopFontSize: 14,
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  /// صف الإحصائيات
  Widget _buildStatsRow(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildStatItem(
            icon: Icons.star,
            color: const Color(0xFFFFD700),
            value: '${userProfile?.totalXP ?? 0}',
            label: 'نقاط الخبرة',
          ),
        ),
        Expanded(
          child: _buildStatItem(
            icon: Icons.diamond,
            color: const Color(0xFF1CB0F6),
            value: '${userProfile?.gems ?? 0}',
            label: 'جواهر',
          ),
        ),
        Expanded(
          child: _buildStatItem(
            icon: Icons.favorite,
            color: const Color(0xFFFF4B4B),
            value: '${userProfile?.hearts ?? 5}',
            label: 'قلوب',
          ),
        ),
        Expanded(
          child: _buildStatItem(
            icon: Icons.school,
            color: const Color(0xFF58CC02),
            value: '${stats?.lessonsCompleted ?? 0}',
            label: 'دروس مكتملة',
          ),
        ),
      ],
    );
  }

  /// عنصر إحصائية
  Widget _buildStatItem({
    required IconData icon,
    required Color color,
    required String value,
    required String label,
  }) {
    return Column(
      children: [
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(25),
          ),
          child: Icon(icon, color: color, size: 24),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        Text(
          label,
          style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// شريط التقدم
  Widget _buildProgressBar(BuildContext context) {
    final currentXP = userProfile?.languageXP[languageCode] ?? 0;
    final nextLevelXP = _getNextLevelXP();
    final progress = currentXP / nextLevelXP;

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'التقدم في ${_getLanguageName()}',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            Text(
              '$currentXP / $nextLevelXP XP',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 10),

        // شريط التقدم
        Container(
          height: 12,
          decoration: BoxDecoration(
            color: Colors.grey.shade200,
            borderRadius: BorderRadius.circular(6),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(6),
            child: LinearProgressIndicator(
              value: progress.clamp(0.0, 1.0),
              backgroundColor: Colors.transparent,
              valueColor: AlwaysStoppedAnimation<Color>(_getLanguageColor()),
            ),
          ),
        ),

        const SizedBox(height: 8),

        // نص التقدم
        Text(
          '${(progress * 100).toInt()}% مكتمل',
          style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
        ),
      ],
    );
  }

  /// حساب المستوى
  int _calculateLevel() {
    final totalXP = userProfile?.totalXP ?? 0;
    return (totalXP / 100).floor() + 1;
  }

  /// الحصول على XP المطلوب للمستوى التالي
  int _getNextLevelXP() {
    final currentLevel = _calculateLevel();
    return currentLevel * 100;
  }

  /// الحصول على اسم اللغة
  String _getLanguageName() {
    final languageNames = {
      'en': 'الإنجليزية',
      'fr': 'الفرنسية',
      'de': 'الألمانية',
      'es': 'الإسبانية',
      'it': 'الإيطالية',
      'ja': 'اليابانية',
      'zh': 'الصينية',
      'tr': 'التركية',
      'ru': 'الروسية',
    };
    return languageNames[languageCode] ?? 'اللغة';
  }

  /// الحصول على لون اللغة
  Color _getLanguageColor() {
    final languageColors = {
      'en': const Color(0xFF1CB0F6),
      'fr': const Color(0xFF4B4DFF),
      'de': const Color(0xFFFF4B4B),
      'es': const Color(0xFFFF9600),
      'it': const Color(0xFF58CC02),
      'ja': const Color(0xFFFF4B4B),
      'zh': const Color(0xFFFF4B4B),
      'tr': const Color(0xFFFF4B4B),
      'ru': const Color(0xFF1CB0F6),
    };
    return languageColors[languageCode] ?? const Color(0xFF58CC02);
  }
}
