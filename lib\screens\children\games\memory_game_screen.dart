import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math';
import '../../../services/educational_service.dart';

/// لعبة الذاكرة التعليمية
class MemoryGameScreen extends StatefulWidget {
  final String category;
  final EducationalService educationalService;

  const MemoryGameScreen({
    super.key,
    required this.category,
    required this.educationalService,
  });

  @override
  State<MemoryGameScreen> createState() => _MemoryGameScreenState();
}

class _MemoryGameScreenState extends State<MemoryGameScreen>
    with TickerProviderStateMixin {
  late AnimationController _flipController;
  late AnimationController _matchController;

  List<MemoryCard> _cards = [];
  final List<int> _flippedCards = [];
  int _score = 0;
  int _moves = 0;
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    _flipController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _matchController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _initializeGame();
  }

  @override
  void dispose() {
    _flipController.dispose();
    _matchController.dispose();
    super.dispose();
  }

  /// تهيئة اللعبة
  void _initializeGame() {
    final words = _getWordsForCategory();
    _cards = [];

    // إنشاء أزواج البطاقات
    for (int i = 0; i < words.length; i++) {
      _cards.add(
        MemoryCard(
          id: i * 2,
          word: words[i],
          isArabic: true,
          isFlipped: false,
          isMatched: false,
        ),
      );
      _cards.add(
        MemoryCard(
          id: i * 2 + 1,
          word: words[i],
          isArabic: false,
          isFlipped: false,
          isMatched: false,
        ),
      );
    }

    // خلط البطاقات
    _cards.shuffle(Random());
  }

  /// الحصول على الكلمات حسب الفئة
  List<String> _getWordsForCategory() {
    switch (widget.category) {
      case 'animals':
        return ['قطة', 'كلب', 'بقرة', 'حصان', 'فيل', 'أسد'];
      case 'colors':
        return ['أحمر', 'أزرق', 'أخضر', 'أصفر', 'بنفسجي', 'برتقالي'];
      case 'family':
        return ['أب', 'أم', 'أخ', 'أخت', 'جد', 'جدة'];
      default:
        return ['قطة', 'كلب', 'بقرة', 'حصان'];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text('لعبة الذاكرة'),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
        actions: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            margin: const EdgeInsets.only(right: 16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              'النقاط: $_score',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          _buildGameInfo(),
          Expanded(child: _buildGameGrid()),
          _buildBottomControls(),
        ],
      ),
    );
  }

  /// معلومات اللعبة
  Widget _buildGameInfo() {
    return Container(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 2,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildInfoItem('الحركات', '$_moves', Icons.touch_app),
          _buildInfoItem(
            'المطابقات',
            '${_getMatchedPairs()}/${_cards.length ~/ 2}',
            Icons.check_circle,
          ),
          _buildInfoItem('النقاط', '$_score', Icons.star),
        ],
      ),
    );
  }

  /// عنصر معلومات
  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.purple, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.purple,
          ),
        ),
        Text(
          label,
          style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
        ),
      ],
    );
  }

  /// شبكة اللعبة
  Widget _buildGameGrid() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4,
          childAspectRatio: 0.8,
          crossAxisSpacing: 10,
          mainAxisSpacing: 10,
        ),
        itemCount: _cards.length,
        itemBuilder: (context, index) {
          return _buildMemoryCard(index);
        },
      ),
    );
  }

  /// بطاقة الذاكرة
  Widget _buildMemoryCard(int index) {
    final card = _cards[index];
    final isFlipped = card.isFlipped || card.isMatched;

    return GestureDetector(
      onTap: () => _onCardTapped(index),
      child: AnimatedBuilder(
        animation: _flipController,
        builder: (context, child) {
          return Transform(
            alignment: Alignment.center,
            transform:
                Matrix4.identity()
                  ..setEntry(3, 2, 0.001)
                  ..rotateY(isFlipped ? 0 : 3.14159),
            child: Container(
              decoration: BoxDecoration(
                color:
                    card.isMatched
                        ? Colors.green.shade100
                        : isFlipped
                        ? Colors.white
                        : Colors.purple.shade100,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color:
                      card.isMatched
                          ? Colors.green
                          : isFlipped
                          ? Colors.purple
                          : Colors.purple.shade300,
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.2),
                    spreadRadius: 1,
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: isFlipped ? _buildCardContent(card) : _buildCardBack(),
            ),
          );
        },
      ),
    );
  }

  /// محتوى البطاقة
  Widget _buildCardContent(MemoryCard card) {
    final translations = widget.educationalService.getWordTranslations(
      card.word,
    );
    final displayText =
        card.isArabic ? card.word : translations['en'] ?? card.word;

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          displayText,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color:
                card.isMatched ? Colors.green.shade700 : Colors.purple.shade700,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        Icon(
          card.isArabic ? Icons.language : Icons.translate,
          color:
              card.isMatched ? Colors.green.shade700 : Colors.purple.shade700,
          size: 16,
        ),
      ],
    );
  }

  /// ظهر البطاقة
  Widget _buildCardBack() {
    return const Center(
      child: Icon(Icons.help_outline, size: 32, color: Colors.purple),
    );
  }

  /// أدوات التحكم السفلية
  Widget _buildBottomControls() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _restartGame,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة البدء'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _showHint,
              icon: const Icon(Icons.lightbulb),
              label: const Text('تلميح'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// النقر على البطاقة
  void _onCardTapped(int index) {
    if (_isProcessing || _cards[index].isFlipped || _cards[index].isMatched) {
      return;
    }

    HapticFeedback.lightImpact();

    setState(() {
      _cards[index].isFlipped = true;
      _flippedCards.add(index);
      _moves++;
    });

    // نطق الكلمة
    _speakCard(_cards[index]);

    if (_flippedCards.length == 2) {
      _checkForMatch();
    }
  }

  /// التحقق من التطابق
  void _checkForMatch() {
    _isProcessing = true;

    final firstCard = _cards[_flippedCards[0]];
    final secondCard = _cards[_flippedCards[1]];

    if (firstCard.word == secondCard.word &&
        firstCard.isArabic != secondCard.isArabic) {
      // تطابق!
      Future.delayed(const Duration(milliseconds: 1000), () {
        setState(() {
          firstCard.isMatched = true;
          secondCard.isMatched = true;
          _score += 10;
        });

        _matchController.forward().then((_) {
          _matchController.reset();
          _checkGameComplete();
        });

        _flippedCards.clear();
        _isProcessing = false;
      });
    } else {
      // عدم تطابق
      Future.delayed(const Duration(milliseconds: 1500), () {
        setState(() {
          firstCard.isFlipped = false;
          secondCard.isFlipped = false;
        });

        _flippedCards.clear();
        _isProcessing = false;
      });
    }
  }

  /// نطق البطاقة
  Future<void> _speakCard(MemoryCard card) async {
    final translations = widget.educationalService.getWordTranslations(
      card.word,
    );
    final text = card.isArabic ? card.word : translations['en'] ?? card.word;
    final languageCode = card.isArabic ? 'ar' : 'en';

    await widget.educationalService.speakInLanguage(text, languageCode);
  }

  /// الحصول على عدد الأزواج المطابقة
  int _getMatchedPairs() {
    return _cards.where((card) => card.isMatched).length ~/ 2;
  }

  /// التحقق من اكتمال اللعبة
  void _checkGameComplete() {
    if (_cards.every((card) => card.isMatched)) {
      _showCompletionDialog();
    }
  }

  /// حوار الإكمال
  void _showCompletionDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            title: const Text('🎉 مبروك!'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('لقد أكملت اللعبة بنجاح!'),
                const SizedBox(height: 10),
                Text('النقاط: $_score'),
                Text('الحركات: $_moves'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  Navigator.pop(context);
                },
                child: const Text('العودة'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _restartGame();
                },
                child: const Text('لعب مرة أخرى'),
              ),
            ],
          ),
    );
  }

  /// إعادة بدء اللعبة
  void _restartGame() {
    setState(() {
      _score = 0;
      _moves = 0;
      _flippedCards.clear();
      _isProcessing = false;
    });
    _initializeGame();
  }

  /// عرض تلميح
  void _showHint() {
    // البحث عن أول بطاقتين غير مطابقتين
    for (int i = 0; i < _cards.length; i++) {
      if (!_cards[i].isMatched && !_cards[i].isFlipped) {
        for (int j = i + 1; j < _cards.length; j++) {
          if (!_cards[j].isMatched &&
              !_cards[j].isFlipped &&
              _cards[i].word == _cards[j].word &&
              _cards[i].isArabic != _cards[j].isArabic) {
            // عرض التلميح
            setState(() {
              _cards[i].isFlipped = true;
              _cards[j].isFlipped = true;
            });

            Future.delayed(const Duration(seconds: 2), () {
              setState(() {
                _cards[i].isFlipped = false;
                _cards[j].isFlipped = false;
              });
            });
            return;
          }
        }
      }
    }
  }
}

/// نموذج بطاقة الذاكرة
class MemoryCard {
  final int id;
  final String word;
  final bool isArabic;
  bool isFlipped;
  bool isMatched;

  MemoryCard({
    required this.id,
    required this.word,
    required this.isArabic,
    this.isFlipped = false,
    this.isMatched = false,
  });
}
