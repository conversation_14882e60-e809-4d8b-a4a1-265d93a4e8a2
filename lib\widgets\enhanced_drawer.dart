import 'package:flutter/material.dart';
import 'enhanced_list_item.dart';

/// قائمة جانبية محسنة مع تأثيرات بصرية ولمسية
class EnhancedDrawer extends StatelessWidget {
  /// عناصر القائمة
  final List<EnhancedDrawerItem> items;

  /// عنوان القائمة
  final Widget? header;

  /// تباعد بين العناصر
  final double itemSpacing;

  /// لون الخلفية
  final Color? backgroundColor;

  /// ارتفاع العنصر
  final double? itemHeight;

  /// تباعد داخلي
  final EdgeInsetsGeometry padding;

  /// منشئ القائمة الجانبية المحسنة
  const EnhancedDrawer({
    super.key,
    required this.items,
    this.header,
    this.itemSpacing = 4,
    this.backgroundColor,
    this.itemHeight,
    this.padding = EdgeInsets.zero,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Drawer(
      backgroundColor: backgroundColor ?? theme.scaffoldBackgroundColor,
      child: Safe<PERSON>rea(
        child: Padding(
          padding: padding,
          child: Column(
            children: [
              // رأس القائمة (إذا كان موجوداً)
              if (header != null) header!,

              // عناصر القائمة
              Expanded(
                child: ListView.separated(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  itemCount: items.length,
                  separatorBuilder:
                      (context, index) => SizedBox(height: itemSpacing),
                  itemBuilder: (context, index) {
                    final item = items[index];

                    // إذا كان العنصر فاصلاً
                    if (item.isDivider) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Divider(
                          color: theme.dividerColor,
                          thickness: 1,
                          height: 1,
                        ),
                      );
                    }

                    // إذا كان العنصر عنواناً
                    if (item.isHeader) {
                      return Padding(
                        padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                        child: Text(
                          item.title,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.primary,
                          ),
                        ),
                      );
                    }

                    // عنصر عادي
                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: EnhancedListItem(
                        icon: item.icon,
                        leading: item.leading,
                        title: item.title,
                        subtitle: item.subtitle,
                        trailing: item.trailing,
                        onTap: () {
                          // إغلاق القائمة الجانبية ثم تنفيذ الإجراء
                          Navigator.pop(context);
                          if (item.onTap != null) {
                            // إضافة تأخير بسيط لتحسين تجربة المستخدم
                            Future.delayed(
                              const Duration(milliseconds: 200),
                              () {
                                item.onTap!();
                              },
                            );
                          }
                        },
                        onLongPress: item.onLongPress,
                        backgroundColor: item.backgroundColor,
                        splashColor: item.splashColor,
                        iconColor: item.iconColor,
                        titleColor: item.titleColor,
                        subtitleColor: item.subtitleColor,
                        iconSize: item.iconSize,
                        titleSize: item.titleSize,
                        subtitleSize: item.subtitleSize,
                        height: itemHeight,
                        borderRadius: 12,
                        isDisabled: item.isDisabled,
                        isSelected: item.isSelected,
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// عنصر القائمة الجانبية المحسنة
class EnhancedDrawerItem {
  /// أيقونة العنصر
  final IconData? icon;

  /// صورة العنصر (بديل للأيقونة)
  final Widget? leading;

  /// عنوان العنصر
  final String title;

  /// وصف العنصر (اختياري)
  final String? subtitle;

  /// عنصر في نهاية العنصر (اختياري)
  final Widget? trailing;

  /// دالة يتم تنفيذها عند الضغط على العنصر
  final VoidCallback? onTap;

  /// دالة يتم تنفيذها عند الضغط المطول على العنصر
  final VoidCallback? onLongPress;

  /// لون خلفية العنصر
  final Color? backgroundColor;

  /// لون العنصر عند الضغط عليه
  final Color? splashColor;

  /// لون الأيقونة
  final Color? iconColor;

  /// لون العنوان
  final Color? titleColor;

  /// لون الوصف
  final Color? subtitleColor;

  /// حجم الأيقونة
  final double? iconSize;

  /// حجم خط العنوان
  final double? titleSize;

  /// حجم خط الوصف
  final double? subtitleSize;

  /// ما إذا كان العنصر معطلاً
  final bool isDisabled;

  /// ما إذا كان العنصر محدداً
  final bool isSelected;

  /// ما إذا كان العنصر فاصلاً
  final bool isDivider;

  /// ما إذا كان العنصر عنواناً
  final bool isHeader;

  /// منشئ عنصر القائمة الجانبية المحسنة
  const EnhancedDrawerItem({
    this.icon,
    this.leading,
    required this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.onLongPress,
    this.backgroundColor,
    this.splashColor,
    this.iconColor,
    this.titleColor,
    this.subtitleColor,
    this.iconSize,
    this.titleSize,
    this.subtitleSize,
    this.isDisabled = false,
    this.isSelected = false,
    this.isDivider = false,
    this.isHeader = false,
  });

  /// إنشاء عنصر فاصل
  static EnhancedDrawerItem divider() {
    return const EnhancedDrawerItem(title: '', isDivider: true);
  }

  /// إنشاء عنصر عنوان
  static EnhancedDrawerItem header(String title) {
    return EnhancedDrawerItem(title: title, isHeader: true);
  }
}
