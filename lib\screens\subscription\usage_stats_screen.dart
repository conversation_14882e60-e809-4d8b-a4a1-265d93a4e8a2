import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../config/app_theme.dart';
import '../../models/usage_model.dart';
import '../../providers/app_state.dart';
import '../../services/subscription/subscription_service.dart';
import '../../services/subscription/usage_tracking_service.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/loading_indicator.dart';
import 'subscription_screen.dart';

/// شاشة إحصائيات الاستخدام
class UsageStatsScreen extends StatefulWidget {
  const UsageStatsScreen({super.key});

  @override
  State<UsageStatsScreen> createState() => _UsageStatsScreenState();
}

class _UsageStatsScreenState extends State<UsageStatsScreen> {
  bool _isLoading = true;
  UsageModel? _usageStats;
  bool _isSubscribed = false;
  String? _errorMessage;
  late UsageTrackingService _usageTrackingService;
  late SubscriptionService _subscriptionService;

  @override
  void initState() {
    super.initState();
    _initServices();
    _loadData();
  }

  /// تهيئة الخدمات
  void _initServices() {
    final appState = Provider.of<AppState>(context, listen: false);
    _usageTrackingService = appState.usageTrackingService;
    _subscriptionService = appState.subscriptionService;

    // الاستماع لتدفق حالة الاشتراك
    _subscriptionService.subscriptionStatus.listen((subscription) {
      if (mounted) {
        setState(() {
          _isSubscribed = subscription != null && subscription.isActive();
        });
      }
    });
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // تحميل إحصائيات الاستخدام
      final usageStats = await _usageTrackingService.getMonthlyUsageStats();
      
      // تحميل حالة الاشتراك
      final subscription = _subscriptionService.currentSubscription;
      final isSubscribed = subscription != null && subscription.isActive();

      if (mounted) {
        setState(() {
          _usageStats = usageStats;
          _isSubscribed = isSubscribed;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'حدث خطأ أثناء تحميل البيانات: $e';
          _isLoading = false;
        });
      }
    }
  }

  /// الانتقال إلى شاشة الاشتراك
  void _navigateToSubscriptionScreen() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SubscriptionScreen(),
      ),
    ).then((_) => _loadData());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'إحصائيات الاستخدام',
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : RefreshIndicator(
              onRefresh: _loadData,
              child: _buildContent(),
            ),
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildContent() {
    if (_errorMessage != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 48,
              ),
              const SizedBox(height: 16),
              Text(
                _errorMessage!,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.red,
                ),
              ),
              const SizedBox(height: 24),
              CustomButton(
                text: 'إعادة المحاولة',
                onPressed: _loadData,
                width: 200,
              ),
            ],
          ),
        ),
      );
    }

    if (_usageStats == null) {
      return const Center(
        child: Text('لا توجد بيانات استخدام متاحة'),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSubscriptionStatus(),
          const SizedBox(height: 24),
          _buildUsageProgress(),
          const SizedBox(height: 24),
          _buildUsageDetails(),
        ],
      ),
    );
  }

  /// بناء حالة الاشتراك
  Widget _buildSubscriptionStatus() {
    return Card(
      color: _isSubscribed ? Colors.green.shade50 : Colors.orange.shade50,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            Icon(
              _isSubscribed ? Icons.verified : Icons.info_outline,
              color: _isSubscribed ? Colors.green : Colors.orange,
              size: 36,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _isSubscribed ? 'مشترك' : 'خطة مجانية',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _isSubscribed
                        ? 'أنت تتمتع بوصول غير محدود إلى جميع الميزات'
                        : 'ترقية إلى خطة مدفوعة للوصول إلى المزيد من الميزات',
                    style: const TextStyle(fontSize: 14),
                  ),
                ],
              ),
            ),
            if (!_isSubscribed)
              CustomButton(
                text: 'ترقية',
                onPressed: _navigateToSubscriptionScreen,
                width: 100,
                height: 40,
              ),
          ],
        ),
      ),
    );
  }

  /// بناء تقدم الاستخدام
  Widget _buildUsageProgress() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'استخدام هذا الشهر',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        if (!_isSubscribed) ...[
          const Text('الترجمة الصوتية:'),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: _usageStats!.totalMinutes / _usageTrackingService.freeTierMinutesLimit,
            backgroundColor: Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(
              _usageStats!.totalMinutes >= _usageTrackingService.freeTierMinutesLimit
                  ? Colors.red
                  : AppTheme.primaryColor,
            ),
            minHeight: 10,
            borderRadius: BorderRadius.circular(5),
          ),
          const SizedBox(height: 8),
          Text(
            '${_usageStats!.totalMinutes.toStringAsFixed(1)} / ${_usageTrackingService.freeTierMinutesLimit} دقيقة',
            style: TextStyle(
              color: _usageStats!.totalMinutes >= _usageTrackingService.freeTierMinutesLimit
                  ? Colors.red
                  : Colors.black,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (_usageStats!.totalMinutes >= _usageTrackingService.freeTierMinutesLimit) ...[
            const SizedBox(height: 8),
            const Text(
              'لقد وصلت إلى الحد الأقصى للاستخدام المجاني. قم بالترقية للاستمرار.',
              style: TextStyle(color: Colors.red),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: CustomButton(
                text: 'ترقية الآن',
                onPressed: _navigateToSubscriptionScreen,
              ),
            ),
          ],
        ] else ...[
          const Text(
            'أنت تتمتع باستخدام غير محدود كجزء من اشتراكك',
            style: TextStyle(color: Colors.green),
          ),
        ],
      ],
    );
  }

  /// بناء تفاصيل الاستخدام
  Widget _buildUsageDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'تفاصيل الاستخدام',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        _buildUsageDetailCard(
          title: 'الترجمة الصوتية',
          usage: _usageStats!.voiceTranslationMinutes,
          icon: Icons.mic,
          unit: 'دقيقة',
        ),
        _buildUsageDetailCard(
          title: 'تمييز المتحدثين',
          usage: _usageStats!.speakerRecognitionMinutes,
          icon: Icons.people,
          unit: 'دقيقة',
        ),
        _buildUsageDetailCard(
          title: 'الترجمة الفورية',
          usage: _usageStats!.realTimeTranslationMinutes,
          icon: Icons.record_voice_over,
          unit: 'دقيقة',
        ),
        _buildUsageDetailCard(
          title: 'ترجمة الصور',
          usage: _usageStats!.imageTranslationCount.toDouble(),
          icon: Icons.image,
          unit: 'صورة',
          isCount: true,
        ),
        _buildUsageDetailCard(
          title: 'ترجمة المستندات',
          usage: _usageStats!.documentTranslationCount.toDouble(),
          icon: Icons.description,
          unit: 'مستند',
          isCount: true,
        ),
        _buildUsageDetailCard(
          title: 'المحادثة الذكية',
          usage: _usageStats!.aiChatMessageCount.toDouble(),
          icon: Icons.chat,
          unit: 'رسالة',
          isCount: true,
        ),
      ],
    );
  }

  /// بناء بطاقة تفاصيل الاستخدام
  Widget _buildUsageDetailCard({
    required String title,
    required double usage,
    required IconData icon,
    required String unit,
    bool isCount = false,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            Icon(icon, size: 28),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    isCount
                        ? '${usage.toInt()} $unit'
                        : '${usage.toStringAsFixed(1)} $unit',
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
