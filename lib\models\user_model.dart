import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج بيانات المستخدم
class UserModel {
  /// معرف المستخدم
  final String id;

  /// البريد الإلكتروني
  final String email;

  /// الاسم
  final String name;

  /// رابط الصورة الشخصية
  final String? photoUrl;

  /// تاريخ إنشاء الحساب
  final DateTime createdAt;

  /// تاريخ آخر تسجيل دخول
  final DateTime lastLoginAt;

  /// هل تم التحقق من البريد الإلكتروني
  final bool isEmailVerified;

  /// هل تم إزالة الإعلانات
  final bool isAdsRemoved;

  /// تاريخ انتهاء الاشتراك المميز
  final DateTime? premiumExpiry;

  UserModel({
    required this.id,
    required this.email,
    required this.name,
    this.photoUrl,
    required this.createdAt,
    required this.lastLoginAt,
    this.isEmailVerified = false,
    this.isAdsRemoved = false,
    this.premiumExpiry,
  });

  /// إنشاء نموذج من بيانات Firestore
  factory UserModel.fromFirestore(Map<String, dynamic> data, String id) {
    return UserModel(
      id: id,
      email: data['email'] ?? '',
      name: data['name'] ?? '',
      photoUrl: data['photoUrl'],
      createdAt:
          data['createdAt'] != null
              ? (data['createdAt'] as Timestamp).toDate()
              : DateTime.now(),
      lastLoginAt:
          data['lastLoginAt'] != null
              ? (data['lastLoginAt'] as Timestamp).toDate()
              : DateTime.now(),
      isEmailVerified: data['isEmailVerified'] ?? false,
      isAdsRemoved: data['isAdsRemoved'] ?? false,
      premiumExpiry:
          data['premiumExpiry'] != null
              ? (data['premiumExpiry'] as Timestamp).toDate()
              : null,
    );
  }

  /// تحويل النموذج إلى بيانات Firestore
  Map<String, dynamic> toFirestore() {
    final data = {
      'email': email,
      'name': name,
      'createdAt': Timestamp.fromDate(createdAt),
      'lastLoginAt': Timestamp.fromDate(lastLoginAt),
      'isEmailVerified': isEmailVerified,
      'isAdsRemoved': isAdsRemoved,
    };

    if (photoUrl != null && photoUrl!.isNotEmpty) {
      data['photoUrl'] = photoUrl!;
    }

    if (premiumExpiry != null) {
      data['premiumExpiry'] = Timestamp.fromDate(premiumExpiry!);
    }

    return data;
  }

  /// نسخة محدثة من النموذج
  UserModel copyWith({
    String? id,
    String? email,
    String? name,
    String? photoUrl,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    bool? isEmailVerified,
    bool? isAdsRemoved,
    DateTime? premiumExpiry,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      photoUrl: photoUrl ?? this.photoUrl,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isAdsRemoved: isAdsRemoved ?? this.isAdsRemoved,
      premiumExpiry: premiumExpiry ?? this.premiumExpiry,
    );
  }

  /// التحقق مما إذا كان المستخدم مشتركًا في الخطة المميزة
  bool isPremium() {
    if (premiumExpiry == null) {
      return false;
    }

    return DateTime.now().isBefore(premiumExpiry!);
  }
}
