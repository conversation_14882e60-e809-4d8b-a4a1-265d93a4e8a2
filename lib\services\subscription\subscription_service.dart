import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../config/constants.dart';
import '../../models/subscription_model.dart';

/// خدمة إدارة الاشتراكات والمشتريات داخل التطبيق
class SubscriptionService {
  // معرفات المنتجات
  static const String _monthlySubscriptionId =
      'translator_monthly_subscription';
  static const String _yearlySubscriptionId = 'translator_yearly_subscription';
  static const String _weeklySubscriptionId = 'translator_weekly_subscription';

  // قائمة معرفات المنتجات
  static const List<String> _productIds = [
    _monthlySubscriptionId,
    _yearlySubscriptionId,
    _weeklySubscriptionId,
  ];

  // حد الاستخدام المجاني (بالدقائق)
  static const double _freeTierMinutesLimit = 30.0;

  // مثيل خدمة المشتريات داخل التطبيق
  final InAppPurchase _inAppPurchase = InAppPurchase.instance;

  // مثيل Firestore
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // مثيل Firebase Auth
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // اشتراك لتدفق المشتريات
  StreamSubscription<List<PurchaseDetails>>? _subscription;

  // قائمة المنتجات المتاحة
  List<ProductDetails> _products = [];

  // حالة توفر خدمة المشتريات
  bool _isAvailable = false;

  // حالة جاري التحميل
  bool _isLoading = true;

  // حالة الاشتراك الحالي
  SubscriptionModel? _currentSubscription;

  // متحكم تدفق حالة الاشتراك
  final _subscriptionStatusController =
      StreamController<SubscriptionModel?>.broadcast();

  // متحكم تدفق حالة التحميل
  final _loadingStatusController = StreamController<bool>.broadcast();

  // متحكم تدفق المنتجات
  final _productsController =
      StreamController<List<ProductDetails>>.broadcast();

  // متحكم تدفق الأخطاء
  final _errorController = StreamController<String>.broadcast();

  /// الحصول على تدفق حالة الاشتراك
  Stream<SubscriptionModel?> get subscriptionStatus =>
      _subscriptionStatusController.stream;

  /// الحصول على تدفق حالة التحميل
  Stream<bool> get loadingStatus => _loadingStatusController.stream;

  /// الحصول على تدفق المنتجات
  Stream<List<ProductDetails>> get products => _productsController.stream;

  /// الحصول على تدفق الأخطاء
  Stream<String> get errors => _errorController.stream;

  /// الحصول على قائمة المنتجات المتاحة
  List<ProductDetails> get availableProducts => _products;

  /// الحصول على حالة التحميل
  bool get isLoading => _isLoading;

  /// الحصول على الاشتراك الحالي
  SubscriptionModel? get currentSubscription => _currentSubscription;

  /// الحصول على حالة توفر خدمة المشتريات
  bool get isAvailable => _isAvailable;

  /// الحصول على حد الاستخدام المجاني
  double get freeTierMinutesLimit => _freeTierMinutesLimit;

  /// المُنشئ
  SubscriptionService() {
    _initialize();
  }

  /// تهيئة الخدمة
  Future<void> _initialize() async {
    _setLoading(true);

    try {
      // التحقق من توفر خدمة المشتريات
      _isAvailable = await _inAppPurchase.isAvailable();

      if (!_isAvailable) {
        _setLoading(false);
        _subscriptionStatusController.add(null);
        _errorController.add('خدمة المشتريات غير متوفرة');
        return;
      }

      // تكوين معالج الاستماع للمشتريات
      final purchaseUpdatedStream = _inAppPurchase.purchaseStream;
      _subscription = purchaseUpdatedStream.listen(
        _listenToPurchaseUpdated,
        onDone: () {
          _subscription?.cancel();
        },
        onError: (error) {
          debugPrint('خطأ في تدفق المشتريات: $error');
          _errorController.add('حدث خطأ في نظام المشتريات');
        },
      );

      // استعلام عن المنتجات المتاحة
      await _loadProducts();

      // التحقق من الاشتراكات النشطة
      await _loadCurrentSubscription();

      // استعادة المشتريات السابقة
      if (_currentSubscription == null) {
        await _restorePurchases();
      }

      _setLoading(false);
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمة الاشتراكات: $e');
      _errorController.add('حدث خطأ في تهيئة نظام الاشتراكات');
      _setLoading(false);
    }
  }

  /// تحميل المنتجات المتاحة
  Future<void> _loadProducts() async {
    try {
      final ProductDetailsResponse response = await _inAppPurchase
          .queryProductDetails(_productIds.toSet());

      if (response.notFoundIDs.isNotEmpty) {
        debugPrint('المنتجات غير الموجودة: ${response.notFoundIDs}');
      }

      if (response.error != null) {
        debugPrint('خطأ في استعلام المنتجات: ${response.error}');
        _errorController.add('حدث خطأ في تحميل المنتجات المتاحة');
        return;
      }

      _products = response.productDetails;
      _productsController.add(_products);
    } catch (e) {
      debugPrint('خطأ في تحميل المنتجات: $e');
      _errorController.add('حدث خطأ في تحميل المنتجات المتاحة');
    }
  }

  /// تحميل الاشتراك الحالي
  Future<void> _loadCurrentSubscription() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        _currentSubscription = null;
        _subscriptionStatusController.add(null);
        return;
      }

      final doc =
          await _firestore
              .collection('users')
              .doc(user.uid)
              .collection('subscriptions')
              .doc('current')
              .get();

      if (!doc.exists) {
        _currentSubscription = null;
        _subscriptionStatusController.add(null);
        return;
      }

      _currentSubscription = SubscriptionModel.fromFirestore(doc.data()!);

      // التحقق من صلاحية الاشتراك
      if (_currentSubscription!.isExpired()) {
        // تحديث حالة الاشتراك إلى منتهي
        final updatedSubscription = _currentSubscription!.copyWith(
          status: SubscriptionStatus.expired,
          lastUpdated: DateTime.now(),
        );

        await _updateSubscriptionInFirestore(updatedSubscription);
        _currentSubscription = updatedSubscription;
      }

      _subscriptionStatusController.add(_currentSubscription);
    } catch (e) {
      debugPrint('خطأ في تحميل الاشتراك الحالي: $e');
      _errorController.add('حدث خطأ في تحميل بيانات الاشتراك');
    }
  }

  /// شراء اشتراك
  Future<bool> purchaseSubscription(String subscriptionId) async {
    if (!_isAvailable) {
      _errorController.add('خدمة المشتريات غير متوفرة');
      return false;
    }

    _setLoading(true);

    try {
      final productDetails = _products.firstWhere(
        (product) => product.id == subscriptionId,
        orElse: () => throw Exception('المنتج غير موجود: $subscriptionId'),
      );

      // إعداد معلومات الشراء
      final PurchaseParam purchaseParam = PurchaseParam(
        productDetails: productDetails,
        applicationUserName: _auth.currentUser?.uid,
      );

      // شراء اشتراك
      final success = await _inAppPurchase.buyNonConsumable(
        purchaseParam: purchaseParam,
      );

      return success;
    } catch (e) {
      debugPrint('خطأ في شراء الاشتراك: $e');
      _errorController.add('حدث خطأ في عملية الشراء');
      _setLoading(false);
      return false;
    }
  }

  /// استعادة المشتريات السابقة
  Future<void> _restorePurchases() async {
    try {
      await _inAppPurchase.restorePurchases();
    } catch (e) {
      debugPrint('خطأ في استعادة المشتريات: $e');
      _errorController.add('حدث خطأ في استعادة المشتريات السابقة');
    }
  }

  /// استعادة المشتريات (يمكن استدعاؤها من واجهة المستخدم)
  Future<void> restorePurchases() async {
    _setLoading(true);
    await _restorePurchases();
    _setLoading(false);
  }

  /// تحديث الاشتراك في Firestore
  Future<void> _updateSubscriptionInFirestore(
    SubscriptionModel subscription,
  ) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      // حفظ الاشتراك الحالي
      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('subscriptions')
          .doc('current')
          .set(subscription.toFirestore());

      // حفظ سجل الاشتراكات
      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('subscriptions')
          .doc('history')
          .collection('items')
          .add(subscription.toFirestore());
    } catch (e) {
      debugPrint('خطأ في تحديث الاشتراك في Firestore: $e');
      throw Exception('فشل في تحديث بيانات الاشتراك');
    }
  }

  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    _loadingStatusController.add(loading);
  }

  /// شراء منتج
  Future<bool> purchaseProduct(ProductDetails product) async {
    try {
      final PurchaseParam purchaseParam = PurchaseParam(
        productDetails: product,
      );

      if (product.id == AppConstants.productIdRemoveAds ||
          product.id == AppConstants.productIdPremium) {
        return await _inAppPurchase.buyNonConsumable(
          purchaseParam: purchaseParam,
        );
      } else {
        return await _inAppPurchase.buyConsumable(purchaseParam: purchaseParam);
      }
    } catch (e) {
      debugPrint('Error purchasing product: $e');
      return false;
    }
  }

  /// معالجة تحديثات المشتريات
  void _listenToPurchaseUpdated(
    List<PurchaseDetails> purchaseDetailsList,
  ) async {
    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      if (purchaseDetails.status == PurchaseStatus.pending) {
        // المشتراة قيد المعالجة
        _setLoading(true);
      } else if (purchaseDetails.status == PurchaseStatus.error) {
        // حدث خطأ في عملية الشراء
        _handlePurchaseError(purchaseDetails.error!);
        _setLoading(false);
      } else if (purchaseDetails.status == PurchaseStatus.purchased ||
          purchaseDetails.status == PurchaseStatus.restored) {
        // تم الشراء أو الاستعادة بنجاح
        await _verifyAndDeliverProduct(purchaseDetails);
        _setLoading(false);
      } else if (purchaseDetails.status == PurchaseStatus.canceled) {
        // تم إلغاء عملية الشراء
        _setLoading(false);
      }

      // إكمال عملية الشراء إذا لزم الأمر
      if (purchaseDetails.pendingCompletePurchase) {
        await _inAppPurchase.completePurchase(purchaseDetails);
      }
    }
  }

  /// التحقق من صحة المشتراة وتسليم المنتج
  Future<void> _verifyAndDeliverProduct(PurchaseDetails purchaseDetails) async {
    try {
      // في بيئة الإنتاج، يجب التحقق من صحة الإيصال مع الخادم
      // للتأكد من أن المشتراة صالحة

      // إنشاء نموذج الاشتراك
      final subscription = await _createSubscriptionFromPurchase(
        purchaseDetails,
      );

      // حفظ الاشتراك في Firestore
      await _updateSubscriptionInFirestore(subscription);

      // تحديث الاشتراك الحالي
      _currentSubscription = subscription;
      _subscriptionStatusController.add(_currentSubscription);

      // حفظ حالة الاشتراك محليًا
      await _saveSubscriptionStatusLocally(subscription);
    } catch (e) {
      debugPrint('خطأ في التحقق من المشتراة: $e');
      _errorController.add('حدث خطأ في تفعيل الاشتراك');
    }
  }

  /// إنشاء نموذج اشتراك من تفاصيل الشراء
  Future<SubscriptionModel> _createSubscriptionFromPurchase(
    PurchaseDetails purchaseDetails,
  ) async {
    // تحديد فترة الاشتراك
    int periodDays = 30; // افتراضي شهري
    SubscriptionType type = SubscriptionType.monthly;
    String name = 'الاشتراك الشهري';

    if (purchaseDetails.productID.contains('yearly')) {
      periodDays = 365;
      type = SubscriptionType.yearly;
      name = 'الاشتراك السنوي';
    } else if (purchaseDetails.productID.contains('weekly')) {
      periodDays = 7;
      type = SubscriptionType.weekly;
      name = 'الاشتراك الأسبوعي';
    }

    // تحديد تواريخ الاشتراك
    final now = DateTime.now();
    final endDate = now.add(Duration(days: periodDays));

    // البحث عن تفاصيل المنتج
    String price = '9.99 USD';
    double priceValue = 9.99;
    String currency = 'USD';
    String description = 'اشتراك في المترجم الذكي';

    for (final product in _products) {
      if (product.id == purchaseDetails.productID) {
        price = product.price;
        currency = product.currencyCode;
        description = product.description;

        // استخراج قيمة السعر
        try {
          final numericString = price.replaceAll(RegExp(r'[^\d.,]'), '');
          final normalizedString = numericString.replaceAll(',', '.');
          priceValue = double.parse(normalizedString);
        } catch (e) {
          debugPrint('خطأ في استخراج قيمة السعر: $e');
        }

        break;
      }
    }

    // إنشاء نموذج الاشتراك
    return SubscriptionModel(
      productId: purchaseDetails.productID,
      name: name,
      description: description,
      price: price,
      priceValue: priceValue,
      currency: currency,
      startDate: now,
      endDate: endDate,
      status: SubscriptionStatus.active,
      type: type,
      autoRenew: true,
      transactionId: purchaseDetails.purchaseID,
      receiptData:
          Platform.isIOS
              ? purchaseDetails.verificationData.serverVerificationData
              : null,
      lastUpdated: now,
    );
  }

  /// حفظ حالة الاشتراك محليًا
  Future<void> _saveSubscriptionStatusLocally(
    SubscriptionModel subscription,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // حفظ معرف المنتج
      await prefs.setString('subscription_product_id', subscription.productId);

      // حفظ تاريخ انتهاء الصلاحية
      await prefs.setString(
        'subscription_end_date',
        subscription.endDate.toIso8601String(),
      );

      // حفظ حالة الاشتراك
      await prefs.setString(
        'subscription_status',
        _statusToString(subscription.status),
      );
    } catch (e) {
      debugPrint('خطأ في حفظ حالة الاشتراك محليًا: $e');
    }
  }

  /// معالجة أخطاء الشراء
  void _handlePurchaseError(IAPError error) {
    debugPrint('خطأ في الشراء: ${error.code} - ${error.message}');

    String errorMessage;
    switch (error.code) {
      case 'purchase-canceled':
        errorMessage = 'تم إلغاء عملية الشراء';
        break;
      case 'aborted':
        errorMessage = 'تم إلغاء عملية الشراء';
        break;
      case 'not-allowed':
        errorMessage = 'عملية الشراء غير مسموح بها';
        break;
      case 'already-owned':
        errorMessage = 'أنت مشترك بالفعل في هذه الخطة';
        break;
      default:
        errorMessage = 'حدث خطأ في عملية الشراء: ${error.message}';
    }

    _errorController.add(errorMessage);
  }

  /// تحديث حالة الاشتراك
  Future<void> _updateSubscriptionStatus({
    bool? isPremium,
    bool? isAdsRemoved,
    DateTime? premiumExpiry,
  }) async {
    try {
      final User? user = _auth.currentUser;

      if (user != null) {
        // تحديث في Firestore
        final Map<String, dynamic> data = {};

        if (isPremium != null) {
          data['isPremium'] = isPremium;
        }

        if (isAdsRemoved != null) {
          data['isAdsRemoved'] = isAdsRemoved;
        }

        if (premiumExpiry != null) {
          data['premiumExpiry'] = Timestamp.fromDate(premiumExpiry);
        }

        await _firestore.collection('users').doc(user.uid).update(data);
      }

      // تحديث في التخزين المحلي
      final prefs = await SharedPreferences.getInstance();

      if (isPremium != null) {
        await prefs.setBool('is_premium', isPremium);
      }

      if (isAdsRemoved != null) {
        await prefs.setBool('is_ads_removed', isAdsRemoved);
      }

      if (premiumExpiry != null) {
        await prefs.setString(
          'premium_expiry',
          premiumExpiry.toIso8601String(),
        );
      }
    } catch (e) {
      debugPrint('Error updating subscription status: $e');
    }
  }

  /// استعادة المشتريات (للتوافق مع الواجهة القديمة)
  Future<bool> restorePurchasesLegacy() async {
    try {
      await restorePurchases();
      return true;
    } catch (e) {
      debugPrint('Error restoring purchases: $e');
      return false;
    }
  }

  /// التحقق من حالة الاشتراك
  Future<Map<String, dynamic>> getSubscriptionStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final isPremium = prefs.getBool('is_premium') ?? false;
      final isAdsRemoved = prefs.getBool('is_ads_removed') ?? false;
      final premiumExpiryStr = prefs.getString('premium_expiry');

      DateTime? premiumExpiry;
      if (premiumExpiryStr != null) {
        premiumExpiry = DateTime.parse(premiumExpiryStr);

        // التحقق مما إذا كان الاشتراك قد انتهى
        if (premiumExpiry.isBefore(DateTime.now())) {
          await _updateSubscriptionStatus(
            isPremium: false,
            premiumExpiry: null,
          );
          return {
            'isPremium': false,
            'isAdsRemoved': isAdsRemoved,
            'premiumExpiry': null,
          };
        }
      }

      return {
        'isPremium': isPremium,
        'isAdsRemoved': isAdsRemoved,
        'premiumExpiry': premiumExpiry,
      };
    } catch (e) {
      debugPrint('Error getting subscription status: $e');
      return {'isPremium': false, 'isAdsRemoved': false, 'premiumExpiry': null};
    }
  }

  /// الحصول على قائمة الاشتراكات المتاحة
  List<ProductDetails> getAvailableProducts() {
    return _products;
  }

  /// تحويل حالة الاشتراك إلى نص
  String _statusToString(SubscriptionStatus status) {
    switch (status) {
      case SubscriptionStatus.active:
        return 'active';
      case SubscriptionStatus.expired:
        return 'expired';
      case SubscriptionStatus.canceled:
        return 'canceled';
      case SubscriptionStatus.pending:
        return 'pending';
      case SubscriptionStatus.inactive:
        return 'inactive';
    }
  }

  /// التخلص من الموارد
  void dispose() {
    _subscription?.cancel();
    _subscriptionStatusController.close();
    _loadingStatusController.close();
    _productsController.close();
    _errorController.close();
  }
}
