import 'package:flutter/material.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:flutter_tts/flutter_tts.dart';
import '../../../services/api/translation_service.dart';

/// خدمة الترجمة الصوتية
class VoiceTranslationService {
  final SpeechToText _speechToText = SpeechToText();
  final FlutterTts _flutterTts = FlutterTts();
  bool _isInitialized = false;
  TranslationService? _translationService;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // تهيئة خدمة التعرف على الكلام
      final available = await _speechToText.initialize(
        onError: (error) => debugPrint('Speech recognition error: $error'),
        onStatus: (status) => debugPrint('Speech recognition status: $status'),
      );

      if (!available) {
        throw Exception('Speech recognition not available');
      }

      // تهيئة خدمة تحويل النص إلى كلام
      await _flutterTts.setLanguage('ar-SA');
      await _flutterTts.setSpeechRate(0.4); // أبطأ للغة العربية
      await _flutterTts.setVolume(1.0);
      await _flutterTts.setPitch(1.1); // أعلى قليلاً للغة العربية

      // الحصول على اللغات المدعومة للتحقق
      final availableLanguages = await _flutterTts.getLanguages;
      final availableVoices = await _flutterTts.getVoices;

      debugPrint('Available TTS languages: $availableLanguages');
      debugPrint('Available TTS voices: $availableVoices');

      _isInitialized = true;
    } catch (e) {
      debugPrint('Error initializing voice translation service: $e');
      rethrow;
    }
  }

  /// بدء الاستماع
  Future<void> startListening({
    required String languageCode,
    required Function(String) onResult,
    required Function() onDone,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      await _speechToText.listen(
        localeId: _getLocaleId(languageCode),
        onResult: (result) {
          final recognizedWords = result.recognizedWords;
          onResult(recognizedWords);

          if (result.finalResult) {
            onDone();
          }
        },
      );
    } catch (e) {
      debugPrint('Error starting listening: $e');
      rethrow;
    }
  }

  /// إيقاف الاستماع
  Future<void> stopListening() async {
    try {
      await _speechToText.stop();
    } catch (e) {
      debugPrint('Error stopping listening: $e');
      rethrow;
    }
  }

  /// ترجمة النص
  Future<String> translateText({
    required String text,
    required String sourceLanguage,
    required String targetLanguage,
  }) async {
    if (_translationService == null) {
      throw Exception(
        'TranslationService not initialized. Call setTranslationService.',
      );
    }
    return await _translationService!.translateText(
      text: text,
      sourceLanguage: sourceLanguage,
      targetLanguage: targetLanguage,
    );
  }

  /// نطق النص
  Future<void> speakText({
    required String text,
    required String languageCode,
    required Function() onDone,
  }) async {
    try {
      // استخدام معرف اللغة المناسب
      final localeId = _getLocaleId(languageCode);
      await _flutterTts.setLanguage(localeId);

      // تعديل إعدادات النطق حسب اللغة
      if (languageCode == 'ar') {
        await _flutterTts.setSpeechRate(0.4); // أبطأ للغة العربية
        await _flutterTts.setPitch(1.1); // أعلى قليلاً للغة العربية
      } else {
        await _flutterTts.setSpeechRate(0.5);
        await _flutterTts.setPitch(1.0);
      }

      // تعيين معالج الانتهاء
      _flutterTts.setCompletionHandler(onDone);

      // تعيين معالج الخطأ
      _flutterTts.setErrorHandler((error) {
        debugPrint('TTS error: $error');
      });

      debugPrint('Speaking text in language: $localeId');
      final result = await _flutterTts.speak(text);

      // إذا فشل النطق، نعيد المحاولة مرة واحدة
      if (result == 0) {
        debugPrint('First attempt to speak failed, trying again...');
        await Future.delayed(const Duration(milliseconds: 500));
        await _flutterTts.speak(text);
      }
    } catch (e) {
      debugPrint('Error speaking text: $e');
      rethrow;
    }
  }

  /// إيقاف النطق
  Future<void> stopSpeaking() async {
    try {
      await _flutterTts.stop();
    } catch (e) {
      debugPrint('Error stopping speaking: $e');
      rethrow;
    }
  }

  /// الحصول على معرف اللغة
  String _getLocaleId(String languageCode) {
    switch (languageCode) {
      case 'ar':
        return 'ar-SA';
      case 'en':
        return 'en-US';
      case 'fr':
        return 'fr-FR';
      case 'es':
        return 'es-ES';
      case 'de':
        return 'de-DE';
      case 'it':
        return 'it-IT';
      case 'ru':
        return 'ru-RU';
      case 'zh':
        return 'zh-CN';
      case 'ja':
        return 'ja-JP';
      case 'ko':
        return 'ko-KR';
      default:
        return languageCode;
    }
  }

  /// تعيين خدمة الترجمة
  void setTranslationService(TranslationService translationService) {
    _translationService = translationService;
  }

  /// التخلص من الموارد
  void dispose() {
    _speechToText.cancel();
    _flutterTts.stop();
  }
}
