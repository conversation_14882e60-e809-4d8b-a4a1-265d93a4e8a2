import 'package:flutter/material.dart';

/// نظام الرسوم المتحركة المتقدم للتطبيق
class AppAnimations {
  // مدة الرسوم المتحركة
  static const Duration fastDuration = Duration(milliseconds: 200);
  static const Duration normalDuration = Duration(milliseconds: 300);
  static const Duration slowDuration = Duration(milliseconds: 500);
  static const Duration extraSlowDuration = Duration(milliseconds: 800);

  // منحنيات الرسوم المتحركة
  static const Curve defaultCurve = Curves.easeInOut;
  static const Curve bounceCurve = Curves.elasticOut;
  static const Curve smoothCurve = Curves.easeOutCubic;
  static const Curve sharpCurve = Curves.easeInCubic;

  /// رسوم متحركة للانتقال بين الصفحات
  static Route<T> createSlideRoute<T>(
    Widget page, {
    SlideDirection direction = SlideDirection.right,
  }) {
    Offset begin;
    switch (direction) {
      case SlideDirection.right:
        begin = const Offset(1.0, 0.0);
        break;
      case SlideDirection.left:
        begin = const Offset(-1.0, 0.0);
        break;
      case SlideDirection.up:
        begin = const Offset(0.0, 1.0);
        break;
      case SlideDirection.down:
        begin = const Offset(0.0, -1.0);
        break;
    }

    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: normalDuration,
      reverseTransitionDuration: normalDuration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        final tween = Tween(begin: begin, end: Offset.zero);
        final offsetAnimation = animation.drive(
          tween.chain(CurveTween(curve: smoothCurve)),
        );

        return SlideTransition(position: offsetAnimation, child: child);
      },
    );
  }

  /// رسوم متحركة للتلاشي
  static Route<T> createFadeRoute<T>(Widget page) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: normalDuration,
      reverseTransitionDuration: normalDuration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: animation.drive(CurveTween(curve: smoothCurve)),
          child: child,
        );
      },
    );
  }

  /// رسوم متحركة للتكبير
  static Route<T> createScaleRoute<T>(Widget page) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: normalDuration,
      reverseTransitionDuration: normalDuration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return ScaleTransition(
          scale: animation.drive(CurveTween(curve: bounceCurve)),
          child: child,
        );
      },
    );
  }

  /// رسوم متحركة مخصصة للبطاقات
  static Route<T> createCardRoute<T>(Widget page) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: normalDuration,
      reverseTransitionDuration: normalDuration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(0.0, 1.0);
        const end = Offset.zero;
        final tween = Tween(begin: begin, end: end);
        final offsetAnimation = animation.drive(
          tween.chain(CurveTween(curve: smoothCurve)),
        );

        return SlideTransition(
          position: offsetAnimation,
          child: FadeTransition(opacity: animation, child: child),
        );
      },
    );
  }

  /// رسوم متحركة للدوران مع التلاشي
  static Route<T> createRotationRoute<T>(Widget page) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: slowDuration,
      reverseTransitionDuration: slowDuration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return RotationTransition(
          turns: animation.drive(CurveTween(curve: smoothCurve)),
          child: FadeTransition(opacity: animation, child: child),
        );
      },
    );
  }
}

/// اتجاهات الانزلاق
enum SlideDirection { right, left, up, down }

/// ويدجت للرسوم المتحركة المخصصة
class CustomAnimatedWidget extends StatefulWidget {
  final Widget child;
  final Duration duration;
  final Curve curve;
  final bool animate;

  const CustomAnimatedWidget({
    super.key,
    required this.child,
    this.duration = AppAnimations.normalDuration,
    this.curve = AppAnimations.defaultCurve,
    this.animate = true,
  });

  @override
  State<CustomAnimatedWidget> createState() => _CustomAnimatedWidgetState();
}

class _CustomAnimatedWidgetState extends State<CustomAnimatedWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: widget.duration, vsync: this);
    _animation = CurvedAnimation(parent: _controller, curve: widget.curve);

    if (widget.animate) {
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _animation,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0, 0.1),
          end: Offset.zero,
        ).animate(_animation),
        child: widget.child,
      ),
    );
  }
}

/// ويدجت للرسوم المتحركة المتتالية
class StaggeredAnimationContainer extends StatefulWidget {
  final List<Widget> children;
  final Duration duration;
  final Duration staggerDelay;
  final Curve curve;

  const StaggeredAnimationContainer({
    super.key,
    required this.children,
    this.duration = AppAnimations.normalDuration,
    this.staggerDelay = const Duration(milliseconds: 100),
    this.curve = AppAnimations.defaultCurve,
  });

  @override
  State<StaggeredAnimationContainer> createState() =>
      _StaggeredAnimationContainerState();
}

class _StaggeredAnimationContainerState
    extends State<StaggeredAnimationContainer>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(
      widget.children.length,
      (index) => AnimationController(duration: widget.duration, vsync: this),
    );

    _animations =
        _controllers.map((controller) {
          return CurvedAnimation(parent: controller, curve: widget.curve);
        }).toList();

    _startStaggeredAnimation();
  }

  void _startStaggeredAnimation() {
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(widget.staggerDelay * i, () {
        if (mounted) {
          _controllers[i].forward();
        }
      });
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: List.generate(widget.children.length, (index) {
        return FadeTransition(
          opacity: _animations[index],
          child: SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0, 0.2),
              end: Offset.zero,
            ).animate(_animations[index]),
            child: widget.children[index],
          ),
        );
      }),
    );
  }
}
