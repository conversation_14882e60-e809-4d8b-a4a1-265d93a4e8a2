import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../config/app_theme.dart';
import '../../../utils/helpers.dart';
import '../../../widgets/three_d_button.dart';
import 'tourism_mode_service.dart';
import 'tourism_phrases_screen.dart';

/// شاشة وضع السياحة
class TourismModeScreen extends StatefulWidget {
  const TourismModeScreen({super.key});

  @override
  State<TourismModeScreen> createState() => _TourismModeScreenState();
}

class _TourismModeScreenState extends State<TourismModeScreen>
    with SingleTickerProviderStateMixin {
  final TourismModeService _tourismService = TourismModeService();
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  String _sourceLanguage = 'ar';
  String _targetLanguage = 'en';
  bool _isListening = false;
  bool _isSpeaking = false;
  final bool _isLoading = false;
  int _selectedCategoryIndex = 0;
  late AnimationController _animationController;

  // قائمة الفئات
  final List<Map<String, dynamic>> _categories = [
    {
      'name': 'عبارات شائعة',
      'icon': Icons.chat_bubble_outline,
      'phrases': [
        {'ar': 'مرحباً', 'en': 'Hello', 'fr': 'Bonjour', 'es': 'Hola'},
        {'ar': 'مع السلامة', 'en': 'Goodbye', 'fr': 'Au revoir', 'es': 'Adiós'},
        {'ar': 'شكراً', 'en': 'Thank you', 'fr': 'Merci', 'es': 'Gracias'},
        {
          'ar': 'من فضلك',
          'en': 'Please',
          'fr': 'S\'il vous plaît',
          'es': 'Por favor',
        },
        {
          'ar': 'عفواً',
          'en': 'Excuse me',
          'fr': 'Excusez-moi',
          'es': 'Disculpe',
        },
        {
          'ar': 'كم الساعة؟',
          'en': 'What time is it?',
          'fr': 'Quelle heure est-il?',
          'es': '¿Qué hora es?',
        },
        {
          'ar': 'أين الحمام؟',
          'en': 'Where is the bathroom?',
          'fr': 'Où sont les toilettes?',
          'es': '¿Dónde está el baño?',
        },
        {
          'ar': 'كم السعر؟',
          'en': 'How much is it?',
          'fr': 'Combien ça coûte?',
          'es': '¿Cuánto cuesta?',
        },
      ],
    },
    {
      'name': 'المطاعم',
      'icon': Icons.restaurant,
      'phrases': [
        {
          'ar': 'أريد طاولة لشخصين',
          'en': 'I want a table for two',
          'fr': 'Je voudrais une table pour deux',
          'es': 'Quiero una mesa para dos',
        },
        {
          'ar': 'قائمة الطعام من فضلك',
          'en': 'The menu please',
          'fr': 'Le menu s\'il vous plaît',
          'es': 'El menú, por favor',
        },
        {'ar': 'ماء', 'en': 'Water', 'fr': 'Eau', 'es': 'Agua'},
        {
          'ar': 'الفاتورة من فضلك',
          'en': 'The bill please',
          'fr': 'L\'addition s\'il vous plaît',
          'es': 'La cuenta, por favor',
        },
        {
          'ar': 'هل تقبلون بطاقات الائتمان؟',
          'en': 'Do you accept credit cards?',
          'fr': 'Acceptez-vous les cartes de crédit?',
          'es': '¿Aceptan tarjetas de crédito?',
        },
        {
          'ar': 'هل لديكم طعام نباتي؟',
          'en': 'Do you have vegetarian food?',
          'fr': 'Avez-vous de la nourriture végétarienne?',
          'es': '¿Tienen comida vegetariana?',
        },
      ],
    },
    {
      'name': 'التنقل',
      'icon': Icons.directions,
      'phrases': [
        {
          'ar': 'أين محطة القطار؟',
          'en': 'Where is the train station?',
          'fr': 'Où est la gare?',
          'es': '¿Dónde está la estación de tren?',
        },
        {
          'ar': 'كم تكلفة التذكرة؟',
          'en': 'How much is the ticket?',
          'fr': 'Combien coûte le billet?',
          'es': '¿Cuánto cuesta el billete?',
        },
        {
          'ar': 'أريد استئجار سيارة',
          'en': 'I want to rent a car',
          'fr': 'Je veux louer une voiture',
          'es': 'Quiero alquilar un coche',
        },
        {
          'ar': 'خذني إلى الفندق',
          'en': 'Take me to the hotel',
          'fr': 'Emmenez-moi à l\'hôtel',
          'es': 'Lléveme al hotel',
        },
        {
          'ar': 'هل هذا الطريق إلى وسط المدينة؟',
          'en': 'Is this the way to the city center?',
          'fr': 'Est-ce le chemin vers le centre-ville?',
          'es': '¿Es este el camino al centro de la ciudad?',
        },
      ],
    },
    {
      'name': 'الفنادق',
      'icon': Icons.hotel,
      'phrases': [
        {
          'ar': 'لدي حجز',
          'en': 'I have a reservation',
          'fr': 'J\'ai une réservation',
          'es': 'Tengo una reserva',
        },
        {
          'ar': 'هل لديكم غرفة متاحة؟',
          'en': 'Do you have a room available?',
          'fr': 'Avez-vous une chambre disponible?',
          'es': '¿Tienen una habitación disponible?',
        },
        {
          'ar': 'متى وقت تسجيل المغادرة؟',
          'en': 'What time is check-out?',
          'fr': 'À quelle heure est le check-out?',
          'es': '¿A qué hora es el check-out?',
        },
        {
          'ar': 'هل يوجد واي فاي؟',
          'en': 'Is there WiFi?',
          'fr': 'Y a-t-il du WiFi?',
          'es': '¿Hay WiFi?',
        },
        {
          'ar': 'أحتاج مناشف إضافية',
          'en': 'I need extra towels',
          'fr': 'J\'ai besoin de serviettes supplémentaires',
          'es': 'Necesito toallas adicionales',
        },
      ],
    },
    {
      'name': 'التسوق',
      'icon': Icons.shopping_bag,
      'phrases': [
        {
          'ar': 'كم سعر هذا؟',
          'en': 'How much is this?',
          'fr': 'Combien ça coûte?',
          'es': '¿Cuánto cuesta esto?',
        },
        {
          'ar': 'هل يمكنني تجربة هذا؟',
          'en': 'Can I try this on?',
          'fr': 'Puis-je essayer ceci?',
          'es': '¿Puedo probarme esto?',
        },
        {
          'ar': 'أبحث عن هدية',
          'en': 'I\'m looking for a gift',
          'fr': 'Je cherche un cadeau',
          'es': 'Estoy buscando un regalo',
        },
        {
          'ar': 'هل تقبلون الدولار الأمريكي؟',
          'en': 'Do you accept US dollars?',
          'fr': 'Acceptez-vous les dollars américains?',
          'es': '¿Aceptan dólares estadounidenses?',
        },
        {
          'ar': 'هذا غالي جداً',
          'en': 'This is too expensive',
          'fr': 'C\'est trop cher',
          'es': 'Esto es demasiado caro',
        },
      ],
    },
    {
      'name': 'الطوارئ',
      'icon': Icons.emergency,
      'phrases': [
        {
          'ar': 'أحتاج طبيباً',
          'en': 'I need a doctor',
          'fr': 'J\'ai besoin d\'un médecin',
          'es': 'Necesito un médico',
        },
        {
          'ar': 'أين أقرب مستشفى؟',
          'en': 'Where is the nearest hospital?',
          'fr': 'Où est l\'hôpital le plus proche?',
          'es': '¿Dónde está el hospital más cercano?',
        },
        {
          'ar': 'هل تتحدث الإنجليزية؟',
          'en': 'Do you speak English?',
          'fr': 'Parlez-vous anglais?',
          'es': '¿Habla inglés?',
        },
        {
          'ar': 'فقدت جواز سفري',
          'en': 'I lost my passport',
          'fr': 'J\'ai perdu mon passeport',
          'es': 'Perdí mi pasaporte',
        },
        {
          'ar': 'أحتاج مساعدة',
          'en': 'I need help',
          'fr': 'J\'ai besoin d\'aide',
          'es': 'Necesito ayuda',
        },
      ],
    },
  ];

  List<Map<String, dynamic>> _filteredPhrases = [];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );

    // تهيئة خدمة وضع السياحة
    _initTourismModeService();

    // تعيين العبارات المفلترة إلى العبارات الافتراضية
    _filteredPhrases =
        _categories[_selectedCategoryIndex]['phrases']
            as List<Map<String, dynamic>>;

    // إضافة مستمع لحقل البحث
    _searchController.addListener(_filterPhrases);
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.removeListener(_filterPhrases);
    _searchController.dispose();
    _scrollController.dispose();
    _tourismService.dispose();
    super.dispose();
  }

  /// تهيئة خدمة وضع السياحة
  Future<void> _initTourismModeService() async {
    try {
      await _tourismService.initialize();
    } catch (e) {
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء تهيئة خدمة وضع السياحة: $e',
          isError: true,
        );
      }
    }
  }

  /// تصفية العبارات بناءً على البحث
  void _filterPhrases() {
    final query = _searchController.text.trim().toLowerCase();

    if (query.isEmpty) {
      setState(() {
        _filteredPhrases =
            _categories[_selectedCategoryIndex]['phrases']
                as List<Map<String, dynamic>>;
      });
      return;
    }

    setState(() {
      _filteredPhrases =
          (_categories[_selectedCategoryIndex]['phrases']
                  as List<Map<String, dynamic>>)
              .where((phrase) {
                final sourceText =
                    phrase[_sourceLanguage].toString().toLowerCase();
                final targetText =
                    phrase[_targetLanguage].toString().toLowerCase();
                return sourceText.contains(query) || targetText.contains(query);
              })
              .toList();
    });
  }

  /// نطق العبارة
  Future<void> _speakPhrase(String phrase, String languageCode) async {
    if (_isSpeaking) return;

    setState(() {
      _isSpeaking = true;
    });

    try {
      await _tourismService.speakText(
        text: phrase,
        languageCode: languageCode,
        onDone: () {
          if (mounted) {
            setState(() {
              _isSpeaking = false;
            });
          }
        },
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _isSpeaking = false;
        });
      }
    }
  }

  /// بدء الاستماع
  Future<void> _startListening() async {
    try {
      setState(() {
        _isListening = true;
      });

      _animationController.repeat(reverse: true);

      // بدء الاستماع
      await _tourismService.startListening(
        languageCode: _sourceLanguage,
        onResult: (text) {
          // البحث عن العبارة المطابقة
          _searchController.text = text;
        },
        onDone: () {
          setState(() {
            _isListening = false;
          });
          _animationController.stop();
          _animationController.reset();
        },
      );
    } catch (e) {
      setState(() {
        _isListening = false;
      });
      _animationController.stop();
      _animationController.reset();

      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء الاستماع: $e',
          isError: true,
        );
      }
    }
  }

  /// إيقاف الاستماع
  Future<void> _stopListening() async {
    try {
      await _tourismService.stopListening();

      setState(() {
        _isListening = false;
      });

      _animationController.stop();
      _animationController.reset();
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// نسخ العبارة
  void _copyPhrase(String phrase) {
    Clipboard.setData(ClipboardData(text: phrase));
    if (mounted) {
      AppHelpers.showSnackBar(context, 'تم نسخ العبارة');
    }
  }

  /// تبديل اللغات
  void _swapLanguages() {
    setState(() {
      final temp = _sourceLanguage;
      _sourceLanguage = _targetLanguage;
      _targetLanguage = temp;
    });
  }

  /// الحصول على اسم اللغة
  String _getLanguageName(String code) {
    switch (code) {
      case 'ar':
        return '🇸🇦 العربية';
      case 'en':
        return '🇺🇸 English';
      case 'fr':
        return '🇫🇷 Français';
      case 'es':
        return '🇪🇸 Español';
      default:
        return code;
    }
  }

  /// عرض مربع حوار اختيار اللغة
  void _showLanguageSelector(bool isSource) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                isSource ? 'اختر لغتك' : 'اختر لغة البلد',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              Expanded(
                child: ListView(
                  children: [
                    _buildLanguageItem(context, 'ar', isSource),
                    _buildLanguageItem(context, 'en', isSource),
                    _buildLanguageItem(context, 'fr', isSource),
                    _buildLanguageItem(context, 'es', isSource),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// بناء عنصر اللغة
  Widget _buildLanguageItem(BuildContext context, String code, bool isSource) {
    final isSelected =
        isSource ? _sourceLanguage == code : _targetLanguage == code;

    return ListTile(
      title: Text(_getLanguageName(code)),
      trailing:
          isSelected
              ? const Icon(Icons.check, color: AppTheme.primaryColor)
              : null,
      onTap: () {
        setState(() {
          if (isSource) {
            _sourceLanguage = code;
          } else {
            _targetLanguage = code;
          }
        });
        Navigator.pop(context);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('وضع السياحة'),
        backgroundColor: AppTheme.primaryColor,
        actions: [
          // زر العبارات السياحية
          IconButton(
            icon: const Icon(Icons.menu_book),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const TourismPhrasesScreen(),
                ),
              );
            },
            tooltip: 'العبارات السياحية',
          ),
        ],
      ),
      body: Column(
        children: [
          // اختيار اللغات
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Color.fromRGBO(0, 0, 0, 0.04),
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                // اللغة المصدر
                Expanded(
                  child: InkWell(
                    onTap: () => _showLanguageSelector(true),
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        _getLanguageName(_sourceLanguage),
                        textAlign: TextAlign.center,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                  ),
                ),

                // زر تبديل اللغات
                IconButton(
                  icon: const Icon(Icons.swap_horiz),
                  onPressed: _swapLanguages,
                  color: AppTheme.primaryColor,
                ),

                // اللغة الهدف
                Expanded(
                  child: InkWell(
                    onTap: () => _showLanguageSelector(false),
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        _getLanguageName(_targetLanguage),
                        textAlign: TextAlign.center,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // فئات العبارات
          SizedBox(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _categories.length,
              padding: const EdgeInsets.all(8),
              itemBuilder: (context, index) {
                final category = _categories[index];
                final isSelected = _selectedCategoryIndex == index;

                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedCategoryIndex = index;
                      _searchController.clear();
                      _filteredPhrases =
                          category['phrases'] as List<Map<String, dynamic>>;
                    });
                  },
                  child: Container(
                    width: 100,
                    margin: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      color: isSelected ? AppTheme.primaryColor : Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Color.fromRGBO(0, 0, 0, 0.04),
                          blurRadius: 5,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          category['icon'] as IconData,
                          color:
                              isSelected ? Colors.white : AppTheme.primaryColor,
                          size: 32,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          category['name'] as String,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color:
                                isSelected
                                    ? Colors.white
                                    : AppTheme.onBackground,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ).animate().fadeIn(delay: (index * 100).ms, duration: 300.ms);
              },
            ),
          ),

          // حقل البحث
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'ابحث عن عبارة...',
                      prefixIcon: const Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      contentPadding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                CircleButton3D(
                  icon: _isListening ? Icons.stop : Icons.mic,
                  size: 50,
                  iconSize: 24,
                  onPressed: _isListening ? _stopListening : _startListening,
                  color: _isListening ? Colors.red : AppTheme.primaryColor,
                ),
              ],
            ),
          ),

          // قائمة العبارات
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _filteredPhrases.isEmpty
                    ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.search_off,
                            size: 80,
                            color: Color.fromRGBO(0, 122, 255, 0.4),
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'لا توجد عبارات مطابقة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    )
                    : ListView.builder(
                      controller: _scrollController,
                      padding: const EdgeInsets.all(16),
                      itemCount: _filteredPhrases.length,
                      itemBuilder: (context, index) {
                        final phrase = _filteredPhrases[index];
                        return _buildPhraseItem(phrase);
                      },
                    ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر العبارة
  Widget _buildPhraseItem(Map<String, dynamic> phrase) {
    return Card(
          margin: const EdgeInsets.only(bottom: 16),
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // العبارة باللغة المصدر
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        phrase[_sourceLanguage] as String,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.volume_up),
                      onPressed:
                          () => _speakPhrase(
                            phrase[_sourceLanguage] as String,
                            _sourceLanguage,
                          ),
                      tooltip: 'نطق',
                    ),
                    IconButton(
                      icon: const Icon(Icons.copy),
                      onPressed:
                          () => _copyPhrase(phrase[_sourceLanguage] as String),
                      tooltip: 'نسخ',
                    ),
                  ],
                ),

                const Divider(),

                // العبارة باللغة الهدف
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        phrase[_targetLanguage] as String,
                        style: TextStyle(
                          fontSize: 18,
                          color: AppTheme.primaryColor,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.volume_up),
                      onPressed:
                          () => _speakPhrase(
                            phrase[_targetLanguage] as String,
                            _targetLanguage,
                          ),
                      tooltip: 'نطق',
                    ),
                    IconButton(
                      icon: const Icon(Icons.copy),
                      onPressed:
                          () => _copyPhrase(phrase[_targetLanguage] as String),
                      tooltip: 'نسخ',
                    ),
                  ],
                ),
              ],
            ),
          ),
        )
        .animate()
        .fadeIn(duration: 300.ms)
        .slideY(begin: 0.2, end: 0, duration: 300.ms);
  }
}
