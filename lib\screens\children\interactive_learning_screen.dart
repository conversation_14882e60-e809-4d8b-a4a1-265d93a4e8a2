import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/educational_content.dart';
import '../../services/educational_service.dart';

/// شاشة التعلم التفاعلي للأطفال - نسخة محسنة مثل Duolingo
class InteractiveLearningScreen extends StatefulWidget {
  const InteractiveLearningScreen({super.key});

  @override
  State<InteractiveLearningScreen> createState() => _InteractiveLearningScreenState();
}

class _InteractiveLearningScreenState extends State<InteractiveLearningScreen>
    with TickerProviderStateMixin {
  final EducationalService _educationalService = EducationalService();
  late TabController _tabController;
  
  List<EducationalLesson> _lessons = [];
  StudentProgress? _progress;
  LessonCategory _selectedCategory = LessonCategory.letters;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: LessonCategory.values.length, vsync: this);
    _initializeEducationalContent();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تهيئة المحتوى التعليمي
  Future<void> _initializeEducationalContent() async {
    try {
      await _educationalService.initialize();
      
      setState(() {
        _lessons = _educationalService.getAllLessons();
        _progress = _educationalService.getStudentProgress();
        _isLoading = false;
      });
      
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة المحتوى التعليمي: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      body: _isLoading ? _buildLoadingScreen() : _buildMainContent(),
    );
  }

  /// شاشة التحميل
  Widget _buildLoadingScreen() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
          ),
          SizedBox(height: 16),
          Text(
            'جاري تحميل المحتوى التعليمي...',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  /// المحتوى الرئيسي
  Widget _buildMainContent() {
    return SafeArea(
      child: Column(
        children: [
          _buildHeader(),
          _buildProgressSection(),
          _buildCategoryTabs(),
          Expanded(
            child: _buildLessonsGrid(),
          ),
        ],
      ),
    );
  }

  /// رأس الشاشة
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.blue.shade400,
            Colors.purple.shade400,
          ],
        ),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(30),
          bottomRight: Radius.circular(30),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.arrow_back, color: Colors.white),
              ),
              const Expanded(
                child: Text(
                  'التعلم التفاعلي',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
              IconButton(
                onPressed: _showAchievements,
                icon: const Icon(Icons.emoji_events, color: Colors.white),
              ),
            ],
          ),
          const SizedBox(height: 10),
          const Text(
            'تعلم مع الأصدقاء الممتعين!',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
          ),
        ],
      ),
    );
  }

  /// قسم التقدم
  Widget _buildProgressSection() {
    final totalLessons = _lessons.length;
    final completedLessons = _progress?.totalLessonsCompleted ?? 0;
    final progressPercentage = totalLessons > 0 ? (completedLessons / totalLessons) : 0.0;

    return Container(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 2,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'تقدمك',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '$completedLessons من $totalLessons درس',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
              CircleAvatar(
                radius: 30,
                backgroundColor: Colors.blue.shade100,
                child: Text(
                  '${(progressPercentage * 100).toInt()}%',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade700,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 15),
          LinearProgressIndicator(
            value: progressPercentage,
            backgroundColor: Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.blue.shade400),
            minHeight: 8,
          ),
        ],
      ),
    );
  }

  /// تبويبات الفئات
  Widget _buildCategoryTabs() {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        indicatorColor: Colors.blue,
        labelColor: Colors.blue,
        unselectedLabelColor: Colors.grey,
        onTap: (index) {
          setState(() {
            _selectedCategory = LessonCategory.values[index];
          });
        },
        tabs: LessonCategory.values.map((category) {
          return Tab(
            text: LessonCategoryNames.arabic[category] ?? '',
          );
        }).toList(),
      ),
    );
  }

  /// شبكة الدروس
  Widget _buildLessonsGrid() {
    final categoryLessons = _lessons
        .where((lesson) => lesson.category == _selectedCategory)
        .toList();

    if (categoryLessons.isEmpty) {
      return _buildEmptyState();
    }

    return Padding(
      padding: const EdgeInsets.all(20),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.8,
          crossAxisSpacing: 15,
          mainAxisSpacing: 15,
        ),
        itemCount: categoryLessons.length,
        itemBuilder: (context, index) {
          final lesson = categoryLessons[index];
          return _buildLessonCard(lesson);
        },
      ),
    );
  }

  /// بطاقة الدرس
  Widget _buildLessonCard(EducationalLesson lesson) {
    final progress = _progress?.lessonProgress[lesson.id];
    final isCompleted = progress?.isCompleted ?? false;
    final score = progress?.score ?? 0;

    return GestureDetector(
      onTap: lesson.isUnlocked ? () => _openLesson(lesson) : null,
      child: Container(
        decoration: BoxDecoration(
          color: lesson.isUnlocked ? Colors.white : Colors.grey.shade200,
          borderRadius: BorderRadius.circular(20),
          boxShadow: lesson.isUnlocked ? [
            BoxShadow(
              color: lesson.primaryColor.withOpacity(0.2),
              spreadRadius: 2,
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ] : null,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أيقونة الدرس
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: lesson.isUnlocked 
                    ? lesson.primaryColor.withOpacity(0.1)
                    : Colors.grey.shade300,
                borderRadius: BorderRadius.circular(15),
              ),
              child: Icon(
                _getCategoryIcon(lesson.category),
                size: 30,
                color: lesson.isUnlocked ? lesson.primaryColor : Colors.grey,
              ),
            ),
            const SizedBox(height: 10),
            
            // عنوان الدرس
            Text(
              lesson.titleAr,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: lesson.isUnlocked ? Colors.black87 : Colors.grey,
              ),
            ),
            const SizedBox(height: 5),
            
            // وصف الدرس
            Text(
              lesson.descriptionAr,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 12,
                color: lesson.isUnlocked ? Colors.grey.shade600 : Colors.grey,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 10),
            
            // مؤشر التقدم أو القفل
            if (lesson.isUnlocked) ...[
              if (isCompleted)
                Icon(
                  Icons.check_circle,
                  color: Colors.green,
                  size: 24,
                )
              else if (score > 0)
                CircularProgressIndicator(
                  value: score / lesson.maxScore,
                  strokeWidth: 3,
                  valueColor: AlwaysStoppedAnimation<Color>(lesson.primaryColor),
                )
              else
                Icon(
                  Icons.play_circle_outline,
                  color: lesson.primaryColor,
                  size: 24,
                ),
            ] else
              Icon(
                Icons.lock,
                color: Colors.grey,
                size: 24,
              ),
          ],
        ),
      ),
    );
  }

  /// حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.school,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 20),
          Text(
            'لا توجد دروس في هذه الفئة حالياً',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 10),
          Text(
            'سيتم إضافة المزيد قريباً!',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على أيقونة الفئة
  IconData _getCategoryIcon(LessonCategory category) {
    switch (category) {
      case LessonCategory.letters:
        return Icons.text_fields;
      case LessonCategory.numbers:
        return Icons.numbers;
      case LessonCategory.colors:
        return Icons.palette;
      case LessonCategory.animals:
        return Icons.pets;
      case LessonCategory.family:
        return Icons.family_restroom;
      case LessonCategory.body:
        return Icons.accessibility;
      case LessonCategory.food:
        return Icons.restaurant;
      case LessonCategory.transport:
        return Icons.directions_car;
      case LessonCategory.shapes:
        return Icons.category;
      case LessonCategory.dialogue:
        return Icons.chat;
    }
  }

  /// فتح الدرس
  void _openLesson(EducationalLesson lesson) {
    HapticFeedback.lightImpact();
    
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => LessonScreen(
          lesson: lesson,
          educationalService: _educationalService,
        ),
      ),
    ).then((_) {
      // تحديث التقدم عند العودة
      setState(() {
        _progress = _educationalService.getStudentProgress();
      });
    });
  }

  /// عرض الإنجازات
  void _showAchievements() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('🏆 إنجازاتك'),
        content: const Text('ستتم إضافة نظام الإنجازات قريباً!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }
}

/// شاشة الدرس (مؤقتة)
class LessonScreen extends StatelessWidget {
  final EducationalLesson lesson;
  final EducationalService educationalService;

  const LessonScreen({
    super.key,
    required this.lesson,
    required this.educationalService,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(lesson.titleAr),
        backgroundColor: lesson.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.construction,
              size: 80,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 20),
            Text(
              'درس ${lesson.titleAr}',
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            Text(
              lesson.descriptionAr,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 30),
            const Text(
              'سيتم تطوير محتوى الدرس قريباً!',
              style: TextStyle(
                fontSize: 16,
                color: Colors.orange,
              ),
            ),
            const SizedBox(height: 30),
            ElevatedButton(
              onPressed: () {
                // محاكاة إكمال الدرس
                educationalService.updateLessonProgress(lesson.id, 85);
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: lesson.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
              ),
              child: const Text('إكمال الدرس'),
            ),
          ],
        ),
      ),
    );
  }
}
