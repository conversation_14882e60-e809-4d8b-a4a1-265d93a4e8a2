import 'package:flutter/material.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:provider/provider.dart';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';
import 'package:speech_to_text/speech_to_text.dart';
import '../../config/app_theme.dart';
import '../../widgets/custom_app_bar.dart';
import '../../config/constants.dart';
import '../../utils/app_helpers.dart';
import '../../services/api/translation_service.dart';
import '../../core/app_state.dart';

/// شاشة الترجمة
class TranslationScreen extends StatefulWidget {
  final String translationType;

  const TranslationScreen({super.key, required this.translationType});

  @override
  State<TranslationScreen> createState() => _TranslationScreenState();
}

class _TranslationScreenState extends State<TranslationScreen> {
  // متغيرات الحالة
  bool _isLoading = false;
  String _sourceLanguage = 'ar';
  String _targetLanguage = 'en';
  final TextEditingController _sourceTextController = TextEditingController();
  String _translatedText = '';
  final FlutterTts _flutterTts = FlutterTts();
  final SpeechToText _speechToText = SpeechToText();
  bool _isListening = false;

  @override
  void dispose() {
    _sourceTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: _getScreenTitle()),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // اختيار اللغات
            _buildLanguageSelector(),

            const SizedBox(height: 16),

            // محتوى الترجمة حسب النوع
            Expanded(child: _buildTranslationContent()),
          ],
        ),
      ),
    );
  }

  /// الحصول على عنوان الشاشة حسب نوع الترجمة
  String _getScreenTitle() {
    final translationType = AppConstants.translationTypes.firstWhere(
      (type) => type['id'] == widget.translationType,
      orElse: () => {'title': 'ترجمة'},
    );

    return translationType['title'] as String;
  }

  /// بناء محدد اللغات
  Widget _buildLanguageSelector() {
    return Row(
      children: [
        // اللغة المصدر
        Expanded(
          child: _buildLanguageDropdown(
            value: _sourceLanguage,
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _sourceLanguage = value;
                });
              }
            },
          ),
        ),

        // زر تبديل اللغات
        IconButton(
          icon: const Icon(Icons.swap_horiz),
          onPressed: _swapLanguages,
          tooltip: 'تبديل اللغات',
        ),

        // اللغة الهدف
        Expanded(
          child: _buildLanguageDropdown(
            value: _targetLanguage,
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _targetLanguage = value;
                });
              }
            },
          ),
        ),
      ],
    );
  }

  /// بناء قائمة منسدلة للغات
  Widget _buildLanguageDropdown({
    required String value,
    required void Function(String?) onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: DropdownButton<String>(
        value: value,
        isExpanded: true,
        underline: Container(),
        onChanged: onChanged,
        items:
            AppConstants.supportedLanguages.map((language) {
              return DropdownMenuItem<String>(
                value: language['code'] as String,
                child: Row(
                  children: [
                    Text(language['flag'] as String),
                    const SizedBox(width: 8),
                    Text(language['name'] as String),
                  ],
                ),
              );
            }).toList(),
      ),
    );
  }

  /// بناء محتوى الترجمة حسب النوع
  Widget _buildTranslationContent() {
    switch (widget.translationType) {
      case 'text':
        return _buildTextTranslation();
      case 'voice':
        return _buildVoiceTranslation();
      case 'image':
        return _buildImageTranslation();
      case 'document':
        return _buildDocumentTranslation();
      case 'conversation':
        return _buildConversationTranslation();
      case 'real_time_conversation':
        return _buildRealTimeConversationTranslation();
      case 'multi_speaker':
        return _buildMultiSpeakerTranslation();
      case 'kids_mode':
        return _buildKidsModeTranslation();
      case 'tourism_mode':
        return _buildTourismModeTranslation();
      case 'ai_chat':
        return _buildAIChatTranslation();
      default:
        return _buildTextTranslation();
    }
  }

  /// تبديل اللغات
  void _swapLanguages() {
    setState(() {
      final temp = _sourceLanguage;
      _sourceLanguage = _targetLanguage;
      _targetLanguage = temp;

      // إذا كان هناك نص مترجم، قم بتبديله أيضًا
      if (_translatedText.isNotEmpty) {
        _sourceTextController.text = _translatedText;
        _translatedText = '';
      }
    });
  }

  /// ترجمة النص
  Future<void> _translateText() async {
    final sourceText = _sourceTextController.text.trim();
    if (sourceText.isEmpty) {
      AppHelpers.showSnackBar(context, 'يرجى إدخال نص للترجمة', isError: true);
      return;
    }
    setState(() {
      _isLoading = true;
    });
    try {
      final appState = Provider.of<AppState>(context, listen: false);
      final remoteConfig = appState.remoteConfig;
      final translationService = TranslationService(remoteConfig);
      // استخدم الذكاء الاصطناعي إذا كان المستخدم Premium أو Gemini API فعال
      final bool useAI =
          appState.isPremium ||
          remoteConfig.getString('gemini_api_key').isNotEmpty;
      final translatedText =
          useAI
              ? await translationService.translateWithAI(
                text: sourceText,
                sourceLanguage: _sourceLanguage,
                targetLanguage: _targetLanguage,
              )
              : await translationService.translateText(
                text: sourceText,
                sourceLanguage: _sourceLanguage,
                targetLanguage: _targetLanguage,
              );
      if (!mounted) return;
      setState(() {
        _translatedText = translatedText;
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء الترجمة: $e',
          isError: true,
        );
      }
    }
  }

  /// نطق النص المترجم
  Future<void> _speakTranslatedText() async {
    if (_translatedText.isEmpty) return;
    await _flutterTts.setLanguage(_targetLanguage == 'ar' ? 'ar-SA' : 'en-US');
    await _flutterTts.speak(_translatedText);
  }

  /// بناء واجهة ترجمة النصوص
  Widget _buildTextTranslation() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // حقل النص المصدر
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(10),
                  blurRadius: 8,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: TextField(
              controller: _sourceTextController,
              maxLines: 5,
              style: TextStyle(fontSize: 16),
              decoration: InputDecoration(
                hintText: 'أدخل النص المراد ترجمته',
                border: InputBorder.none,
                contentPadding: EdgeInsets.all(16),
              ),
            ),
          ),
          const SizedBox(height: 16),
          // زر الترجمة
          SizedBox(
            height: 48,
            child: ElevatedButton.icon(
              onPressed: _isLoading ? null : _translateText,
              icon: Icon(Icons.translate),
              label:
                  _isLoading
                      ? const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.white,
                        ),
                      )
                      : const Text('ترجمة', style: TextStyle(fontSize: 16)),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
          const SizedBox(height: 24),
          // النص المترجم وخصائصه
          if (_translatedText.isNotEmpty)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withAlpha(18),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    _translatedText,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.copy),
                        tooltip: 'نسخ',
                        onPressed: () {
                          Clipboard.setData(
                            ClipboardData(text: _translatedText),
                          );
                          AppHelpers.showSnackBar(context, 'تم نسخ الترجمة');
                        },
                      ),
                      IconButton(
                        icon: const Icon(Icons.volume_up),
                        tooltip: 'استمع',
                        onPressed: _speakTranslatedText,
                      ),
                      IconButton(
                        icon: const Icon(Icons.share),
                        tooltip: 'مشاركة',
                        onPressed: () {
                          Share.share(_translatedText);
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  /// بناء واجهة الترجمة الصوتية
  Widget _buildVoiceTranslation() {
    return Column(
      children: [
        const SizedBox(height: 20),
        // منطقة التسجيل
        Container(
          width: double.infinity,
          height: 200,
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withAlpha(26),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // أيقونة الميكروفون
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(26),
                      blurRadius: 10,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: IconButton(
                  icon: Icon(_isListening ? Icons.stop : Icons.mic, size: 40),
                  color: AppTheme.primaryColor,
                  onPressed:
                      _isListening
                          ? _stopListeningAndTranslate
                          : _startListening,
                  tooltip: _isListening ? 'إيقاف الاستماع' : 'بدء الاستماع',
                ),
              ),
              const SizedBox(height: 16),
              Text(
                _isListening ? 'جاري الاستماع... تحدث الآن' : 'اضغط للتحدث',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'سيتم كتابة كلامك بالعربية وترجمته تلقائياً للإنجليزية',
                style: TextStyle(fontSize: 14, color: Colors.grey),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        // منطقة النتائج
        Expanded(
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'النص المتعرف عليه:',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Text(
                  _sourceTextController.text.isNotEmpty
                      ? _sourceTextController.text
                      : 'سيظهر هنا النص الذي قلته بالعربية',
                  style: const TextStyle(color: Colors.black87),
                ),
                const SizedBox(height: 16),
                const Text(
                  'الترجمة:',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : Text(
                      _translatedText.isNotEmpty
                          ? _translatedText
                          : 'ستظهر هنا الترجمة الإنجليزية',
                      style: const TextStyle(color: Colors.black87),
                    ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _startListening() async {
    try {
      bool available = await _speechToText.initialize(
        onStatus: (status) {
          if (status == 'done' || status == 'notListening') {
            _stopListeningAndTranslate();
          }
        },
        onError: (error) {
          setState(() => _isListening = false);
          AppHelpers.showSnackBar(
            context,
            'حدث خطأ أثناء الاستماع: ${error.errorMsg}',
            isError: true,
          );
        },
      );
      if (!available) {
        if (mounted) {
          AppHelpers.showSnackBar(
            context,
            'التعرف على الكلام غير متاح على هذا الجهاز',
            isError: true,
          );
        }
        return;
      }
      setState(() {
        _isListening = true;
        _sourceTextController.text = '';
        _translatedText = '';
      });
      await _speechToText.listen(
        localeId: 'ar-SA',
        onResult: (result) {
          setState(() {
            _sourceTextController.text = result.recognizedWords;
          });
        },
      );
    } catch (e) {
      setState(() => _isListening = false);
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'تعذر بدء الاستماع: $e',
          isError: true,
        );
      }
    }
  }

  Future<void> _stopListeningAndTranslate() async {
    if (!_isListening) return;
    setState(() => _isListening = false);
    await _speechToText.stop();
    final spokenText = _sourceTextController.text.trim();
    if (spokenText.isEmpty) {
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'لم يتم التعرف على أي كلام',
          isError: true,
        );
      }
      return;
    }
    await _translateText();
  }

  /// بناء واجهة ترجمة الصور
  Widget _buildImageTranslation() {
    return Column(
      children: [
        const SizedBox(height: 20),

        // منطقة اختيار الصورة
        Container(
          width: double.infinity,
          height: 200,
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withAlpha(26),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.grey.shade300, width: 1),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // أيقونة الكاميرا
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(26),
                      blurRadius: 10,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: IconButton(
                  icon: const Icon(Icons.camera_alt, size: 40),
                  color: AppTheme.primaryColor,
                  onPressed: () {
                    // تنفيذ التقاط صورة
                    AppHelpers.showSnackBar(context, 'جاري فتح الكاميرا...');
                  },
                ),
              ),

              const SizedBox(height: 16),

              // أزرار الاختيار
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ElevatedButton.icon(
                    icon: const Icon(Icons.camera_alt),
                    label: const Text('التقاط صورة'),
                    onPressed: () {
                      AppHelpers.showSnackBar(context, 'جاري فتح الكاميرا...');
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                    ),
                  ),

                  const SizedBox(width: 16),

                  ElevatedButton.icon(
                    icon: const Icon(Icons.photo_library),
                    label: const Text('اختيار من المعرض'),
                    onPressed: () {
                      AppHelpers.showSnackBar(context, 'جاري فتح المعرض...');
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        const SizedBox(height: 20),

        // منطقة النتائج
        Expanded(
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'النص المستخرج:',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),

                const SizedBox(height: 8),

                const Text(
                  'سيظهر هنا النص المستخرج من الصورة',
                  style: TextStyle(color: Colors.grey),
                ),

                const SizedBox(height: 16),

                const Text(
                  'الترجمة:',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),

                const SizedBox(height: 8),

                const Text(
                  'ستظهر هنا ترجمة النص المستخرج',
                  style: TextStyle(color: Colors.grey),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// ترجمة المستندات
  Widget _buildDocumentTranslation() {
    return Center(child: Text('ترجمة المستندات غير متوفرة حالياً'));
  }

  /// ترجمة المحادثة
  Widget _buildConversationTranslation() {
    return Center(child: Text('ترجمة المحادثة غير متوفرة حالياً'));
  }

  /// الترجمة الفورية
  Widget _buildRealTimeConversationTranslation() {
    return Center(child: Text('الترجمة الفورية غير متوفرة حالياً'));
  }

  /// ترجمة المتحدثين المتعددين
  Widget _buildMultiSpeakerTranslation() {
    return Center(child: Text('ترجمة المتحدثين المتعددين غير متوفرة حالياً'));
  }

  /// وضع الأطفال
  Widget _buildKidsModeTranslation() {
    return Center(child: Text('وضع الأطفال غير متوفر حالياً'));
  }

  /// وضع السياحة
  Widget _buildTourismModeTranslation() {
    return Center(child: Text('وضع السياحة غير متوفر حالياً'));
  }

  /// الدردشة الذكية
  Widget _buildAIChatTranslation() {
    return Center(child: Text('الدردشة الذكية غير متوفرة حالياً'));
  }
}
