import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../config/app_theme.dart';

/// بطاقة ثلاثية الأبعاد مخصصة
class Card3D extends StatefulWidget {
  /// محتوى البطاقة
  final Widget child;

  /// عرض البطاقة (اختياري)
  final double? width;

  /// ارتفاع البطاقة (اختياري)
  final double? height;

  /// لون البطاقة (اختياري)
  final Color? color;

  /// نصف قطر الحواف
  final double borderRadius;

  /// عمق تأثير ثلاثي الأبعاد
  final double depth;

  /// الإجراء الذي سيتم تنفيذه عند النقر على البطاقة (اختياري)
  final VoidCallback? onTap;

  /// ما إذا كانت البطاقة قابلة للنقر
  final bool isClickable;

  /// ما إذا كانت البطاقة مرتفعة
  final bool isElevated;

  /// ما إذا كانت البطاقة تحتوي على تأثير تحويم
  final bool hasHoverEffect;

  /// ما إذا كانت البطاقة تحتوي على تأثير نقر
  final bool hasTapEffect;

  /// ما إذا كانت البطاقة تحتوي على تأثير ظل
  final bool hasShadow;

  const Card3D({
    super.key,
    required this.child,
    this.width,
    this.height,
    this.color,
    this.borderRadius = 16.0,
    this.depth = 4.0,
    this.onTap,
    this.isClickable = true,
    this.isElevated = true,
    this.hasHoverEffect = true,
    this.hasTapEffect = true,
    this.hasShadow = true,
  });

  @override
  State<Card3D> createState() => _Card3DState();
}

class _Card3DState extends State<Card3D> with SingleTickerProviderStateMixin {
  bool _isHovered = false;
  bool _isPressed = false;
  late AnimationController _animationController;
  late Animation<double> _pressedAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 100),
    );
    _pressedAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (widget.isClickable && widget.hasTapEffect) {
      setState(() {
        _isPressed = true;
      });
      _animationController.forward();
    }
  }

  void _onTapUp(TapUpDetails details) {
    if (widget.isClickable && widget.hasTapEffect) {
      setState(() {
        _isPressed = false;
      });
      _animationController.reverse();
    }
  }

  void _onTapCancel() {
    if (widget.isClickable && widget.hasTapEffect) {
      setState(() {
        _isPressed = false;
      });
      _animationController.reverse();
    }
  }

  void _onHover(bool isHovered) {
    if (widget.isClickable && widget.hasHoverEffect) {
      setState(() {
        _isHovered = isHovered;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final Color cardColor = widget.color ?? Colors.white;
    final Color shadowColor = Color.fromRGBO(
      0,
      122,
      255,
      0.2,
    ); // استخدام قيم ثابتة بدلاً من withAlpha

    Widget cardWidget = Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        boxShadow:
            widget.hasShadow && widget.isElevated
                ? [
                  // الظل السفلي (تأثير العمق)
                  BoxShadow(
                    color: shadowColor,
                    offset: Offset(
                      0,
                      widget.depth * (1 - (_isPressed ? 1 : 0)),
                    ),
                    blurRadius: widget.depth * 2,
                    spreadRadius: 0,
                  ),
                  // الظل العلوي (تأثير الإضاءة)
                  const BoxShadow(
                    color: Colors.white,
                    offset: Offset(0, 1),
                    blurRadius: 1,
                    spreadRadius: 0,
                  ),
                ]
                : null,
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [cardColor, Color.fromRGBO(255, 255, 255, 0.9)],
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(widget.borderRadius),
        child: widget.child,
      ),
    );

    if (widget.isClickable) {
      cardWidget = AnimatedBuilder(
        animation: _pressedAnimation,
        builder: (context, child) {
          return Transform.translate(
            offset:
                widget.hasTapEffect
                    ? Offset(0, widget.depth * 0.5 * _pressedAnimation.value)
                    : Offset.zero,
            child: Transform.scale(
              scale:
                  widget.hasHoverEffect
                      ? _isHovered
                          ? 1.03
                          : 1.0
                      : 1.0,
              child: child,
            ),
          );
        },
        child: cardWidget,
      );

      cardWidget = MouseRegion(
        onEnter: (_) => _onHover(true),
        onExit: (_) => _onHover(false),
        cursor: SystemMouseCursors.click,
        child: GestureDetector(
          onTapDown: _onTapDown,
          onTapUp: _onTapUp,
          onTapCancel: _onTapCancel,
          onTap: widget.onTap,
          child: cardWidget,
        ),
      );
    }

    return cardWidget;
  }
}

/// بطاقة ميزة ثلاثية الأبعاد
class FeatureCard3D extends StatelessWidget {
  /// عنوان الميزة
  final String title;

  /// أيقونة الميزة
  final IconData icon;

  /// لون الميزة
  final Color? color;

  /// الإجراء الذي سيتم تنفيذه عند النقر على البطاقة
  final VoidCallback onTap;

  /// ما إذا كانت الميزة مقفلة
  final bool isLocked;

  /// ما إذا كانت الميزة جديدة
  final bool isNew;

  const FeatureCard3D({
    super.key,
    required this.title,
    required this.icon,
    this.color,
    required this.onTap,
    this.isLocked = false,
    this.isNew = false,
  });

  @override
  Widget build(BuildContext context) {
    final Color cardColor = color ?? AppTheme.primaryColor;

    return Card3D(
      onTap: isLocked ? null : onTap,
      isClickable: !isLocked,
      color: isLocked ? Colors.grey.shade200 : Colors.white,
      child: Stack(
        children: [
          // محتوى البطاقة
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // أيقونة الميزة
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color:
                        isLocked
                            ? Colors.grey.shade300
                            : Color.fromRGBO(0, 122, 255, 0.12),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    icon,
                    size: 30,
                    color: isLocked ? Colors.grey : cardColor,
                  ),
                ).animate().scale(
                  duration: 300.ms,
                  curve: Curves.easeOut,
                  begin: const Offset(0.8, 0.8),
                  end: const Offset(1.0, 1.0),
                ),

                const SizedBox(height: 16),

                // عنوان الميزة
                Text(
                  title,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: isLocked ? Colors.grey : AppTheme.onBackground,
                  ),
                ),
              ],
            ),
          ),

          // شارة القفل
          if (isLocked)
            Positioned(
              top: 8,
              right: 8,
              child: Icon(Icons.lock, size: 20, color: Colors.grey.shade400),
            ),

          // شارة جديد
          if (isNew && !isLocked)
            Positioned(
              top: 8,
              left: 8,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'جديد',
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
