const http = require('http');
const fs = require('fs');
const path = require('path');

const server = http.createServer((req, res) => {
    // تحديد مسار الملف
    let filePath = path.join(__dirname, 'build', 'web', req.url === '/' ? 'index.html' : req.url);
    
    // تحديد نوع المحتوى
    const extname = path.extname(filePath);
    let contentType = 'text/html';
    
    switch(extname) {
        case '.js':
            contentType = 'text/javascript';
            break;
        case '.css':
            contentType = 'text/css';
            break;
        case '.json':
            contentType = 'application/json';
            break;
        case '.png':
            contentType = 'image/png';
            break;
        case '.jpg':
            contentType = 'image/jpg';
            break;
        case '.ico':
            contentType = 'image/x-icon';
            break;
        case '.wasm':
            contentType = 'application/wasm';
            break;
    }
    
    // قراءة الملف
    fs.readFile(filePath, (error, content) => {
        if (error) {
            if(error.code == 'ENOENT') {
                // إذا لم يوجد الملف، أرجع index.html (للتوجيه)
                fs.readFile(path.join(__dirname, 'build', 'web', 'index.html'), (err, indexContent) => {
                    if (err) {
                        res.writeHead(404);
                        res.end('File not found');
                    } else {
                        res.writeHead(200, { 'Content-Type': 'text/html' });
                        res.end(indexContent, 'utf-8');
                    }
                });
            } else {
                res.writeHead(500);
                res.end('Server error: ' + error.code);
            }
        } else {
            res.writeHead(200, { 'Content-Type': contentType });
            res.end(content, 'utf-8');
        }
    });
});

const PORT = 3003;
server.listen(PORT, () => {
    console.log(`🚀 AI Smart Translator is running at:`);
    console.log(`   http://localhost:${PORT}`);
    console.log(`   http://127.0.0.1:${PORT}`);
    console.log('');
    console.log('📱 Open the URL in your browser to see the app!');
});
