# دليل إعداد Firebase - مترجم الذكاء الاصطناعي الجديد

## نظرة عامة
هذا الدليل يوضح كيفية إعداد واستخدام Firebase CLI مع مشروع مترجم الذكاء الاصطناعي الجديد.

## معلومات المشروع
- **اسم المشروع**: AI Smart Translator New
- **معرف المشروع**: ar-project-4063d
- **Package Name**: com.ar.ai_smart_translator_new
- **رقم المشروع**: 706005855038

## المتطلبات المسبقة
- ✅ Node.js (مثبت)
- ✅ Firebase CLI (مثبت)
- ✅ Flutter SDK
- ✅ حساب Firebase

## الملفات المهمة

### ملفات التكوين
- `.firebaserc` - تحديد المشروع الافتراضي
- `firebase.json` - إعدادات Firebase الرئيسية
- `firestore.rules` - قواعد أمان Firestore
- `firestore.indexes.json` - فهارس Firestore
- `storage.rules` - قواعد أمان Storage
- `remoteconfig.template.json` - قالب Remote Config

### سكريبتات التشغيل
- `setup-project.bat` - تهيئة المشروع الجديد
- `deploy.bat` - نشر التطبيق كاملاً
- `update-config.bat` - تحديث Remote Config

## الأوامر الأساسية

### تسجيل الدخول
```bash
firebase login
```

### عرض المشاريع
```bash
firebase projects:list
```

### تحديد المشروع
```bash
firebase use ar-project-4063d
```

### نشر التطبيق
```bash
# نشر كامل
firebase deploy

# نشر جزئي
firebase deploy --only hosting
firebase deploy --only firestore:rules
firebase deploy --only storage
```

### إدارة Remote Config
```bash
# عرض التكوين الحالي
firebase remoteconfig:get

# تحديث التكوين
firebase remoteconfig:set remoteconfig.template.json

# نشر التكوين
firebase remoteconfig:publish
```

## إعدادات Remote Config

### مفاتيح API
- `openai_api_key` - مفتاح OpenAI
- `gemini_api_key` - مفتاح Gemini المجاني
- `gemini_api_key_paid` - مفتاح Gemini المدفوع
- `openrouter_api_key` - مفتاح OpenRouter

### إعدادات التطبيق
- `active_api_service` - الخدمة النشطة (gemini/openai/openrouter)
- `max_translation_length` - الحد الأقصى لطول النص
- `supported_languages` - اللغات المدعومة

### حدود الاستخدام
- `daily_limit_openai` - الحد اليومي لـ OpenAI
- `daily_limit_gemini` - الحد اليومي لـ Gemini
- `daily_limit_openrouter` - الحد اليومي لـ OpenRouter

## قواعد الأمان

### Firestore
- المستخدمون يمكنهم الوصول لبياناتهم فقط
- الترجمات مرتبطة بـ userId
- الاشتراكات محمية بـ userId

### Storage
- رفع الملفات للمستخدمين المسجلين فقط
- حدود حجم الملفات (10MB للمستندات، 5MB للصور)
- صور الملف الشخصي قابلة للقراءة العامة

## استكشاف الأخطاء

### مشاكل شائعة
1. **خطأ في تسجيل الدخول**: تأكد من `firebase login`
2. **مشروع غير موجود**: تحقق من `.firebaserc`
3. **فشل النشر**: تحقق من الأذونات والاتصال

### حلول
```bash
# إعادة تسجيل الدخول
firebase logout
firebase login

# تحديث Firebase CLI
npm install -g firebase-tools@latest

# تنظيف الكاش
firebase use --clear-cache
```

## الدعم
للمساعدة أو الاستفسارات، راجع:
- [وثائق Firebase](https://firebase.google.com/docs)
- [وثائق Flutter Firebase](https://firebase.flutter.dev/)
