import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math';

/// نشاط أصوات الحيوانات التفاعلي
class AnimalSoundsActivity extends StatefulWidget {
  final Function(int score) onComplete;

  const AnimalSoundsActivity({super.key, required this.onComplete});

  @override
  State<AnimalSoundsActivity> createState() => _AnimalSoundsActivityState();
}

class _AnimalSoundsActivityState extends State<AnimalSoundsActivity>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _celebrationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _celebrationAnimation;

  int _currentQuestionIndex = 0;
  int _score = 0;
  final int _totalQuestions = 5;
  bool _isAnswered = false;
  bool _showCelebration = false;

  final List<Map<String, dynamic>> _questions = [
    {
      'animal': '🐄',
      'name': 'البقرة',
      'sound': 'مووو',
      'options': ['مووو', 'نهيق', 'عواء', 'زقزقة'],
      'correctIndex': 0,
    },
    {
      'animal': '🐱',
      'name': 'القطة',
      'sound': 'مياو',
      'options': ['عواء', 'مياو', 'نباح', 'صهيل'],
      'correctIndex': 1,
    },
    {
      'animal': '🐶',
      'name': 'الكلب',
      'sound': 'نباح',
      'options': ['مياو', 'زقزقة', 'نباح', 'خوار'],
      'correctIndex': 2,
    },
    {
      'animal': '🐴',
      'name': 'الحصان',
      'sound': 'صهيل',
      'options': ['نهيق', 'صهيل', 'عواء', 'نباح'],
      'correctIndex': 1,
    },
    {
      'animal': '🐦',
      'name': 'العصفور',
      'sound': 'زقزقة',
      'options': ['مووو', 'نباح', 'مياو', 'زقزقة'],
      'correctIndex': 3,
    },
  ];

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _shuffleQuestions();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _celebrationController.dispose();
    super.dispose();
  }

  void _initAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _celebrationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
    );

    _celebrationAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _celebrationController, curve: Curves.bounceOut),
    );

    _animationController.forward();
  }

  void _shuffleQuestions() {
    _questions.shuffle(Random());
    for (var question in _questions) {
      final correctAnswer = question['options'][question['correctIndex']];
      question['options'].shuffle(Random());
      question['correctIndex'] = question['options'].indexOf(correctAnswer);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.orange.shade100,
              Colors.pink.shade100,
              Colors.purple.shade100,
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              _buildProgressBar(),
              Expanded(
                child:
                    _showCelebration
                        ? _buildCelebrationScreen()
                        : _buildQuestionScreen(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.purple),
            onPressed: () => Navigator.pop(context),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'أصوات الحيوانات 🐾',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.purple,
                  ),
                ),
                Text(
                  'اختر الصوت الصحيح لكل حيوان',
                  style: TextStyle(fontSize: 14, color: Colors.purple.shade600),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(15),
              boxShadow: [
                BoxShadow(
                  color: Colors.purple.withValues(alpha: 0.2),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Text(
              'النقاط: $_score',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.purple,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressBar() {
    final progress = (_currentQuestionIndex + 1) / _totalQuestions;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'السؤال ${_currentQuestionIndex + 1} من $_totalQuestions',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.purple,
                ),
              ),
              Text(
                '${(progress * 100).round()}%',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.purple,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.purple.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(Colors.purple),
            minHeight: 8,
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionScreen() {
    if (_currentQuestionIndex >= _questions.length) {
      return _buildCompletionScreen();
    }

    final question = _questions[_currentQuestionIndex];

    return ScaleTransition(
      scale: _scaleAnimation,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // الحيوان
            Container(
              padding: const EdgeInsets.all(30),
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.purple.withValues(alpha: 0.3),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: Text(
                question['animal'],
                style: const TextStyle(fontSize: 80),
              ),
            ),

            const SizedBox(height: 30),

            Text(
              'ما هو صوت ${question['name']}؟',
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.purple,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 40),

            // خيارات الإجابة
            ...List.generate(
              question['options'].length,
              (index) => _buildAnswerOption(
                question['options'][index],
                index,
                question['correctIndex'],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnswerOption(String option, int index, int correctIndex) {
    final isCorrect = index == correctIndex;

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 12),
      child: ElevatedButton(
        onPressed:
            _isAnswered ? null : () => _selectAnswer(index, correctIndex),
        style: ElevatedButton.styleFrom(
          backgroundColor:
              _isAnswered
                  ? (isCorrect
                      ? Colors.green
                      : Colors.red.withValues(alpha: 0.3))
                  : Colors.white,
          foregroundColor: _isAnswered ? Colors.white : Colors.purple,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          elevation: _isAnswered && isCorrect ? 8 : 2,
        ),
        child: Text(
          option,
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  void _selectAnswer(int selectedIndex, int correctIndex) {
    setState(() {
      _isAnswered = true;
    });

    HapticFeedback.lightImpact();

    if (selectedIndex == correctIndex) {
      _score += 20;
      HapticFeedback.heavyImpact();

      // رسوم متحركة للإجابة الصحيحة
      _celebrationController.forward().then((_) {
        _celebrationController.reset();
      });
    }

    // الانتقال للسؤال التالي بعد ثانيتين
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _currentQuestionIndex++;
          _isAnswered = false;
        });

        if (_currentQuestionIndex < _questions.length) {
          _animationController.reset();
          _animationController.forward();
        }
      }
    });
  }

  Widget _buildCompletionScreen() {
    final percentage = (_score / (_totalQuestions * 20) * 100).round();

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ScaleTransition(
            scale: _celebrationAnimation,
            child: Container(
              padding: const EdgeInsets.all(40),
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.purple.withValues(alpha: 0.3),
                    blurRadius: 30,
                    offset: const Offset(0, 15),
                  ),
                ],
              ),
              child: Text(
                percentage >= 80
                    ? '🏆'
                    : percentage >= 60
                    ? '⭐'
                    : '👍',
                style: const TextStyle(fontSize: 80),
              ),
            ),
          ),

          const SizedBox(height: 30),

          Text(
            percentage >= 80
                ? 'ممتاز!'
                : percentage >= 60
                ? 'جيد جداً!'
                : 'أحسنت!',
            style: const TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: Colors.purple,
            ),
          ),

          const SizedBox(height: 16),

          Text(
            'لقد حصلت على $_score نقطة من ${_totalQuestions * 20}',
            style: const TextStyle(fontSize: 18, color: Colors.purple),
          ),

          Text(
            'النسبة: $percentage%',
            style: const TextStyle(fontSize: 16, color: Colors.purple),
          ),

          const SizedBox(height: 40),

          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton.icon(
                onPressed: _restartActivity,
                icon: const Icon(Icons.refresh),
                label: const Text('إعادة المحاولة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 12,
                  ),
                ),
              ),
              ElevatedButton.icon(
                onPressed: () {
                  widget.onComplete(_score);
                  Navigator.pop(context);
                },
                icon: const Icon(Icons.check),
                label: const Text('إنهاء'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 12,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCelebrationScreen() {
    return Center(
      child: ScaleTransition(
        scale: _celebrationAnimation,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(30),
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.green.withValues(alpha: 0.3),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: const Text('🎉', style: TextStyle(fontSize: 60)),
            ),
            const SizedBox(height: 20),
            const Text(
              'إجابة صحيحة!',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _restartActivity() {
    setState(() {
      _currentQuestionIndex = 0;
      _score = 0;
      _isAnswered = false;
      _showCelebration = false;
    });

    _shuffleQuestions();
    _animationController.reset();
    _animationController.forward();
  }
}
