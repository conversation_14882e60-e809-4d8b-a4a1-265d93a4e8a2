/// نموذج إحصائيات الاستخدام
class UsageStats {
  final int voiceTranslations;
  final int textTranslations;
  final int imageTranslations;
  final int documentTranslations;
  final int conversations;
  final int aiChats;
  final int totalTranslations;
  final int dailyUsage;
  final int weeklyUsage;
  final int monthlyUsage;

  UsageStats({
    this.voiceTranslations = 0,
    this.textTranslations = 0,
    this.imageTranslations = 0,
    this.documentTranslations = 0,
    this.conversations = 0,
    this.aiChats = 0,
    this.dailyUsage = 0,
    this.weeklyUsage = 0,
    this.monthlyUsage = 0,
  }) : totalTranslations = voiceTranslations +
            textTranslations +
            imageTranslations +
            documentTranslations +
            conversations;

  /// إنشاء نسخة جديدة مع تعديل بعض الخصائص
  UsageStats copyWith({
    int? voiceTranslations,
    int? textTranslations,
    int? imageTranslations,
    int? documentTranslations,
    int? conversations,
    int? aiChats,
    int? dailyUsage,
    int? weeklyUsage,
    int? monthlyUsage,
  }) {
    return UsageStats(
      voiceTranslations: voiceTranslations ?? this.voiceTranslations,
      textTranslations: textTranslations ?? this.textTranslations,
      imageTranslations: imageTranslations ?? this.imageTranslations,
      documentTranslations: documentTranslations ?? this.documentTranslations,
      conversations: conversations ?? this.conversations,
      aiChats: aiChats ?? this.aiChats,
      dailyUsage: dailyUsage ?? this.dailyUsage,
      weeklyUsage: weeklyUsage ?? this.weeklyUsage,
      monthlyUsage: monthlyUsage ?? this.monthlyUsage,
    );
  }

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'voiceTranslations': voiceTranslations,
      'textTranslations': textTranslations,
      'imageTranslations': imageTranslations,
      'documentTranslations': documentTranslations,
      'conversations': conversations,
      'aiChats': aiChats,
      'totalTranslations': totalTranslations,
      'dailyUsage': dailyUsage,
      'weeklyUsage': weeklyUsage,
      'monthlyUsage': monthlyUsage,
    };
  }

  /// إنشاء نموذج من Map
  factory UsageStats.fromMap(Map<String, dynamic> map) {
    return UsageStats(
      voiceTranslations: map['voiceTranslations'] ?? 0,
      textTranslations: map['textTranslations'] ?? 0,
      imageTranslations: map['imageTranslations'] ?? 0,
      documentTranslations: map['documentTranslations'] ?? 0,
      conversations: map['conversations'] ?? 0,
      aiChats: map['aiChats'] ?? 0,
      dailyUsage: map['dailyUsage'] ?? 0,
      weeklyUsage: map['weeklyUsage'] ?? 0,
      monthlyUsage: map['monthlyUsage'] ?? 0,
    );
  }

  /// تحويل النموذج إلى JSON
  Map<String, dynamic> toJson() => toMap();

  /// إنشاء نموذج من JSON
  factory UsageStats.fromJson(Map<String, dynamic> json) => UsageStats.fromMap(json);

  @override
  String toString() {
    return 'UsageStats(voiceTranslations: $voiceTranslations, textTranslations: $textTranslations, imageTranslations: $imageTranslations, documentTranslations: $documentTranslations, conversations: $conversations, aiChats: $aiChats, totalTranslations: $totalTranslations, dailyUsage: $dailyUsage, weeklyUsage: $weeklyUsage, monthlyUsage: $monthlyUsage)';
  }
}
