import 'package:flutter/material.dart';
import '../config/app_theme.dart';

/// زر ثلاثي الأبعاد مخصص
///
/// هذا الملف يحتوي على جميع أنواع الأزرار ثلاثية الأبعاد:
/// - Button3D: الزر الأساسي
/// - SmallButton3D: زر صغير
/// - LargeButton3D: زر كبير
/// - CircleButton3D: زر دائري
class Button3D extends StatefulWidget {
  /// النص المعروض على الزر
  final String text;

  /// أيقونة الزر (اختياري)
  final IconData? icon;

  /// الإجراء الذي سيتم تنفيذه عند النقر على الزر
  final Function()? onPressed;

  /// لون الزر الرئيسي
  final Color? color;

  /// لون النص
  final Color? textColor;

  /// عرض الزر (اختياري)
  final double? width;

  /// ارتفاع الزر (اختياري)
  final double? height;

  /// حجم النص (اختياري)
  final double? fontSize;

  /// حجم الأيقونة (اختياري)
  final double? iconSize;

  /// ما إذا كان الزر معطلاً
  final bool isDisabled;

  /// ما إذا كان الزر قيد التحميل
  final bool isLoading;

  /// نصف قطر الحواف
  final double borderRadius;

  /// عمق تأثير ثلاثي الأبعاد
  final double depth;

  const Button3D({
    super.key,
    required this.text,
    required this.onPressed,
    this.icon,
    this.color,
    this.textColor,
    this.width,
    this.height = 56.0,
    this.fontSize = 16.0,
    this.iconSize = 24.0,
    this.isDisabled = false,
    this.isLoading = false,
    this.borderRadius = 16.0,
    this.depth = 4.0,
  });

  @override
  State<Button3D> createState() => _Button3DState();
}

class _Button3DState extends State<Button3D>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _pressedAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 100),
    );
    _pressedAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (!widget.isDisabled && !widget.isLoading) {
      _animationController.forward();
    }
  }

  void _onTapUp(TapUpDetails details) {
    if (!widget.isDisabled && !widget.isLoading) {
      _animationController.reverse();
    }
  }

  void _onTapCancel() {
    if (!widget.isDisabled && !widget.isLoading) {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final Color buttonColor = widget.color ?? AppTheme.primaryColor;
    final Color textColor = widget.textColor ?? Colors.white;
    // استخدام Color.fromRGBO بدلاً من withOpacity
    final Color shadowColor = Color.fromRGBO(
      0, // استخدام قيم ثابتة بدلاً من استخدام الخصائص المهملة
      122,
      255,
      0.5,
    );
    final Color disabledColor = Colors.grey.shade300;

    return GestureDetector(
      onTapDown: _onTapDown,
      onTapUp: _onTapUp,
      onTapCancel: _onTapCancel,
      onTap:
          widget.isDisabled || widget.isLoading || widget.onPressed == null
              ? null
              : widget.onPressed,
      child: AnimatedBuilder(
        animation: _pressedAnimation,
        builder: (context, child) {
          return Container(
            width: widget.width,
            height: widget.height,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(widget.borderRadius),
              boxShadow: [
                // الظل السفلي (تأثير العمق)
                BoxShadow(
                  color: widget.isDisabled ? Colors.grey.shade400 : shadowColor,
                  offset: Offset(
                    0,
                    widget.depth * (1 - _pressedAnimation.value),
                  ),
                  blurRadius: widget.depth,
                ),
              ],
            ),
            child: Transform.translate(
              offset: Offset(0, widget.depth * _pressedAnimation.value),
              child: Container(
                decoration: BoxDecoration(
                  color: widget.isDisabled ? disabledColor : buttonColor,
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                  boxShadow: [
                    // الظل العلوي (تأثير الإضاءة)
                    BoxShadow(
                      color: Color.fromRGBO(255, 255, 255, 0.3),
                      offset: const Offset(0, 2),
                      blurRadius: 5,
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Center(
                  child:
                      widget.isLoading
                          ? SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              color: textColor,
                              strokeWidth: 2,
                            ),
                          )
                          : Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              if (widget.icon != null) ...[
                                Icon(
                                  widget.icon,
                                  color: textColor,
                                  size: widget.iconSize,
                                ),
                                const SizedBox(width: 8),
                              ],
                              Text(
                                widget.text,
                                style: TextStyle(
                                  color: textColor,
                                  fontSize: widget.fontSize,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

/// زر ثلاثي الأبعاد صغير
class SmallButton3D extends Button3D {
  const SmallButton3D({
    super.key,
    required super.text,
    required super.onPressed,
    super.icon,
    super.color,
    super.textColor,
    super.width,
    super.height = 40.0,
    super.fontSize = 14.0,
    super.iconSize = 18.0,
    super.isDisabled = false,
    super.isLoading = false,
    super.borderRadius = 12.0,
    super.depth = 3.0,
  });
}

/// زر ثلاثي الأبعاد كبير
class LargeButton3D extends Button3D {
  const LargeButton3D({
    super.key,
    required super.text,
    required super.onPressed,
    super.icon,
    super.color,
    super.textColor,
    super.width,
    super.height = 64.0,
    super.fontSize = 18.0,
    super.iconSize = 28.0,
    super.isDisabled = false,
    super.isLoading = false,
    super.borderRadius = 20.0,
    super.depth = 5.0,
  });
}

/// زر ثلاثي الأبعاد دائري
class CircleButton3D extends StatefulWidget {
  /// أيقونة الزر
  final IconData icon;

  /// الإجراء الذي سيتم تنفيذه عند النقر على الزر
  final Function()? onPressed;

  /// لون الزر الرئيسي
  final Color? color;

  /// لون الأيقونة
  final Color? iconColor;

  /// حجم الزر
  final double size;

  /// حجم الأيقونة
  final double iconSize;

  /// ما إذا كان الزر معطلاً
  final bool isDisabled;

  /// ما إذا كان الزر قيد التحميل
  final bool isLoading;

  /// عمق تأثير ثلاثي الأبعاد
  final double depth;

  const CircleButton3D({
    super.key,
    required this.icon,
    required this.onPressed,
    this.color,
    this.iconColor,
    this.size = 56.0,
    this.iconSize = 24.0,
    this.isDisabled = false,
    this.isLoading = false,
    this.depth = 4.0,
  });

  @override
  State<CircleButton3D> createState() => _CircleButton3DState();
}

class _CircleButton3DState extends State<CircleButton3D>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _pressedAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 100),
    );
    _pressedAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (!widget.isDisabled && !widget.isLoading) {
      _animationController.forward();
    }
  }

  void _onTapUp(TapUpDetails details) {
    if (!widget.isDisabled && !widget.isLoading) {
      _animationController.reverse();
    }
  }

  void _onTapCancel() {
    if (!widget.isDisabled && !widget.isLoading) {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final Color buttonColor = widget.color ?? AppTheme.primaryColor;
    final Color iconColor = widget.iconColor ?? Colors.white;
    // استخدام Color.fromRGBO بدلاً من withOpacity
    final Color shadowColor = Color.fromRGBO(
      0, // استخدام قيم ثابتة بدلاً من استخدام الخصائص المهملة
      122,
      255,
      0.5,
    );
    final Color disabledColor = Colors.grey.shade300;

    return GestureDetector(
      onTapDown: _onTapDown,
      onTapUp: _onTapUp,
      onTapCancel: _onTapCancel,
      onTap:
          widget.isDisabled || widget.isLoading || widget.onPressed == null
              ? null
              : widget.onPressed,
      child: AnimatedBuilder(
        animation: _pressedAnimation,
        builder: (context, child) {
          return Container(
            width: widget.size,
            height: widget.size,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                // الظل السفلي (تأثير العمق)
                BoxShadow(
                  color: widget.isDisabled ? Colors.grey.shade400 : shadowColor,
                  offset: Offset(
                    0,
                    widget.depth * (1 - _pressedAnimation.value),
                  ),
                  blurRadius: widget.depth,
                ),
              ],
            ),
            child: Transform.translate(
              offset: Offset(0, widget.depth * _pressedAnimation.value),
              child: Container(
                decoration: BoxDecoration(
                  color: widget.isDisabled ? disabledColor : buttonColor,
                  shape: BoxShape.circle,
                  boxShadow: [
                    // الظل العلوي (تأثير الإضاءة)
                    BoxShadow(
                      color: Color.fromRGBO(255, 255, 255, 0.3),
                      offset: const Offset(0, 2),
                      blurRadius: 5,
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Center(
                  child:
                      widget.isLoading
                          ? SizedBox(
                            width: widget.iconSize,
                            height: widget.iconSize,
                            child: CircularProgressIndicator(
                              color: iconColor,
                              strokeWidth: 2,
                            ),
                          )
                          : Icon(
                            widget.icon,
                            color: iconColor,
                            size: widget.iconSize,
                          ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
