import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../config/duolingo_theme.dart';
import '../../services/professional_audio_service.dart';
import '../../utils/responsive_helper.dart';

/// شاشة Duolingo الاحترافية الحقيقية
class ProfessionalDuolingoScreen extends StatefulWidget {
  const ProfessionalDuolingoScreen({super.key});

  @override
  State<ProfessionalDuolingoScreen> createState() => _ProfessionalDuolingoScreenState();
}

class _ProfessionalDuolingoScreenState extends State<ProfessionalDuolingoScreen>
    with TickerProviderStateMixin {
  
  late AnimationController _mainAnimationController;
  late AnimationController _heartAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _heartBeatAnimation;
  
  final ProfessionalAudioService _audioService = ProfessionalAudioService();
  
  // بيانات المستخدم
  int _currentXP = 1250;
  int _currentStreak = 7;
  int _gems = 45;
  int _hearts = 5;
  String _currentLanguage = 'الإنجليزية';
  String _currentFlag = '🇺🇸';
  
  // بيانات التقدم
  final List<LessonUnit> _lessons = [];
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeAudio();
    _loadLessons();
  }
  
  void _initializeAnimations() {
    _mainAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _heartAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _mainAnimationController, curve: Curves.easeOut),
    );
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _mainAnimationController, curve: Curves.easeOut));
    
    _heartBeatAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _heartAnimationController, curve: Curves.elasticOut),
    );
    
    _mainAnimationController.forward();
  }
  
  Future<void> _initializeAudio() async {
    await _audioService.initialize();
    await _audioService.playBackgroundMusic();
  }
  
  void _loadLessons() {
    _lessons.addAll([
      LessonUnit(
        id: 1,
        title: 'الأساسيات',
        description: 'تعلم الكلمات الأساسية',
        color: DuolingoTheme.primaryGreen,
        icon: Icons.abc,
        progress: 1.0,
        isUnlocked: true,
        lessons: 5,
        completedLessons: 5,
      ),
      LessonUnit(
        id: 2,
        title: 'العائلة',
        description: 'أفراد العائلة والأقارب',
        color: DuolingoTheme.primaryBlue,
        icon: Icons.family_restroom,
        progress: 0.6,
        isUnlocked: true,
        lessons: 5,
        completedLessons: 3,
      ),
      LessonUnit(
        id: 3,
        title: 'الطعام',
        description: 'أسماء الأطعمة والمشروبات',
        color: DuolingoTheme.primaryOrange,
        icon: Icons.restaurant,
        progress: 0.2,
        isUnlocked: true,
        lessons: 5,
        completedLessons: 1,
      ),
      LessonUnit(
        id: 4,
        title: 'الألوان',
        description: 'تعلم أسماء الألوان',
        color: DuolingoTheme.primaryPurple,
        icon: Icons.palette,
        progress: 0.0,
        isUnlocked: false,
        lessons: 4,
        completedLessons: 0,
      ),
    ]);
  }
  
  @override
  void dispose() {
    _mainAnimationController.dispose();
    _heartAnimationController.dispose();
    _audioService.stopBackgroundMusic();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Theme(
      data: DuolingoTheme.theme,
      child: Scaffold(
        backgroundColor: DuolingoTheme.backgroundPrimary,
        body: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: CustomScrollView(
                slivers: [
                  _buildAppBar(),
                  SliverToBoxAdapter(child: _buildHeader()),
                  SliverToBoxAdapter(child: _buildLanguageSelector()),
                  SliverToBoxAdapter(child: _buildLessonsTitle()),
                  _buildLessonsGrid(),
                  const SliverToBoxAdapter(child: SizedBox(height: 100)),
                ],
              ),
            ),
          ),
        ),
        floatingActionButton: _buildFloatingActionButton(),
      ),
    );
  }
  
  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 0,
      floating: false,
      pinned: true,
      backgroundColor: DuolingoTheme.primaryGreen,
      elevation: 0,
      leading: IconButton(
        onPressed: () {
          _audioService.playEffect(AudioEffect.buttonClick);
          Navigator.pop(context);
        },
        icon: const Icon(Icons.arrow_back, color: Colors.white),
      ),
      title: const Text(
        'تعلم اللغات',
        style: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontSize: 20,
        ),
      ),
      actions: [
        IconButton(
          onPressed: () => _audioService.playEffect(AudioEffect.buttonClick),
          icon: const Icon(Icons.settings, color: Colors.white),
        ),
      ],
    );
  }
  
  Widget _buildHeader() {
    return Container(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(20),
      decoration: DuolingoTheme.elevatedCardDecoration,
      child: Column(
        children: [
          _buildUserInfo(),
          const SizedBox(height: 20),
          _buildStatsRow(),
        ],
      ),
    );
  }
  
  Widget _buildUserInfo() {
    return Row(
      children: [
        // صورة المستخدم
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            gradient: DuolingoTheme.primaryGradient,
            borderRadius: BorderRadius.circular(30),
            border: Border.all(color: Colors.white, width: 3),
            boxShadow: DuolingoTheme.cardShadow,
          ),
          child: const Icon(Icons.person, color: Colors.white, size: 30),
        ),
        
        const SizedBox(width: 15),
        
        // معلومات المستخدم
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'مرحباً، أحمد!',
                style: DuolingoTheme.headingSmall,
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  const Icon(
                    Icons.local_fire_department,
                    color: DuolingoTheme.primaryOrange,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '$_currentStreak يوم متتالي',
                    style: DuolingoTheme.bodySmall,
                  ),
                ],
              ),
            ],
          ),
        ),
        
        // مستوى المستخدم
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: DuolingoTheme.primaryOrange,
            borderRadius: BorderRadius.circular(15),
          ),
          child: const Text(
            'المستوى 12',
            style: TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }
  
  Widget _buildStatsRow() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildStatItem(
          icon: Icons.flash_on,
          value: '$_currentXP',
          label: 'XP',
          color: DuolingoTheme.primaryOrange,
        ),
        _buildStatItem(
          icon: Icons.diamond,
          value: '$_gems',
          label: 'جواهر',
          color: DuolingoTheme.primaryBlue,
        ),
        AnimatedBuilder(
          animation: _heartBeatAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _heartBeatAnimation.value,
              child: _buildStatItem(
                icon: Icons.favorite,
                value: '$_hearts',
                label: 'قلوب',
                color: DuolingoTheme.primaryRed,
              ),
            );
          },
        ),
      ],
    );
  }
  
  Widget _buildStatItem({
    required IconData icon,
    required String value,
    required String label,
    required Color color,
  }) {
    return GestureDetector(
      onTap: () {
        _audioService.playEffect(AudioEffect.buttonClick);
        if (icon == Icons.favorite) {
          _heartAnimationController.forward().then((_) {
            _heartAnimationController.reverse();
          });
        }
      },
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(height: 8),
          Text(value, style: DuolingoTheme.headingSmall),
          Text(label, style: DuolingoTheme.bodySmall),
        ],
      ),
    );
  }
  
  Widget _buildLanguageSelector() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(16),
      decoration: DuolingoTheme.cardDecoration,
      child: Row(
        children: [
          Text(
            _currentFlag,
            style: const TextStyle(fontSize: 32),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'أتعلم $_currentLanguage',
                  style: DuolingoTheme.bodyLarge,
                ),
                const SizedBox(height: 4),
                LinearProgressIndicator(
                  value: 0.65,
                  backgroundColor: DuolingoTheme.borderLight,
                  valueColor: const AlwaysStoppedAnimation<Color>(
                    DuolingoTheme.primaryGreen,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => _audioService.playEffect(AudioEffect.buttonClick),
            icon: const Icon(Icons.keyboard_arrow_down),
          ),
        ],
      ),
    );
  }
  
  Widget _buildLessonsTitle() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          const Text(
            'الدروس',
            style: DuolingoTheme.headingMedium,
          ),
          const Spacer(),
          TextButton(
            onPressed: () => _audioService.playEffect(AudioEffect.buttonClick),
            child: const Text(
              'عرض الكل',
              style: TextStyle(color: DuolingoTheme.primaryBlue),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildLessonsGrid() {
    return SliverPadding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            final lesson = _lessons[index];
            return AnimatedContainer(
              duration: Duration(milliseconds: 300 + (index * 100)),
              margin: const EdgeInsets.only(bottom: 16),
              child: ProfessionalLessonCard(
                lesson: lesson,
                onTap: () => _openLesson(lesson),
              ),
            );
          },
          childCount: _lessons.length,
        ),
      ),
    );
  }
  
  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: () {
        _audioService.playEffect(AudioEffect.achievement);
        // فتح التحدي اليومي
      },
      backgroundColor: DuolingoTheme.primaryOrange,
      icon: const Icon(Icons.flash_on, color: Colors.white),
      label: const Text(
        'التحدي اليومي',
        style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
      ),
    );
  }
  
  void _openLesson(LessonUnit lesson) {
    if (!lesson.isUnlocked) {
      _audioService.playEffect(AudioEffect.incorrect);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب إكمال الدروس السابقة أولاً'),
          backgroundColor: DuolingoTheme.primaryRed,
        ),
      );
      return;
    }
    
    _audioService.playEffect(AudioEffect.buttonClick);
    // فتح شاشة الدرس
  }
}

/// نموذج وحدة الدرس
class LessonUnit {
  final int id;
  final String title;
  final String description;
  final Color color;
  final IconData icon;
  final double progress;
  final bool isUnlocked;
  final int lessons;
  final int completedLessons;
  
  LessonUnit({
    required this.id,
    required this.title,
    required this.description,
    required this.color,
    required this.icon,
    required this.progress,
    required this.isUnlocked,
    required this.lessons,
    required this.completedLessons,
  });
}

/// بطاقة الدرس الاحترافية
class ProfessionalLessonCard extends StatefulWidget {
  final LessonUnit lesson;
  final VoidCallback onTap;
  
  const ProfessionalLessonCard({
    super.key,
    required this.lesson,
    required this.onTap,
  });
  
  @override
  State<ProfessionalLessonCard> createState() => _ProfessionalLessonCardState();
}

class _ProfessionalLessonCardState extends State<ProfessionalLessonCard>
    with SingleTickerProviderStateMixin {
  
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: (_) => _animationController.forward(),
            onTapUp: (_) => _animationController.reverse(),
            onTapCancel: () => _animationController.reverse(),
            onTap: widget.onTap,
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: widget.lesson.isUnlocked 
                    ? DuolingoTheme.backgroundCard 
                    : DuolingoTheme.backgroundCard.withOpacity(0.5),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: widget.lesson.isUnlocked 
                      ? widget.lesson.color 
                      : DuolingoTheme.borderLight,
                  width: 2,
                ),
                boxShadow: widget.lesson.isUnlocked 
                    ? DuolingoTheme.cardShadow 
                    : [],
              ),
              child: Row(
                children: [
                  // أيقونة الدرس
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: widget.lesson.isUnlocked 
                          ? widget.lesson.color 
                          : DuolingoTheme.borderMedium,
                      borderRadius: BorderRadius.circular(30),
                    ),
                    child: Icon(
                      widget.lesson.isUnlocked 
                          ? widget.lesson.icon 
                          : Icons.lock,
                      color: Colors.white,
                      size: 30,
                    ),
                  ),
                  
                  const SizedBox(width: 16),
                  
                  // معلومات الدرس
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.lesson.title,
                          style: DuolingoTheme.headingSmall.copyWith(
                            color: widget.lesson.isUnlocked 
                                ? DuolingoTheme.textPrimary 
                                : DuolingoTheme.textLight,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          widget.lesson.description,
                          style: DuolingoTheme.bodySmall.copyWith(
                            color: widget.lesson.isUnlocked 
                                ? DuolingoTheme.textSecondary 
                                : DuolingoTheme.textLight,
                          ),
                        ),
                        const SizedBox(height: 8),
                        if (widget.lesson.isUnlocked) ...[
                          LinearProgressIndicator(
                            value: widget.lesson.progress,
                            backgroundColor: DuolingoTheme.borderLight,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              widget.lesson.color,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '${widget.lesson.completedLessons}/${widget.lesson.lessons} دروس',
                            style: DuolingoTheme.caption,
                          ),
                        ],
                      ],
                    ),
                  ),
                  
                  // زر الصوت
                  if (widget.lesson.isUnlocked)
                    ProfessionalSoundButton(
                      text: widget.lesson.title,
                      language: 'ar',
                      size: 40,
                      color: widget.lesson.color,
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
