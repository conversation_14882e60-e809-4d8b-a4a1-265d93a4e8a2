import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../config/app_theme.dart';

/// عنصر قائمة محسن مع تأثيرات بصرية ولمسية
class EnhancedListItem extends StatefulWidget {
  /// أيقونة العنصر
  final IconData? icon;

  /// صورة العنصر (بديل للأيقونة)
  final Widget? leading;

  /// عنوان العنصر
  final String title;

  /// وصف العنصر (اختياري)
  final String? subtitle;

  /// عنصر في نهاية العنصر (اختياري)
  final Widget? trailing;

  /// دالة يتم تنفيذها عند الضغط على العنصر
  final VoidCallback? onTap;

  /// دالة يتم تنفيذها عند الضغط المطول على العنصر
  final VoidCallback? onLongPress;

  /// لون خلفية العنصر
  final Color? backgroundColor;

  /// لون العنصر عند الضغط عليه
  final Color? splashColor;

  /// لون الأيقونة
  final Color? iconColor;

  /// لون العنوان
  final Color? titleColor;

  /// لون الوصف
  final Color? subtitleColor;

  /// حجم الأيقونة
  final double? iconSize;

  /// حجم خط العنوان
  final double? titleSize;

  /// حجم خط الوصف
  final double? subtitleSize;

  /// ارتفاع العنصر
  final double? height;

  /// تباعد داخلي
  final EdgeInsetsGeometry? padding;

  /// نصف قطر الحواف
  final double? borderRadius;

  /// ما إذا كان العنصر معطلاً
  final bool isDisabled;

  /// ما إذا كان العنصر محدداً
  final bool isSelected;

  /// منشئ عنصر القائمة المحسن
  const EnhancedListItem({
    super.key,
    this.icon,
    this.leading,
    required this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.onLongPress,
    this.backgroundColor,
    this.splashColor,
    this.iconColor,
    this.titleColor,
    this.subtitleColor,
    this.iconSize,
    this.titleSize,
    this.subtitleSize,
    this.height,
    this.padding,
    this.borderRadius,
    this.isDisabled = false,
    this.isSelected = false,
  });

  @override
  State<EnhancedListItem> createState() => _EnhancedListItemState();
}

class _EnhancedListItemState extends State<EnhancedListItem>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 100),
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (!widget.isDisabled &&
        (widget.onTap != null || widget.onLongPress != null)) {
      setState(() {
        _isPressed = true;
      });
      _controller.forward();
      HapticFeedback.lightImpact();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    if (!widget.isDisabled && _isPressed) {
      setState(() {
        _isPressed = false;
      });
      _controller.reverse();
    }
  }

  void _handleTapCancel() {
    if (!widget.isDisabled && _isPressed) {
      setState(() {
        _isPressed = false;
      });
      _controller.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveIconColor = widget.iconColor ?? AppTheme.primaryColor;
    final effectiveTitleColor =
        widget.titleColor ?? theme.textTheme.titleLarge?.color;
    final effectiveSubtitleColor =
        widget.subtitleColor ?? theme.textTheme.bodyMedium?.color;

    return ScaleTransition(
      scale: _scaleAnimation,
      child: GestureDetector(
        onTapDown: _handleTapDown,
        onTapUp: _handleTapUp,
        onTapCancel: _handleTapCancel,
        onTap: widget.isDisabled ? null : widget.onTap,
        onLongPress: widget.isDisabled ? null : widget.onLongPress,
        child: Material(
          color: Colors.transparent,
          child: Container(
            height: widget.height,
            padding:
                widget.padding ??
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color:
                  widget.isSelected
                      ? (widget.backgroundColor ??
                          theme.colorScheme.primary.withAlpha(26))
                      : (widget.backgroundColor ?? theme.cardColor),
              borderRadius: BorderRadius.circular(widget.borderRadius ?? 12),
            ),
            child: InkWell(
              onTap: widget.isDisabled ? null : widget.onTap,
              onLongPress: widget.isDisabled ? null : widget.onLongPress,
              splashColor:
                  widget.splashColor ?? theme.colorScheme.primary.withAlpha(51),
              highlightColor: theme.colorScheme.primary.withAlpha(26),
              borderRadius: BorderRadius.circular(widget.borderRadius ?? 12),
              child: Row(
                children: [
                  // أيقونة أو صورة العنصر
                  if (widget.leading != null) ...[
                    widget.leading!,
                    const SizedBox(width: 16),
                  ] else if (widget.icon != null) ...[
                    Icon(
                      widget.icon,
                      color:
                          widget.isDisabled
                              ? effectiveIconColor.withAlpha(128)
                              : effectiveIconColor,
                      size: widget.iconSize ?? 24,
                    ),
                    const SizedBox(width: 16),
                  ],

                  // محتوى العنصر (العنوان والوصف)
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // العنوان
                        Text(
                          widget.title,
                          style: TextStyle(
                            fontSize: widget.titleSize ?? 16,
                            fontWeight: FontWeight.w500,
                            color:
                                widget.isDisabled
                                    ? effectiveTitleColor?.withAlpha(128)
                                    : effectiveTitleColor,
                          ),
                        ),

                        // الوصف (إذا كان موجوداً)
                        if (widget.subtitle != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            widget.subtitle!,
                            style: TextStyle(
                              fontSize: widget.subtitleSize ?? 14,
                              color:
                                  widget.isDisabled
                                      ? effectiveSubtitleColor?.withAlpha(128)
                                      : effectiveSubtitleColor,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),

                  // عنصر في نهاية العنصر (إذا كان موجوداً)
                  if (widget.trailing != null) widget.trailing!,
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
