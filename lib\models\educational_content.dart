import 'package:flutter/material.dart';

/// نموذج الدرس التعليمي
class EducationalLesson {
  final String id;
  final String title;
  final String titleAr;
  final String description;
  final String descriptionAr;
  final LessonCategory category;
  final int level;
  final int order;
  final String iconPath;
  final Color primaryColor;
  final List<LessonActivity> activities;
  final int requiredScore;
  final bool isUnlocked;
  final int maxScore;

  const EducationalLesson({
    required this.id,
    required this.title,
    required this.titleAr,
    required this.description,
    required this.descriptionAr,
    required this.category,
    required this.level,
    required this.order,
    required this.iconPath,
    required this.primaryColor,
    required this.activities,
    this.requiredScore = 0,
    this.isUnlocked = false,
    this.maxScore = 100,
  });
}

/// فئات الدروس التعليمية
enum LessonCategory {
  letters,      // الحروف
  numbers,      // الأرقام
  colors,       // الألوان
  animals,      // الحيوانات
  family,       // العائلة
  body,         // أجزاء الجسم
  food,         // الطعام
  transport,    // المواصلات
  shapes,       // الأشكال
  dialogue,     // الحوارات
}

/// أسماء فئات الدروس
class LessonCategoryNames {
  static const Map<LessonCategory, String> arabic = {
    LessonCategory.letters: 'الحروف',
    LessonCategory.numbers: 'الأرقام',
    LessonCategory.colors: 'الألوان',
    LessonCategory.animals: 'الحيوانات',
    LessonCategory.family: 'العائلة',
    LessonCategory.body: 'أجزاء الجسم',
    LessonCategory.food: 'الطعام والشراب',
    LessonCategory.transport: 'المواصلات',
    LessonCategory.shapes: 'الأشكال',
    LessonCategory.dialogue: 'الحوارات',
  };

  static const Map<LessonCategory, String> english = {
    LessonCategory.letters: 'Letters',
    LessonCategory.numbers: 'Numbers',
    LessonCategory.colors: 'Colors',
    LessonCategory.animals: 'Animals',
    LessonCategory.family: 'Family',
    LessonCategory.body: 'Body Parts',
    LessonCategory.food: 'Food & Drinks',
    LessonCategory.transport: 'Transportation',
    LessonCategory.shapes: 'Shapes',
    LessonCategory.dialogue: 'Dialogues',
  };
}

/// نشاط الدرس
class LessonActivity {
  final String id;
  final String title;
  final String titleAr;
  final ActivityType type;
  final List<ActivityItem> items;
  final int maxScore;
  final String instructions;
  final String instructionsAr;

  const LessonActivity({
    required this.id,
    required this.title,
    required this.titleAr,
    required this.type,
    required this.items,
    this.maxScore = 20,
    required this.instructions,
    required this.instructionsAr,
  });
}

/// أنواع الأنشطة التعليمية
enum ActivityType {
  multipleChoice,    // اختيار متعدد
  matching,          // مطابقة
  listening,         // استماع
  speaking,          // نطق
  tracing,           // تتبع الحروف
  memory,            // ذاكرة
  dialogue,          // حوار
  story,             // قصة
  puzzle,            // أحجية
  coloring,          // تلوين
}

/// عنصر النشاط
class ActivityItem {
  final String id;
  final String question;
  final String questionAr;
  final String correctAnswer;
  final List<String> options;
  final String? imagePath;
  final String? audioPath;
  final String? videoPath;
  final Map<String, dynamic>? metadata;

  const ActivityItem({
    required this.id,
    required this.question,
    required this.questionAr,
    required this.correctAnswer,
    this.options = const [],
    this.imagePath,
    this.audioPath,
    this.videoPath,
    this.metadata,
  });
}

/// حوار تعليمي
class EducationalDialogue {
  final String id;
  final String title;
  final String titleAr;
  final String scenario;
  final String scenarioAr;
  final List<DialogueLine> lines;
  final String backgroundImage;
  final List<DialogueCharacter> characters;

  const EducationalDialogue({
    required this.id,
    required this.title,
    required this.titleAr,
    required this.scenario,
    required this.scenarioAr,
    required this.lines,
    required this.backgroundImage,
    required this.characters,
  });
}

/// سطر الحوار
class DialogueLine {
  final String id;
  final String characterId;
  final String text;
  final String textAr;
  final String audioPath;
  final String audioPathAr;
  final Duration duration;
  final String? animation;

  const DialogueLine({
    required this.id,
    required this.characterId,
    required this.text,
    required this.textAr,
    required this.audioPath,
    required this.audioPathAr,
    required this.duration,
    this.animation,
  });
}

/// شخصية الحوار
class DialogueCharacter {
  final String id;
  final String name;
  final String nameAr;
  final String imagePath;
  final String description;
  final String descriptionAr;
  final Color primaryColor;

  const DialogueCharacter({
    required this.id,
    required this.name,
    required this.nameAr,
    required this.imagePath,
    required this.description,
    required this.descriptionAr,
    required this.primaryColor,
  });
}

/// تقدم الطالب
class StudentProgress {
  final String studentId;
  final Map<String, LessonProgress> lessonProgress;
  final int totalScore;
  final int totalLessonsCompleted;
  final List<Achievement> achievements;
  final DateTime lastActivity;

  const StudentProgress({
    required this.studentId,
    required this.lessonProgress,
    this.totalScore = 0,
    this.totalLessonsCompleted = 0,
    this.achievements = const [],
    required this.lastActivity,
  });
}

/// تقدم الدرس
class LessonProgress {
  final String lessonId;
  final int score;
  final int maxScore;
  final bool isCompleted;
  final DateTime? completedAt;
  final Map<String, ActivityProgress> activityProgress;

  const LessonProgress({
    required this.lessonId,
    this.score = 0,
    this.maxScore = 100,
    this.isCompleted = false,
    this.completedAt,
    this.activityProgress = const {},
  });

  double get progressPercentage => maxScore > 0 ? (score / maxScore) * 100 : 0;
}

/// تقدم النشاط
class ActivityProgress {
  final String activityId;
  final int score;
  final int maxScore;
  final bool isCompleted;
  final int attempts;
  final DateTime? completedAt;

  const ActivityProgress({
    required this.activityId,
    this.score = 0,
    this.maxScore = 20,
    this.isCompleted = false,
    this.attempts = 0,
    this.completedAt,
  });
}

/// الإنجازات
class Achievement {
  final String id;
  final String title;
  final String titleAr;
  final String description;
  final String descriptionAr;
  final String iconPath;
  final Color color;
  final DateTime earnedAt;
  final AchievementType type;

  const Achievement({
    required this.id,
    required this.title,
    required this.titleAr,
    required this.description,
    required this.descriptionAr,
    required this.iconPath,
    required this.color,
    required this.earnedAt,
    required this.type,
  });
}

/// أنواع الإنجازات
enum AchievementType {
  firstLesson,      // أول درس
  perfectScore,     // نتيجة مثالية
  streakDays,       // أيام متتالية
  categoryMaster,   // إتقان فئة
  speedLearner,     // متعلم سريع
  persistent,       // مثابر
}

/// عنصر تعليمي أساسي
class EducationalItem {
  final String id;
  final String nameAr;
  final String nameEn;
  final String imagePath;
  final String audioPathAr;
  final String audioPathEn;
  final String? description;
  final String? descriptionAr;
  final Color? primaryColor;

  const EducationalItem({
    required this.id,
    required this.nameAr,
    required this.nameEn,
    required this.imagePath,
    required this.audioPathAr,
    required this.audioPathEn,
    this.description,
    this.descriptionAr,
    this.primaryColor,
  });
}
