import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../config/constants.dart';

/// خدمة إدارة الإعلانات
class AdsService {
  final FirebaseRemoteConfig _remoteConfig;
  
  // متغيرات للإعلانات
  BannerAd? _bannerAd;
  InterstitialAd? _interstitialAd;
  RewardedAd? _rewardedAd;
  
  // عدد مرات استخدام التطبيق قبل عرض الإعلان
  int _interstitialAdCounter = 0;
  
  AdsService(this._remoteConfig) {
    _init();
  }
  
  /// تهيئة خدمة الإعلانات
  Future<void> _init() async {
    await MobileAds.instance.initialize();
    _loadInterstitialAd();
    _loadRewardedAd();
  }
  
  /// التحقق مما إذا كان يجب عرض الإعلانات
  Future<bool> shouldShowAds() async {
    try {
      // التحقق من حالة الاشتراك
      final prefs = await SharedPreferences.getInstance();
      final isAdsRemoved = prefs.getBool('is_ads_removed') ?? false;
      
      if (isAdsRemoved) {
        return false;
      }
      
      // التحقق من إعدادات Firebase Remote Config
      final showAds = _remoteConfig.getBool(AppConstants.keyShowAds);
      
      return showAds;
    } catch (e) {
      debugPrint('Error checking ads status: $e');
      return true; // عرض الإعلانات افتراضيًا في حالة حدوث خطأ
    }
  }
  
  /// الحصول على تردد عرض الإعلانات من Firebase Remote Config
  int getAdFrequency() {
    return _remoteConfig.getInt(AppConstants.keyAdFrequency);
  }
  
  /// تحميل إعلان البانر
  Future<BannerAd?> loadBannerAd() async {
    if (!await shouldShowAds()) {
      return null;
    }
    
    _bannerAd = BannerAd(
      adUnitId: AppConstants.adUnitIdBanner,
      size: AdSize.banner,
      request: const AdRequest(),
      listener: BannerAdListener(
        onAdLoaded: (ad) {
          debugPrint('Banner ad loaded');
        },
        onAdFailedToLoad: (ad, error) {
          debugPrint('Banner ad failed to load: $error');
          ad.dispose();
          _bannerAd = null;
        },
      ),
    );
    
    await _bannerAd?.load();
    return _bannerAd;
  }
  
  /// تحميل إعلان بيني
  void _loadInterstitialAd() async {
    if (!await shouldShowAds()) {
      return;
    }
    
    InterstitialAd.load(
      adUnitId: AppConstants.adUnitIdInterstitial,
      request: const AdRequest(),
      adLoadCallback: InterstitialAdLoadCallback(
        onAdLoaded: (ad) {
          _interstitialAd = ad;
          debugPrint('Interstitial ad loaded');
        },
        onAdFailedToLoad: (error) {
          debugPrint('Interstitial ad failed to load: $error');
          _interstitialAd = null;
        },
      ),
    );
  }
  
  /// تحميل إعلان المكافآت
  void _loadRewardedAd() async {
    if (!await shouldShowAds()) {
      return;
    }
    
    RewardedAd.load(
      adUnitId: AppConstants.adUnitIdRewarded,
      request: const AdRequest(),
      rewardedAdLoadCallback: RewardedAdLoadCallback(
        onAdLoaded: (ad) {
          _rewardedAd = ad;
          debugPrint('Rewarded ad loaded');
        },
        onAdFailedToLoad: (error) {
          debugPrint('Rewarded ad failed to load: $error');
          _rewardedAd = null;
        },
      ),
    );
  }
  
  /// عرض إعلان بيني
  Future<bool> showInterstitialAd() async {
    if (!await shouldShowAds()) {
      return false;
    }
    
    // زيادة العداد
    _interstitialAdCounter++;
    
    // التحقق مما إذا كان يجب عرض الإعلان بناءً على التردد
    final adFrequency = getAdFrequency();
    if (_interstitialAdCounter < adFrequency) {
      return false;
    }
    
    // إعادة تعيين العداد
    _interstitialAdCounter = 0;
    
    if (_interstitialAd == null) {
      _loadInterstitialAd();
      return false;
    }
    
    bool adShown = false;
    
    _interstitialAd!.fullScreenContentCallback = FullScreenContentCallback(
      onAdDismissedFullScreenContent: (ad) {
        ad.dispose();
        _loadInterstitialAd();
      },
      onAdFailedToShowFullScreenContent: (ad, error) {
        debugPrint('Failed to show interstitial ad: $error');
        ad.dispose();
        _loadInterstitialAd();
      },
    );
    
    await _interstitialAd!.show();
    adShown = true;
    _interstitialAd = null;
    
    return adShown;
  }
  
  /// عرض إعلان المكافآت
  Future<bool> showRewardedAd({
    required Function onRewarded,
  }) async {
    if (!await shouldShowAds()) {
      // منح المكافأة مباشرة إذا كان المستخدم قد أزال الإعلانات
      onRewarded();
      return true;
    }
    
    if (_rewardedAd == null) {
      _loadRewardedAd();
      return false;
    }
    
    bool adShown = false;
    
    _rewardedAd!.fullScreenContentCallback = FullScreenContentCallback(
      onAdDismissedFullScreenContent: (ad) {
        ad.dispose();
        _loadRewardedAd();
      },
      onAdFailedToShowFullScreenContent: (ad, error) {
        debugPrint('Failed to show rewarded ad: $error');
        ad.dispose();
        _loadRewardedAd();
      },
    );
    
    await _rewardedAd!.show(
      onUserEarnedReward: (_, reward) {
        debugPrint('User earned reward: ${reward.amount}');
        onRewarded();
      },
    );
    
    adShown = true;
    _rewardedAd = null;
    
    return adShown;
  }
  
  /// التخلص من الإعلانات
  void dispose() {
    _bannerAd?.dispose();
    _interstitialAd?.dispose();
    _rewardedAd?.dispose();
  }
}
