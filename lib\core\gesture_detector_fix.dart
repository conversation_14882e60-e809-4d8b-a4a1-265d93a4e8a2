import 'package:flutter/material.dart';

/// زر مخصص يستخدم InkWell بدلاً من GestureDetector
/// لتحسين استجابة الضغطات
class CustomButton extends StatelessWidget {
  /// نص الزر
  final String text;

  /// دالة يتم تنفيذها عند الضغط على الزر
  final VoidCallback? onPressed;

  /// لون الزر
  final Color? color;

  /// لون النص
  final Color? textColor;

  /// عرض الزر
  final double? width;

  /// ارتفاع الزر
  final double? height;

  /// حجم الخط
  final double? fontSize;

  /// أيقونة الزر (اختياري)
  final IconData? icon;

  /// حجم الأيقونة
  final double? iconSize;

  /// نصف قطر الحواف
  final double? borderRadius;

  /// ما إذا كان الزر معطلاً
  final bool isDisabled;

  /// ما إذا كان الزر في حالة تحميل
  final bool isLoading;

  /// منشئ الزر المخصص
  const CustomButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.color,
    this.textColor,
    this.width,
    this.height = 50.0,
    this.fontSize = 16.0,
    this.icon,
    this.iconSize = 20.0,
    this.borderRadius = 12.0,
    this.isDisabled = false,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveColor = color ?? Theme.of(context).primaryColor;
    final effectiveTextColor = textColor ?? Colors.white;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: isDisabled || isLoading ? null : onPressed,
        borderRadius: BorderRadius.circular(borderRadius!),
        child: Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: isDisabled ? Colors.grey.shade300 : effectiveColor,
            borderRadius: BorderRadius.circular(borderRadius!),
          ),
          child: Center(
            child: isLoading
                ? SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      color: effectiveTextColor,
                      strokeWidth: 2,
                    ),
                  )
                : Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (icon != null) ...[
                        Icon(
                          icon,
                          color: effectiveTextColor,
                          size: iconSize,
                        ),
                        const SizedBox(width: 8),
                      ],
                      Text(
                        text,
                        style: TextStyle(
                          color: effectiveTextColor,
                          fontSize: fontSize,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
          ),
        ),
      ),
    );
  }
}

/// زر مخصص كبير
class LargeCustomButton extends CustomButton {
  /// منشئ الزر المخصص الكبير
  const LargeCustomButton({
    super.key,
    required super.text,
    required super.onPressed,
    super.color,
    super.textColor,
    super.width,
    super.height = 64.0,
    super.fontSize = 18.0,
    super.icon,
    super.iconSize = 28.0,
    super.borderRadius = 20.0,
    super.isDisabled = false,
    super.isLoading = false,
  });
}

/// زر مخصص صغير
class SmallCustomButton extends CustomButton {
  /// منشئ الزر المخصص الصغير
  const SmallCustomButton({
    super.key,
    required super.text,
    required super.onPressed,
    super.color,
    super.textColor,
    super.width,
    super.height = 40.0,
    super.fontSize = 14.0,
    super.icon,
    super.iconSize = 18.0,
    super.borderRadius = 12.0,
    super.isDisabled = false,
    super.isLoading = false,
  });
}
