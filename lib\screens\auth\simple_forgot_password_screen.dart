import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../config/app_theme.dart';
import '../../utils/app_helpers.dart';

/// شاشة استعادة كلمة المرور مبسطة وموثوقة
class SimpleForgotPasswordScreen extends StatefulWidget {
  const SimpleForgotPasswordScreen({super.key});

  @override
  State<SimpleForgotPasswordScreen> createState() =>
      _SimpleForgotPasswordScreenState();
}

class _SimpleForgotPasswordScreenState
    extends State<SimpleForgotPasswordScreen> {
  // متحكم حقل البريد الإلكتروني
  final _emailController = TextEditingController();

  // مفتاح النموذج للتحقق من صحة الإدخال
  final _formKey = GlobalKey<FormState>();

  // حالة تحميل إرسال البريد
  bool _isLoading = false;

  // حالة إرسال البريد
  bool _emailSent = false;

  // مثيل Firebase Auth
  final _auth = FirebaseAuth.instance;

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppTheme.onBackground),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: _emailSent ? _buildSuccessView() : _buildFormView(),
          ),
        ),
      ),
    );
  }

  /// بناء نموذج إعادة تعيين كلمة المرور
  Widget _buildFormView() {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // أيقونة
          _buildIcon(),
          const SizedBox(height: 32),

          // العنوان والوصف
          _buildTitle(),
          const SizedBox(height: 32),

          // حقل البريد الإلكتروني
          _buildEmailField(),
          const SizedBox(height: 32),

          // زر إرسال رابط إعادة التعيين
          _buildResetButton(),
          const SizedBox(height: 16),

          // رابط العودة لتسجيل الدخول
          _buildBackToLoginLink(),
        ],
      ),
    );
  }

  /// بناء عرض النجاح
  Widget _buildSuccessView() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // أيقونة النجاح
        Container(
          height: 120,
          width: 120,
          decoration: BoxDecoration(
            color: Colors.green.withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: const Icon(Icons.check_circle, size: 60, color: Colors.green),
        ),
        const SizedBox(height: 32),

        // رسالة النجاح
        Text(
          'تم إرسال الرابط!',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: AppTheme.onBackground,
          ),
        ),
        const SizedBox(height: 16),

        Text(
          'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني. يرجى التحقق من صندوق الوارد.',
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 16, color: AppTheme.onSurfaceVariant),
        ),
        const SizedBox(height: 32),

        // زر العودة لتسجيل الدخول
        ElevatedButton(
          onPressed: () => Navigator.pop(context),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          child: const Text(
            'العودة لتسجيل الدخول',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
          ),
        ),
      ],
    );
  }

  /// بناء الأيقونة
  Widget _buildIcon() {
    return Container(
      height: 120,
      width: 120,
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withValues(alpha: 0.1),
        shape: BoxShape.circle,
      ),
      child: Icon(Icons.lock_reset, size: 60, color: AppTheme.primaryColor),
    );
  }

  /// بناء العنوان والوصف
  Widget _buildTitle() {
    return Column(
      children: [
        Text(
          'نسيت كلمة المرور؟',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: AppTheme.onBackground,
          ),
        ),
        const SizedBox(height: 16),
        Text(
          'أدخل بريدك الإلكتروني وسنرسل لك رابط إعادة تعيين كلمة المرور',
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 16, color: AppTheme.onSurfaceVariant),
        ),
      ],
    );
  }

  /// بناء حقل البريد الإلكتروني
  Widget _buildEmailField() {
    return TextFormField(
      controller: _emailController,
      keyboardType: TextInputType.emailAddress,
      textDirection: TextDirection.ltr,
      decoration: InputDecoration(
        labelText: 'البريد الإلكتروني',
        hintText: '<EMAIL>',
        prefixIcon: const Icon(Icons.email_outlined),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى إدخال البريد الإلكتروني';
        }
        if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
          return 'يرجى إدخال بريد إلكتروني صالح';
        }
        return null;
      },
    );
  }

  /// بناء زر إرسال رابط إعادة التعيين
  Widget _buildResetButton() {
    return ElevatedButton(
      onPressed: _isLoading ? null : _resetPassword,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        elevation: 2,
      ),
      child:
          _isLoading
              ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
              : const Text(
                'إرسال رابط إعادة التعيين',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
    );
  }

  /// بناء رابط العودة لتسجيل الدخول
  Widget _buildBackToLoginLink() {
    return TextButton(
      onPressed: () => Navigator.pop(context),
      child: const Text('العودة لتسجيل الدخول'),
    );
  }

  /// إرسال رابط إعادة تعيين كلمة المرور
  Future<void> _resetPassword() async {
    // التحقق من صحة النموذج
    if (_formKey.currentState == null || !_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // إرسال رابط إعادة تعيين كلمة المرور
      await _auth.sendPasswordResetEmail(email: _emailController.text.trim());

      // تحديث الحالة لإظهار رسالة النجاح
      if (mounted) {
        setState(() {
          _emailSent = true;
          _isLoading = false;
        });
      }
    } on FirebaseAuthException catch (e) {
      // معالجة أخطاء إرسال البريد
      String errorMessage;

      switch (e.code) {
        case 'user-not-found':
          errorMessage = 'لم يتم العثور على مستخدم بهذا البريد الإلكتروني';
          break;
        case 'invalid-email':
          errorMessage = 'البريد الإلكتروني غير صالح';
          break;
        default:
          errorMessage = 'حدث خطأ أثناء إرسال رابط إعادة التعيين: ${e.message}';
      }

      // عرض رسالة الخطأ
      if (mounted) {
        AppHelpers.showSnackBar(context, errorMessage, isError: true);
      }
    } catch (e) {
      // معالجة الأخطاء الأخرى
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء إرسال رابط إعادة التعيين: $e',
          isError: true,
        );
      }
    } finally {
      if (mounted && !_emailSent) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
