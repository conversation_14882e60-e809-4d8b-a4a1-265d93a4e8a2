import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'config/app_theme.dart' as app_theme;
import 'providers/app_state.dart';
import 'firebase_options.dart';
import 'screens/splash/splash_screen.dart';
import 'core/touch_handler_wrapper.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة Firebase
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // تعيين اتجاه التطبيق للعربية (من اليمين إلى اليسار)
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // تعيين إعدادات اللمس لتحسين استجابة العناصر التفاعلية
  await SystemChannels.platform.invokeMethod<void>(
    'SystemChrome.setSystemUIChangeListener',
    <String, dynamic>{'enabled': true},
  );

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => AppState(),
      child: Consumer<AppState>(
        builder: (context, appState, child) {
          return MaterialApp(
            title: 'المترجم الذكي الجديد AI',
            debugShowCheckedModeBanner: false,
            theme: app_theme.AppTheme.lightTheme,
            darkTheme:
                appState.isAmoledMode
                    ? app_theme.AppTheme.amoledDarkTheme
                    : app_theme.AppTheme.darkTheme,
            themeMode: appState.isDarkMode ? ThemeMode.dark : ThemeMode.light,

            // إضافة دعم اللغات
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [
              Locale('ar'), // العربية
              Locale('en'), // الإنجليزية
            ],
            locale: Locale(appState.selectedLanguage),

            // تطبيق مغلف التعامل مع اللمسات على الشاشة الرئيسية
            home: const TouchHandlerWrapper(child: SplashScreen()),

            // تعديل بناء الصفحات لتطبيق مغلف التعامل مع اللمسات
            builder: (context, child) {
              if (child == null) return const SizedBox.shrink();

              // تطبيق مغلف التعامل مع اللمسات على جميع الصفحات
              return TouchHandlerWrapper(child: child);
            },
          );
        },
      ),
    );
  }
}
