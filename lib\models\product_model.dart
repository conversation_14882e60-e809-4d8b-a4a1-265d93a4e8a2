import 'package:in_app_purchase/in_app_purchase.dart';

/// نموذج بيانات المنتج (الاشتراك)
class ProductModel {
  /// معرف المنتج
  final String id;
  
  /// عنوان المنتج
  final String title;
  
  /// وصف المنتج
  final String description;
  
  /// السعر كنص (مثل $9.99)
  final String price;
  
  /// القيمة الرقمية للسعر
  final double priceValue;
  
  /// عملة السعر
  final String currency;
  
  /// نوع المنتج (اشتراك أو منتج مستهلك)
  final ProductType type;
  
  /// فترة الاشتراك بالأيام
  final int subscriptionPeriodDays;
  
  /// قائمة الميزات المتاحة في هذا المنتج
  final List<String> features;

  ProductModel({
    required this.id,
    required this.title,
    required this.description,
    required this.price,
    required this.priceValue,
    required this.currency,
    required this.type,
    required this.subscriptionPeriodDays,
    required this.features,
  });

  /// إنشاء نموذج من تفاصيل المنتج
  factory ProductModel.fromProductDetails(ProductDetails details) {
    // تحديد نوع المنتج
    final type = details.id.contains('subscription')
        ? ProductType.subscription
        : ProductType.consumable;
    
    // تحديد فترة الاشتراك
    int periodDays = 30; // افتراضي شهري
    if (details.id.contains('yearly')) {
      periodDays = 365;
    } else if (details.id.contains('weekly')) {
      periodDays = 7;
    } else if (details.id.contains('monthly')) {
      periodDays = 30;
    }
    
    // استخراج قيمة السعر
    final priceValue = _extractPriceValue(details.price);
    
    // تحديد الميزات حسب نوع المنتج
    final features = _getFeaturesForProduct(details.id);
    
    return ProductModel(
      id: details.id,
      title: details.title,
      description: details.description,
      price: details.price,
      priceValue: priceValue,
      currency: details.currencyCode,
      type: type,
      subscriptionPeriodDays: periodDays,
      features: features,
    );
  }

  /// استخراج قيمة السعر من النص
  static double _extractPriceValue(String priceString) {
    try {
      // إزالة رمز العملة والأحرف غير الرقمية
      final numericString = priceString.replaceAll(RegExp(r'[^\d.,]'), '');
      // استبدال الفاصلة بنقطة إذا كانت موجودة
      final normalizedString = numericString.replaceAll(',', '.');
      // تحويل النص إلى رقم
      return double.parse(normalizedString);
    } catch (e) {
      return 0.0;
    }
  }

  /// الحصول على قائمة الميزات حسب نوع المنتج
  static List<String> _getFeaturesForProduct(String productId) {
    if (productId.contains('monthly')) {
      return [
        'ترجمة صوتية غير محدودة',
        'دعم أكثر من 35 لغة',
        'تمييز المتحدثين في المحادثات الجماعية',
        'ترجمة فورية مستمرة',
        'بدون إعلانات',
      ];
    } else if (productId.contains('yearly')) {
      return [
        'ترجمة صوتية غير محدودة',
        'دعم أكثر من 35 لغة',
        'تمييز المتحدثين في المحادثات الجماعية',
        'ترجمة فورية مستمرة',
        'بدون إعلانات',
        'توفير 20% مقارنة بالاشتراك الشهري',
      ];
    } else if (productId.contains('weekly')) {
      return [
        'ترجمة صوتية غير محدودة',
        'دعم أكثر من 35 لغة',
        'تمييز المتحدثين في المحادثات الجماعية',
        'ترجمة فورية مستمرة',
        'بدون إعلانات',
      ];
    }
    
    // منتجات أخرى
    return [
      'ميزات إضافية',
      'بدون إعلانات',
    ];
  }

  /// الحصول على اسم المنتج بالعربية
  String getLocalizedName() {
    if (id.contains('monthly')) {
      return 'الاشتراك الشهري';
    } else if (id.contains('yearly')) {
      return 'الاشتراك السنوي';
    } else if (id.contains('weekly')) {
      return 'الاشتراك الأسبوعي';
    }
    return title;
  }

  /// الحصول على وصف المنتج بالعربية
  String getLocalizedDescription() {
    if (id.contains('monthly')) {
      return 'اشتراك شهري في المترجم الذكي';
    } else if (id.contains('yearly')) {
      return 'اشتراك سنوي في المترجم الذكي مع توفير 20%';
    } else if (id.contains('weekly')) {
      return 'اشتراك أسبوعي في المترجم الذكي';
    }
    return description;
  }
}

/// أنواع المنتجات
enum ProductType {
  subscription,  // اشتراك
  consumable,    // منتج مستهلك
}
