{"conditions": [{"name": "premium_users", "expression": "user.inRandomPercentile(0, 10)", "tagColor": "BLUE"}], "parameters": {"openai_api_key": {"defaultValue": {"value": ""}, "description": "مفتاح OpenAI API للترجمة"}, "gemini_api_key": {"defaultValue": {"value": "AIzaSyDzPBZY2TeUIm6L3WVRyF_3DkeTJpx-988"}, "description": "مفتاح Gemini API للترجمة والدردشة"}, "gemini_api_key_paid": {"defaultValue": {"value": "AIzaSyDyTjcLnqnqAZ71WzXKwWm-dqQTidhGUoQ"}, "description": "مفتاح Gemini API المدفوع"}, "gemini_end_date": {"defaultValue": {"value": "2024-12-31"}, "description": "تاريخ انتهاء المفتاح المدفوع"}, "openrouter_api_key": {"defaultValue": {"value": ""}, "description": "مفتاح OpenRouter API"}, "google_translate_api_key": {"defaultValue": {"value": ""}, "description": "مفتاح Google Translate API"}, "active_api_service": {"defaultValue": {"value": "gemini"}, "description": "الخدمة النشطة للترجمة (openai, gemini, openrouter)"}, "daily_limit_openai": {"defaultValue": {"value": "100"}, "description": "الحد اليومي لاستخدام OpenAI"}, "daily_limit_gemini": {"defaultValue": {"value": "1000"}, "description": "الحد اليومي لاستخدام Gemini"}, "daily_limit_openrouter": {"defaultValue": {"value": "50"}, "description": "الحد اليومي لاستخدام OpenRouter"}, "enable_premium_features": {"defaultValue": {"value": "false"}, "conditionalValues": {"premium_users": {"value": "true"}}, "description": "تفعيل الميزات المتقدمة للمستخدمين المميزين"}, "max_translation_length": {"defaultValue": {"value": "5000"}, "description": "ال<PERSON><PERSON> الأقصى لطول النص للترجمة"}, "supported_languages": {"defaultValue": {"value": "ar,en,fr,es,de,it,ru,zh,ja,ko"}, "description": "اللغات المدعومة مفصولة بفواصل"}, "app_version": {"defaultValue": {"value": "1.0.0"}, "description": "إصدار التطبيق الحالي"}, "maintenance_mode": {"defaultValue": {"value": "false"}, "description": "وضع الصيانة"}, "maintenance_message": {"defaultValue": {"value": "التطبيق تحت الصيانة، يرجى المحاولة لاحقاً"}, "description": "رسالة الصيانة"}}, "parameterGroups": {"API Keys": {"description": "مفاتيح APIs للخدمات الخارجية", "parameters": {"openai_api_key": {}, "gemini_api_key": {}, "gemini_api_key_paid": {}, "openrouter_api_key": {}, "google_translate_api_key": {}}}, "App Configuration": {"description": "إعدادات التطبيق العامة", "parameters": {"active_api_service": {}, "max_translation_length": {}, "supported_languages": {}, "app_version": {}}}, "Usage Limits": {"description": "حدود الاستخدام اليومية", "parameters": {"daily_limit_openai": {}, "daily_limit_gemini": {}, "daily_limit_openrouter": {}}}, "Premium Features": {"description": "الميزات المتقدمة", "parameters": {"enable_premium_features": {}}}, "Maintenance": {"description": "إعدادات الصيانة", "parameters": {"maintenance_mode": {}, "maintenance_message": {}}}}, "version": {"versionNumber": "1", "updateTime": "2024-01-01T00:00:00Z", "updateUser": {"email": "<EMAIL>"}, "updateOrigin": "CONSOLE", "updateType": "INCREMENTAL_UPDATE"}}