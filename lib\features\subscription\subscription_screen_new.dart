import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../config/app_theme.dart';
import '../../widgets/three_d_button.dart';

/// شاشة الاشتراكات
class SubscriptionScreen extends StatefulWidget {
  const SubscriptionScreen({super.key});

  @override
  State<SubscriptionScreen> createState() => _SubscriptionScreenState();
}

class _SubscriptionScreenState extends State<SubscriptionScreen> {
  int _selectedPlanIndex = 1; // الخطة الشهرية محددة افتراضيًا
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الاشتراكات'),
        backgroundColor: AppTheme.primaryColor,
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.white, AppTheme.primaryColor.withAlpha(50)],
          ),
        ),
        child: Safe<PERSON><PERSON>(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const SizedBox(height: 20),

                // عنوان الصفحة
                Text(
                  'اشترك الآن للوصول إلى جميع الميزات',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.onBackground,
                  ),
                ).animate().fadeIn(duration: 600.ms),

                const SizedBox(height: 16),

                // وصف الصفحة
                Text(
                  'احصل على ميزات حصرية وتجربة خالية من الإعلانات',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16,
                    color: AppTheme.onSurfaceVariant,
                  ),
                ).animate().fadeIn(delay: 200.ms, duration: 600.ms),

                const SizedBox(height: 40),

                // خطط الاشتراك
                _buildSubscriptionPlans(),

                const SizedBox(height: 40),

                // ميزات الاشتراك
                _buildSubscriptionFeatures(),

                const SizedBox(height: 40),

                // زر الاشتراك
                Button3D(
                  text: 'اشترك الآن',
                  icon: Icons.workspace_premium,
                  width: double.infinity,
                  onPressed: _isLoading ? null : _subscribe,
                  isLoading: _isLoading,
                  color: AppTheme.primaryColor,
                ).animate().fadeIn(delay: 800.ms, duration: 600.ms),

                const SizedBox(height: 16),

                // نص الشروط والأحكام
                Text(
                  'بالاشتراك، أنت توافق على شروط الاستخدام وسياسة الخصوصية',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 12,
                    color: AppTheme.onSurfaceVariant,
                  ),
                ).animate().fadeIn(delay: 1000.ms, duration: 600.ms),

                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء خطط الاشتراك
  Widget _buildSubscriptionPlans() {
    return Column(
      children: [
        // خطة شهرية
        _buildSubscriptionPlanCard(
          index: 0,
          title: 'اشتراك شهري',
          price: '9.99',
          period: 'شهر',
          discount: null,
          isPopular: false,
        ),

        const SizedBox(height: 16),

        // خطة سنوية
        _buildSubscriptionPlanCard(
          index: 1,
          title: 'اشتراك سنوي',
          price: '59.99',
          period: 'سنة',
          discount: '50%',
          isPopular: true,
        ),

        const SizedBox(height: 16),

        // خطة مدى الحياة
        _buildSubscriptionPlanCard(
          index: 2,
          title: 'اشتراك مدى الحياة',
          price: '199.99',
          period: 'لمرة واحدة',
          discount: null,
          isPopular: false,
        ),
      ],
    );
  }

  /// بناء بطاقة خطة الاشتراك
  Widget _buildSubscriptionPlanCard({
    required int index,
    required String title,
    required String price,
    required String period,
    String? discount,
    required bool isPopular,
  }) {
    final isSelected = _selectedPlanIndex == index;

    return GestureDetector(
          onTap: () {
            setState(() {
              _selectedPlanIndex = index;
            });
          },
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color:
                  isSelected ? Color.fromRGBO(0, 122, 255, 0.12) : Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color:
                    isSelected ? AppTheme.primaryColor : Colors.grey.shade300,
                width: 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: Color.fromRGBO(0, 0, 0, 0.04),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Row(
              children: [
                // زر الاختيار
                Radio(
                  value: index,
                  groupValue: _selectedPlanIndex,
                  onChanged: (value) {
                    setState(() {
                      _selectedPlanIndex = value as int;
                    });
                  },
                  activeColor: AppTheme.primaryColor,
                ),

                const SizedBox(width: 8),

                // معلومات الخطة
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // عنوان الخطة
                      Row(
                        children: [
                          Text(
                            title,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color:
                                  isSelected
                                      ? AppTheme.primaryColor
                                      : AppTheme.onBackground,
                            ),
                          ),

                          if (isPopular) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: AppTheme.primaryColor,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Text(
                                'الأكثر شيوعًا',
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),

                      const SizedBox(height: 8),

                      // السعر
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            '\$$price',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color:
                                  isSelected
                                      ? AppTheme.primaryColor
                                      : AppTheme.onBackground,
                            ),
                          ),

                          const SizedBox(width: 4),

                          Text(
                            '/ $period',
                            style: TextStyle(
                              fontSize: 14,
                              color: AppTheme.onSurfaceVariant,
                            ),
                          ),

                          if (discount != null) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.green,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                'خصم $discount',
                                style: const TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        )
        .animate()
        .fadeIn(
          delay: Duration(milliseconds: 400 + (index * 200)),
          duration: 600.ms,
        )
        .slideY(
          delay: Duration(milliseconds: 400 + (index * 200)),
          begin: 0.2,
          end: 0,
          duration: 600.ms,
          curve: Curves.easeOut,
        );
  }

  /// بناء ميزات الاشتراك
  Widget _buildSubscriptionFeatures() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم
        Text(
          'ميزات الاشتراك',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppTheme.onBackground,
          ),
        ).animate().fadeIn(delay: 600.ms, duration: 600.ms),

        const SizedBox(height: 16),

        // قائمة الميزات
        _buildFeatureItem(
          icon: Icons.check_circle,
          text: 'ترجمة غير محدودة للنصوص والصوت والصور',
          delay: 700,
        ),

        _buildFeatureItem(
          icon: Icons.check_circle,
          text: 'ترجمة المستندات بدقة عالية',
          delay: 800,
        ),

        _buildFeatureItem(
          icon: Icons.check_circle,
          text: 'وضع الأطفال ووضع السياحة',
          delay: 900,
        ),

        _buildFeatureItem(
          icon: Icons.check_circle,
          text: 'دعم اللغات النادرة',
          delay: 1000,
        ),

        _buildFeatureItem(
          icon: Icons.check_circle,
          text: 'محادثة ذكية مع الذكاء الاصطناعي',
          delay: 1100,
        ),

        _buildFeatureItem(
          icon: Icons.check_circle,
          text: 'تجربة خالية من الإعلانات',
          delay: 1200,
        ),
      ],
    );
  }

  /// بناء عنصر الميزة
  Widget _buildFeatureItem({
    required IconData icon,
    required String text,
    required int delay,
  }) {
    return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: Row(
            children: [
              Icon(icon, color: AppTheme.primaryColor, size: 20),

              const SizedBox(width: 12),

              Expanded(
                child: Text(
                  text,
                  style: TextStyle(fontSize: 14, color: AppTheme.onBackground),
                ),
              ),
            ],
          ),
        )
        .animate()
        .fadeIn(delay: Duration(milliseconds: delay), duration: 600.ms)
        .slideX(
          delay: Duration(milliseconds: delay),
          begin: 0.2,
          end: 0,
          duration: 600.ms,
          curve: Curves.easeOut,
        );
  }

  /// الاشتراك في الخطة المحددة
  Future<void> _subscribe() async {
    setState(() {
      _isLoading = true;
    });

    // محاكاة عملية الاشتراك
    await Future.delayed(const Duration(seconds: 2));

    if (!mounted) return;

    setState(() {
      _isLoading = false;
    });

    // عرض رسالة نجاح الاشتراك
    _showSubscriptionSuccessDialog();
  }

  /// عرض مربع حوار نجاح الاشتراك
  void _showSubscriptionSuccessDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تم الاشتراك بنجاح'),
            content: const Text(
              'شكرًا لاشتراكك! يمكنك الآن الاستمتاع بجميع ميزات التطبيق.',
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  Navigator.pop(context);
                },
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }
}
