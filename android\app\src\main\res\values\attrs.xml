<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- سمات مخصصة لتحسين استجابة القوائم -->
    <declare-styleable name="TouchableView">
        <attr name="touchFeedbackColor" format="color" />
        <attr name="touchRippleRadius" format="dimension" />
        <attr name="touchRippleDuration" format="integer" />
        <attr name="touchHighlightColor" format="color" />
    </declare-styleable>
    
    <!-- سمات مخصصة للقوائم -->
    <declare-styleable name="MenuView">
        <attr name="menuItemHeight" format="dimension" />
        <attr name="menuItemPadding" format="dimension" />
        <attr name="menuItemTextColor" format="color" />
        <attr name="menuItemBackground" format="reference|color" />
        <attr name="menuItemSelectedBackground" format="reference|color" />
    </declare-styleable>
</resources>
