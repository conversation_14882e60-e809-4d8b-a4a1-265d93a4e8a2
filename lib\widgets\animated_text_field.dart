import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../config/app_theme.dart';
import '../core/animations/app_animations.dart';

/// حقل نص محسن مع رسوم متحركة وتأثيرات بصرية
class AnimatedTextField extends StatefulWidget {
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final String? labelText;
  final String? hintText;
  final IconData? prefixIcon;
  final Widget? suffixIcon;
  final bool obscureText;
  final TextInputType? keyboardType;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final void Function()? onTap;
  final bool readOnly;
  final int? maxLines;
  final int? minLines;
  final TextInputAction? textInputAction;
  final void Function(String)? onSubmitted;

  const AnimatedTextField({
    super.key,
    this.controller,
    this.focusNode,
    this.labelText,
    this.hintText,
    this.prefixIcon,
    this.suffixIcon,
    this.obscureText = false,
    this.keyboardType,
    this.validator,
    this.onChanged,
    this.onTap,
    this.readOnly = false,
    this.maxLines = 1,
    this.minLines,
    this.textInputAction,
    this.onSubmitted,
  });

  @override
  State<AnimatedTextField> createState() => _AnimatedTextFieldState();
}

class _AnimatedTextFieldState extends State<AnimatedTextField>
    with TickerProviderStateMixin {
  late AnimationController _focusController;
  late Animation<double> _focusAnimation;
  late Animation<Color?> _borderColorAnimation;

  bool _isFocused = false;

  @override
  void initState() {
    super.initState();

    _focusController = AnimationController(
      duration: AppAnimations.normalDuration,
      vsync: this,
    );

    _focusAnimation = CurvedAnimation(
      parent: _focusController,
      curve: AppAnimations.smoothCurve,
    );

    _borderColorAnimation = ColorTween(
      begin: AppTheme.outlineColor,
      end: AppTheme.primaryColor,
    ).animate(_focusAnimation);

    widget.focusNode?.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    _focusController.dispose();
    widget.focusNode?.removeListener(_onFocusChange);
    super.dispose();
  }

  void _onFocusChange() {
    if (widget.focusNode?.hasFocus ?? false) {
      if (!_isFocused) {
        setState(() {
          _isFocused = true;
        });
        _focusController.forward();
        HapticFeedback.lightImpact();
      }
    } else {
      if (_isFocused) {
        setState(() {
          _isFocused = false;
        });
        _focusController.reverse();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AnimatedBuilder(
      animation: _focusAnimation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppTheme.mediumRadius),
            boxShadow:
                _isFocused
                    ? [
                      BoxShadow(
                        color: AppTheme.primaryColor.withValues(alpha: 0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ]
                    : null,
          ),
          child: TextFormField(
            controller: widget.controller,
            focusNode: widget.focusNode,
            obscureText: widget.obscureText,
            keyboardType: widget.keyboardType,
            onChanged: widget.onChanged,
            onTap: widget.onTap,
            readOnly: widget.readOnly,
            maxLines: widget.maxLines,
            minLines: widget.minLines,
            textInputAction: widget.textInputAction,
            onFieldSubmitted: widget.onSubmitted,
            style: theme.textTheme.bodyLarge,
            validator: widget.validator,
            decoration: InputDecoration(
              labelText: widget.labelText,
              hintText: widget.hintText,
              prefixIcon:
                  widget.prefixIcon != null
                      ? Icon(
                        widget.prefixIcon,
                        color:
                            _isFocused
                                ? AppTheme.primaryColor
                                : AppTheme.onSurfaceVariant,
                      )
                      : null,
              suffixIcon: widget.suffixIcon,
              filled: true,
              fillColor:
                  _isFocused
                      ? AppTheme.primaryContainer.withValues(alpha: 0.1)
                      : AppTheme.surfaceVariant,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppTheme.mediumRadius),
                borderSide: BorderSide(
                  color: _borderColorAnimation.value ?? AppTheme.outlineColor,
                  width: _isFocused ? 2 : 1,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppTheme.mediumRadius),
                borderSide: const BorderSide(
                  color: AppTheme.outlineColor,
                  width: 1,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppTheme.mediumRadius),
                borderSide: const BorderSide(
                  color: AppTheme.primaryColor,
                  width: 2,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppTheme.mediumRadius),
                borderSide: const BorderSide(
                  color: AppTheme.errorColor,
                  width: 1,
                ),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppTheme.mediumRadius),
                borderSide: const BorderSide(
                  color: AppTheme.errorColor,
                  width: 2,
                ),
              ),
              labelStyle: TextStyle(
                color:
                    _isFocused
                        ? AppTheme.primaryColor
                        : AppTheme.onSurfaceVariant,
              ),
              hintStyle: TextStyle(
                color: AppTheme.onSurfaceVariant.withValues(alpha: 0.6),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: AppTheme.spacing16,
                vertical: AppTheme.spacing16,
              ),
            ),
          ),
        );
      },
    );
  }
}
