@echo off
echo ========================================
echo    Flutter Web Local Testing
echo    AI Smart Translator New Project
echo ========================================

echo.
echo [1/4] Checking Flutter Web support...
call flutter config --enable-web
if %errorlevel% neq 0 (
    echo Error: Failed to enable Flutter Web!
    pause
    exit /b 1
)

echo.
echo [2/4] Getting dependencies...
call flutter pub get
if %errorlevel% neq 0 (
    echo Error: Failed to get dependencies!
    pause
    exit /b 1
)

echo.
echo [3/4] Building for web (debug mode)...
call flutter build web --debug --base-href /
if %errorlevel% neq 0 (
    echo Error: Failed to build for web!
    pause
    exit /b 1
)

echo.
echo [4/4] Starting local web server...
echo.
echo ========================================
echo    Local Web Server Starting...
echo ========================================
echo.
echo Your app will be available at:
echo http://localhost:8080
echo.
echo Press Ctrl+C to stop the server
echo ========================================
echo.

cd build\web
python -m http.server 8080 2>nul || (
    echo Python not found, trying alternative...
    start "" "http://localhost:8080"
    call flutter run -d chrome --web-port 8080
)

pause
