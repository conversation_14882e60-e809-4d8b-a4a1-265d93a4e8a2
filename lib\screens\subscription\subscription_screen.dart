import 'package:flutter/material.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:provider/provider.dart';

import '../../config/app_theme.dart';
import '../../models/subscription_model.dart';
import '../../providers/app_state.dart';
// ignore: unused_import
import '../../services/subscription/subscription_service.dart';
import '../../utils/app_helpers.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/loading_indicator.dart';

/// شاشة الاشتراكات
class SubscriptionScreen extends StatefulWidget {
  const SubscriptionScreen({super.key});

  @override
  State<SubscriptionScreen> createState() => _SubscriptionScreenState();
}

class _SubscriptionScreenState extends State<SubscriptionScreen> {
  bool _isLoading = true;
  List<ProductDetails> _products = [];
  int _selectedProductIndex = 0;
  SubscriptionModel? _currentSubscription;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final appState = Provider.of<AppState>(context, listen: false);
      final subscriptionService = appState.subscriptionService;

      // الاستماع لتدفق الأخطاء
      subscriptionService.errors.listen((error) {
        if (mounted) {
          setState(() {
            _errorMessage = error;
          });
        }
      });

      // الاستماع لتدفق حالة التحميل
      subscriptionService.loadingStatus.listen((isLoading) {
        if (mounted) {
          setState(() {
            _isLoading = isLoading;
          });
        }
      });

      // الاستماع لتدفق حالة الاشتراك
      subscriptionService.subscriptionStatus.listen((subscription) {
        if (mounted) {
          setState(() {
            _currentSubscription = subscription;
          });
        }
      });

      // الاستماع لتدفق المنتجات
      subscriptionService.products.listen((products) {
        if (mounted) {
          setState(() {
            _products = products;
          });
        }
      });

      // تحميل البيانات الأولية
      _currentSubscription = subscriptionService.currentSubscription;
      _products = subscriptionService.availableProducts;
      _isLoading = subscriptionService.isLoading;
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'حدث خطأ أثناء تحميل البيانات: $e';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// شراء اشتراك
  Future<void> _purchaseSubscription() async {
    if (_products.isEmpty || _selectedProductIndex >= _products.length) {
      // التحقق من أن الويدجت لا يزال مثبتًا قبل عرض رسالة الخطأ
      if (!mounted) return;

      AppHelpers.showSnackBar(
        context,
        'لا توجد منتجات متاحة للشراء',
        isError: true,
      );
      return;
    }

    // التحقق من أن الويدجت لا يزال مثبتًا قبل الوصول إلى السياق
    if (!mounted) return;

    final appState = Provider.of<AppState>(context, listen: false);
    final subscriptionService = appState.subscriptionService;

    try {
      final product = _products[_selectedProductIndex];
      await subscriptionService.purchaseSubscription(product.id);

      // لا نحتاج إلى عرض رسالة نجاح هنا لأن خدمة الاشتراك ستقوم بتحديث حالة التطبيق
    } catch (e) {
      // التحقق من أن الويدجت لا يزال مثبتًا بعد العملية غير المتزامنة
      if (!mounted) return;

      AppHelpers.showSnackBar(
        context,
        'حدث خطأ أثناء عملية الشراء: $e',
        isError: true,
      );
    }
  }

  /// استعادة المشتريات
  Future<void> _restorePurchases() async {
    // التحقق من أن الويدجت لا يزال مثبتًا قبل الوصول إلى السياق
    if (!mounted) return;

    final appState = Provider.of<AppState>(context, listen: false);
    final subscriptionService = appState.subscriptionService;

    try {
      await subscriptionService.restorePurchases();

      // التحقق من أن الويدجت لا يزال مثبتًا بعد العملية غير المتزامنة
      if (!mounted) return;

      AppHelpers.showSnackBar(context, 'تمت محاولة استعادة المشتريات');
    } catch (e) {
      // التحقق من أن الويدجت لا يزال مثبتًا بعد الخطأ
      if (!mounted) return;

      AppHelpers.showSnackBar(
        context,
        'حدث خطأ أثناء استعادة المشتريات: $e',
        isError: true,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'الاشتراكات',
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _restorePurchases,
            tooltip: 'استعادة المشتريات',
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: LoadingIndicator())
              : _buildContent(),
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildContent() {
    if (_errorMessage != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, color: Colors.red, size: 48),
              const SizedBox(height: 16),
              Text(
                _errorMessage!,
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 16, color: Colors.red),
              ),
              const SizedBox(height: 24),
              CustomButton(
                text: 'إعادة المحاولة',
                onPressed: _loadData,
                width: 200,
              ),
            ],
          ),
        ),
      );
    }

    if (_products.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.shopping_cart_outlined,
                color: Colors.grey,
                size: 48,
              ),
              const SizedBox(height: 16),
              const Text(
                'لا توجد منتجات متاحة حاليًا',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16, color: Colors.grey),
              ),
              const SizedBox(height: 24),
              CustomButton(text: 'تحديث', onPressed: _loadData, width: 200),
            ],
          ),
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSubscriptionStatus(),
          const SizedBox(height: 24),
          const Text(
            'اختر خطة الاشتراك المناسبة لك:',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          _buildProductsList(),
          const SizedBox(height: 24),
          _buildPurchaseButton(),
          const SizedBox(height: 24),
          _buildLegalInfo(),
        ],
      ),
    );
  }

  /// بناء حالة الاشتراك
  Widget _buildSubscriptionStatus() {
    if (_currentSubscription != null && _currentSubscription!.isActive()) {
      return Card(
        color: Colors.green.shade50,
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.green, size: 24),
                  const SizedBox(width: 8),
                  Text(
                    'أنت مشترك في ${_currentSubscription!.name}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                'تاريخ انتهاء الاشتراك: ${AppHelpers.formatDate(_currentSubscription!.endDate)}',
                style: const TextStyle(fontSize: 14),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      color: Colors.orange.shade50,
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: const Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.orange, size: 24),
                SizedBox(width: 8),
                Text(
                  'أنت تستخدم النسخة المجانية',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            SizedBox(height: 8),
            Text(
              'اشترك للاستفادة من جميع الميزات المتقدمة بدون قيود',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قائمة المنتجات
  Widget _buildProductsList() {
    return Column(
      children: List.generate(_products.length, (index) {
        final product = _products[index];
        final isSelected = index == _selectedProductIndex;

        // استخراج الميزات
        List<String> features = [];
        if (product.id.contains('monthly')) {
          features = [
            'ترجمة صوتية غير محدودة',
            'دعم أكثر من 35 لغة',
            'تمييز المتحدثين في المحادثات الجماعية',
            'ترجمة فورية مستمرة',
            'بدون إعلانات',
          ];
        } else if (product.id.contains('yearly')) {
          features = [
            'ترجمة صوتية غير محدودة',
            'دعم أكثر من 35 لغة',
            'تمييز المتحدثين في المحادثات الجماعية',
            'ترجمة فورية مستمرة',
            'بدون إعلانات',
            'توفير 20% مقارنة بالاشتراك الشهري',
          ];
        } else if (product.id.contains('weekly')) {
          features = [
            'ترجمة صوتية غير محدودة',
            'دعم أكثر من 35 لغة',
            'تمييز المتحدثين في المحادثات الجماعية',
            'ترجمة فورية مستمرة',
            'بدون إعلانات',
          ];
        }

        return _buildProductCard(
          product: product,
          features: features,
          isSelected: isSelected,
          onTap: () {
            setState(() {
              _selectedProductIndex = index;
            });
          },
        );
      }),
    );
  }

  /// بناء بطاقة المنتج
  Widget _buildProductCard({
    required ProductDetails product,
    required List<String> features,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    // استخراج نوع الاشتراك
    String subscriptionType = 'اشتراك';
    if (product.id.contains('monthly')) {
      subscriptionType = 'الاشتراك الشهري';
    } else if (product.id.contains('yearly')) {
      subscriptionType = 'الاشتراك السنوي';
    } else if (product.id.contains('weekly')) {
      subscriptionType = 'الاشتراك الأسبوعي';
    }

    return Card(
      elevation: isSelected ? 4 : 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: isSelected ? AppTheme.primaryColor : Colors.transparent,
          width: 2,
        ),
      ),
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    subscriptionType,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (isSelected)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        'مختار',
                        style: TextStyle(color: Colors.white, fontSize: 12),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                product.price,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor,
                ),
              ),
              const SizedBox(height: 16),
              ...features.map(
                (feature) => Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.check_circle,
                        color: Colors.green,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Expanded(child: Text(feature)),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء زر الشراء
  Widget _buildPurchaseButton() {
    final bool isSubscribed =
        _currentSubscription != null && _currentSubscription!.isActive();

    return SizedBox(
      width: double.infinity,
      child: CustomButton(
        text: isSubscribed ? 'أنت مشترك بالفعل' : 'اشترك الآن',
        onPressed: isSubscribed ? null : _purchaseSubscription,
        color: isSubscribed ? Colors.grey : AppTheme.primaryColor,
      ),
    );
  }

  /// بناء المعلومات القانونية
  Widget _buildLegalInfo() {
    return const Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'معلومات هامة:',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        SizedBox(height: 8),
        Text(
          '• سيتم تجديد الاشتراك تلقائيًا ما لم يتم إلغاؤه قبل 24 ساعة من نهاية الفترة الحالية.\n'
          '• يمكن إدارة الاشتراك وإلغاؤه من إعدادات الحساب في متجر التطبيقات.\n'
          '• عند الاشتراك، ستتم محاسبتك من خلال حسابك في متجر التطبيقات.\n'
          '• تطبق سياسة الخصوصية وشروط الاستخدام.',
          style: TextStyle(fontSize: 14),
        ),
      ],
    );
  }
}
