import 'dart:async';
import 'package:flutter/material.dart';
import '../../config/app_theme.dart';
import '../auth/simple_login_screen.dart';

/// شاشة البداية
class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  // متحكم الرسوم المتحركة
  late AnimationController _animationController;

  // رسوم متحركة للشعار
  late Animation<double> _logoAnimation;

  // رسوم متحركة للنص
  late Animation<double> _textAnimation;

  // مؤقت للانتقال
  Timer? _navigationTimer;

  // متغير للتحكم في إظهار زر البدء
  bool _showStartButton = false;

  @override
  void initState() {
    super.initState();

    // تهيئة متحكم الرسوم المتحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    // تهيئة رسوم متحركة للشعار
    _logoAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.7, curve: Curves.easeOut),
      ),
    );

    // تهيئة رسوم متحركة للنص
    _textAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.3, 1.0, curve: Curves.easeOut),
      ),
    );

    // بدء الرسوم المتحركة
    _animationController.forward();

    // إظهار زر البدء بعد 1.5 ثانية
    Future.delayed(const Duration(milliseconds: 1500), () {
      if (mounted) {
        setState(() {
          _showStartButton = true;
        });
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _navigationTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.primaryColor,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // الشعار
            FadeTransition(
              opacity: _logoAnimation,
              child: ScaleTransition(
                scale: _logoAnimation,
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Color.fromRGBO(0, 0, 0, 0.2),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Center(
                    child: Icon(
                      Icons.translate,
                      size: 80,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 30),
            // النص
            FadeTransition(
              opacity: _textAnimation,
              child: const Column(
                children: [
                  Text(
                    'المترجم الذكي',
                    style: TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  SizedBox(height: 10),
                  Text(
                    'ترجمة ذكية بتقنية الذكاء الاصطناعي',
                    style: TextStyle(fontSize: 16, color: Colors.white),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 50),

            // إظهار زر البدء أو مؤشر التحميل
            if (_showStartButton)
              // زر البدء المبسط
              ElevatedButton.icon(
                onPressed: () {
                  // الانتقال مباشرة إلى شاشة تسجيل الدخول
                  Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(
                      builder: (_) => const SimpleLoginScreen(),
                    ),
                  );
                },
                icon: const Icon(Icons.arrow_forward_ios),
                label: const Text('ابدأ الآن'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: AppTheme.primaryColor,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32,
                    vertical: 16,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(30),
                  ),
                  elevation: 5,
                ),
              )
            else
              // مؤشر التحميل
              const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
          ],
        ),
      ),
    );
  }
}
