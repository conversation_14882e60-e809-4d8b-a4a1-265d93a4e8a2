const functions = require('firebase-functions');
const admin = require('firebase-admin');
const axios = require('axios');

admin.initializeApp();

// تخزين المفاتيح في بيئة Firebase Functions
// يمكن تعيينها باستخدام: firebase functions:config:set openai.key="YOUR_KEY" gemini.key="YOUR_KEY" openrouter.key="YOUR_KEY"
const API_KEYS = {
  openai: functions.config().openai?.key,
  gemini_paid: functions.config().gemini?.paid_key,
  gemini_free: functions.config().gemini?.free_key,
  openrouter: functions.config().openrouter?.key
};

// تواريخ انتهاء صلاحية المفاتيح المدفوعة
const API_EXPIRY_DATES = {
  openai: functions.config().openai?.end_date || '2025-12-31',
  gemini: functions.config().gemini?.end_date || '2025-12-31'
};

// حدود الاستخدام اليومية
const DAILY_LIMITS = {
  openai: functions.config().limits?.openai || 100,
  gemini: functions.config().limits?.gemini || 100,
  openrouter: functions.config().limits?.openrouter || 100
};

// دالة للتحقق من المصادقة
const verifyAuth = async (req) => {
  try {
    const idToken = req.headers.authorization?.split('Bearer ')[1];
    if (!idToken) throw new Error('No token provided');
    
    const decodedToken = await admin.auth().verifyIdToken(idToken);
    return decodedToken.uid;
  } catch (error) {
    throw new Error('Unauthorized');
  }
};

// دالة للتحقق من حدود الاستخدام
const checkUsageLimits = async (uid, serviceType) => {
  const today = new Date().toISOString().split('T')[0];
  const usageRef = admin.firestore().collection('users').doc(uid).collection('usage').doc(today);
  
  const usageDoc = await usageRef.get();
  const usage = usageDoc.exists ? usageDoc.data() : { openai: 0, gemini: 0, openrouter: 0 };
  
  // الحصول على حدود الاستخدام
  const limit = DAILY_LIMITS[serviceType] || 100;
  
  if (usage[serviceType] >= limit) {
    throw new Error(`Daily limit exceeded for ${serviceType}`);
  }
  
  // تحديث الاستخدام
  await usageRef.set({
    [serviceType]: (usage[serviceType] || 0) + 1,
    lastUpdated: admin.firestore.FieldValue.serverTimestamp()
  }, { merge: true });
  
  return true;
};

// دالة للتحقق من صلاحية المفتاح المدفوع
const isPaidKeyValid = (serviceType) => {
  try {
    const endDateStr = API_EXPIRY_DATES[serviceType];
    if (!endDateStr) return false;
    
    const endDate = new Date(endDateStr);
    return new Date() < endDate;
  } catch (e) {
    console.error(`Error checking key validity for ${serviceType}:`, e);
    return false;
  }
};

// دالة للحصول على المفتاح المناسب
const getApiKey = (serviceType) => {
  switch (serviceType) {
    case 'openai':
      return API_KEYS.openai;
    case 'gemini':
      return isPaidKeyValid('gemini') ? API_KEYS.gemini_paid : API_KEYS.gemini_free;
    case 'openrouter':
      return API_KEYS.openrouter;
    default:
      throw new Error(`Unknown service type: ${serviceType}`);
  }
};

// دالة تخزين مؤقت للطلبات المتكررة
const cache = {};

const getCachedResponse = (cacheKey) => {
  if (cache[cacheKey]) {
    const { timestamp, data } = cache[cacheKey];
    // التخزين المؤقت صالح لمدة ساعة واحدة
    if (Date.now() - timestamp < 60 * 60 * 1000) {
      return data;
    }
    delete cache[cacheKey];
  }
  return null;
};

const setCachedResponse = (cacheKey, data) => {
  cache[cacheKey] = {
    timestamp: Date.now(),
    data
  };
};

// دالة OpenAI للترجمة
exports.translateWithOpenAI = functions.https.onCall(async (data, context) => {
  try {
    // التحقق من المصادقة
    const uid = context.auth?.uid;
    if (!uid) throw new Error('Unauthorized');
    
    // التحقق من حدود الاستخدام
    await checkUsageLimits(uid, 'openai');
    
    const { text, sourceLanguage, targetLanguage, context: translationContext } = data;
    
    // إنشاء مفتاح التخزين المؤقت
    const cacheKey = `openai_translate_${sourceLanguage}_${targetLanguage}_${text}`;
    const cachedResult = getCachedResponse(cacheKey);
    if (cachedResult) {
      return cachedResult;
    }
    
    // استدعاء OpenAI API
    const response = await axios.post(
      'https://api.openai.com/v1/chat/completions',
      {
        model: 'gpt-3.5-turbo',
        messages: [
          { role: 'system', content: `You are a translator. Translate from ${sourceLanguage} to ${targetLanguage}.` },
          { role: 'user', content: `Translate the following text: ${text}` }
        ]
      },
      {
        headers: {
          'Authorization': `Bearer ${getApiKey('openai')}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    const result = {
      translatedText: response.data.choices[0].message.content.trim(),
      serviceUsed: 'openai'
    };
    
    // تخزين النتيجة في التخزين المؤقت
    setCachedResponse(cacheKey, result);
    
    return result;
  } catch (error) {
    console.error('OpenAI translation error:', error.message, error);
    throw new functions.https.HttpsError('internal', error.message);
  }
});

// دالة Gemini للترجمة
exports.translateWithGemini = functions.https.onCall(async (data, context) => {
  try {
    // التحقق من المصادقة
    const uid = context.auth?.uid;
    if (!uid) throw new Error('Unauthorized');
    
    // التحقق من حدود الاستخدام
    await checkUsageLimits(uid, 'gemini');
    
    const { text, sourceLanguage, targetLanguage, context: translationContext } = data;
    
    // إنشاء مفتاح التخزين المؤقت
    const cacheKey = `gemini_translate_${sourceLanguage}_${targetLanguage}_${text}`;
    const cachedResult = getCachedResponse(cacheKey);
    if (cachedResult) {
      return cachedResult;
    }
    
    // الحصول على المفتاح المناسب (مدفوع أو مجاني)
    const apiKey = getApiKey('gemini');
    
    // استدعاء Gemini API
    const response = await axios.post(
      `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${apiKey}`,
      {
        contents: [
          {
            parts: [
              { text: `Translate the following text from ${sourceLanguage} to ${targetLanguage}: ${text}` }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.2,
          maxOutputTokens: 1000,
        }
      }
    );
    
    let translatedText = '';
    if (response.data.candidates && 
        response.data.candidates.length > 0 && 
        response.data.candidates[0].content && 
        response.data.candidates[0].content.parts && 
        response.data.candidates[0].content.parts.length > 0) {
      translatedText = response.data.candidates[0].content.parts[0].text;
    } else {
      throw new Error('Invalid response format from Gemini API');
    }
    
    const result = {
      translatedText: translatedText.trim(),
      serviceUsed: 'gemini'
    };
    
    // تخزين النتيجة في التخزين المؤقت
    setCachedResponse(cacheKey, result);
    
    return result;
  } catch (error) {
    console.error('Gemini translation error:', error.message, error);
    throw new functions.https.HttpsError('internal', error.message);
  }
});

// دالة OpenRouter للترجمة
exports.translateWithOpenRouter = functions.https.onCall(async (data, context) => {
  try {
    // التحقق من المصادقة
    const uid = context.auth?.uid;
    if (!uid) throw new Error('Unauthorized');
    
    // التحقق من حدود الاستخدام
    await checkUsageLimits(uid, 'openrouter');
    
    const { text, sourceLanguage, targetLanguage, context: translationContext } = data;
    
    // إنشاء مفتاح التخزين المؤقت
    const cacheKey = `openrouter_translate_${sourceLanguage}_${targetLanguage}_${text}`;
    const cachedResult = getCachedResponse(cacheKey);
    if (cachedResult) {
      return cachedResult;
    }
    
    // استدعاء OpenRouter API
    const response = await axios.post(
      'https://openrouter.ai/api/v1/chat/completions',
      {
        model: 'openai/gpt-3.5-turbo',
        messages: [
          { role: 'system', content: `You are a translator. Translate from ${sourceLanguage} to ${targetLanguage}.` },
          { role: 'user', content: `Translate the following text: ${text}` }
        ]
      },
      {
        headers: {
          'Authorization': `Bearer ${getApiKey('openrouter')}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://translator-ai-8c4a4.web.app'
        }
      }
    );
    
    const result = {
      translatedText: response.data.choices[0].message.content.trim(),
      serviceUsed: 'openrouter'
    };
    
    // تخزين النتيجة في التخزين المؤقت
    setCachedResponse(cacheKey, result);
    
    return result;
  } catch (error) {
    console.error('OpenRouter translation error:', error.message, error);
    throw new functions.https.HttpsError('internal', error.message);
  }
});

// دالة Gemini للمحادثة
exports.chatWithGemini = functions.https.onCall(async (data, context) => {
  try {
    // التحقق من المصادقة
    const uid = context.auth?.uid;
    if (!uid) throw new Error('Unauthorized');
    
    // التحقق من حدود الاستخدام
    await checkUsageLimits(uid, 'gemini');
    
    const { message, responseType, chatHistory } = data;
    
    // الحصول على المفتاح المناسب (مدفوع أو مجاني)
    const apiKey = getApiKey('gemini');
    
    // بناء سياق المحادثة
    let prompt = '';
    
    // إضافة سياق النظام حسب نوع الاستجابة
    switch (responseType) {
      case 'general':
        prompt += 'Instructions: أنت مساعد ذكي ومفيد. قدم إجابات دقيقة ومفيدة على أسئلة المستخدم. استخدم اللغة العربية في إجاباتك.\n\n';
        break;
      case 'academic':
        prompt += 'Instructions: أنت مساعد أكاديمي متخصص. قدم إجابات علمية دقيقة ومفصلة مع ذكر المصادر إن أمكن. استخدم اللغة العربية الفصحى في إجاباتك.\n\n';
        break;
      case 'creative':
        prompt += 'Instructions: أنت مساعد إبداعي. قدم إجابات إبداعية وملهمة. يمكنك استخدام الأسلوب الأدبي والشعري عند الحاجة. استخدم اللغة العربية في إجاباتك.\n\n';
        break;
      default:
        prompt += 'Instructions: أنت مساعد ذكي ومفيد. قدم إجابات دقيقة ومفيدة على أسئلة المستخدم. استخدم اللغة العربية في إجاباتك.\n\n';
    }
    
    // إضافة سجل المحادثة السابق
    if (chatHistory && chatHistory.length > 0) {
      for (const msg of chatHistory) {
        if (msg.role === 'user') {
          prompt += `User: ${msg.content}\n\n`;
        } else if (msg.role === 'assistant') {
          prompt += `Assistant: ${msg.content}\n\n`;
        }
      }
    }
    
    // إضافة رسالة المستخدم الحالية
    prompt += `User: ${message}\n\n`;
    
    // استدعاء Gemini API
    const response = await axios.post(
      `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${apiKey}`,
      {
        contents: [
          {
            parts: [
              { text: prompt }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.7,
          maxOutputTokens: 1000,
        }
      }
    );
    
    let responseText = '';
    if (response.data.candidates && 
        response.data.candidates.length > 0 && 
        response.data.candidates[0].content && 
        response.data.candidates[0].content.parts && 
        response.data.candidates[0].content.parts.length > 0) {
      responseText = response.data.candidates[0].content.parts[0].text;
    } else {
      throw new Error('Invalid response format from Gemini API');
    }
    
    return {
      response: responseText.trim(),
      serviceUsed: 'gemini'
    };
  } catch (error) {
    console.error('Gemini chat error:', error.message, error);
    throw new functions.https.HttpsError('internal', error.message);
  }
});
