import 'dart:convert';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// نموذج بيانات المستخدم
class UserModel {
  final String uid;
  final String? email;
  final String? displayName;
  final String? photoURL;
  final bool isPremium;
  final bool isAdsRemoved;
  final DateTime? premiumExpiry;

  UserModel({
    required this.uid,
    this.email,
    this.displayName,
    this.photoURL,
    this.isPremium = false,
    this.isAdsRemoved = false,
    this.premiumExpiry,
  });

  factory UserModel.fromFirebaseUser(User user) {
    return UserModel(
      uid: user.uid,
      email: user.email,
      displayName: user.displayName,
      photoURL: user.photoURL,
    );
  }

  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      uid: map['uid'],
      email: map['email'],
      displayName: map['displayName'],
      photoURL: map['photoURL'],
      isPremium: map['isPremium'] ?? false,
      isAdsRemoved: map['isAdsRemoved'] ?? false,
      premiumExpiry:
          map['premiumExpiry'] != null
              ? (map['premiumExpiry'] as Timestamp).toDate()
              : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'uid': uid,
      'email': email,
      'displayName': displayName,
      'photoURL': photoURL,
      'isPremium': isPremium,
      'isAdsRemoved': isAdsRemoved,
      'premiumExpiry':
          premiumExpiry != null ? Timestamp.fromDate(premiumExpiry!) : null,
    };
  }

  UserModel copyWith({
    String? uid,
    String? email,
    String? displayName,
    String? photoURL,
    bool? isPremium,
    bool? isAdsRemoved,
    DateTime? premiumExpiry,
  }) {
    return UserModel(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      photoURL: photoURL ?? this.photoURL,
      isPremium: isPremium ?? this.isPremium,
      isAdsRemoved: isAdsRemoved ?? this.isAdsRemoved,
      premiumExpiry: premiumExpiry ?? this.premiumExpiry,
    );
  }
}

/// خدمة المصادقة
class AuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // الحصول على المستخدم الحالي
  User? get currentUser => _auth.currentUser;

  // تدفق حالة المصادقة
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
  Future<UserCredential> signInWithEmailAndPassword(
    String email,
    String password,
  ) async {
    try {
      final result = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      // تحديث بيانات المستخدم في التخزين المحلي
      await _updateUserLocalData(result.user);

      return result;
    } catch (e) {
      debugPrint('Sign in error: $e');
      rethrow;
    }
  }

  // إنشاء حساب جديد بالبريد الإلكتروني وكلمة المرور
  Future<UserCredential> createUserWithEmailAndPassword(
    String email,
    String password,
    String displayName,
  ) async {
    try {
      final result = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      // تحديث اسم المستخدم
      await result.user?.updateDisplayName(displayName);

      // إنشاء وثيقة المستخدم في Firestore
      await _createUserDocument(result.user!, displayName: displayName);

      // تحديث بيانات المستخدم في التخزين المحلي
      await _updateUserLocalData(result.user);

      return result;
    } catch (e) {
      debugPrint('Create user error: $e');
      rethrow;
    }
  }

  // تسجيل الدخول باستخدام Google
  Future<UserCredential?> signInWithGoogle() async {
    try {
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        throw Exception('Google sign in cancelled by user');
      }

      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      final result = await _auth.signInWithCredential(credential);

      // التحقق مما إذا كان المستخدم جديدًا
      final isNewUser = result.additionalUserInfo?.isNewUser ?? false;

      if (isNewUser) {
        // إنشاء وثيقة المستخدم في Firestore
        await _createUserDocument(result.user!);
      }

      // تحديث بيانات المستخدم في التخزين المحلي
      await _updateUserLocalData(result.user);

      return result;
    } catch (e) {
      debugPrint('Google sign in error: $e');
      rethrow;
    }
  }

  // تسجيل الدخول كضيف
  Future<UserCredential> signInAnonymously() async {
    try {
      final result = await _auth.signInAnonymously();

      // إنشاء وثيقة المستخدم في Firestore
      await _createUserDocument(result.user!, displayName: 'Guest');

      // تحديث بيانات المستخدم في التخزين المحلي
      await _updateUserLocalData(result.user);

      return result;
    } catch (e) {
      debugPrint('Anonymous sign in error: $e');
      rethrow;
    }
  }

  // تسجيل الخروج
  Future<void> signOut() async {
    try {
      await _googleSignIn.signOut();
      await _auth.signOut();

      // حذف بيانات المستخدم من التخزين المحلي
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('user_data');
      await prefs.setBool('is_logged_in', false);
    } catch (e) {
      debugPrint('Sign out error: $e');
      rethrow;
    }
  }

  // إعادة تعيين كلمة المرور
  Future<void> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } catch (e) {
      debugPrint('Reset password error: $e');
      rethrow;
    }
  }

  // إنشاء وثيقة المستخدم في Firestore
  Future<void> _createUserDocument(User user, {String? displayName}) async {
    try {
      final userDoc = _firestore.collection('users').doc(user.uid);

      final userData = {
        'uid': user.uid,
        'email': user.email,
        'displayName': displayName ?? user.displayName,
        'photoURL': user.photoURL,
        'createdAt': FieldValue.serverTimestamp(),
        'isPremium': false,
        'isAdsRemoved': false,
        'premiumExpiry': null,
      };

      await userDoc.set(userData);
    } catch (e) {
      debugPrint('Create user document error: $e');
      rethrow;
    }
  }

  // الحصول على بيانات المستخدم من Firestore
  Future<UserModel?> getUserData(String uid) async {
    try {
      final doc = await _firestore.collection('users').doc(uid).get();

      if (doc.exists) {
        return UserModel.fromMap(doc.data() as Map<String, dynamic>);
      }

      return null;
    } catch (e) {
      debugPrint('Get user data error: $e');
      rethrow;
    }
  }

  // تحديث بيانات المستخدم في Firestore
  Future<void> updateUserData(UserModel user) async {
    try {
      await _firestore.collection('users').doc(user.uid).update(user.toMap());

      // تحديث بيانات المستخدم في التخزين المحلي
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_data', json.encode(user.toMap()));
    } catch (e) {
      debugPrint('Update user data error: $e');
      rethrow;
    }
  }

  // تحديث بيانات المستخدم في التخزين المحلي
  Future<void> _updateUserLocalData(User? user) async {
    if (user == null) return;

    try {
      final userData = await getUserData(user.uid);

      if (userData != null) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('user_data', json.encode(userData.toMap()));
        await prefs.setBool('is_logged_in', true);
      }
    } catch (e) {
      debugPrint('Update user local data error: $e');
    }
  }
}
