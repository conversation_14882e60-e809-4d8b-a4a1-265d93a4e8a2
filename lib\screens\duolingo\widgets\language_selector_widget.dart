import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// ويدجت اختيار اللغة مثل Duolingo
class LanguageSelectorWidget extends StatelessWidget {
  final String selectedLanguage;
  final Function(String) onLanguageChanged;

  const LanguageSelectorWidget({
    super.key,
    required this.selectedLanguage,
    required this.onLanguageChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'اختر لغة التعلم',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 15),
          SizedBox(
            height: 120,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _getSupportedLanguages().length,
              itemBuilder: (context, index) {
                final language = _getSupportedLanguages()[index];
                return _buildLanguageCard(language);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// بطاقة اللغة
  Widget _buildLanguageCard(Map<String, String> language) {
    final isSelected = selectedLanguage == language['code'];
    
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onLanguageChanged(language['code']!);
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        width: 100,
        margin: const EdgeInsets.only(right: 15),
        decoration: BoxDecoration(
          gradient: isSelected
              ? LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    _getLanguageColor(language['code']!),
                    _getLanguageColor(language['code']!).withValues(alpha: 0.8),
                  ],
                )
              : LinearGradient(
                  colors: [
                    Colors.white,
                    Colors.grey.shade50,
                  ],
                ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected 
                ? _getLanguageColor(language['code']!)
                : Colors.grey.shade300,
            width: isSelected ? 3 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: isSelected
                  ? _getLanguageColor(language['code']!).withValues(alpha: 0.3)
                  : Colors.grey.withValues(alpha: 0.1),
              spreadRadius: isSelected ? 3 : 1,
              blurRadius: isSelected ? 15 : 5,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // علم الدولة
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: isSelected 
                    ? Colors.white.withValues(alpha: 0.2)
                    : _getLanguageColor(language['code']!).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(25),
              ),
              child: Center(
                child: Text(
                  _getLanguageFlag(language['code']!),
                  style: const TextStyle(fontSize: 28),
                ),
              ),
            ),
            
            const SizedBox(height: 8),
            
            // اسم اللغة
            Text(
              language['name']!,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: isSelected ? Colors.white : Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            
            // اسم اللغة بالإنجليزية
            Text(
              language['englishName']!,
              style: TextStyle(
                fontSize: 10,
                color: isSelected 
                    ? Colors.white.withValues(alpha: 0.8)
                    : Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// الحصول على قائمة اللغات المدعومة
  List<Map<String, String>> _getSupportedLanguages() {
    return [
      {'code': 'en', 'name': 'الإنجليزية', 'englishName': 'English'},
      {'code': 'fr', 'name': 'الفرنسية', 'englishName': 'French'},
      {'code': 'de', 'name': 'الألمانية', 'englishName': 'German'},
      {'code': 'es', 'name': 'الإسبانية', 'englishName': 'Spanish'},
      {'code': 'it', 'name': 'الإيطالية', 'englishName': 'Italian'},
      {'code': 'ja', 'name': 'اليابانية', 'englishName': 'Japanese'},
      {'code': 'zh', 'name': 'الصينية', 'englishName': 'Chinese'},
      {'code': 'tr', 'name': 'التركية', 'englishName': 'Turkish'},
      {'code': 'ru', 'name': 'الروسية', 'englishName': 'Russian'},
    ];
  }

  /// الحصول على لون اللغة
  Color _getLanguageColor(String languageCode) {
    final colorMap = {
      'en': const Color(0xFF1CB0F6), // أزرق
      'fr': const Color(0xFF4B4DFF), // بنفسجي
      'de': const Color(0xFFFF4B4B), // أحمر
      'es': const Color(0xFFFF9600), // برتقالي
      'it': const Color(0xFF58CC02), // أخضر
      'ja': const Color(0xFFFF1744), // أحمر ياباني
      'zh': const Color(0xFFFF5722), // أحمر صيني
      'tr': const Color(0xFFE91E63), // وردي تركي
      'ru': const Color(0xFF3F51B5), // أزرق روسي
    };
    
    return colorMap[languageCode] ?? const Color(0xFF58CC02);
  }

  /// الحصول على علم الدولة
  String _getLanguageFlag(String languageCode) {
    final flagMap = {
      'en': '🇺🇸',
      'fr': '🇫🇷',
      'de': '🇩🇪',
      'es': '🇪🇸',
      'it': '🇮🇹',
      'ja': '🇯🇵',
      'zh': '🇨🇳',
      'tr': '🇹🇷',
      'ru': '🇷🇺',
    };
    
    return flagMap[languageCode] ?? '🌍';
  }
}
