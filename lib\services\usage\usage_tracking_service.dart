import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import '../../models/usage_stats.dart';

/// خدمة تتبع الاستخدام
class UsageTrackingService {
  final SharedPreferences _prefs;
  final FirebaseAuth _auth;
  final FirebaseFirestore _firestore;

  UsageTrackingService(this._prefs, this._auth, this._firestore);

  /// تسجيل استخدام ميزة
  Future<void> trackFeatureUsage(String featureType) async {
    try {
      final today = DateTime.now().toIso8601String().split('T')[0];
      final key = 'usage_${featureType}_$today';

      // تحديث الإحصائيات المحلية
      int currentUsage = _prefs.getInt(key) ?? 0;
      await _prefs.setInt(key, currentUsage + 1);

      // تحديث الإحصائيات في Firestore إذا كان المستخدم مسجلاً
      final user = _auth.currentUser;
      if (user != null) {
        await _firestore
            .collection('users')
            .doc(user.uid)
            .collection('usage')
            .doc(today)
            .set({
              featureType: FieldValue.increment(1),
              'lastUpdated': FieldValue.serverTimestamp(),
            }, SetOptions(merge: true));
      }
    } catch (e) {
      // تسجيل الخطأ ولكن لا نريد أن نعطل تجربة المستخدم
      debugPrint('Error tracking feature usage: $e');
    }
  }

  /// الحصول على إحصائيات الاستخدام
  Future<UsageStats> getUsageStats() async {
    try {
      final today = DateTime.now().toIso8601String().split('T')[0];

      // الحصول على الإحصائيات المحلية
      final voiceTranslations = _prefs.getInt('usage_voice_$today') ?? 0;
      final textTranslations = _prefs.getInt('usage_text_$today') ?? 0;
      final imageTranslations = _prefs.getInt('usage_image_$today') ?? 0;
      final documentTranslations = _prefs.getInt('usage_document_$today') ?? 0;
      final conversations = _prefs.getInt('usage_conversation_$today') ?? 0;
      final aiChats = _prefs.getInt('usage_ai_chat_$today') ?? 0;

      // الحصول على إحصائيات الاستخدام اليومي والأسبوعي والشهري
      final dailyUsage = _calculateDailyUsage();
      final weeklyUsage = _calculateWeeklyUsage();
      final monthlyUsage = _calculateMonthlyUsage();

      return UsageStats(
        voiceTranslations: voiceTranslations,
        textTranslations: textTranslations,
        imageTranslations: imageTranslations,
        documentTranslations: documentTranslations,
        conversations: conversations,
        aiChats: aiChats,
        dailyUsage: dailyUsage,
        weeklyUsage: weeklyUsage,
        monthlyUsage: monthlyUsage,
      );
    } catch (e) {
      // تسجيل الخطأ وإرجاع إحصائيات فارغة
      debugPrint('Error getting usage stats: $e');
      return UsageStats();
    }
  }

  /// حساب الاستخدام اليومي
  int _calculateDailyUsage() {
    final today = DateTime.now().toIso8601String().split('T')[0];
    int total = 0;

    // جمع جميع أنواع الاستخدام لليوم الحالي
    total += _prefs.getInt('usage_voice_$today') ?? 0;
    total += _prefs.getInt('usage_text_$today') ?? 0;
    total += _prefs.getInt('usage_image_$today') ?? 0;
    total += _prefs.getInt('usage_document_$today') ?? 0;
    total += _prefs.getInt('usage_conversation_$today') ?? 0;
    total += _prefs.getInt('usage_ai_chat_$today') ?? 0;

    return total;
  }

  /// حساب الاستخدام الأسبوعي
  int _calculateWeeklyUsage() {
    int total = 0;
    final now = DateTime.now();

    // حساب الاستخدام للأيام السبعة الماضية
    for (int i = 0; i < 7; i++) {
      final date = now.subtract(Duration(days: i));
      final day = date.toIso8601String().split('T')[0];

      total += _prefs.getInt('usage_voice_$day') ?? 0;
      total += _prefs.getInt('usage_text_$day') ?? 0;
      total += _prefs.getInt('usage_image_$day') ?? 0;
      total += _prefs.getInt('usage_document_$day') ?? 0;
      total += _prefs.getInt('usage_conversation_$day') ?? 0;
      total += _prefs.getInt('usage_ai_chat_$day') ?? 0;
    }

    return total;
  }

  /// حساب الاستخدام الشهري
  int _calculateMonthlyUsage() {
    int total = 0;
    final now = DateTime.now();

    // حساب الاستخدام للثلاثين يومًا الماضية
    for (int i = 0; i < 30; i++) {
      final date = now.subtract(Duration(days: i));
      final day = date.toIso8601String().split('T')[0];

      total += _prefs.getInt('usage_voice_$day') ?? 0;
      total += _prefs.getInt('usage_text_$day') ?? 0;
      total += _prefs.getInt('usage_image_$day') ?? 0;
      total += _prefs.getInt('usage_document_$day') ?? 0;
      total += _prefs.getInt('usage_conversation_$day') ?? 0;
      total += _prefs.getInt('usage_ai_chat_$day') ?? 0;
    }

    return total;
  }

  /// التحقق من تجاوز الحد اليومي
  Future<bool> hasExceededDailyLimit(String featureType, int limit) async {
    final today = DateTime.now().toIso8601String().split('T')[0];
    final key = 'usage_${featureType}_$today';
    final usage = _prefs.getInt(key) ?? 0;

    return usage >= limit;
  }

  /// مسح إحصائيات الاستخدام
  Future<void> clearUsageStats() async {
    try {
      // الحصول على جميع المفاتيح
      final keys = _prefs.getKeys();

      // حذف جميع مفاتيح الاستخدام
      for (final key in keys) {
        if (key.startsWith('usage_')) {
          await _prefs.remove(key);
        }
      }
    } catch (e) {
      debugPrint('Error clearing usage stats: $e');
    }
  }

  /// مزامنة إحصائيات الاستخدام مع Firestore
  Future<void> syncUsageStats() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      // الحصول على جميع المفاتيح
      final keys = _prefs.getKeys();
      final Map<String, Map<String, int>> usageByDate = {};

      // تجميع الإحصائيات حسب التاريخ
      for (final key in keys) {
        if (key.startsWith('usage_')) {
          final parts = key.split('_');
          if (parts.length >= 3) {
            final featureType = parts[1];
            final date = parts[2];

            usageByDate[date] = usageByDate[date] ?? {};
            usageByDate[date]![featureType] = _prefs.getInt(key) ?? 0;
          }
        }
      }

      // تحديث Firestore
      final batch = _firestore.batch();
      usageByDate.forEach((date, features) {
        final docRef = _firestore
            .collection('users')
            .doc(user.uid)
            .collection('usage')
            .doc(date);
        batch.set(docRef, {
          ...features,
          'lastSynced': FieldValue.serverTimestamp(),
        }, SetOptions(merge: true));
      });

      await batch.commit();
    } catch (e) {
      debugPrint('Error syncing usage stats: $e');
    }
  }
}
