import 'package:flutter/material.dart';
import '../utils/responsive_helper.dart';

/// ثيم متجاوب للتطبيق
class ResponsiveTheme {
  
  /// الحصول على ثيم متجاوب
  static ThemeData getResponsiveTheme(BuildContext context) {
    final deviceType = ResponsiveHelper.getDeviceType(context);
    
    return ThemeData(
      useMaterial3: true,
      fontFamily: 'Cairo',
      
      // الألوان الأساسية
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFF58CC02),
        brightness: Brightness.light,
      ),
      
      // ثيم شريط التطبيق
      appBarTheme: AppBarTheme(
        backgroundColor: const Color(0xFF58CC02),
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontSize: _getAppBarTitleSize(deviceType),
          fontWeight: FontWeight.bold,
          color: Colors.white,
          fontFamily: 'Cairo',
        ),
        toolbarHeight: _getAppBarHeight(deviceType),
      ),
      
      // ثيم الأزرار المرفوعة
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          minimumSize: Size(
            double.infinity,
            _getButtonHeight(deviceType),
          ),
          textStyle: TextStyle(
            fontSize: _getButtonTextSize(deviceType),
            fontWeight: FontWeight.bold,
            fontFamily: 'Cairo',
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_getBorderRadius(deviceType)),
          ),
        ),
      ),
      
      // ثيم البطاقات
      cardTheme: CardTheme(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius(deviceType)),
        ),
        margin: EdgeInsets.all(_getCardMargin(deviceType)),
      ),
      
      // ثيم النصوص
      textTheme: TextTheme(
        // العناوين الكبيرة
        headlineLarge: TextStyle(
          fontSize: _getHeadlineLargeSize(deviceType),
          fontWeight: FontWeight.bold,
          color: Colors.black87,
          fontFamily: 'Cairo',
        ),
        
        // العناوين المتوسطة
        headlineMedium: TextStyle(
          fontSize: _getHeadlineMediumSize(deviceType),
          fontWeight: FontWeight.bold,
          color: Colors.black87,
          fontFamily: 'Cairo',
        ),
        
        // العناوين الصغيرة
        headlineSmall: TextStyle(
          fontSize: _getHeadlineSmallSize(deviceType),
          fontWeight: FontWeight.w600,
          color: Colors.black87,
          fontFamily: 'Cairo',
        ),
        
        // النص الأساسي
        bodyLarge: TextStyle(
          fontSize: _getBodyLargeSize(deviceType),
          color: Colors.black87,
          fontFamily: 'Cairo',
        ),
        
        // النص المتوسط
        bodyMedium: TextStyle(
          fontSize: _getBodyMediumSize(deviceType),
          color: Colors.black87,
          fontFamily: 'Cairo',
        ),
        
        // النص الصغير
        bodySmall: TextStyle(
          fontSize: _getBodySmallSize(deviceType),
          color: Colors.grey.shade600,
          fontFamily: 'Cairo',
        ),
        
        // تسميات الأزرار
        labelLarge: TextStyle(
          fontSize: _getButtonTextSize(deviceType),
          fontWeight: FontWeight.bold,
          fontFamily: 'Cairo',
        ),
      ),
      
      // ثيم حقول الإدخال
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius(deviceType)),
        ),
        contentPadding: EdgeInsets.all(_getInputPadding(deviceType)),
        labelStyle: TextStyle(
          fontSize: _getBodyMediumSize(deviceType),
          fontFamily: 'Cairo',
        ),
      ),
      
      // ثيم الأيقونات
      iconTheme: IconThemeData(
        size: _getIconSize(deviceType),
        color: Colors.grey.shade700,
      ),
      
      // ثيم القوائم
      listTileTheme: ListTileThemeData(
        contentPadding: EdgeInsets.symmetric(
          horizontal: _getListTilePadding(deviceType),
          vertical: _getListTilePadding(deviceType) / 2,
        ),
        titleTextStyle: TextStyle(
          fontSize: _getBodyLargeSize(deviceType),
          fontWeight: FontWeight.w500,
          color: Colors.black87,
          fontFamily: 'Cairo',
        ),
        subtitleTextStyle: TextStyle(
          fontSize: _getBodySmallSize(deviceType),
          color: Colors.grey.shade600,
          fontFamily: 'Cairo',
        ),
      ),
      
      // ثيم الحوارات
      dialogTheme: DialogTheme(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius(deviceType) * 1.5),
        ),
        titleTextStyle: TextStyle(
          fontSize: _getHeadlineSmallSize(deviceType),
          fontWeight: FontWeight.bold,
          color: Colors.black87,
          fontFamily: 'Cairo',
        ),
        contentTextStyle: TextStyle(
          fontSize: _getBodyMediumSize(deviceType),
          color: Colors.black87,
          fontFamily: 'Cairo',
        ),
      ),
      
      // ثيم شريط التنقل السفلي
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        selectedItemColor: const Color(0xFF58CC02),
        unselectedItemColor: Colors.grey.shade600,
        selectedLabelStyle: TextStyle(
          fontSize: _getBodySmallSize(deviceType),
          fontWeight: FontWeight.w600,
          fontFamily: 'Cairo',
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: _getBodySmallSize(deviceType),
          fontFamily: 'Cairo',
        ),
      ),
    );
  }
  
  // دوال مساعدة لحساب الأحجام
  
  static double _getAppBarTitleSize(DeviceType deviceType) {
    switch (deviceType) {
      case DeviceType.mobile:
        return 20;
      case DeviceType.tablet:
        return 22;
      case DeviceType.desktop:
        return 24;
    }
  }
  
  static double _getAppBarHeight(DeviceType deviceType) {
    switch (deviceType) {
      case DeviceType.mobile:
        return kToolbarHeight + 10;
      case DeviceType.tablet:
        return kToolbarHeight + 5;
      case DeviceType.desktop:
        return kToolbarHeight;
    }
  }
  
  static double _getButtonHeight(DeviceType deviceType) {
    switch (deviceType) {
      case DeviceType.mobile:
        return 56;
      case DeviceType.tablet:
        return 52;
      case DeviceType.desktop:
        return 48;
    }
  }
  
  static double _getButtonTextSize(DeviceType deviceType) {
    switch (deviceType) {
      case DeviceType.mobile:
        return 16;
      case DeviceType.tablet:
        return 17;
      case DeviceType.desktop:
        return 18;
    }
  }
  
  static double _getBorderRadius(DeviceType deviceType) {
    switch (deviceType) {
      case DeviceType.mobile:
        return 12;
      case DeviceType.tablet:
        return 16;
      case DeviceType.desktop:
        return 20;
    }
  }
  
  static double _getCardMargin(DeviceType deviceType) {
    switch (deviceType) {
      case DeviceType.mobile:
        return 8;
      case DeviceType.tablet:
        return 12;
      case DeviceType.desktop:
        return 16;
    }
  }
  
  static double _getHeadlineLargeSize(DeviceType deviceType) {
    switch (deviceType) {
      case DeviceType.mobile:
        return 28;
      case DeviceType.tablet:
        return 32;
      case DeviceType.desktop:
        return 36;
    }
  }
  
  static double _getHeadlineMediumSize(DeviceType deviceType) {
    switch (deviceType) {
      case DeviceType.mobile:
        return 24;
      case DeviceType.tablet:
        return 26;
      case DeviceType.desktop:
        return 28;
    }
  }
  
  static double _getHeadlineSmallSize(DeviceType deviceType) {
    switch (deviceType) {
      case DeviceType.mobile:
        return 20;
      case DeviceType.tablet:
        return 22;
      case DeviceType.desktop:
        return 24;
    }
  }
  
  static double _getBodyLargeSize(DeviceType deviceType) {
    switch (deviceType) {
      case DeviceType.mobile:
        return 16;
      case DeviceType.tablet:
        return 17;
      case DeviceType.desktop:
        return 18;
    }
  }
  
  static double _getBodyMediumSize(DeviceType deviceType) {
    switch (deviceType) {
      case DeviceType.mobile:
        return 14;
      case DeviceType.tablet:
        return 15;
      case DeviceType.desktop:
        return 16;
    }
  }
  
  static double _getBodySmallSize(DeviceType deviceType) {
    switch (deviceType) {
      case DeviceType.mobile:
        return 12;
      case DeviceType.tablet:
        return 13;
      case DeviceType.desktop:
        return 14;
    }
  }
  
  static double _getIconSize(DeviceType deviceType) {
    switch (deviceType) {
      case DeviceType.mobile:
        return 24;
      case DeviceType.tablet:
        return 28;
      case DeviceType.desktop:
        return 32;
    }
  }
  
  static double _getInputPadding(DeviceType deviceType) {
    switch (deviceType) {
      case DeviceType.mobile:
        return 16;
      case DeviceType.tablet:
        return 18;
      case DeviceType.desktop:
        return 20;
    }
  }
  
  static double _getListTilePadding(DeviceType deviceType) {
    switch (deviceType) {
      case DeviceType.mobile:
        return 16;
      case DeviceType.tablet:
        return 20;
      case DeviceType.desktop:
        return 24;
    }
  }
}
