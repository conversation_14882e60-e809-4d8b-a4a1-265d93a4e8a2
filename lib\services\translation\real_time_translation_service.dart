import 'dart:async';
import 'package:flutter/material.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import '../speech/azure_speech_service.dart';
import '../api/translation_service.dart';

/// خدمة الترجمة الفورية المستمرة
class RealTimeTranslationService {
  final FirebaseRemoteConfig _remoteConfig;
  late final AzureSpeechService _speechService;
  late final TranslationService _translationService;

  bool _isListening = false;
  Timer? _silenceTimer;
  // حساسية الميكروفون (0.0 - 1.0)
  // int _silenceThreshold = 1500; // عتبة الصمت بالمللي ثانية

  // تدفق بيانات الترجمة
  final _translationController =
      StreamController<Map<String, String>>.broadcast();
  Stream<Map<String, String>> get translationStream =>
      _translationController.stream;

  // تدفق بيانات حالة الاستماع
  final _listeningStatusController = StreamController<bool>.broadcast();
  Stream<bool> get listeningStatusStream => _listeningStatusController.stream;

  RealTimeTranslationService(this._remoteConfig) {
    _speechService = AzureSpeechService(_remoteConfig);
    _translationService = TranslationService(_remoteConfig);
  }

  /// بدء الاستماع
  Future<bool> startListening({
    required String sourceLanguage,
    required String targetLanguage,
  }) async {
    if (_isListening) return true;

    try {
      // بدء التسجيل
      final success = await _speechService.startRecording();
      if (!success) {
        return false;
      }

      _isListening = true;
      _listeningStatusController.add(true);

      // بدء المؤقت للكشف عن الصمت
      _startSilenceDetection(sourceLanguage, targetLanguage);

      return true;
    } catch (e) {
      debugPrint('Error starting listening: $e');
      _isListening = false;
      _listeningStatusController.add(false);
      return false;
    }
  }

  /// إيقاف الاستماع
  Future<void> stopListening() async {
    if (!_isListening) return;

    try {
      _cancelSilenceDetection();

      // إيقاف التسجيل والتعرف على الكلام
      final recognizedText = await _speechService.stopRecordingAndRecognize(
        languageCode: 'ar', // يمكن تغييره حسب اللغة المصدر
      );

      _isListening = false;
      _listeningStatusController.add(false);

      // إذا تم التعرف على نص، قم بترجمته
      if (recognizedText.isNotEmpty) {
        await _translateAndEmit(recognizedText, 'ar', 'en');
      }
    } catch (e) {
      debugPrint('Error stopping listening: $e');
      _isListening = false;
      _listeningStatusController.add(false);
    }
  }

  /// بدء الكشف عن الصمت
  void _startSilenceDetection(String sourceLanguage, String targetLanguage) {
    _cancelSilenceDetection();

    _silenceTimer = Timer.periodic(const Duration(milliseconds: 500), (
      timer,
    ) async {
      // في التطبيق الفعلي، هنا سنقوم بالتحقق من مستوى الصوت
      // إذا كان مستوى الصوت أقل من العتبة لفترة معينة، نعتبره صمتًا

      // محاكاة الكشف عن الصمت
      final isSilent =
          DateTime.now().millisecond < 200; // 20% احتمالية اكتشاف الصمت

      if (isSilent) {
        // إيقاف التسجيل مؤقتًا والتعرف على الكلام
        final recognizedText = await _speechService.stopRecordingAndRecognize(
          languageCode: sourceLanguage,
        );

        // إذا تم التعرف على نص، قم بترجمته
        if (recognizedText.isNotEmpty) {
          await _translateAndEmit(
            recognizedText,
            sourceLanguage,
            targetLanguage,
          );
        }

        // إعادة بدء التسجيل
        if (_isListening) {
          await _speechService.startRecording();
        }
      }
    });
  }

  /// إلغاء الكشف عن الصمت
  void _cancelSilenceDetection() {
    _silenceTimer?.cancel();
    _silenceTimer = null;
  }

  /// ترجمة النص وإرساله إلى التدفق
  Future<void> _translateAndEmit(
    String text,
    String sourceLanguage,
    String targetLanguage,
  ) async {
    try {
      // ترجمة النص
      final translatedText = await _translationService.translateText(
        text: text,
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
      );

      // إرسال النص الأصلي والمترجم إلى التدفق
      _translationController.add({
        'sourceText': text,
        'translatedText': translatedText,
      });
    } catch (e) {
      debugPrint('Error translating text: $e');
    }
  }

  /// تعيين حساسية الميكروفون
  void setMicrophoneSensitivity(double sensitivity) {
    // في التطبيق الفعلي، هنا سنقوم بتعديل حساسية الميكروفون
    double micSensitivity = sensitivity.clamp(0.0, 1.0);
    debugPrint('تم تعيين حساسية الميكروفون إلى: $micSensitivity');
  }

  /// تعيين عتبة الصمت
  void setSilenceThreshold(int milliseconds) {
    // في التطبيق الفعلي، هنا سنقوم بتعديل عتبة الصمت
    int silenceThreshold = milliseconds.clamp(500, 5000);
    debugPrint('تم تعيين عتبة الصمت إلى: $silenceThreshold مللي ثانية');
  }

  /// التخلص من الموارد
  void dispose() {
    _cancelSilenceDetection();
    _translationController.close();
    _listeningStatusController.close();
    _speechService.dispose();
  }
}
