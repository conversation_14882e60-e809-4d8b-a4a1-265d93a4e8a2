/// ملف الثوابت للتطبيق
class AppConstants {
  // عام
  static const String appName = 'المترجم الذكي AI';
  static const String appVersion = '1.0.0';

  // مفاتيح التخزين المحلي
  static const String keyIsFirstTime = 'is_first_time';
  static const String keyIsLoggedIn = 'is_logged_in';
  static const String keyUserData = 'user_data';
  static const String keySelectedLanguage = 'selected_language';
  static const String keyIsDarkMode = 'is_dark_mode';
  static const String keyThemeMode =
      'theme_mode'; // مفتاح وضع السمة (فاتح، داكن، AMOLED)
  static const String keySubscriptionStatus = 'subscription_status';
  static const String keyRegistrationDate = 'registration_date';
  static const String keyFavorites = 'favorites';
  static const String keyTranslationHistory = 'translation_history';
  static const String keyTourismFavorites = 'tourism_favorites';

  // قيم وضع السمة
  static const String themeModeLight = 'light';
  static const String themeModeDark = 'dark';
  static const String themeModeAmoled = 'amoled';

  // مفاتيح Firebase Remote Config
  static const String keyOpenAIApiKey = 'openai_api_key';
  static const String keyGeminiApiKey = 'gemini_api_key';
  static const String keyOpenRouterApiKey = 'openrouter_api_key';
  static const String keyGoogleTranslateApiKey = 'google_translate_api_key';
  static const String keyActiveApiService = 'active_api_service';
  static const String keyOpenAIEndDate = 'openai_end_date';
  static const String keyGeminiEndDate = 'gemini_end_date';
  static const String keyDailyLimitOpenAI = 'daily_limit_openai';
  static const String keyDailyLimitGemini = 'daily_limit_gemini';
  static const String keyDailyLimitOpenRouter = 'daily_limit_openrouter';
  static const String keyShowAds = 'show_ads';
  static const String keyAdFrequency = 'ad_frequency';

  // مفاتيح خدمات التعرف على الكلام
  static const String keyAzureSpeechKey = 'azure_speech_key';
  static const String keyAzureSpeechRegion = 'azure_speech_region';
  static const String keyGoogleSpeechKey = 'google_speech_key';

  // أنواع خدمات API
  static const String apiServiceOpenAI = 'openai';
  static const String apiServiceGemini = 'gemini';
  static const String apiServiceOpenRouter = 'openrouter';
  static const String apiServiceGoogleTranslate = 'google_translate';

  // مفاتيح API الحقيقية
  static const String keyGeminiApiKeyPaid =
      'AIzaSyDzPBZY2TeUIm6L3WVRyF_3DkeTJpx-988'; // المفتاح المدفوع الأساسي
  static const String keyGeminiApiKeyFree =
      'AIzaSyDyTjcLnqnqAZ71WzXKwWm-dqQTidhGUoQ'; // المفتاح المجاني الاحتياطي

  // مفاتيح API إضافية
  static const String keyOpenAIApiKeyDirect =
      'sk-your-openai-key-here'; // يحتاج إضافة
  static const String keyOpenRouterApiKeyDirect =
      'sk-or-your-openrouter-key-here'; // يحتاج إضافة

  // روابط
  static const String privacyPolicyUrl = 'https://example.com/privacy-policy';
  static const String termsOfServiceUrl =
      'https://example.com/terms-of-service';
  static const String supportUrl = 'https://example.com/support';
  static const String contactUsUrl = 'https://example.com/contact';

  // معرفات المنتجات للاشتراكات
  static const String productIdRemoveAds = 'remove_ads_subscription';
  static const String productIdPremium = 'premium_subscription';

  // معرفات الإعلانات
  static const String adUnitIdBanner =
      'ca-app-pub-3940256099942544/6300978111'; // معرف تجريبي
  static const String adUnitIdInterstitial =
      'ca-app-pub-3940256099942544/1033173712'; // معرف تجريبي
  static const String adUnitIdRewarded =
      'ca-app-pub-3940256099942544/5224354917'; // معرف تجريبي

  // اللغات المدعومة
  static const List<Map<String, dynamic>> supportedLanguages = [
    {'code': 'ar', 'name': 'العربية', 'flag': '🇸🇦'},
    {'code': 'en', 'name': 'English', 'flag': '🇺🇸'},
    {'code': 'fr', 'name': 'Français', 'flag': '🇫🇷'},
    {'code': 'es', 'name': 'Español', 'flag': '🇪🇸'},
    {'code': 'de', 'name': 'Deutsch', 'flag': '🇩🇪'},
    {'code': 'it', 'name': 'Italiano', 'flag': '🇮🇹'},
    {'code': 'ru', 'name': 'Русский', 'flag': '🇷🇺'},
    {'code': 'zh', 'name': '中文', 'flag': '🇨🇳'},
    {'code': 'ja', 'name': '日本語', 'flag': '🇯🇵'},
    {'code': 'ko', 'name': '한국어', 'flag': '🇰🇷'},
    {'code': 'tr', 'name': 'Türkçe', 'flag': '🇹🇷'},
    {'code': 'pt', 'name': 'Português', 'flag': '🇵🇹'},
    {'code': 'hi', 'name': 'हिन्दी', 'flag': '🇮🇳'},
    {'code': 'ur', 'name': 'اردو', 'flag': '🇵🇰'},
    {'code': 'fa', 'name': 'فارسی', 'flag': '🇮🇷'},
    {'code': 'nl', 'name': 'Nederlands', 'flag': '🇳🇱'},
    {'code': 'sv', 'name': 'Svenska', 'flag': '🇸🇪'},
    {'code': 'da', 'name': 'Dansk', 'flag': '🇩🇰'},
    {'code': 'no', 'name': 'Norsk', 'flag': '🇳🇴'},
    {'code': 'fi', 'name': 'Suomi', 'flag': '🇫🇮'},
    {'code': 'pl', 'name': 'Polski', 'flag': '🇵🇱'},
    {'code': 'cs', 'name': 'Čeština', 'flag': '🇨🇿'},
    {'code': 'sk', 'name': 'Slovenčina', 'flag': '🇸🇰'},
    {'code': 'hu', 'name': 'Magyar', 'flag': '🇭🇺'},
    {'code': 'ro', 'name': 'Română', 'flag': '🇷🇴'},
    {'code': 'bg', 'name': 'Български', 'flag': '🇧🇬'},
    {'code': 'el', 'name': 'Ελληνικά', 'flag': '🇬🇷'},
    {'code': 'he', 'name': 'עברית', 'flag': '🇮🇱'},
    {'code': 'th', 'name': 'ไทย', 'flag': '🇹🇭'},
    {'code': 'vi', 'name': 'Tiếng Việt', 'flag': '🇻🇳'},
    {'code': 'id', 'name': 'Bahasa Indonesia', 'flag': '🇮🇩'},
    {'code': 'ms', 'name': 'Bahasa Melayu', 'flag': '🇲🇾'},
    {'code': 'fil', 'name': 'Filipino', 'flag': '🇵🇭'},
    {'code': 'uk', 'name': 'Українська', 'flag': '🇺🇦'},
    {'code': 'sw', 'name': 'Kiswahili', 'flag': '🇰🇪'},
    {'code': 'am', 'name': 'አማርኛ', 'flag': '🇪🇹'},
  ];

  // أنواع الترجمة
  static const List<Map<String, dynamic>> translationTypes = [
    {'id': 'voice', 'title': 'ترجمة صوتية', 'icon': 'mic'},
    {'id': 'text', 'title': 'ترجمة نصوص', 'icon': 'text_fields'},
    {'id': 'image', 'title': 'ترجمة صور', 'icon': 'camera_alt'},
    {'id': 'document', 'title': 'ترجمة مستندات', 'icon': 'description'},
    {'id': 'conversation', 'title': 'ترجمة محادثات', 'icon': 'forum'},
    {
      'id': 'real_time_conversation',
      'title': 'ترجمة فورية مستمرة',
      'icon': 'record_voice_over',
    },
    {
      'id': 'multi_speaker',
      'title': 'محادثات متعددة المتحدثين',
      'icon': 'groups',
    },
    {'id': 'kids_mode', 'title': 'وضع الأطفال', 'icon': 'child_care'},
    {'id': 'duolingo_mode', 'title': 'تعلم اللغات', 'icon': 'school'},
    {'id': 'tourism_mode', 'title': 'وضع السياحة', 'icon': 'flight'},
    {'id': 'ai_chat', 'title': 'محادثة ذكية', 'icon': 'smart_toy'},
  ];

  // أنواع الاستجابة للذكاء الاصطناعي
  static const List<Map<String, dynamic>> aiResponseTypes = [
    {'id': 'general', 'title': 'عام'},
    {'id': 'islamic', 'title': 'إسلامي'},
    {'id': 'translation', 'title': 'ترجمة'},
    {'id': 'kids', 'title': 'أطفال'},
    {'id': 'tourism', 'title': 'سياحة'},
  ];
}
