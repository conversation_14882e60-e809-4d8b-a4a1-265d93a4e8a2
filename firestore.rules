rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // قواعد المستخدمين - يمكن للمستخدم قراءة وكتابة بياناته فقط
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // قواعد الترجمات - يمكن للمستخدم قراءة وكتابة ترجماته فقط
    match /translations/{translationId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
    }
    
    // قواعد الاشتراكات - يمكن للمستخدم قراءة وكتابة اشتراكه فقط
    match /subscriptions/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // قواعد إحصائيات الاستخدام - يمكن للمستخدم قراءة وكتابة إحصائياته فقط
    match /usage_stats/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // قواعد العبارات السياحية المفضلة - يمكن للمستخدم قراءة وكتابة مفضلاته فقط
    match /tourism_favorites/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // قواعد تاريخ الدردشة مع الذكاء الاصطناعي - يمكن للمستخدم قراءة وكتابة تاريخه فقط
    match /chat_history/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // قواعد عامة - منع الوصول لأي مجموعة أخرى
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
