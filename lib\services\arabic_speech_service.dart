import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_tts/flutter_tts.dart';

/// خدمة النطق العربي المحسنة - حل جذري لمشاكل النطق العربي
class ArabicSpeechService {
  static final ArabicSpeechService _instance = ArabicSpeechService._internal();
  factory ArabicSpeechService() => _instance;
  ArabicSpeechService._internal();

  late FlutterTts _tts;
  bool _isInitialized = false;
  bool _isSpeaking = false;
  Timer? _speechTimer;
  Completer<void>? _speechCompleter;

  /// تهيئة خدمة النطق العربي
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🚀 تهيئة خدمة النطق العربي المحسنة');

      _tts = FlutterTts();

      // إعداد معالجات الأحداث
      await _setupEventHandlers();

      // تهيئة الإعدادات الأساسية
      await _initializeBasicSettings();

      _isInitialized = true;
      debugPrint('✅ تم تهيئة خدمة النطق العربي بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة النطق العربي: $e');
      rethrow;
    }
  }

  /// إعداد معالجات الأحداث
  Future<void> _setupEventHandlers() async {
    try {
      // معالج بداية النطق
      _tts.setStartHandler(() {
        debugPrint('🎤 بدء النطق العربي - الصوت يجب أن يكون مسموعاً الآن');
        _isSpeaking = true;
      });

      // معالج انتهاء النطق
      _tts.setCompletionHandler(() {
        debugPrint('✅ انتهى النطق العربي بنجاح - تم تشغيل الصوت');
        _isSpeaking = false;
        _speechTimer?.cancel();
        if (_speechCompleter != null && !_speechCompleter!.isCompleted) {
          _speechCompleter!.complete();
        }
      });

      // معالج الأخطاء
      _tts.setErrorHandler((msg) {
        debugPrint('❌ خطأ في النطق العربي: $msg');
        debugPrint(
          '🔧 قد يكون السبب: مشكلة في الصوت أو اللغة العربية غير مدعومة',
        );
        _isSpeaking = false;
        _speechTimer?.cancel();
        if (_speechCompleter != null && !_speechCompleter!.isCompleted) {
          _speechCompleter!.completeError(Exception('خطأ في النطق: $msg'));
        }
      });

      // معالج الإيقاف المؤقت
      _tts.setPauseHandler(() {
        debugPrint('⏸️ تم إيقاف النطق مؤقتاً');
      });

      // معالج الاستكمال
      _tts.setContinueHandler(() {
        debugPrint('▶️ تم استكمال النطق');
      });
    } catch (e) {
      debugPrint('❌ خطأ في إعداد معالجات الأحداث: $e');
    }
  }

  /// تهيئة الإعدادات الأساسية
  Future<void> _initializeBasicSettings() async {
    try {
      // إعدادات أساسية للنطق العربي
      await _tts.setVolume(1.0);
      await _tts.setSpeechRate(0.6);
      await _tts.setPitch(1.0);

      // محاولة تعيين اللغة العربية
      await _setArabicLanguage();
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة الإعدادات الأساسية: $e');
    }
  }

  /// تعيين اللغة العربية مع محاولات متعددة
  Future<bool> _setArabicLanguage() async {
    final arabicCodes = [
      'ar-SA', // السعودية (الأولوية الأولى)
      'ar-EG', // مصر
      'ar-AE', // الإمارات
      'ar-JO', // الأردن
      'ar-LB', // لبنان
      'ar', // عربي عام
      'ar_SA', // تنسيق بديل
      'ar_EG', // تنسيق بديل
    ];

    for (final code in arabicCodes) {
      try {
        debugPrint('🔧 محاولة تعيين اللغة العربية: $code');
        final result = await _tts.setLanguage(code);

        if (result == 1) {
          debugPrint('✅ نجح تعيين اللغة العربية: $code');
          return true;
        } else {
          debugPrint('⚠️ فشل تعيين اللغة $code - النتيجة: $result');
        }
      } catch (e) {
        debugPrint('❌ خطأ في تعيين اللغة $code: $e');
        continue;
      }
    }

    debugPrint('❌ فشل في تعيين أي لغة عربية');
    return false;
  }

  /// النطق العربي الرئيسي - الحل الجذري
  Future<void> speakArabic(String text) async {
    if (!_isInitialized) {
      await initialize();
    }

    if (text.trim().isEmpty) {
      debugPrint('⚠️ النص فارغ، تم تجاهل النطق');
      return;
    }

    try {
      debugPrint('🇸🇦 بدء النطق العربي المحسن للنص: "$text"');

      // إيقاف أي نطق جاري
      await stopSpeaking();

      // تطبيق الحل الجذري حسب المنصة
      if (kIsWeb) {
        await _speakArabicWeb(text);
      } else {
        await _speakArabicMobile(text);
      }
    } catch (e) {
      debugPrint('❌ خطأ في النطق العربي: $e');
      // محاولة بديلة أخيرة
      await _fallbackSpeak(text);
    }
  }

  /// النطق العربي في بيئة الويب - حل محسن مع صوت مسموع
  Future<void> _speakArabicWeb(String text) async {
    try {
      debugPrint('🌐 النطق العربي في بيئة الويب مع صوت مسموع');

      // إعداد مهلة زمنية للنطق
      _speechCompleter = Completer<void>();

      // تعيين مؤقت للمهلة الزمنية أطول
      _speechTimer = Timer(const Duration(seconds: 12), () {
        if (!_speechCompleter!.isCompleted) {
          debugPrint('⏰ انتهت المهلة الزمنية للنطق');
          _speechCompleter!.complete();
        }
      });

      // تعيين اللغة والإعدادات مع التأكد من الصوت
      await _setArabicLanguage();

      // إعدادات محسنة للصوت المسموع
      await _tts.setSpeechRate(0.7); // سرعة أبطأ للوضوح
      await _tts.setPitch(1.1); // نبرة أعلى للوضوح
      await _tts.setVolume(1.0); // أقصى صوت

      // انتظار لتطبيق الإعدادات
      await Future.delayed(const Duration(milliseconds: 500));

      debugPrint('🔊 بدء النطق العربي بصوت مسموع للنص: "$text"');

      // بدء النطق مع التحقق من النتيجة
      final result = await _tts.speak(text);
      debugPrint('📢 نتيجة النطق العربي: $result');

      // التحقق من نجاح النطق
      if (result == 1) {
        debugPrint('✅ تم بدء النطق العربي بنجاح');
      } else {
        debugPrint('⚠️ قد يكون هناك مشكلة في النطق، النتيجة: $result');
      }

      // انتظار أطول للسماح للنطق بالاكتمال
      await Future.delayed(const Duration(milliseconds: 3000));

      // انتظار اكتمال النطق أو انتهاء المهلة
      await _speechCompleter!.future;
    } catch (e) {
      debugPrint('❌ خطأ في النطق العربي للويب: $e');
      rethrow;
    } finally {
      _speechTimer?.cancel();
      _speechCompleter = null;
    }
  }

  /// النطق العربي للمنصات المحمولة
  Future<void> _speakArabicMobile(String text) async {
    try {
      debugPrint('📱 النطق العربي للمنصات المحمولة');

      // تعيين اللغة العربية
      final languageSet = await _setArabicLanguage();
      if (!languageSet) {
        throw Exception('فشل في تعيين اللغة العربية');
      }

      // إعدادات محسنة للمنصات المحمولة
      await _tts.setSpeechRate(0.65);
      await _tts.setPitch(1.0);
      await _tts.setVolume(1.0);

      // انتظار قصير لتطبيق الإعدادات
      await Future.delayed(const Duration(milliseconds: 300));

      // بدء النطق
      final result = await _tts.speak(text);

      if (result != 1) {
        throw Exception('فشل في النطق - النتيجة: $result');
      }

      debugPrint('✅ نجح النطق العربي للمنصات المحمولة');
    } catch (e) {
      debugPrint('❌ خطأ في النطق العربي للمنصات المحمولة: $e');
      rethrow;
    }
  }

  /// النطق البديل عند فشل النطق الأساسي
  Future<void> _fallbackSpeak(String text) async {
    try {
      debugPrint('🆘 محاولة النطق البديل للعربية');

      // محاولة مع إعدادات مبسطة
      await _tts.setLanguage('ar');
      await _tts.setSpeechRate(0.5);
      await _tts.setPitch(1.0);
      await _tts.setVolume(1.0);

      await Future.delayed(const Duration(milliseconds: 500));

      final result = await _tts.speak(text);

      if (result == 1) {
        debugPrint('✅ نجح النطق البديل');
      } else {
        debugPrint('❌ فشل النطق البديل أيضاً');
        // كحل أخير، عرض النص فقط
        debugPrint('📝 النص العربي: $text');
      }
    } catch (e) {
      debugPrint('❌ فشل في النطق البديل: $e');
    }
  }

  /// إيقاف النطق
  Future<void> stopSpeaking() async {
    try {
      if (_isSpeaking) {
        await _tts.stop();
        _isSpeaking = false;
        _speechTimer?.cancel();

        if (_speechCompleter != null && !_speechCompleter!.isCompleted) {
          _speechCompleter!.complete();
        }

        debugPrint('⏹️ تم إيقاف النطق');
      }
    } catch (e) {
      debugPrint('❌ خطأ في إيقاف النطق: $e');
    }
  }

  /// التحقق من حالة النطق
  bool get isSpeaking => _isSpeaking;

  /// تنظيف الموارد
  void dispose() {
    _speechTimer?.cancel();
    _speechCompleter = null;
    _isSpeaking = false;
    debugPrint('🧹 تم تنظيف موارد خدمة النطق العربي');
  }
}
