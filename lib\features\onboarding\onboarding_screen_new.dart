import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:lottie/lottie.dart';
import '../../config/app_theme.dart';
import '../../widgets/three_d_button.dart';
import '../../widgets/three_d_transition.dart';
import '../home/<USER>';
import 'onboarding_data.dart';

/// صفحة الترحيب
class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  bool _isLastPage = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  /// الانتقال إلى الصفحة التالية
  void _nextPage() {
    if (_isLastPage) {
      _navigateToHome();
    } else {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  /// الانتقال إلى الصفحة السابقة
  void _previousPage() {
    _pageController.previousPage(
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  /// تخطي الترحيب والانتقال إلى الصفحة الرئيسية
  void _skipOnboarding() {
    _navigateToHome();
  }

  /// الانتقال إلى الصفحة الرئيسية
  void _navigateToHome() {
    Navigator.of(context).pushReplacement(
      Transition3D(page: const HomeScreen(), type: TransitionType.slide3D),
    );
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.white, AppTheme.primaryColor.withAlpha(50)],
          ),
        ),
        child: SafeArea(
          child: Stack(
            children: [
              // محتوى الصفحات
              PageView.builder(
                controller: _pageController,
                itemCount: onboardingData.length,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                    _isLastPage = index == onboardingData.length - 1;
                  });
                },
                itemBuilder: (context, index) {
                  final data = onboardingData[index];
                  return _buildOnboardingPage(data, size);
                },
              ),

              // زر التخطي
              if (!_isLastPage)
                Positioned(
                  top: 20,
                  right: 20,
                  child: TextButton(
                    onPressed: _skipOnboarding,
                    child: Text(
                      'تخطي',
                      style: TextStyle(
                        color: AppTheme.primaryColor,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),

              // زر الرجوع
              if (_currentPage > 0)
                Positioned(
                  top: 20,
                  left: 20,
                  child: CircleButton3D(
                    icon: Icons.arrow_back_ios_new,
                    size: 48,
                    iconSize: 20,
                    onPressed: _previousPage,
                    color: Colors.white,
                    iconColor: AppTheme.primaryColor,
                    depth: 2,
                  ),
                ),

              // مؤشرات الصفحات
              Positioned(
                bottom: 120,
                left: 0,
                right: 0,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    onboardingData.length,
                    (index) => AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      margin: const EdgeInsets.symmetric(horizontal: 5),
                      width: _currentPage == index ? 30 : 10,
                      height: 10,
                      decoration: BoxDecoration(
                        color:
                            _currentPage == index
                                ? AppTheme.primaryColor
                                : AppTheme.primaryColor.withAlpha(100),
                        borderRadius: BorderRadius.circular(5),
                      ),
                    ),
                  ),
                ),
              ),

              // زر التالي
              Positioned(
                bottom: 40,
                left: 0,
                right: 0,
                child: Center(
                  child: Button3D(
                    text: _isLastPage ? 'ابدأ الآن' : 'التالي',
                    icon: _isLastPage ? Icons.check : Icons.arrow_forward_ios,
                    width: size.width * 0.8,
                    onPressed: _nextPage,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء صفحة الترحيب
  Widget _buildOnboardingPage(OnboardingItem data, Size size) {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // الرسوم المتحركة
          SizedBox(
                height: size.height * 0.4,
                child: Lottie.asset(data.animationPath, fit: BoxFit.contain),
              )
              .animate()
              .fadeIn(duration: 600.ms)
              .scale(
                duration: 600.ms,
                curve: Curves.easeOutBack,
                begin: const Offset(0.8, 0.8),
                end: const Offset(1.0, 1.0),
              ),

          const SizedBox(height: 40),

          // العنوان
          Text(
            data.title,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: AppTheme.onBackground,
            ),
          ).animate().fadeIn(delay: 200.ms, duration: 600.ms),

          const SizedBox(height: 20),

          // الوصف
          Text(
            data.description,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              color: AppTheme.onSurfaceVariant,
              height: 1.5,
            ),
          ).animate().fadeIn(delay: 400.ms, duration: 600.ms),

          const SizedBox(height: 60),
        ],
      ),
    );
  }
}
