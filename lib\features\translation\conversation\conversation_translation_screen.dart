import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../../../core/app_state.dart';
import '../../../services/api/translation_service.dart';
import '../../../config/app_theme.dart';
import '../../../utils/helpers.dart';
import '../../../widgets/three_d_button.dart';
import 'conversation_translation_service.dart';

/// نموذج رسالة المحادثة
class ConversationMessage {
  final String text;
  final String translatedText;
  final String languageCode;
  final bool isUser1;
  final DateTime timestamp;

  ConversationMessage({
    required this.text,
    required this.translatedText,
    required this.languageCode,
    required this.isUser1,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();
}

/// شاشة ترجمة المحادثات
class ConversationTranslationScreen extends StatefulWidget {
  const ConversationTranslationScreen({super.key});

  @override
  State<ConversationTranslationScreen> createState() =>
      _ConversationTranslationScreenState();
}

class _ConversationTranslationScreenState
    extends State<ConversationTranslationScreen>
    with SingleTickerProviderStateMixin {
  final _conversationService = ConversationTranslationService();
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final List<ConversationMessage> _messages = [];

  String _user1Language = 'ar';
  String _user2Language = 'en';
  String _user1Name = 'أنا';
  String _user2Name = 'الآخر';
  bool _isListening = false;
  bool _isTranslating = false;
  bool _isSpeaking = false;
  bool _isUser1Active = true;
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );

    // تهيئة خدمة الترجمة الصوتية
    _initVoiceService();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _messageController.dispose();
    _scrollController.dispose();
    _conversationService.dispose();
    super.dispose();
  }

  /// تهيئة خدمة الترجمة الصوتية
  Future<void> _initVoiceService() async {
    try {
      await _conversationService.initialize();
    } catch (e) {
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء تهيئة خدمة الترجمة الصوتية: $e',
          isError: true,
        );
      }
    }
  }

  /// بدء الاستماع
  Future<void> _startListening() async {
    try {
      setState(() {
        _isListening = true;
      });

      _animationController.repeat(reverse: true);

      // بدء الاستماع
      await _conversationService.startListening(
        languageCode: _isUser1Active ? _user1Language : _user2Language,
        onResult: (text) {
          setState(() {
            _messageController.text = text;
          });
        },
        onDone: () {
          setState(() {
            _isListening = false;
          });
          _animationController.stop();
          _animationController.reset();

          // إرسال الرسالة تلقائيًا إذا كان هناك نص
          if (_messageController.text.isNotEmpty) {
            _sendMessage();
          }
        },
      );
    } catch (e) {
      setState(() {
        _isListening = false;
      });
      _animationController.stop();
      _animationController.reset();

      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء الاستماع: $e',
          isError: true,
        );
      }
    }
  }

  /// إيقاف الاستماع
  Future<void> _stopListening() async {
    try {
      await _conversationService.stopListening();

      setState(() {
        _isListening = false;
      });

      _animationController.stop();
      _animationController.reset();
    } catch (e) {
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء إيقاف الاستماع: $e',
          isError: true,
        );
      }
    }
  }

  /// إرسال رسالة
  Future<void> _sendMessage() async {
    final text = _messageController.text.trim();
    if (text.isEmpty) return;

    setState(() {
      _isTranslating = true;
    });

    try {
      final appState = Provider.of<AppState>(context, listen: false);
      final remoteConfig = appState.remoteConfig;
      final translationService = TranslationService(remoteConfig);
      _conversationService.setTranslationService(translationService);
      final sourceLanguage = _isUser1Active ? _user1Language : _user2Language;
      final targetLanguage = _isUser1Active ? _user2Language : _user1Language;
      final translatedText = await _conversationService.translateText(
        text: text,
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
      );

      if (mounted) {
        setState(() {
          _messages.add(
            ConversationMessage(
              text: text,
              translatedText: translatedText,
              languageCode: sourceLanguage,
              isUser1: _isUser1Active,
            ),
          );
          _messageController.clear();
          _isTranslating = false;
        });

        _scrollToBottom();
        _speakTranslatedText(translatedText, targetLanguage);
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isTranslating = false;
        });
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء الترجمة: $e',
          isError: true,
        );
      }
    }
  }

  /// نطق النص المترجم
  Future<void> _speakTranslatedText(String text, String languageCode) async {
    if (text.isEmpty || _isSpeaking) return;

    setState(() {
      _isSpeaking = true;
    });

    try {
      await _conversationService.speakText(
        text: text,
        languageCode: languageCode,
        onDone: () {
          if (mounted) {
            setState(() {
              _isSpeaking = false;
            });
          }
        },
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _isSpeaking = false;
        });
      }
    }
  }

  // تم إزالة دالة _stopSpeaking لأنها غير مستخدمة

  /// التمرير إلى أسفل المحادثة
  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  /// تبديل المستخدم النشط
  void _toggleActiveUser() {
    setState(() {
      _isUser1Active = !_isUser1Active;
      _messageController.clear();
    });
  }

  /// نسخ النص
  void _copyText(String text) {
    Clipboard.setData(ClipboardData(text: text));
    AppHelpers.showSnackBar(context, 'تم نسخ النص');
  }

  /// الحصول على اسم اللغة
  String _getLanguageName(String code) {
    switch (code) {
      case 'ar':
        return '🇸🇦 العربية';
      case 'en':
        return '🇺🇸 English';
      case 'fr':
        return '🇫🇷 Français';
      case 'es':
        return '🇪🇸 Español';
      case 'de':
        return '🇩🇪 Deutsch';
      case 'it':
        return '🇮🇹 Italiano';
      case 'ru':
        return '🇷🇺 Русский';
      case 'zh':
        return '🇨🇳 中文';
      case 'ja':
        return '🇯🇵 日本語';
      case 'ko':
        return '🇰🇷 한국어';
      default:
        return code;
    }
  }

  /// عرض مربع حوار اختيار اللغة
  void _showLanguageSelector(bool isUser1) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                isUser1 ? 'اختر لغة المستخدم 1' : 'اختر لغة المستخدم 2',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              Expanded(
                child: ListView(
                  children: [
                    _buildLanguageItem(context, 'ar', isUser1),
                    _buildLanguageItem(context, 'en', isUser1),
                    _buildLanguageItem(context, 'fr', isUser1),
                    _buildLanguageItem(context, 'es', isUser1),
                    _buildLanguageItem(context, 'de', isUser1),
                    _buildLanguageItem(context, 'it', isUser1),
                    _buildLanguageItem(context, 'ru', isUser1),
                    _buildLanguageItem(context, 'zh', isUser1),
                    _buildLanguageItem(context, 'ja', isUser1),
                    _buildLanguageItem(context, 'ko', isUser1),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// بناء عنصر اللغة
  Widget _buildLanguageItem(BuildContext context, String code, bool isUser1) {
    final isSelected =
        isUser1 ? _user1Language == code : _user2Language == code;

    return ListTile(
      title: Text(_getLanguageName(code)),
      trailing:
          isSelected
              ? const Icon(Icons.check, color: AppTheme.primaryColor)
              : null,
      onTap: () {
        setState(() {
          if (isUser1) {
            _user1Language = code;
          } else {
            _user2Language = code;
          }
        });
        Navigator.pop(context);
      },
    );
  }

  /// عرض مربع حوار تعديل الأسماء
  void _showEditNamesDialog() {
    final user1Controller = TextEditingController(text: _user1Name);
    final user2Controller = TextEditingController(text: _user2Name);

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تعديل الأسماء'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: user1Controller,
                decoration: InputDecoration(
                  labelText: 'اسم المستخدم 1',
                  hintText: 'أدخل اسم المستخدم 1',
                  prefixIcon: const Icon(Icons.person),
                  suffixText: _getLanguageName(_user1Language),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: user2Controller,
                decoration: InputDecoration(
                  labelText: 'اسم المستخدم 2',
                  hintText: 'أدخل اسم المستخدم 2',
                  prefixIcon: const Icon(Icons.person),
                  suffixText: _getLanguageName(_user2Language),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _user1Name =
                      user1Controller.text.trim().isNotEmpty
                          ? user1Controller.text.trim()
                          : 'أنا';
                  _user2Name =
                      user2Controller.text.trim().isNotEmpty
                          ? user2Controller.text.trim()
                          : 'الآخر';
                });
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
              ),
              child: const Text('حفظ'),
            ),
          ],
        );
      },
    );
  }

  /// تنسيق التاريخ
  String _formatTimestamp(DateTime timestamp) {
    return DateFormat('HH:mm').format(timestamp);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('ترجمة المحادثات'),
        backgroundColor: AppTheme.primaryColor,
        actions: [
          // زر تعديل الأسماء
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: _showEditNamesDialog,
            tooltip: 'تعديل الأسماء',
          ),
        ],
      ),
      body: Column(
        children: [
          // اختيار اللغات
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(10),
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                // المستخدم 1
                Expanded(
                  child: InkWell(
                    onTap: () => _showLanguageSelector(true),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color:
                            _isUser1Active
                                ? AppTheme.primaryColor.withAlpha(30)
                                : Colors.transparent,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color:
                              _isUser1Active
                                  ? AppTheme.primaryColor
                                  : Colors.transparent,
                          width: 1,
                        ),
                      ),
                      child: Column(
                        children: [
                          Text(
                            _user1Name,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color:
                                  _isUser1Active
                                      ? AppTheme.primaryColor
                                      : AppTheme.onBackground,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _getLanguageName(_user1Language),
                            style: TextStyle(
                              fontSize: 12,
                              color:
                                  _isUser1Active
                                      ? AppTheme.primaryColor
                                      : AppTheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                // زر تبديل المستخدم النشط
                IconButton(
                  icon: const Icon(Icons.swap_horiz),
                  onPressed: _toggleActiveUser,
                  color: AppTheme.primaryColor,
                ),

                // المستخدم 2
                Expanded(
                  child: InkWell(
                    onTap: () => _showLanguageSelector(false),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color:
                            !_isUser1Active
                                ? AppTheme.primaryColor.withAlpha(30)
                                : Colors.transparent,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color:
                              !_isUser1Active
                                  ? AppTheme.primaryColor
                                  : Colors.transparent,
                          width: 1,
                        ),
                      ),
                      child: Column(
                        children: [
                          Text(
                            _user2Name,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color:
                                  !_isUser1Active
                                      ? AppTheme.primaryColor
                                      : AppTheme.onBackground,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _getLanguageName(_user2Language),
                            style: TextStyle(
                              fontSize: 12,
                              color:
                                  !_isUser1Active
                                      ? AppTheme.primaryColor
                                      : AppTheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // قائمة الرسائل
          Expanded(
            child:
                _messages.isEmpty
                    ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.chat,
                            size: 80,
                            color: AppTheme.primaryColor.withAlpha(100),
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'ابدأ محادثة مترجمة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'اكتب أو تحدث وسيتم ترجمة رسائلك تلقائيًا',
                            style: TextStyle(
                              fontSize: 14,
                              color: AppTheme.onSurfaceVariant,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    )
                    : ListView.builder(
                      controller: _scrollController,
                      padding: const EdgeInsets.all(16),
                      itemCount: _messages.length,
                      itemBuilder: (context, index) {
                        final message = _messages[index];
                        return _buildMessageItem(message);
                      },
                    ),
          ),

          // حقل إدخال الرسالة
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(10),
                  blurRadius: 5,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Row(
              children: [
                // زر الميكروفون
                CircleButton3D(
                  icon: _isListening ? Icons.stop : Icons.mic,
                  size: 50,
                  iconSize: 24,
                  onPressed: _isListening ? _stopListening : _startListening,
                  color: _isListening ? Colors.red : AppTheme.primaryColor,
                ),

                const SizedBox(width: 8),

                // حقل الإدخال
                Expanded(
                  child: TextField(
                    controller: _messageController,
                    decoration: InputDecoration(
                      hintText: 'اكتب رسالتك هنا...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: Colors.grey.shade100,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                    ),
                    textInputAction: TextInputAction.send,
                    onSubmitted: (_) => _sendMessage(),
                    maxLines: null,
                    // تعيين اتجاه النص بناءً على اللغة النشطة
                    // ملاحظة: نحن نستخدم null هنا لأن TextField يتوقع TextDirection؟
                    textDirection: null,
                  ),
                ),

                const SizedBox(width: 8),

                // زر الإرسال
                CircleButton3D(
                  icon: Icons.send,
                  size: 50,
                  iconSize: 24,
                  onPressed: _isTranslating ? null : () => _sendMessage(),
                  isLoading: _isTranslating,
                  color: AppTheme.primaryColor,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر الرسالة
  Widget _buildMessageItem(ConversationMessage message) {
    final isCurrentUser = message.isUser1 == _isUser1Active;
    final userName = message.isUser1 ? _user1Name : _user2Name;

    return Align(
          alignment:
              isCurrentUser ? Alignment.centerRight : Alignment.centerLeft,
          child: Container(
            margin: const EdgeInsets.only(bottom: 16),
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.75,
            ),
            child: Column(
              crossAxisAlignment:
                  isCurrentUser
                      ? CrossAxisAlignment.end
                      : CrossAxisAlignment.start,
              children: [
                // اسم المستخدم
                Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Text(
                    userName,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color:
                          message.isUser1
                              ? AppTheme.primaryColor
                              : AppTheme.secondaryColor,
                    ),
                  ),
                ),

                // فقاعة الرسالة
                Container(
                  decoration: BoxDecoration(
                    color:
                        message.isUser1
                            ? AppTheme.primaryColor
                            : AppTheme.secondaryColor,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // النص الأصلي
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Flexible(
                            child: Text(
                              message.text,
                              style: const TextStyle(
                                fontSize: 16,
                                color: Colors.white,
                              ),
                            ),
                          ),
                          const SizedBox(width: 4),
                          InkWell(
                            onTap: () => _copyText(message.text),
                            child: const Icon(
                              Icons.copy,
                              size: 16,
                              color: Colors.white70,
                            ),
                          ),
                        ],
                      ),

                      const Divider(color: Colors.white30),

                      // النص المترجم
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Flexible(
                            child: Text(
                              message.translatedText,
                              style: const TextStyle(
                                fontSize: 16,
                                color: Colors.white,
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                          ),
                          const SizedBox(width: 4),
                          InkWell(
                            onTap: () => _copyText(message.translatedText),
                            child: const Icon(
                              Icons.copy,
                              size: 16,
                              color: Colors.white70,
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 4),

                      // توقيت الرسالة
                      Text(
                        _formatTimestamp(message.timestamp),
                        style: const TextStyle(
                          fontSize: 10,
                          color: Colors.white70,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        )
        .animate()
        .fadeIn(duration: 300.ms)
        .slideY(begin: 0.2, end: 0, duration: 300.ms);
  }
}
