import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/app_state.dart';
import '../../config/app_theme.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/custom_button.dart';
import '../../config/constants.dart';
import '../subscription/subscription_screen.dart';
import '../profile/profile_screen.dart';
import '../history/history_screen.dart';
import '../favorites/favorites_screen.dart';
import '../settings/settings_screen.dart';
import '../translation/translation_screen_final.dart';
import '../duolingo/duolingo_main_screen.dart';
import '../../features/chat/chat_floating_button.dart';

/// الشاشة الرئيسية
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  // مؤشر التبويب الحالي
  int _currentIndex = 0;

  // قائمة أنواع الترجمة
  final List<Map<String, dynamic>> _translationTypes =
      AppConstants.translationTypes;

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppState>(context);

    return Scaffold(
      appBar: CustomAppBar(
        title: 'المترجم الذكي',
        showBackButton: false,
        actions: [
          // زر الملف الشخصي
          IconButton(
            icon: const Icon(Icons.person),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const ProfileScreen()),
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الحالة
          _buildStatusBar(appState),

          // محتوى الشاشة
          Expanded(child: _buildScreenContent()),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'الرئيسية'),
          BottomNavigationBarItem(icon: Icon(Icons.history), label: 'السجل'),
          BottomNavigationBarItem(icon: Icon(Icons.favorite), label: 'المفضلة'),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: 'الإعدادات',
          ),
        ],
      ),
      floatingActionButton: const ChatFloatingButton(),
    );
  }

  /// بناء شريط الحالة
  Widget _buildStatusBar(AppState appState) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: AppTheme.primaryColor.withAlpha(26),
      child: Row(
        children: [
          // حالة الاشتراك
          Expanded(
            child: Row(
              children: [
                Icon(
                  appState.isSubscribed ? Icons.verified : Icons.info_outline,
                  color: appState.isSubscribed ? Colors.green : Colors.orange,
                  size: 18,
                ),
                const SizedBox(width: 8),
                Text(
                  appState.isSubscribed ? 'مشترك' : 'نسخة مجانية',
                  style: TextStyle(
                    color: appState.isSubscribed ? Colors.green : Colors.orange,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          // زر الاشتراك
          if (!appState.isSubscribed)
            CustomButton(
              text: 'ترقية',
              icon: Icons.star,
              color: Colors.amber,
              height: 36,
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SubscriptionScreen(),
                  ),
                );
              },
            ),
        ],
      ),
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildScreenContent() {
    switch (_currentIndex) {
      case 0:
        return _buildHomeTab();
      case 1:
        return _buildHistoryTab();
      case 2:
        return _buildFavoritesTab();
      case 3:
        return _buildSettingsTab();
      default:
        return _buildHomeTab();
    }
  }

  /// بناء تبويب الرئيسية
  Widget _buildHomeTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'اختر نوع الترجمة',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 1.2,
              ),
              itemCount: _translationTypes.length,
              itemBuilder: (context, index) {
                final type = _translationTypes[index];
                return _buildTranslationTypeCard(type);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة نوع الترجمة
  Widget _buildTranslationTypeCard(Map<String, dynamic> type) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          _navigateToTranslationScreen(type['id']);
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                _getIconData(type['icon']),
                size: 32,
                color: AppTheme.primaryColor,
              ),
              const SizedBox(height: 12),
              Text(
                type['title'],
                textAlign: TextAlign.center,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء تبويب السجل
  Widget _buildHistoryTab() {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const HistoryScreen()),
        );
      },
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.history, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'اضغط هنا للانتقال إلى سجل الترجمات',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 18),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء تبويب المفضلة
  Widget _buildFavoritesTab() {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const FavoritesScreen()),
        );
      },
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.favorite, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'اضغط هنا للانتقال إلى المفضلة',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 18),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء تبويب الإعدادات
  Widget _buildSettingsTab() {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const SettingsScreen()),
        );
      },
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.settings, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'اضغط هنا للانتقال إلى الإعدادات',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 18),
            ),
          ],
        ),
      ),
    );
  }

  /// الحصول على أيقونة بناءً على النوع
  IconData _getIconData(String iconType) {
    switch (iconType) {
      case 'mic':
        return Icons.mic;
      case 'text_fields':
        return Icons.text_fields;
      case 'camera_alt':
        return Icons.camera_alt;
      case 'description':
        return Icons.description;
      case 'forum':
        return Icons.forum;
      case 'record_voice_over':
        return Icons.record_voice_over;
      case 'groups':
        return Icons.groups;
      case 'child_care':
        return Icons.child_care;
      case 'flight':
        return Icons.flight;
      case 'smart_toy':
        return Icons.smart_toy;
      case 'school':
        return Icons.school;
      default:
        return Icons.translate;
    }
  }

  /// الانتقال إلى شاشة الترجمة
  void _navigateToTranslationScreen(String typeId) {
    if (typeId == 'duolingo_mode') {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const DuolingoMainScreen()),
      );
    } else {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => TranslationScreen(translationType: typeId),
        ),
      );
    }
  }
}
