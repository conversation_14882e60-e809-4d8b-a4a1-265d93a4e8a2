import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../../widgets/custom_button.dart';
import 'subscription_screen.dart';

/// حوار تنبيه تجاوز الحد المجاني
class UsageLimitExceededDialog extends StatelessWidget {
  /// نوع الخدمة
  final String serviceType;

  const UsageLimitExceededDialog({
    super.key,
    required this.serviceType,
  });

  @override
  Widget build(BuildContext context) {
    // تحديد عنوان الخدمة
    String serviceTitle;
    switch (serviceType) {
      case 'voice_translation':
        serviceTitle = 'الترجمة الصوتية';
        break;
      case 'speaker_recognition':
        serviceTitle = 'تمييز المتحدثين';
        break;
      case 'real_time_translation':
        serviceTitle = 'الترجمة الفورية';
        break;
      case 'image_translation':
        serviceTitle = 'ترجمة الصور';
        break;
      case 'document_translation':
        serviceTitle = 'ترجمة المستندات';
        break;
      case 'ai_chat':
        serviceTitle = 'المحادثة الذكية';
        break;
      default:
        serviceTitle = 'هذه الخدمة';
    }

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.warning_amber_rounded,
              color: Colors.orange,
              size: 64,
            ),
            const SizedBox(height: 16),
            const Text(
              'تم تجاوز الحد المجاني',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'لقد استخدمت الحد الأقصى المسموح به للاستخدام المجاني لخدمة $serviceTitle هذا الشهر. قم بالترقية إلى خطة مدفوعة للاستمرار في استخدام هذه الميزة.',
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('لاحقًا'),
                ),
                CustomButton(
                  text: 'ترقية الآن',
                  onPressed: () {
                    Navigator.pop(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const SubscriptionScreen(),
                      ),
                    );
                  },
                  width: 120,
                  height: 40,
                  color: AppTheme.primaryColor,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
