import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../services/auth/auth_service.dart';
import '../services/subscription/subscription_service.dart';
import '../services/ads/ads_service.dart';
import '../services/firebase/remote_config_service.dart';
import '../services/api/api_key_manager.dart';
import '../services/usage/usage_tracking_service.dart';
import '../models/usage_stats.dart';
import '../models/translation_item.dart';
import '../config/constants.dart';

/// مدير الحالة الرئيسي للتطبيق
class AppState extends ChangeNotifier {
  // الخدمات
  final AuthService _authService = AuthService();
  late SubscriptionService _subscriptionService;
  late AdsService _adsService;
  late FirebaseRemoteConfig _remoteConfig;
  late ApiKeyManager _apiKeyManager;
  late UsageTrackingService _usageTrackingService;

  // حالة المستخدم
  UserModel? _currentUser;
  bool _isLoggedIn = false;
  bool _isFirstTime = true;
  bool _isLoading = true;

  // إعدادات التطبيق
  String _themeMode =
      AppConstants.themeModeLight; // وضع السمة (فاتح، داكن، AMOLED)
  String _selectedLanguage = 'ar';

  // حالة الاشتراك
  bool _isPremium = false;
  bool _isAdsRemoved = false;
  DateTime? _premiumExpiry;
  DateTime? _subscriptionEndDate;

  // إحصائيات الاستخدام
  UsageStats _usageStats = UsageStats();
  DateTime _registrationDate = DateTime.now();

  // الوصول للخدمات
  AuthService get authService => _authService;
  SubscriptionService get subscriptionService => _subscriptionService;
  AdsService get adsService => _adsService;
  FirebaseRemoteConfig get remoteConfig => _remoteConfig;
  ApiKeyManager get apiKeyManager => _apiKeyManager;
  UsageTrackingService get usageTrackingService => _usageTrackingService;

  // الوصول لحالة المستخدم
  UserModel? get currentUser => _currentUser;
  bool get isLoggedIn => _isLoggedIn;
  bool get isFirstTime => _isFirstTime;
  bool get isLoading => _isLoading;

  // الوصول لإعدادات التطبيق
  bool get isDarkMode => _themeMode != AppConstants.themeModeLight;
  bool get isAmoledMode => _themeMode == AppConstants.themeModeAmoled;
  String get themeMode => _themeMode;
  String get selectedLanguage => _selectedLanguage;

  // الوصول لحالة الاشتراك
  bool get isPremium => _isPremium;
  bool get isSubscribed =>
      _isPremium; // إضافة getter للتوافق مع الشاشات الجديدة
  bool get isAdsRemoved => _isAdsRemoved;
  DateTime? get premiumExpiry => _premiumExpiry;
  DateTime? get subscriptionEndDate => _subscriptionEndDate;

  // الوصول لإحصائيات الاستخدام
  UsageStats get usageStats => _usageStats;
  DateTime get registrationDate => _registrationDate;

  AppState() {
    // استدعاء دالة التهيئة بشكل غير متزامن
    Future.microtask(() => _initializeApp());
  }

  /// تهيئة التطبيق
  Future<void> _initializeApp() async {
    _isLoading = true;
    notifyListeners();

    try {
      // تهيئة Firebase Remote Config
      await _initializeRemoteConfig();

      // تهيئة الخدمات
      _subscriptionService = SubscriptionService();
      _adsService = AdsService(_remoteConfig);

      // تهيئة خدمة إدارة مفاتيح API
      await _initializeApiKeyManager();

      // تهيئة خدمة تتبع الاستخدام
      await _initializeUsageTrackingService();

      // تحميل الإعدادات من التخزين المحلي
      await _loadSettings();

      // الاستماع لتغييرات حالة المصادقة
      _authService.authStateChanges.listen(_handleAuthStateChanges);

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      debugPrint('Error initializing app: $e');
      _isLoading = false;
      notifyListeners();
    }
  }

  /// تهيئة خدمة إدارة مفاتيح API
  Future<void> _initializeApiKeyManager() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _apiKeyManager = ApiKeyManager(_remoteConfig, prefs);
      debugPrint('API Key Manager initialized');
    } catch (e) {
      debugPrint('Error initializing API Key Manager: $e');
    }
  }

  /// تهيئة خدمة تتبع الاستخدام
  Future<void> _initializeUsageTrackingService() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final auth = FirebaseAuth.instance;
      final firestore = FirebaseFirestore.instance;
      _usageTrackingService = UsageTrackingService(prefs, auth, firestore);

      // تحميل إحصائيات الاستخدام
      _usageStats = await _usageTrackingService.getUsageStats();

      debugPrint('Usage Tracking Service initialized');
    } catch (e) {
      debugPrint('Error initializing Usage Tracking Service: $e');
    }
  }

  /// تهيئة Firebase Remote Config
  Future<void> _initializeRemoteConfig() async {
    _remoteConfig = FirebaseRemoteConfig.instance;

    // استخدام خدمة Remote Config
    final remoteConfigService = RemoteConfigService(_remoteConfig);
    await remoteConfigService.initialize();
  }

  /// تحميل الإعدادات من التخزين المحلي
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // تحميل إعدادات التطبيق
      _isFirstTime = prefs.getBool(AppConstants.keyIsFirstTime) ?? true;

      // تحميل وضع السمة
      _themeMode =
          prefs.getString(AppConstants.keyThemeMode) ??
          AppConstants.themeModeLight;

      // للتوافق مع الإصدارات القديمة
      if (prefs.containsKey(AppConstants.keyIsDarkMode)) {
        final oldDarkMode = prefs.getBool(AppConstants.keyIsDarkMode) ?? false;
        if (oldDarkMode && _themeMode == AppConstants.themeModeLight) {
          _themeMode = AppConstants.themeModeDark;
        }
      }

      _selectedLanguage =
          prefs.getString(AppConstants.keySelectedLanguage) ?? 'ar';

      // تحميل حالة المستخدم
      _isLoggedIn = prefs.getBool(AppConstants.keyIsLoggedIn) ?? false;

      // تحميل حالة الاشتراك
      final subscriptionStatus =
          await _subscriptionService.getSubscriptionStatus();
      _isPremium = subscriptionStatus['isPremium'] ?? false;
      _isAdsRemoved = subscriptionStatus['isAdsRemoved'] ?? false;
      _premiumExpiry = subscriptionStatus['premiumExpiry'];
      _subscriptionEndDate = subscriptionStatus['subscriptionEndDate'];

      // تحميل تاريخ التسجيل
      final registrationDateMillis = prefs.getInt(
        AppConstants.keyRegistrationDate,
      );
      if (registrationDateMillis != null) {
        _registrationDate = DateTime.fromMillisecondsSinceEpoch(
          registrationDateMillis,
        );
      } else {
        // إذا لم يكن هناك تاريخ تسجيل محفوظ، احفظ التاريخ الحالي
        _registrationDate = DateTime.now();
        await prefs.setInt(
          AppConstants.keyRegistrationDate,
          _registrationDate.millisecondsSinceEpoch,
        );
      }

      notifyListeners();
    } catch (e) {
      debugPrint('Error loading settings: $e');
    }
  }

  /// معالجة تغييرات حالة المصادقة
  Future<void> _handleAuthStateChanges(User? user) async {
    if (user != null) {
      // المستخدم مسجل الدخول
      final userData = await _authService.getUserData(user.uid);

      if (userData != null) {
        _currentUser = userData;
        _isLoggedIn = true;

        // تحديث حالة الاشتراك
        _isPremium = userData.isPremium;
        _isAdsRemoved = userData.isAdsRemoved;
        _premiumExpiry = userData.premiumExpiry;

        // حفظ حالة تسجيل الدخول
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool(AppConstants.keyIsLoggedIn, true);
      }
    } else {
      // المستخدم غير مسجل الدخول
      _currentUser = null;
      _isLoggedIn = false;

      // إعادة تعيين حالة الاشتراك
      _isPremium = false;
      _isAdsRemoved = false;
      _premiumExpiry = null;

      // حفظ حالة تسجيل الدخول
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(AppConstants.keyIsLoggedIn, false);
    }

    notifyListeners();
  }

  /// تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
  Future<bool> signInWithEmailAndPassword(String email, String password) async {
    try {
      await _authService.signInWithEmailAndPassword(email, password);
      return true;
    } catch (e) {
      debugPrint('Sign in error: $e');
      return false;
    }
  }

  /// إنشاء حساب جديد بالبريد الإلكتروني وكلمة المرور
  Future<bool> createUserWithEmailAndPassword(
    String email,
    String password,
    String displayName,
  ) async {
    try {
      await _authService.createUserWithEmailAndPassword(
        email,
        password,
        displayName,
      );
      return true;
    } catch (e) {
      debugPrint('Create user error: $e');
      return false;
    }
  }

  /// تسجيل الدخول باستخدام Google
  Future<bool> signInWithGoogle() async {
    try {
      await _authService.signInWithGoogle();
      return true;
    } catch (e) {
      debugPrint('Google sign in error: $e');
      return false;
    }
  }

  /// تسجيل الدخول كضيف
  Future<bool> signInAnonymously() async {
    try {
      await _authService.signInAnonymously();
      return true;
    } catch (e) {
      debugPrint('Anonymous sign in error: $e');
      return false;
    }
  }

  /// تسجيل الخروج
  Future<bool> signOut() async {
    try {
      await _authService.signOut();
      return true;
    } catch (e) {
      debugPrint('Sign out error: $e');
      return false;
    }
  }

  /// تغيير وضع السمة (فاتح/داكن)
  Future<void> toggleThemeMode() async {
    // تبديل بين الوضع الفاتح والداكن
    if (_themeMode == AppConstants.themeModeLight) {
      await setThemeMode(AppConstants.themeModeDark);
    } else {
      await setThemeMode(AppConstants.themeModeLight);
    }
  }

  /// تعيين وضع السمة
  Future<void> setThemeMode(String mode) async {
    if (_themeMode != mode) {
      _themeMode = mode;

      // حفظ الإعداد
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConstants.keyThemeMode, mode);

      // للتوافق مع الإصدارات القديمة
      await prefs.setBool(
        AppConstants.keyIsDarkMode,
        mode != AppConstants.themeModeLight,
      );

      notifyListeners();
    }
  }

  /// تغيير اللغة
  Future<void> setLanguage(String languageCode) async {
    _selectedLanguage = languageCode;

    // حفظ الإعداد
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.keySelectedLanguage, _selectedLanguage);

    notifyListeners();
  }

  /// تعيين حالة الاستخدام الأول
  Future<void> setFirstTimeStatus(bool isFirstTime) async {
    _isFirstTime = isFirstTime;

    // حفظ الإعداد
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(AppConstants.keyIsFirstTime, _isFirstTime);

    notifyListeners();
  }

  /// تعيين حالة الاشتراك
  void setSubscriptionStatus(bool status) {
    _isPremium = status;
    _isAdsRemoved = status;

    // تعيين تاريخ انتهاء الاشتراك بعد شهر من الآن
    _premiumExpiry = DateTime.now().add(const Duration(days: 30));

    notifyListeners();
  }

  /// الحصول على المفضلة
  Future<List<TranslationItem>> getFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson =
          prefs.getStringList(AppConstants.keyFavorites) ?? [];

      return favoritesJson
          .map(
            (json) => TranslationItem.fromJson(
              Map<String, dynamic>.from(jsonDecode(json) as Map),
            ),
          )
          .toList();
    } catch (e) {
      debugPrint('Error getting favorites: $e');
      return [];
    }
  }

  /// إضافة إلى المفضلة
  Future<bool> addToFavorites(TranslationItem item) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favorites = await getFavorites();

      // التحقق مما إذا كان العنصر موجودًا بالفعل
      if (favorites.any((favorite) => favorite.id == item.id)) {
        return true;
      }

      // إضافة العنصر إلى المفضلة
      final updatedItem = item.copyWith(isFavorite: true);
      favorites.add(updatedItem);

      // حفظ المفضلة
      final favoritesJson =
          favorites.map((item) => jsonEncode(item.toJson())).toList();

      await prefs.setStringList(AppConstants.keyFavorites, favoritesJson);

      // تتبع الاستخدام
      await _usageTrackingService.trackFeatureUsage('favorites_add');

      return true;
    } catch (e) {
      debugPrint('Error adding to favorites: $e');
      return false;
    }
  }

  /// إزالة من المفضلة
  Future<bool> removeFromFavorites(String id) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favorites = await getFavorites();

      // إزالة العنصر من المفضلة
      favorites.removeWhere((item) => item.id == id);

      // حفظ المفضلة
      final favoritesJson =
          favorites.map((item) => jsonEncode(item.toJson())).toList();

      await prefs.setStringList(AppConstants.keyFavorites, favoritesJson);

      return true;
    } catch (e) {
      debugPrint('Error removing from favorites: $e');
      return false;
    }
  }

  /// الحصول على سجل الترجمات
  Future<List<TranslationItem>> getTranslationHistory({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson =
          prefs.getStringList(AppConstants.keyTranslationHistory) ?? [];

      List<TranslationItem> history =
          historyJson
              .map(
                (json) => TranslationItem.fromJson(
                  Map<String, dynamic>.from(jsonDecode(json) as Map),
                ),
              )
              .toList();

      // ترتيب العناصر حسب التاريخ (الأحدث أولاً)
      history.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      // تصفية حسب التاريخ إذا تم تحديده
      if (startDate != null && endDate != null) {
        history =
            history.where((item) {
              return item.timestamp.isAfter(startDate) &&
                  item.timestamp.isBefore(endDate.add(const Duration(days: 1)));
            }).toList();
      }

      return history;
    } catch (e) {
      debugPrint('Error getting translation history: $e');
      return [];
    }
  }

  /// إضافة إلى سجل الترجمات
  Future<bool> addToHistory(TranslationItem item) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final history = await getTranslationHistory();

      // إضافة العنصر إلى السجل
      history.add(item);

      // حفظ السجل (الاحتفاظ بآخر 100 عنصر فقط)
      final historyJson =
          history.take(100).map((item) => jsonEncode(item.toJson())).toList();

      await prefs.setStringList(
        AppConstants.keyTranslationHistory,
        historyJson,
      );

      // تتبع الاستخدام
      await _usageTrackingService.trackFeatureUsage(item.type);

      return true;
    } catch (e) {
      debugPrint('Error adding to history: $e');
      return false;
    }
  }

  /// حذف من سجل الترجمات
  Future<bool> deleteFromHistory(String id) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final history = await getTranslationHistory();

      // إزالة العنصر من السجل
      history.removeWhere((item) => item.id == id);

      // حفظ السجل
      final historyJson =
          history.map((item) => jsonEncode(item.toJson())).toList();

      await prefs.setStringList(
        AppConstants.keyTranslationHistory,
        historyJson,
      );

      return true;
    } catch (e) {
      debugPrint('Error deleting from history: $e');
      return false;
    }
  }

  /// مسح سجل الترجمات
  Future<bool> clearHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(AppConstants.keyTranslationHistory, []);
      return true;
    } catch (e) {
      debugPrint('Error clearing history: $e');
      return false;
    }
  }

  /// التخلص من الموارد
  @override
  void dispose() {
    _adsService.dispose();
    _subscriptionService.dispose();
    super.dispose();
  }
}
