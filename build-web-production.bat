@echo off
echo ========================================
echo    Flutter Web Production Build
echo    AI Smart Translator New Project
echo ========================================

echo.
echo [1/6] Cleaning previous builds...
call flutter clean
if %errorlevel% neq 0 (
    echo Warning: Flutter clean failed!
)

echo.
echo [2/6] Getting dependencies...
call flutter pub get
if %errorlevel% neq 0 (
    echo Error: Failed to get dependencies!
    pause
    exit /b 1
)

echo.
echo [3/6] Analyzing code...
call flutter analyze
if %errorlevel% neq 0 (
    echo Warning: Code analysis found issues!
    echo Continuing with build...
)

echo.
echo [4/6] Building for web (release mode)...
echo This may take several minutes...
call flutter build web --release --base-href / --pwa-strategy offline-first
if %errorlevel% neq 0 (
    echo Error: Failed to build for web!
    pause
    exit /b 1
)

echo.
echo [5/6] Optimizing build...
echo Checking build size...
dir build\web /s

echo.
echo [6/6] Build completed successfully!
echo.
echo ========================================
echo    Production Build Ready!
echo ========================================
echo.
echo Build location: build\web\
echo.
echo Next steps:
echo 1. Test locally: test-web-local.bat
echo 2. Deploy to Firebase: deploy.bat
echo.
echo Build optimizations applied:
echo - Release mode (minified)
echo - CanvasKit renderer (better performance)
echo - Base href configured for hosting
echo.
pause
