import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../config/app_theme.dart';

import '../../widgets/three_d_transition.dart';
import '../../screens/auth/simple_login_screen.dart';
import '../settings/settings_screen.dart';
import '../subscription/subscription_screen.dart';

/// القائمة الجانبية للصفحة الرئيسية
class HomeDrawer extends StatelessWidget {
  const HomeDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: Colors.white,
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          // رأس القائمة
          _buildDrawerHeader(),

          // عناصر القائمة
          _buildDrawerItems(context),
        ],
      ),
    );
  }

  /// بناء رأس القائمة
  Widget _buildDrawerHeader() {
    return DrawerHeader(
      decoration: BoxDecoration(gradient: AppTheme.primaryGradient),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // شعار التطبيق
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(15),
              boxShadow: [
                BoxShadow(
                  color: Color.fromRGBO(0, 0, 0, 0.16),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Center(
              child: Icon(
                Icons.translate,
                size: 30,
                color: AppTheme.primaryColor,
              ),
            ),
          ),

          const SizedBox(height: 15),

          // اسم التطبيق
          const Text(
            'المترجم الذكي AI',
            style: TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 5),

          // وصف التطبيق
          Text(
            'ترجمة ذكية بتقنيات الذكاء الاصطناعي',
            style: TextStyle(
              color: Color.fromRGBO(255, 255, 255, 0.8),
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عناصر القائمة
  Widget _buildDrawerItems(BuildContext context) {
    return Column(
      children: [
        // الصفحة الرئيسية
        _buildDrawerItem(
          context: context,
          icon: Icons.home,
          title: 'الصفحة الرئيسية',
          onTap: () {
            Navigator.pop(context);
          },
        ),

        // الإعدادات
        _buildDrawerItem(
          context: context,
          icon: Icons.settings,
          title: 'الإعدادات',
          onTap: () {
            Navigator.pop(context);
            Navigator.of(context).push(
              Transition3D(
                page: const SettingsScreen(),
                type: TransitionType.rightToLeft,
              ),
            );
          },
        ),

        // الاشتراكات
        _buildDrawerItem(
          context: context,
          icon: Icons.workspace_premium,
          title: 'الاشتراكات',
          onTap: () {
            Navigator.pop(context);
            Navigator.of(context).push(
              Transition3D(
                page: const SubscriptionScreen(),
                type: TransitionType.rightToLeft,
              ),
            );
          },
        ),

        // المساعدة
        _buildDrawerItem(
          context: context,
          icon: Icons.help,
          title: 'المساعدة',
          onTap: () {
            Navigator.pop(context);
            // سيتم تنفيذه لاحقًا
          },
        ),

        // حول التطبيق
        _buildDrawerItem(
          context: context,
          icon: Icons.info,
          title: 'حول التطبيق',
          onTap: () {
            Navigator.pop(context);
            // سيتم تنفيذه لاحقًا
          },
        ),

        const Divider(),

        // تسجيل الخروج
        _buildDrawerItem(
          context: context,
          icon: Icons.logout,
          title: 'تسجيل الخروج',
          onTap: () {
            Navigator.pop(context);
            _showLogoutConfirmationDialog(context);
          },
        ),
      ],
    );
  }

  /// بناء عنصر القائمة
  Widget _buildDrawerItem({
    required BuildContext context,
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
          leading: Icon(icon, color: AppTheme.primaryColor),
          title: Text(
            title,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
          ),
          onTap: onTap,
        )
        .animate()
        .fadeIn(duration: 300.ms)
        .slideX(begin: -0.2, end: 0, duration: 300.ms);
  }

  /// عرض مربع حوار تأكيد تسجيل الخروج
  void _showLogoutConfirmationDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تسجيل الخروج'),
            content: const Text('هل أنت متأكد من رغبتك في تسجيل الخروج؟'),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  _logout(context);
                },
                child: const Text('تسجيل الخروج'),
              ),
            ],
          ),
    );
  }

  /// تسجيل الخروج
  void _logout(BuildContext context) {
    // سيتم تنفيذه لاحقًا عند ربط Firebase
    Navigator.of(context).pushReplacement(
      Transition3D(page: const SimpleLoginScreen(), type: TransitionType.fade),
    );
  }
}
