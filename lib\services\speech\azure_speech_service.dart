import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import '../../config/constants.dart';

/// خدمة التعرف على الكلام باستخدام Azure Speech Service
class AzureSpeechService {
  final FirebaseRemoteConfig _remoteConfig;
  // سيتم استخدام مكتبة أخرى للتسجيل في التنفيذ الفعلي
  // final recorder = Record();
  String? _audioFilePath;
  bool _isInitialized = false;
  bool _isRecording = false;

  // معلومات Azure Speech Service
  late String _subscriptionKey;
  late String _region;
  String _baseUrl = '';
  String _token = '';
  DateTime? _tokenExpiryTime;

  AzureSpeechService(this._remoteConfig) {
    _initService();
  }

  /// تهيئة الخدمة
  Future<void> _initService() async {
    if (_isInitialized) return;

    try {
      _subscriptionKey = _remoteConfig.getString(
        AppConstants.keyAzureSpeechKey,
      );
      _region = _remoteConfig.getString(AppConstants.keyAzureSpeechRegion);
      _baseUrl =
          'https://$_region.api.cognitive.microsoft.com/sts/v1.0/issuetoken';

      // الحصول على رمز الوصول
      await _getAccessToken();

      _isInitialized = true;
    } catch (e) {
      debugPrint('Error initializing Azure Speech Service: $e');
      rethrow;
    }
  }

  /// الحصول على رمز الوصول
  Future<void> _getAccessToken() async {
    try {
      // التحقق مما إذا كان الرمز الحالي صالحًا
      if (_token.isNotEmpty &&
          _tokenExpiryTime != null &&
          DateTime.now().isBefore(_tokenExpiryTime!)) {
        return;
      }

      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Ocp-Apim-Subscription-Key': _subscriptionKey,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      );

      if (response.statusCode == 200) {
        _token = response.body;
        // رمز الوصول صالح لمدة 10 دقائق
        _tokenExpiryTime = DateTime.now().add(const Duration(minutes: 9));
      } else {
        throw Exception(
          'Failed to get access token: ${response.statusCode} ${response.body}',
        );
      }
    } catch (e) {
      debugPrint('Error getting access token: $e');
      rethrow;
    }
  }

  /// بدء التسجيل
  Future<bool> startRecording() async {
    if (!_isInitialized) {
      await _initService();
    }

    if (_isRecording) return true;

    try {
      // التحقق من الأذونات
      // في الإصدار الحديث من المكتبة، لا نحتاج للتحقق من الأذونات
      {
        // إنشاء مسار الملف المؤقت
        final tempDir = await getTemporaryDirectory();
        _audioFilePath =
            '${tempDir.path}/speech_${DateTime.now().millisecondsSinceEpoch}.wav';

        // بدء التسجيل
        // في التنفيذ الفعلي، سيتم استخدام مكتبة للتسجيل
        // هذا مجرد محاكاة للتسجيل
        await Future.delayed(const Duration(milliseconds: 100));

        _isRecording = true;
        return true;
      }
    } catch (e) {
      debugPrint('Error starting recording: $e');
      _isRecording = false;
      return false;
    }
  }

  /// إيقاف التسجيل والتعرف على الكلام
  Future<String> stopRecordingAndRecognize({
    required String languageCode,
  }) async {
    if (!_isRecording || _audioFilePath == null) {
      return '';
    }

    try {
      // إيقاف التسجيل
      // في التنفيذ الفعلي، سيتم استخدام مكتبة للتسجيل
      await Future.delayed(const Duration(milliseconds: 100));
      _isRecording = false;

      // التعرف على الكلام
      final recognizedText = await _recognizeSpeech(
        filePath: _audioFilePath!,
        languageCode: languageCode,
      );

      return recognizedText;
    } catch (e) {
      debugPrint('Error stopping recording and recognizing: $e');
      _isRecording = false;
      return '';
    }
  }

  /// التعرف على الكلام
  Future<String> _recognizeSpeech({
    required String filePath,
    required String languageCode,
  }) async {
    try {
      // التأكد من وجود رمز وصول صالح
      await _getAccessToken();

      // تحويل رمز اللغة إلى الصيغة المناسبة لـ Azure
      final azureLanguageCode = _getAzureLanguageCode(languageCode);

      // إعداد عنوان URL للتعرف على الكلام
      final recognitionUrl =
          'https://$_region.stt.speech.microsoft.com/speech/recognition/conversation/cognitiveservices/v1?language=$azureLanguageCode&format=detailed';

      // قراءة ملف الصوت
      final file = File(filePath);
      final bytes = await file.readAsBytes();

      // إرسال طلب التعرف على الكلام
      final response = await http.post(
        Uri.parse(recognitionUrl),
        headers: {
          'Authorization': 'Bearer $_token',
          'Content-Type': 'audio/wav; codecs=audio/pcm; samplerate=16000',
          'Accept': 'application/json',
        },
        body: bytes,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['RecognitionStatus'] == 'Success') {
          return data['NBest'][0]['Display'] ??
              data['NBest'][0]['Lexical'] ??
              '';
        } else {
          debugPrint('Recognition failed: ${data['RecognitionStatus']}');
          return '';
        }
      } else {
        throw Exception(
          'Failed to recognize speech: ${response.statusCode} ${response.body}',
        );
      }
    } catch (e) {
      debugPrint('Error recognizing speech: $e');
      return '';
    } finally {
      // حذف الملف المؤقت
      try {
        final file = File(filePath);
        if (await file.exists()) {
          await file.delete();
        }
      } catch (e) {
        debugPrint('Error deleting temporary file: $e');
      }
    }
  }

  /// تحويل رمز اللغة إلى الصيغة المناسبة لـ Azure
  String _getAzureLanguageCode(String languageCode) {
    // تعيين رموز اللغات المدعومة في Azure
    final Map<String, String> languageCodes = {
      'ar': 'ar-SA', // العربية (السعودية)
      'ar-EG': 'ar-EG', // العربية (مصر)
      'en': 'en-US', // الإنجليزية (الولايات المتحدة)
      'en-GB': 'en-GB', // الإنجليزية (بريطانيا)
      'fr': 'fr-FR', // الفرنسية
      'de': 'de-DE', // الألمانية
      'es': 'es-ES', // الإسبانية
      'it': 'it-IT', // الإيطالية
      'ja': 'ja-JP', // اليابانية
      'ko': 'ko-KR', // الكورية
      'pt': 'pt-BR', // البرتغالية (البرازيل)
      'ru': 'ru-RU', // الروسية
      'zh': 'zh-CN', // الصينية (الماندرين)
    };

    return languageCodes[languageCode] ?? 'en-US';
  }

  /// التخلص من الموارد
  Future<void> dispose() async {
    try {
      if (_isRecording) {
        // في التنفيذ الفعلي، سيتم استخدام مكتبة للتسجيل
        await Future.delayed(const Duration(milliseconds: 100));
        _isRecording = false;
      }
    } catch (e) {
      debugPrint('Error disposing Azure Speech Service: $e');
    }
  }
}
