import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../config/app_theme.dart';
import '../../../core/app_state.dart';
import '../../../services/speech/speaker_recognition_service.dart';
import '../../../utils/helpers.dart';
import '../../../widgets/three_d_button.dart';

/// شاشة المحادثة متعددة المتحدثين
class MultiSpeakerConversationScreen extends StatefulWidget {
  const MultiSpeakerConversationScreen({super.key});

  @override
  State<MultiSpeakerConversationScreen> createState() =>
      _MultiSpeakerConversationScreenState();
}

class _MultiSpeakerConversationScreenState
    extends State<MultiSpeakerConversationScreen> {
  late SpeakerRecognitionService _speakerService;
  final List<Speaker> _speakers = [];
  Speaker? _selectedSpeaker;
  bool _isListening = false;
  bool _isAutoDetectEnabled = true;

  final List<Map<String, dynamic>> _conversationHistory = [];

  @override
  void initState() {
    super.initState();

    // الحصول على خدمة تمييز المتحدثين
    final appState = Provider.of<AppState>(context, listen: false);
    _speakerService = SpeakerRecognitionService(
      appState.remoteConfig,
      appState.apiKeyManager,
    );

    // الاستماع لتغييرات المتحدث النشط
    _speakerService.activeSpeakerStream.listen((speaker) {
      if (mounted && _isAutoDetectEnabled) {
        setState(() {
          _selectedSpeaker = speaker;
        });
      }
    });
  }

  @override
  void dispose() {
    _speakerService.dispose();
    super.dispose();
  }

  /// إضافة متحدث جديد
  Future<void> _addNewSpeaker() async {
    // التحقق من أن الويدجت لا يزال مثبتًا قبل عرض مربع الحوار
    if (!mounted) return;

    final nameController = TextEditingController();

    final name = await showDialog<String>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('إضافة متحدث جديد'),
            content: TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'اسم المتحدث',
                hintText: 'أدخل اسم المتحدث',
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, nameController.text),
                child: const Text('إضافة'),
              ),
            ],
          ),
    );

    if (name == null || name.isEmpty) return;

    // التحقق من أن الويدجت لا يزال مثبتًا بعد مربع الحوار
    if (!mounted) return;

    // عرض رسالة لتسجيل صوت المتحدث
    AppHelpers.showSnackBar(
      context,
      'سيتم تسجيل صوتك لمدة 10 ثوانٍ. تحدث بصوت واضح.',
    );

    // في التطبيق الفعلي، ستقوم بتسجيل عينات صوتية للمتحدث
    // وإرسالها إلى خدمة تمييز المتحدثين

    // هذا مجرد مثال، وسيتم استبداله بالتنفيذ الفعلي
    await Future.delayed(const Duration(seconds: 2));

    // التحقق من أن الويدجت لا يزال مثبتًا بعد التأخير
    if (!mounted) return;

    try {
      final speaker = await _speakerService.registerSpeaker(
        name,
        ['sample1', 'sample2'], // عينات صوتية وهمية
      );

      // التحقق من أن الويدجت لا يزال مثبتًا بعد تسجيل المتحدث
      if (!mounted) return;

      setState(() {
        _speakers.add(speaker);
        _selectedSpeaker = speaker;
      });

      // التحقق من أن الويدجت لا يزال مثبتًا قبل عرض رسالة النجاح
      if (!mounted) return;

      AppHelpers.showSnackBar(context, 'تم إضافة المتحدث $name بنجاح');
    } catch (e) {
      // التحقق من أن الويدجت لا يزال مثبتًا قبل عرض رسالة الخطأ
      if (!mounted) return;

      AppHelpers.showSnackBar(
        context,
        'حدث خطأ أثناء إضافة المتحدث: $e',
        isError: true,
      );
    }
  }

  /// بدء الاستماع
  Future<void> _startListening() async {
    if (_isListening) return;

    setState(() {
      _isListening = true;
    });

    try {
      // في التطبيق الفعلي، ستقوم ببدء الاستماع وتحديد المتحدث
      // وترجمة الكلام في الوقت الفعلي

      // هذا مجرد مثال، وسيتم استبداله بالتنفيذ الفعلي
      await Future.delayed(const Duration(seconds: 3));

      // التحقق من أن الويدجت لا يزال مثبتًا بعد التأخير
      if (!mounted) return;

      // إضافة رسالة وهمية إلى سجل المحادثة
      setState(() {
        _conversationHistory.add({
          'speaker': _selectedSpeaker?.name ?? 'غير معروف',
          'text': 'هذا مثال على نص منطوق تم التعرف عليه',
          'translation': 'This is an example of recognized spoken text',
          'timestamp': DateTime.now(),
        });
      });

      setState(() {
        _isListening = false;
      });
    } catch (e) {
      // التحقق من أن الويدجت لا يزال مثبتًا بعد الخطأ
      if (!mounted) return;

      setState(() {
        _isListening = false;
      });

      // التحقق من أن الويدجت لا يزال مثبتًا قبل عرض رسالة الخطأ
      if (!mounted) return;

      AppHelpers.showSnackBar(
        context,
        'حدث خطأ أثناء الاستماع: $e',
        isError: true,
      );
    }
  }

  /// إيقاف الاستماع
  void _stopListening() {
    if (!_isListening) return;

    setState(() {
      _isListening = false;
    });

    // في التطبيق الفعلي، ستقوم بإيقاف الاستماع
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('محادثة متعددة المتحدثين'),
        backgroundColor: AppTheme.primaryColor,
        actions: [
          // زر إضافة متحدث جديد
          IconButton(
            icon: const Icon(Icons.person_add),
            onPressed: _addNewSpeaker,
            tooltip: 'إضافة متحدث جديد',
          ),

          // زر تبديل الكشف التلقائي
          IconButton(
            icon: Icon(
              _isAutoDetectEnabled
                  ? Icons.auto_awesome
                  : Icons.auto_awesome_outlined,
            ),
            onPressed: () {
              setState(() {
                _isAutoDetectEnabled = !_isAutoDetectEnabled;
              });

              AppHelpers.showSnackBar(
                context,
                _isAutoDetectEnabled
                    ? 'تم تفعيل الكشف التلقائي عن المتحدث'
                    : 'تم تعطيل الكشف التلقائي عن المتحدث',
              );
            },
            tooltip:
                _isAutoDetectEnabled
                    ? 'تعطيل الكشف التلقائي'
                    : 'تفعيل الكشف التلقائي',
          ),
        ],
      ),
      body: Column(
        children: [
          // قائمة المتحدثين
          Container(
            padding: const EdgeInsets.all(8),
            color: Colors.grey.shade200,
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  // زر إضافة متحدث جديد
                  Padding(
                    padding: const EdgeInsets.all(4),
                    child: ActionChip(
                      avatar: const Icon(Icons.add),
                      label: const Text('إضافة متحدث'),
                      onPressed: _addNewSpeaker,
                    ),
                  ),

                  // قائمة المتحدثين
                  ..._speakers.map(
                    (speaker) => Padding(
                      padding: const EdgeInsets.all(4),
                      child: ChoiceChip(
                        label: Text(speaker.name),
                        selected: _selectedSpeaker?.id == speaker.id,
                        onSelected: (selected) {
                          if (selected) {
                            setState(() {
                              _selectedSpeaker = speaker;
                              _isAutoDetectEnabled = false;
                            });

                            _speakerService.setActiveSpeaker(speaker);
                          }
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // سجل المحادثة
          Expanded(
            child:
                _conversationHistory.isEmpty
                    ? const Center(
                      child: Text(
                        'ابدأ المحادثة بالضغط على زر الميكروفون',
                        style: TextStyle(color: Colors.grey),
                      ),
                    )
                    : ListView.builder(
                      itemCount: _conversationHistory.length,
                      itemBuilder: (context, index) {
                        final item = _conversationHistory[index];

                        return Card(
                          margin: const EdgeInsets.all(8),
                          child: Padding(
                            padding: const EdgeInsets.all(12),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // اسم المتحدث
                                Text(
                                  item['speaker'],
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.primaryColor,
                                  ),
                                ),

                                const SizedBox(height: 4),

                                // النص الأصلي
                                Text(item['text']),

                                const Divider(),

                                // الترجمة
                                Text(
                                  item['translation'],
                                  style: const TextStyle(
                                    fontStyle: FontStyle.italic,
                                    color: AppTheme.secondaryColor,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
          ),

          // أزرار التحكم
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // زر الاستماع
                CircleButton3D(
                  icon: _isListening ? Icons.stop : Icons.mic,
                  size: 80,
                  iconSize: 40,
                  onPressed: _isListening ? _stopListening : _startListening,
                  color: _isListening ? Colors.red : AppTheme.primaryColor,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
