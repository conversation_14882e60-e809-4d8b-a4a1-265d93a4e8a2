import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/app_state.dart';
import '../../config/app_theme.dart';
import '../../utils/helpers.dart';

/// شاشة نسيت كلمة المرور
class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();

  bool _isLoading = false;
  bool _emailSent = false;

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  /// إرسال رابط إعادة تعيين كلمة المرور
  Future<void> _resetPassword() async {
    if (_formKey.currentState == null || !_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final appState = Provider.of<AppState>(context, listen: false);
      await appState.authService.resetPassword(_emailController.text.trim());

      if (mounted) {
        setState(() {
          _emailSent = true;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // عرض رسالة خطأ
        AppHelpers.showSnackBar(context, 'حدث خطأ: $e', isError: true);
      }
    }
  }

  /// العودة إلى شاشة تسجيل الدخول
  void _navigateBack() {
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppTheme.onBackground),
          onPressed: _navigateBack,
        ),
      ),
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20.0),
            child: _emailSent ? _buildSuccessView() : _buildFormView(),
          ),
        ),
      ),
    );
  }

  /// بناء نموذج إعادة تعيين كلمة المرور
  Widget _buildFormView() {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // أيقونة
          const Icon(Icons.lock_reset, size: 80, color: AppTheme.primaryColor)
              .animate()
              .fadeIn(duration: 500.ms)
              .scale(delay: 200.ms, duration: 500.ms),

          const SizedBox(height: 20),

          // عنوان الصفحة
          const Text(
            'نسيت كلمة المرور؟',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: AppTheme.onBackground,
            ),
          ).animate().fadeIn(delay: 300.ms, duration: 500.ms),

          const SizedBox(height: 10),

          // وصف الصفحة
          const Text(
            'أدخل بريدك الإلكتروني وسنرسل لك رابطًا لإعادة تعيين كلمة المرور',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 16, color: AppTheme.onSurfaceVariant),
          ).animate().fadeIn(delay: 400.ms, duration: 500.ms),

          const SizedBox(height: 40),

          // حقل البريد الإلكتروني
          TextFormField(
            controller: _emailController,
            keyboardType: TextInputType.emailAddress,
            decoration: const InputDecoration(
              labelText: 'البريد الإلكتروني',
              prefixIcon: Icon(Icons.email),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'يرجى إدخال البريد الإلكتروني';
              }
              if (!value.contains('@') || !value.contains('.')) {
                return 'يرجى إدخال بريد إلكتروني صحيح';
              }
              return null;
            },
          ).animate().fadeIn(delay: 500.ms, duration: 500.ms),

          const SizedBox(height: 40),

          // زر إرسال رابط إعادة تعيين كلمة المرور
          ElevatedButton(
            onPressed: _isLoading ? null : _resetPassword,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              minimumSize: const Size(double.infinity, 56),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
            ),
            child:
                _isLoading
                    ? const CircularProgressIndicator(color: Colors.white)
                    : const Text(
                      'إرسال رابط إعادة التعيين',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
          ).animate().fadeIn(delay: 600.ms, duration: 500.ms),

          const SizedBox(height: 20),

          // زر العودة إلى شاشة تسجيل الدخول
          TextButton(
            onPressed: _navigateBack,
            child: const Text(
              'العودة إلى تسجيل الدخول',
              style: TextStyle(color: AppTheme.primaryColor),
            ),
          ).animate().fadeIn(delay: 700.ms, duration: 500.ms),
        ],
      ),
    );
  }

  /// بناء عرض نجاح إرسال البريد الإلكتروني
  Widget _buildSuccessView() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // أيقونة النجاح
        const Icon(Icons.check_circle, size: 100, color: AppTheme.successColor)
            .animate()
            .fadeIn(duration: 500.ms)
            .scale(delay: 200.ms, duration: 500.ms),

        const SizedBox(height: 30),

        // عنوان النجاح
        const Text(
          'تم إرسال البريد الإلكتروني',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: AppTheme.onBackground,
          ),
        ).animate().fadeIn(delay: 300.ms, duration: 500.ms),

        const SizedBox(height: 20),

        // وصف النجاح
        Text(
          'تم إرسال رابط إعادة تعيين كلمة المرور إلى ${_emailController.text}. يرجى التحقق من بريدك الإلكتروني واتباع التعليمات.',
          textAlign: TextAlign.center,
          style: const TextStyle(
            fontSize: 16,
            color: AppTheme.onSurfaceVariant,
            height: 1.5,
          ),
        ).animate().fadeIn(delay: 400.ms, duration: 500.ms),

        const SizedBox(height: 40),

        // زر العودة إلى شاشة تسجيل الدخول
        ElevatedButton(
          onPressed: _navigateBack,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            foregroundColor: Colors.white,
            minimumSize: const Size(double.infinity, 56),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
          ),
          child: const Text(
            'العودة إلى تسجيل الدخول',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
        ).animate().fadeIn(delay: 500.ms, duration: 500.ms),
      ],
    );
  }
}
