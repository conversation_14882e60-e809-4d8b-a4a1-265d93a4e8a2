import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math';

/// نشاط تطابق الألوان التفاعلي
class ColorsMatchingActivity extends StatefulWidget {
  final Function(int score) onComplete;

  const ColorsMatchingActivity({super.key, required this.onComplete});

  @override
  State<ColorsMatchingActivity> createState() => _ColorsMatchingActivityState();
}

class _ColorsMatchingActivityState extends State<ColorsMatchingActivity>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _successController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _successAnimation;

  int _score = 0;
  int _attempts = 0;
  final int _maxAttempts = 10;
  bool _gameCompleted = false;

  final List<Map<String, dynamic>> _colors = [
    {'name': 'أحمر', 'color': Colors.red, 'emoji': '🍎'},
    {'name': 'أزرق', 'color': Colors.blue, 'emoji': '🌊'},
    {'name': 'أخضر', 'color': Colors.green, 'emoji': '🌿'},
    {'name': 'أصفر', 'color': Colors.yellow, 'emoji': '🌞'},
    {'name': 'بنفسجي', 'color': Colors.purple, 'emoji': '🍇'},
    {'name': 'برتقالي', 'color': Colors.orange, 'emoji': '🍊'},
    {'name': 'وردي', 'color': Colors.pink, 'emoji': '🌸'},
    {'name': 'بني', 'color': Colors.brown, 'emoji': '🤎'},
  ];

  Map<String, dynamic>? _currentColor;
  List<Map<String, dynamic>> _options = [];
  int? _selectedIndex;
  bool _showResult = false;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _generateNewQuestion();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _successController.dispose();
    super.dispose();
  }

  void _initAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _successController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _successAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _successController,
      curve: Curves.elasticOut,
    ));

    _animationController.forward();
  }

  void _generateNewQuestion() {
    if (_attempts >= _maxAttempts) {
      setState(() {
        _gameCompleted = true;
      });
      return;
    }

    setState(() {
      _currentColor = _colors[Random().nextInt(_colors.length)];
      _options = List.from(_colors)..shuffle();
      _options = _options.take(4).toList();
      
      // التأكد من وجود الإجابة الصحيحة
      if (!_options.contains(_currentColor)) {
        _options[Random().nextInt(4)] = _currentColor!;
      }
      
      _options.shuffle();
      _selectedIndex = null;
      _showResult = false;
    });

    _animationController.reset();
    _animationController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.cyan.shade100,
              Colors.blue.shade100,
              Colors.indigo.shade100,
            ],
          ),
        ),
        child: SafeArea(
          child: _gameCompleted
              ? _buildCompletionScreen()
              : Column(
                  children: [
                    _buildHeader(),
                    _buildProgressBar(),
                    Expanded(child: _buildGameScreen()),
                  ],
                ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.indigo),
            onPressed: () => Navigator.pop(context),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'تطابق الألوان 🌈',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.indigo,
                  ),
                ),
                Text(
                  'اختر اللون المطابق للاسم',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.indigo.shade600,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(15),
              boxShadow: [
                BoxShadow(
                  color: Colors.indigo.withValues(alpha: 0.2),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Text(
              'النقاط: $_score',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.indigo,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressBar() {
    final progress = _attempts / _maxAttempts;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'السؤال ${_attempts + 1} من $_maxAttempts',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.indigo,
                ),
              ),
              Text(
                '${(progress * 100).round()}%',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.indigo,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.indigo.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(Colors.indigo),
            minHeight: 8,
          ),
        ],
      ),
    );
  }

  Widget _buildGameScreen() {
    return ScaleTransition(
      scale: _scaleAnimation,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // عرض اللون المطلوب
            Container(
              padding: const EdgeInsets.all(30),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.indigo.withValues(alpha: 0.3),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Text(
                    _currentColor?['emoji'] ?? '',
                    style: const TextStyle(fontSize: 60),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'اختر اللون:',
                    style: const TextStyle(
                      fontSize: 18,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _currentColor?['name'] ?? '',
                    style: const TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: Colors.indigo,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 40),

            // خيارات الألوان
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 1.5,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: _options.length,
              itemBuilder: (context, index) {
                return _buildColorOption(index);
              },
            ),

            if (_showResult) ...[
              const SizedBox(height: 30),
              _buildResultMessage(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildColorOption(int index) {
    final option = _options[index];
    final isSelected = _selectedIndex == index;
    final isCorrect = option == _currentColor;
    final showResult = _showResult && isSelected;

    return GestureDetector(
      onTap: _showResult ? null : () => _selectColor(index),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        decoration: BoxDecoration(
          color: option['color'],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: showResult
                ? (isCorrect ? Colors.green : Colors.red)
                : (isSelected ? Colors.white : Colors.transparent),
            width: showResult ? 4 : (isSelected ? 3 : 0),
          ),
          boxShadow: [
            BoxShadow(
              color: option['color'].withValues(alpha: 0.4),
              blurRadius: isSelected ? 20 : 10,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (showResult)
                Icon(
                  isCorrect ? Icons.check_circle : Icons.cancel,
                  color: Colors.white,
                  size: 30,
                ),
              Text(
                option['emoji'],
                style: const TextStyle(fontSize: 30),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildResultMessage() {
    final isCorrect = _selectedIndex != null && 
                     _options[_selectedIndex!] == _currentColor;

    return ScaleTransition(
      scale: _successAnimation,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isCorrect ? Colors.green : Colors.red,
          borderRadius: BorderRadius.circular(15),
        ),
        child: Text(
          isCorrect ? 'أحسنت! إجابة صحيحة! 🎉' : 'حاول مرة أخرى! 💪',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  void _selectColor(int index) {
    setState(() {
      _selectedIndex = index;
      _showResult = true;
    });

    final isCorrect = _options[index] == _currentColor;
    
    HapticFeedback.lightImpact();

    if (isCorrect) {
      _score += 10;
      HapticFeedback.heavyImpact();
      _successController.forward().then((_) {
        _successController.reverse();
      });
    }

    _attempts++;

    // الانتقال للسؤال التالي
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        _generateNewQuestion();
      }
    });
  }

  Widget _buildCompletionScreen() {
    final percentage = (_score / (_maxAttempts * 10) * 100).round();
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(40),
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.indigo.withValues(alpha: 0.3),
                    blurRadius: 30,
                    offset: const Offset(0, 15),
                  ),
                ],
              ),
              child: Text(
                percentage >= 80 ? '🏆' : percentage >= 60 ? '⭐' : '🌈',
                style: const TextStyle(fontSize: 80),
              ),
            ),

            const SizedBox(height: 30),

            Text(
              percentage >= 80 ? 'رائع!' : percentage >= 60 ? 'جيد!' : 'أحسنت!',
              style: const TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Colors.indigo,
              ),
            ),

            const SizedBox(height: 16),

            Text(
              'لقد حصلت على $_score نقطة من ${_maxAttempts * 10}',
              style: const TextStyle(
                fontSize: 18,
                color: Colors.indigo,
              ),
            ),

            Text(
              'النسبة: $percentage%',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.indigo,
              ),
            ),

            const SizedBox(height: 40),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  onPressed: _restartGame,
                  icon: const Icon(Icons.refresh),
                  label: const Text('إعادة اللعب'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 12,
                    ),
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () {
                    widget.onComplete(_score);
                    Navigator.pop(context);
                  },
                  icon: const Icon(Icons.check),
                  label: const Text('إنهاء'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 12,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _restartGame() {
    setState(() {
      _score = 0;
      _attempts = 0;
      _gameCompleted = false;
      _selectedIndex = null;
      _showResult = false;
    });
    
    _generateNewQuestion();
  }
}
