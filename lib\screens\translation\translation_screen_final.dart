import 'package:flutter/material.dart';
import '../../config/app_theme.dart';
import '../../widgets/custom_app_bar.dart';
import '../../config/constants.dart';
import '../../utils/app_helpers.dart';

/// شاشة الترجمة
class TranslationScreen extends StatefulWidget {
  final String translationType;

  const TranslationScreen({super.key, required this.translationType});

  @override
  State<TranslationScreen> createState() => _TranslationScreenState();
}

class _TranslationScreenState extends State<TranslationScreen> {
  // متغيرات الحالة
  bool _isLoading = false;
  String _sourceLanguage = 'ar';
  String _targetLanguage = 'en';
  final TextEditingController _sourceTextController = TextEditingController();
  String _translatedText = '';

  @override
  void dispose() {
    _sourceTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: _getScreenTitle()),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // اختيار اللغات
            _buildLanguageSelector(),

            const SizedBox(height: 16),

            // محتوى الترجمة حسب النوع
            Expanded(child: _buildTranslationContent()),
          ],
        ),
      ),
    );
  }

  /// الحصول على عنوان الشاشة حسب نوع الترجمة
  String _getScreenTitle() {
    final translationType = AppConstants.translationTypes.firstWhere(
      (type) => type['id'] == widget.translationType,
      orElse: () => {'title': 'ترجمة'},
    );

    return translationType['title'] as String;
  }

  /// بناء محدد اللغات
  Widget _buildLanguageSelector() {
    return Row(
      children: [
        // اللغة المصدر
        Expanded(
          child: _buildLanguageDropdown(
            value: _sourceLanguage,
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _sourceLanguage = value;
                });
              }
            },
          ),
        ),

        // زر تبديل اللغات
        IconButton(
          icon: const Icon(Icons.swap_horiz),
          onPressed: _swapLanguages,
          tooltip: 'تبديل اللغات',
        ),

        // اللغة الهدف
        Expanded(
          child: _buildLanguageDropdown(
            value: _targetLanguage,
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _targetLanguage = value;
                });
              }
            },
          ),
        ),
      ],
    );
  }

  /// بناء قائمة منسدلة للغات
  Widget _buildLanguageDropdown({
    required String value,
    required void Function(String?) onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: DropdownButton<String>(
        value: value,
        isExpanded: true,
        underline: Container(),
        onChanged: onChanged,
        items:
            AppConstants.supportedLanguages.map((language) {
              return DropdownMenuItem<String>(
                value: language['code'] as String,
                child: Row(
                  children: [
                    Text(language['flag'] as String),
                    const SizedBox(width: 8),
                    Text(language['name'] as String),
                  ],
                ),
              );
            }).toList(),
      ),
    );
  }

  /// بناء محتوى الترجمة حسب النوع
  Widget _buildTranslationContent() {
    switch (widget.translationType) {
      case 'text':
        return _buildTextTranslation();
      case 'voice':
        return _buildVoiceTranslation();
      case 'image':
        return _buildImageTranslation();
      case 'document':
        return _buildDocumentTranslation();
      case 'conversation':
        return _buildConversationTranslation();
      case 'real_time_conversation':
        return _buildRealTimeConversationTranslation();
      case 'multi_speaker':
        return _buildMultiSpeakerTranslation();
      case 'kids_mode':
        return _buildKidsModeTranslation();
      case 'tourism_mode':
        return _buildTourismModeTranslation();
      case 'ai_chat':
        return _buildAIChatTranslation();
      default:
        return _buildTextTranslation();
    }
  }

  /// تبديل اللغات
  void _swapLanguages() {
    setState(() {
      final temp = _sourceLanguage;
      _sourceLanguage = _targetLanguage;
      _targetLanguage = temp;

      // إذا كان هناك نص مترجم، قم بتبديله أيضًا
      if (_translatedText.isNotEmpty) {
        _sourceTextController.text = _translatedText;
        _translatedText = '';
      }
    });
  }

  /// ترجمة النص
  Future<void> _translateText() async {
    if (_sourceTextController.text.isEmpty) {
      AppHelpers.showSnackBar(context, 'الرجاء إدخال نص للترجمة');
      return;
    }

    // حفظ مرجع للسياق قبل العملية غير المتزامنة
    final scaffoldMessengerState = ScaffoldMessenger.of(context);

    setState(() {
      _isLoading = true;
    });

    try {
      // هنا سيتم استدعاء خدمة الترجمة
      await Future.delayed(const Duration(seconds: 1)); // محاكاة الترجمة

      if (!mounted) return;

      setState(() {
        _translatedText = 'هذا نص مترجم تجريبي: ${_sourceTextController.text}';
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      // استخدام المرجع المحفوظ مسبقًا
      scaffoldMessengerState.showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء الترجمة: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// بناء واجهة ترجمة النصوص
  Widget _buildTextTranslation() {
    return Column(
      children: [
        // حقل النص المصدر
        TextField(
          controller: _sourceTextController,
          maxLines: 5,
          decoration: InputDecoration(
            hintText: 'أدخل النص المراد ترجمته',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
          ),
        ),

        const SizedBox(height: 16),

        // زر الترجمة
        ElevatedButton(
          onPressed: _isLoading ? null : _translateText,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            padding: const EdgeInsets.symmetric(vertical: 12),
            minimumSize: const Size(double.infinity, 48),
          ),
          child:
              _isLoading
                  ? const CircularProgressIndicator(color: Colors.white)
                  : const Text('ترجمة', style: TextStyle(fontSize: 16)),
        ),

        const SizedBox(height: 16),

        // النص المترجم
        if (_translatedText.isNotEmpty)
          Expanded(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withAlpha(26),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(_translatedText),
            ),
          ),
      ],
    );
  }

  /// بناء واجهة الترجمة الصوتية
  Widget _buildVoiceTranslation() {
    return Column(
      children: [
        const SizedBox(height: 20),

        // منطقة التسجيل
        Container(
          width: double.infinity,
          height: 200,
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withAlpha(26),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // أيقونة الميكروفون
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(26),
                      blurRadius: 10,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: IconButton(
                  icon: const Icon(Icons.mic, size: 40),
                  color: AppTheme.primaryColor,
                  onPressed: () {
                    // تنفيذ بدء التسجيل
                    AppHelpers.showSnackBar(context, 'جاري الاستماع...');
                  },
                ),
              ),

              const SizedBox(height: 16),

              // نص إرشادي
              const Text(
                'اضغط للتحدث',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),

              const SizedBox(height: 8),

              const Text(
                'سيتم ترجمة كلامك تلقائيًا',
                style: TextStyle(fontSize: 14, color: Colors.grey),
              ),
            ],
          ),
        ),

        const SizedBox(height: 20),

        // منطقة النتائج
        Expanded(
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'النتائج:',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),

                const SizedBox(height: 8),

                const Text(
                  'ستظهر هنا نتائج الترجمة الصوتية',
                  style: TextStyle(color: Colors.grey),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// بناء واجهة ترجمة الصور
  Widget _buildImageTranslation() {
    return Column(
      children: [
        const SizedBox(height: 20),

        // منطقة اختيار الصورة
        Container(
          width: double.infinity,
          height: 200,
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withAlpha(26),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.grey.shade300, width: 1),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // أيقونة الكاميرا
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(26),
                      blurRadius: 10,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: IconButton(
                  icon: const Icon(Icons.camera_alt, size: 40),
                  color: AppTheme.primaryColor,
                  onPressed: () {
                    // تنفيذ التقاط صورة
                    AppHelpers.showSnackBar(context, 'جاري فتح الكاميرا...');
                  },
                ),
              ),

              const SizedBox(height: 16),

              // أزرار الاختيار
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ElevatedButton.icon(
                    icon: const Icon(Icons.camera_alt),
                    label: const Text('التقاط صورة'),
                    onPressed: () {
                      AppHelpers.showSnackBar(context, 'جاري فتح الكاميرا...');
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                    ),
                  ),

                  const SizedBox(width: 16),

                  ElevatedButton.icon(
                    icon: const Icon(Icons.photo_library),
                    label: const Text('اختيار من المعرض'),
                    onPressed: () {
                      AppHelpers.showSnackBar(context, 'جاري فتح المعرض...');
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        const SizedBox(height: 20),

        // منطقة النتائج
        Expanded(
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'النص المستخرج:',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),

                const SizedBox(height: 8),

                const Text(
                  'سيظهر هنا النص المستخرج من الصورة',
                  style: TextStyle(color: Colors.grey),
                ),

                const SizedBox(height: 16),

                const Text(
                  'الترجمة:',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),

                const SizedBox(height: 8),

                const Text(
                  'ستظهر هنا ترجمة النص المستخرج',
                  style: TextStyle(color: Colors.grey),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// بناء واجهة ترجمة المستندات
  Widget _buildDocumentTranslation() {
    return Column(
      children: [
        const SizedBox(height: 20),

        // منطقة اختيار المستند
        Container(
          width: double.infinity,
          height: 180,
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withAlpha(26),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.grey.shade300, width: 1),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // أيقونة المستند
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(26),
                      blurRadius: 10,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: IconButton(
                  icon: const Icon(Icons.description, size: 40),
                  color: AppTheme.primaryColor,
                  onPressed: () {
                    // تنفيذ اختيار مستند
                    AppHelpers.showSnackBar(
                      context,
                      'جاري فتح مستكشف الملفات...',
                    );
                  },
                ),
              ),

              const SizedBox(height: 16),

              // زر اختيار المستند
              ElevatedButton.icon(
                icon: const Icon(Icons.upload_file),
                label: const Text('اختيار مستند'),
                onPressed: () {
                  AppHelpers.showSnackBar(
                    context,
                    'جاري فتح مستكشف الملفات...',
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                ),
              ),

              const SizedBox(height: 8),

              const Text(
                'PDF, DOCX, TXT',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
        ),

        const SizedBox(height: 20),

        // منطقة النتائج
        Expanded(
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'حالة الترجمة:',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),

                const SizedBox(height: 8),

                const Text(
                  'اختر مستندًا للبدء في الترجمة',
                  style: TextStyle(color: Colors.grey),
                ),

                const SizedBox(height: 16),

                // زر تنزيل المستند المترجم (معطل حاليًا)
                ElevatedButton.icon(
                  icon: const Icon(Icons.download),
                  label: const Text('تنزيل المستند المترجم'),
                  onPressed: null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    disabledBackgroundColor: Colors.grey.shade300,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// بناء واجهة ترجمة المحادثات
  Widget _buildConversationTranslation() {
    return Column(
      children: [
        const SizedBox(height: 20),

        // منطقة المحادثة
        Expanded(
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'المحادثة:',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),

                const SizedBox(height: 16),

                // رسائل المحادثة
                Expanded(
                  child: ListView(
                    children: [
                      _buildChatMessage(
                        'مرحبًا، كيف حالك؟',
                        'Hello, how are you?',
                        isUser: true,
                      ),
                      _buildChatMessage(
                        'أنا بخير، شكرًا لسؤالك. وأنت؟',
                        'I am fine, thank you for asking. And you?',
                        isUser: false,
                      ),
                      _buildChatMessage(
                        'أنا بخير أيضًا. هل يمكننا البدء في الاجتماع؟',
                        'I am fine too. Can we start the meeting?',
                        isUser: true,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),

        const SizedBox(height: 16),

        // منطقة إدخال النص
        Row(
          children: [
            Expanded(
              child: TextField(
                decoration: InputDecoration(
                  hintText: 'اكتب رسالتك هنا...',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(24),
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                ),
              ),
            ),

            const SizedBox(width: 8),

            // زر الميكروفون
            Container(
              decoration: BoxDecoration(
                color: AppTheme.primaryColor,
                shape: BoxShape.circle,
              ),
              child: IconButton(
                icon: const Icon(Icons.mic, color: Colors.white),
                onPressed: () {
                  AppHelpers.showSnackBar(context, 'جاري الاستماع...');
                },
              ),
            ),

            const SizedBox(width: 8),

            // زر الإرسال
            Container(
              decoration: BoxDecoration(
                color: AppTheme.primaryColor,
                shape: BoxShape.circle,
              ),
              child: IconButton(
                icon: const Icon(Icons.send, color: Colors.white),
                onPressed: () {
                  AppHelpers.showSnackBar(context, 'تم إرسال الرسالة');
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء فقاعة محادثة
  Widget _buildChatMessage(
    String originalText,
    String translatedText, {
    required bool isUser,
  }) {
    return Align(
      alignment: isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color:
              isUser
                  ? AppTheme.primaryColor.withAlpha(26)
                  : Colors.grey.shade200,
          borderRadius: BorderRadius.circular(12),
        ),
        constraints: const BoxConstraints(maxWidth: 250),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              originalText,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 4),
            Text(
              translatedText,
              style: TextStyle(color: Colors.grey.shade700, fontSize: 13),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء واجهة الترجمة الفورية المستمرة
  Widget _buildRealTimeConversationTranslation() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.record_voice_over, size: 80, color: Colors.blue),
          const SizedBox(height: 20),
          const Text(
            'الترجمة الفورية المستمرة',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 10),
          const Text(
            'هذه الميزة ستكون متاحة قريبًا',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
          const SizedBox(height: 20),
          ElevatedButton(
            onPressed: () {
              AppHelpers.showSnackBar(context, 'هذه الميزة قيد التطوير');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: const Text('تجربة الميزة'),
          ),
        ],
      ),
    );
  }

  /// بناء واجهة محادثات متعددة المتحدثين
  Widget _buildMultiSpeakerTranslation() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.group, size: 80, color: Colors.green),
          const SizedBox(height: 20),
          const Text(
            'محادثات متعددة المتحدثين',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 10),
          const Text(
            'هذه الميزة ستكون متاحة قريبًا',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
          const SizedBox(height: 20),
          ElevatedButton(
            onPressed: () {
              AppHelpers.showSnackBar(context, 'هذه الميزة قيد التطوير');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: const Text('تجربة الميزة'),
          ),
        ],
      ),
    );
  }

  /// بناء واجهة وضع الأطفال
  Widget _buildKidsModeTranslation() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.child_care, size: 80, color: Colors.purple),
          const SizedBox(height: 20),
          const Text(
            'وضع الأطفال',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 10),
          const Text(
            'هذه الميزة ستكون متاحة قريبًا',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
          const SizedBox(height: 20),
          ElevatedButton(
            onPressed: () {
              AppHelpers.showSnackBar(context, 'هذه الميزة قيد التطوير');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: const Text('تجربة الميزة'),
          ),
        ],
      ),
    );
  }

  /// بناء واجهة وضع السياحة
  Widget _buildTourismModeTranslation() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.flight, size: 80, color: Colors.blue),
          const SizedBox(height: 20),
          const Text(
            'وضع السياحة',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 10),
          const Text(
            'هذه الميزة ستكون متاحة قريبًا',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
          const SizedBox(height: 20),
          ElevatedButton(
            onPressed: () {
              AppHelpers.showSnackBar(context, 'هذه الميزة قيد التطوير');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: const Text('تجربة الميزة'),
          ),
        ],
      ),
    );
  }

  /// بناء واجهة المحادثة الذكية
  Widget _buildAIChatTranslation() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.smart_toy, size: 80, color: Colors.orange),
          const SizedBox(height: 20),
          const Text(
            'المحادثة الذكية',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 10),
          const Text(
            'هذه الميزة ستكون متاحة قريبًا',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
          const SizedBox(height: 20),
          ElevatedButton(
            onPressed: () {
              AppHelpers.showSnackBar(context, 'هذه الميزة قيد التطوير');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: const Text('تجربة الميزة'),
          ),
        ],
      ),
    );
  }
}
