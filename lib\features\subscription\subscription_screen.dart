import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import '../../core/app_state.dart';
import '../../config/app_theme.dart';
import '../../utils/helpers.dart';
import '../../models/subscription_model.dart';

/// شاشة الاشتراكات
class SubscriptionScreen extends StatefulWidget {
  const SubscriptionScreen({super.key});

  @override
  State<SubscriptionScreen> createState() => _SubscriptionScreenState();
}

class _SubscriptionScreenState extends State<SubscriptionScreen> {
  bool _isLoading = true;
  List<ProductDetails>? _products;
  List<SubscriptionModel> _subscriptions = [];
  int _selectedSubscriptionIndex = 0;

  @override
  void initState() {
    super.initState();
    _loadProducts();

    // إضافة خطط اشتراك افتراضية في حالة عدم توفر المنتجات
    if (_subscriptions.isEmpty) {
      _addDefaultSubscriptions();
    }
  }

  /// إضافة خطط اشتراك افتراضية
  void _addDefaultSubscriptions() {
    final now = DateTime.now();

    _subscriptions = [
      SubscriptionModel(
          productId: 'translator_monthly_subscription',
          name: 'الاشتراك الشهري',
          description: 'اشتراك شهري بميزات متقدمة',
          price: '9.99\$',
          priceValue: 9.99,
          currency: 'USD',
          startDate: now,
          endDate: now.add(const Duration(days: 30)),
          status: SubscriptionStatus.inactive,
          type: SubscriptionType.monthly,
          autoRenew: true,
          lastUpdated: now,
        )
        ..features = [
          'جميع ميزات التطبيق',
          'بدون إعلانات',
          'ترجمة غير محدودة',
          'دعم فني',
        ]
        ..title = 'الاشتراك الشهري'
        ..period = 'شهرياً',

      SubscriptionModel(
          productId: 'translator_yearly_subscription',
          name: 'الاشتراك السنوي',
          description: 'اشتراك سنوي بميزات متقدمة مع خصم 50%',
          price: '59.99\$',
          priceValue: 59.99,
          currency: 'USD',
          startDate: now,
          endDate: now.add(const Duration(days: 365)),
          status: SubscriptionStatus.inactive,
          type: SubscriptionType.yearly,
          autoRenew: true,
          lastUpdated: now,
        )
        ..features = [
          'جميع ميزات التطبيق',
          'بدون إعلانات',
          'ترجمة غير محدودة',
          'دعم فني متميز',
          'تحديثات مجانية',
          'خصم 50% مقارنة بالاشتراك الشهري',
        ]
        ..title = 'الاشتراك السنوي'
        ..period = 'سنوياً',

      SubscriptionModel(
          productId: 'translator_weekly_subscription',
          name: 'الاشتراك الأسبوعي',
          description: 'اشتراك أسبوعي للتجربة',
          price: '2.99\$',
          priceValue: 2.99,
          currency: 'USD',
          startDate: now,
          endDate: now.add(const Duration(days: 7)),
          status: SubscriptionStatus.inactive,
          type: SubscriptionType.weekly,
          autoRenew: true,
          lastUpdated: now,
        )
        ..features = ['جميع ميزات التطبيق', 'بدون إعلانات', 'ترجمة محدودة']
        ..title = 'الاشتراك الأسبوعي'
        ..period = 'أسبوعياً',
    ];

    setState(() {
      _isLoading = false;
    });
  }

  /// تحميل المنتجات
  Future<void> _loadProducts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل المنتجات من متجر التطبيقات
      final products = await InAppPurchase.instance.queryProductDetails({
        'translator_monthly_subscription',
        'translator_yearly_subscription',
        'translator_weekly_subscription',
      });

      // إنشاء نماذج الاشتراكات المتاحة
      final subscriptions = _createSubscriptionModels(products.productDetails);

      if (mounted) {
        setState(() {
          _products = products.productDetails;
          _subscriptions = subscriptions;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء تحميل المنتجات: $e',
          isError: true,
        );
      }
    }
  }

  /// إنشاء نماذج الاشتراكات من المنتجات
  List<SubscriptionModel> _createSubscriptionModels(
    List<ProductDetails> products,
  ) {
    final List<SubscriptionModel> subscriptions = [];
    final now = DateTime.now();

    for (final product in products) {
      SubscriptionType type = SubscriptionType.monthly;
      String name = 'الاشتراك الشهري';
      int periodDays = 30;
      List<String> features = [];

      if (product.id.contains('yearly')) {
        type = SubscriptionType.yearly;
        name = 'الاشتراك السنوي';
        periodDays = 365;
        features = [
          'جميع ميزات التطبيق',
          'بدون إعلانات',
          'ترجمة غير محدودة',
          'دعم فني متميز',
          'تحديثات مجانية',
        ];
      } else if (product.id.contains('weekly')) {
        type = SubscriptionType.weekly;
        name = 'الاشتراك الأسبوعي';
        periodDays = 7;
        features = ['جميع ميزات التطبيق', 'بدون إعلانات', 'ترجمة محدودة'];
      } else {
        // الاشتراك الشهري
        features = [
          'جميع ميزات التطبيق',
          'بدون إعلانات',
          'ترجمة غير محدودة',
          'دعم فني',
        ];
      }

      subscriptions.add(
        SubscriptionModel(
            productId: product.id,
            name: name,
            description: product.description,
            price: product.price,
            priceValue:
                double.tryParse(
                  product.price
                      .replaceAll(RegExp(r'[^\d.,]'), '')
                      .replaceAll(',', '.'),
                ) ??
                9.99,
            currency: product.currencyCode,
            startDate: now,
            endDate: now.add(Duration(days: periodDays)),
            status: SubscriptionStatus.inactive,
            type: type,
            autoRenew: true,
            lastUpdated: now,
          )
          ..features =
              features // إضافة الميزات
          ..title =
              name // إضافة العنوان
          ..period = _getPeriodText(type), // إضافة فترة الاشتراك
      );
    }

    return subscriptions;
  }

  /// الحصول على نص فترة الاشتراك
  String _getPeriodText(SubscriptionType type) {
    switch (type) {
      case SubscriptionType.monthly:
        return 'شهريًا';
      case SubscriptionType.yearly:
        return 'سنويًا';
      case SubscriptionType.weekly:
        return 'أسبوعيًا';
    }
  }

  /// شراء اشتراك
  Future<void> _purchaseSubscription(int index) async {
    setState(() {
      _isLoading = true;
    });

    try {
      // البحث عن المنتج المطابق
      final subscription = _subscriptions[index];

      // التحقق من توفر المنتجات الحقيقية
      if (_products != null && _products!.isNotEmpty) {
        try {
          final product = _products!.firstWhere(
            (p) => p.id == subscription.productId,
          );

          // شراء المنتج
          final purchaseParam = PurchaseParam(productDetails: product);
          final success = await InAppPurchase.instance.buyNonConsumable(
            purchaseParam: purchaseParam,
          );

          if (mounted) {
            setState(() {
              _isLoading = false;
            });

            if (success) {
              AppHelpers.showSnackBar(context, 'تم بدء عملية الشراء');
            } else {
              AppHelpers.showSnackBar(
                context,
                'فشلت عملية الشراء',
                isError: true,
              );
            }
          }
        } catch (e) {
          // المنتج غير متوفر، استخدم الاشتراك الافتراضي
          _showSubscriptionDialog(subscription);
        }
      } else {
        // استخدام الاشتراكات الافتراضية
        _showSubscriptionDialog(subscription);
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء الشراء: $e',
          isError: true,
        );
      }
    }
  }

  /// عرض حوار الاشتراك
  void _showSubscriptionDialog(SubscriptionModel subscription) {
    setState(() {
      _isLoading = false;
    });

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('اشتراك تجريبي'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('اشتراك: ${subscription.name}'),
                const SizedBox(height: 8),
                Text('السعر: ${subscription.price}'),
                const SizedBox(height: 8),
                const Text('هذا اشتراك تجريبي لأغراض العرض فقط.'),
                const Text('سيتم تفعيل الاشتراكات الحقيقية قريباً.'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();

                  // تحديث حالة الاشتراك في التطبيق (للعرض فقط)
                  final appState = Provider.of<AppState>(
                    context,
                    listen: false,
                  );
                  appState.setSubscriptionStatus(true);

                  AppHelpers.showSnackBar(
                    context,
                    'تم تفعيل الاشتراك التجريبي بنجاح!',
                    isError: false,
                  );
                },
                child: const Text('تفعيل الاشتراك التجريبي'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
            ],
          ),
    );
  }

  /// استعادة المشتريات
  Future<void> _restorePurchases() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // استعادة المشتريات
      await InAppPurchase.instance.restorePurchases();

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        AppHelpers.showSnackBar(
          context,
          'تمت استعادة المشتريات بنجاح',
          isError: false,
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء استعادة المشتريات: $e',
          isError: true,
        );
      }
    }
  }

  /// اختيار اشتراك
  void _selectSubscription(int index) {
    setState(() {
      _selectedSubscriptionIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppState>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('الاشتراكات'),
        centerTitle: true,
        backgroundColor: AppTheme.primaryColor,
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // عنوان الصفحة
                    const Text(
                      'اختر خطة الاشتراك',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.onBackground,
                      ),
                    ).animate().fadeIn(duration: 500.ms),

                    const SizedBox(height: 8),

                    // وصف الصفحة
                    const Text(
                      'اشترك للوصول إلى جميع ميزات التطبيق',
                      style: TextStyle(
                        fontSize: 16,
                        color: AppTheme.onSurfaceVariant,
                      ),
                    ).animate().fadeIn(delay: 200.ms, duration: 500.ms),

                    const SizedBox(height: 24),

                    // حالة الاشتراك الحالية
                    _buildCurrentSubscriptionStatus(appState),

                    const SizedBox(height: 24),

                    // بطاقات الاشتراكات
                    ..._subscriptions.asMap().entries.map((entry) {
                      final index = entry.key;
                      final subscription = entry.value;
                      return _buildSubscriptionCard(
                        subscription: subscription,
                        isSelected: _selectedSubscriptionIndex == index,
                        onTap: () => _selectSubscription(index),
                        index: index,
                      );
                    }),

                    const SizedBox(height: 24),

                    // زر الاشتراك
                    ElevatedButton(
                      onPressed:
                          _isLoading
                              ? null
                              : () => _purchaseSubscription(
                                _selectedSubscriptionIndex,
                              ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryColor,
                        foregroundColor: Colors.white,
                        minimumSize: const Size(double.infinity, 56),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                      ),
                      child:
                          _isLoading
                              ? const CircularProgressIndicator(
                                color: Colors.white,
                              )
                              : const Text(
                                'اشترك الآن',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                    ).animate().fadeIn(delay: 400.ms, duration: 500.ms),

                    const SizedBox(height: 16),

                    // زر استعادة المشتريات
                    Center(
                      child: TextButton(
                        onPressed: _isLoading ? null : _restorePurchases,
                        child: const Text(
                          'استعادة المشتريات',
                          style: TextStyle(
                            color: AppTheme.primaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ).animate().fadeIn(delay: 500.ms, duration: 500.ms),

                    const SizedBox(height: 24),

                    // معلومات الاشتراك
                    const Text(
                      'معلومات الاشتراك:',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.onBackground,
                      ),
                    ).animate().fadeIn(delay: 600.ms, duration: 500.ms),

                    const SizedBox(height: 8),

                    // تفاصيل الاشتراك
                    const Text(
                      '• سيتم تجديد الاشتراك تلقائيًا ما لم يتم إلغاؤه قبل 24 ساعة على الأقل من نهاية الفترة الحالية.\n'
                      '• يمكن إدارة الاشتراك وإلغاؤه من خلال إعدادات الحساب في متجر التطبيقات.\n'
                      '• سيتم فرض رسوم التجديد على حسابك خلال 24 ساعة قبل نهاية الفترة الحالية.',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppTheme.onSurfaceVariant,
                        height: 1.5,
                      ),
                    ).animate().fadeIn(delay: 700.ms, duration: 500.ms),
                  ],
                ),
              ),
    );
  }

  /// بناء حالة الاشتراك الحالية
  Widget _buildCurrentSubscriptionStatus(AppState appState) {
    final isPremium = appState.isPremium;
    final isAdsRemoved = appState.isAdsRemoved;
    final premiumExpiry = appState.premiumExpiry;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color:
            isPremium ? Color.fromRGBO(0, 122, 255, 0.1) : Colors.grey.shade100,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isPremium ? AppTheme.primaryColor : Colors.grey.shade300,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isPremium ? Icons.workspace_premium : Icons.info,
                color: isPremium ? AppTheme.primaryColor : Colors.grey,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'الاشتراك الحالي: ${isPremium ? 'الخطة المميزة' : 'الخطة المجانية'}',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color:
                      isPremium ? AppTheme.primaryColor : AppTheme.onBackground,
                ),
              ),
            ],
          ),

          if (isPremium && premiumExpiry != null) ...[
            const SizedBox(height: 8),
            Text(
              'ينتهي في: ${AppHelpers.formatDate(premiumExpiry, format: 'yyyy-MM-dd')}',
              style: TextStyle(fontSize: 14, color: AppTheme.onSurfaceVariant),
            ),
          ],

          const SizedBox(height: 8),

          Text(
            'الإعلانات: ${isAdsRemoved ? 'تم إزالتها' : 'مفعلة'}',
            style: TextStyle(fontSize: 14, color: AppTheme.onSurfaceVariant),
          ),
        ],
      ),
    ).animate().fadeIn(delay: 300.ms, duration: 500.ms);
  }

  /// بناء بطاقة الاشتراك
  Widget _buildSubscriptionCard({
    required SubscriptionModel subscription,
    required bool isSelected,
    required VoidCallback onTap,
    required int index,
  }) {
    return Card(
          elevation: isSelected ? 4 : 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(
              color: isSelected ? AppTheme.primaryColor : Colors.transparent,
              width: 2,
            ),
          ),
          child: InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // عنوان الاشتراك
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        subscription.title,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.onBackground,
                        ),
                      ),
                      if (isSelected)
                        const Icon(
                          Icons.check_circle,
                          color: AppTheme.primaryColor,
                        ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // وصف الاشتراك
                  Text(
                    subscription.description,
                    style: TextStyle(
                      fontSize: 14,
                      color: AppTheme.onSurfaceVariant,
                    ),
                  ),

                  const SizedBox(height: 16),

                  // السعر والفترة
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '\$${subscription.price}',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                      Text(
                        subscription.period,
                        style: TextStyle(
                          fontSize: 14,
                          color: AppTheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // الميزات
                  ...subscription.features.map((feature) {
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.check,
                            color: AppTheme.primaryColor,
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            feature,
                            style: TextStyle(
                              fontSize: 14,
                              color: AppTheme.onBackground,
                            ),
                          ),
                        ],
                      ),
                    );
                  }),
                ],
              ),
            ),
          ),
        )
        .animate()
        .fadeIn(
          delay: Duration(milliseconds: 300 + (index * 100)),
          duration: 500.ms,
        )
        .slideY(
          begin: 0.2,
          end: 0,
          delay: Duration(milliseconds: 300 + (index * 100)),
          duration: 500.ms,
        );
  }
}
