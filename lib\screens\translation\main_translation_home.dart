import 'package:flutter/material.dart';
import '../../config/app_theme.dart';
import '../../config/constants.dart';
import 'translation_screen.dart';
import '../../features/chat/quick_chat_screen.dart';

class MainTranslationHome extends StatelessWidget {
  const MainTranslationHome({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المترجم الذكي AI'),
        backgroundColor: AppTheme.primaryColor,
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: GridView.count(
          crossAxisCount: 2,
          mainAxisSpacing: 16,
          crossAxisSpacing: 16,
          children: [
            ...AppConstants.translationTypes.map((type) {
              return GestureDetector(
                onTap: () {
                  if (type['id'] == 'ai_chat') {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (_) => const QuickChatScreen(),
                      ),
                    );
                  } else {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder:
                            (_) =>
                                TranslationScreen(translationType: type['id']),
                      ),
                    );
                  }
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(20),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        _getIconData(type['icon']),
                        size: 48,
                        color: AppTheme.primaryColor,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        type['title'],
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  IconData _getIconData(String? iconName) {
    switch (iconName) {
      case 'mic':
        return Icons.mic;
      case 'text_fields':
        return Icons.text_fields;
      case 'camera_alt':
        return Icons.camera_alt;
      case 'description':
        return Icons.description;
      case 'forum':
        return Icons.forum;
      case 'record_voice_over':
        return Icons.record_voice_over;
      case 'groups':
        return Icons.groups;
      case 'child_care':
        return Icons.child_care;
      case 'flight':
        return Icons.flight;
      case 'smart_toy':
        return Icons.smart_toy;
      default:
        return Icons.translate;
    }
  }
}
