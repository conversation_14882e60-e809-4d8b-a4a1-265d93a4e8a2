import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../models/duolingo_models.dart';

/// بطاقة الوحدة مثل Duolingo
class UnitCard extends StatefulWidget {
  final LearningUnit unit;
  final VoidCallback onTap;
  final Function(Lesson) onLessonTap;

  const UnitCard({
    super.key,
    required this.unit,
    required this.onTap,
    required this.onLessonTap,
  });

  @override
  State<UnitCard> createState() => _UnitCardState();
}

class _UnitCardState extends State<UnitCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildUnitHeader(),
        AnimatedSize(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          child: _isExpanded ? _buildLessonsList() : const SizedBox.shrink(),
        ),
      ],
    );
  }

  /// رأس الوحدة
  Widget _buildUnitHeader() {
    return GestureDetector(
      onTap: _toggleExpansion,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: widget.unit.isUnlocked
              ? LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    widget.unit.themeColor,
                    widget.unit.themeColor.withValues(alpha: 0.8),
                  ],
                )
              : LinearGradient(
                  colors: [
                    Colors.grey.shade300,
                    Colors.grey.shade400,
                  ],
                ),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: widget.unit.isUnlocked
                  ? widget.unit.themeColor.withValues(alpha: 0.3)
                  : Colors.grey.withValues(alpha: 0.2),
              spreadRadius: 2,
              blurRadius: 15,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Row(
          children: [
            // أيقونة الوحدة
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(30),
              ),
              child: Icon(
                widget.unit.isUnlocked ? _getUnitIcon() : Icons.lock,
                color: Colors.white,
                size: 30,
              ),
            ),
            
            const SizedBox(width: 15),
            
            // معلومات الوحدة
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.unit.titleAr,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    widget.unit.descriptionAr,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.white.withValues(alpha: 0.9),
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildProgressIndicator(),
                ],
              ),
            ),
            
            // أيقونة التوسيع
            AnimatedRotation(
              turns: _isExpanded ? 0.5 : 0,
              duration: const Duration(milliseconds: 300),
              child: Icon(
                Icons.keyboard_arrow_down,
                color: Colors.white,
                size: 30,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// مؤشر التقدم
  Widget _buildProgressIndicator() {
    final completedLessons = widget.unit.lessons
        .where((lesson) => lesson.status == LessonStatus.completed)
        .length;
    final totalLessons = widget.unit.lessons.length;
    final progress = totalLessons > 0 ? completedLessons / totalLessons : 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              '$completedLessons من $totalLessons دروس',
              style: TextStyle(
                fontSize: 12,
                color: Colors.white.withValues(alpha: 0.8),
              ),
            ),
            const Spacer(),
            Text(
              '${(progress * 100).toInt()}%',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.white.withValues(alpha: 0.9),
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Container(
          height: 6,
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(3),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(3),
            child: LinearProgressIndicator(
              value: progress,
              backgroundColor: Colors.transparent,
              valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ),
        ),
      ],
    );
  }

  /// قائمة الدروس
  Widget _buildLessonsList() {
    return Container(
      margin: const EdgeInsets.only(top: 10),
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: widget.unit.lessons.map((lesson) {
          return _buildLessonItem(lesson);
        }).toList(),
      ),
    );
  }

  /// عنصر الدرس
  Widget _buildLessonItem(Lesson lesson) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        widget.onLessonTap(lesson);
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 10),
        padding: const EdgeInsets.all(15),
        decoration: BoxDecoration(
          color: _getLessonBackgroundColor(lesson.status),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: _getLessonBorderColor(lesson.status),
            width: 2,
          ),
        ),
        child: Row(
          children: [
            // أيقونة الدرس
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: _getLessonIconColor(lesson.status),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(
                _getLessonIcon(lesson.status),
                color: Colors.white,
                size: 20,
              ),
            ),
            
            const SizedBox(width: 15),
            
            // معلومات الدرس
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    lesson.titleAr,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: lesson.status == LessonStatus.locked
                          ? Colors.grey.shade500
                          : Colors.black87,
                    ),
                  ),
                  if (lesson.status == LessonStatus.completed) ...[
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        ...List.generate(3, (index) {
                          return Icon(
                            index < lesson.stars ? Icons.star : Icons.star_border,
                            color: Colors.amber,
                            size: 16,
                          );
                        }),
                        const SizedBox(width: 8),
                        Text(
                          '${lesson.xpReward} XP',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
            
            // حالة الدرس
            if (lesson.status == LessonStatus.completed)
              const Icon(
                Icons.check_circle,
                color: Color(0xFF58CC02),
                size: 24,
              )
            else if (lesson.status == LessonStatus.locked)
              const Icon(
                Icons.lock,
                color: Colors.grey,
                size: 24,
              )
            else
              Icon(
                Icons.play_circle_fill,
                color: widget.unit.themeColor,
                size: 24,
              ),
          ],
        ),
      ),
    );
  }

  /// تبديل التوسيع
  void _toggleExpansion() {
    if (!widget.unit.isUnlocked) {
      widget.onTap();
      return;
    }

    setState(() {
      _isExpanded = !_isExpanded;
    });

    if (_isExpanded) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  /// الحصول على أيقونة الوحدة
  IconData _getUnitIcon() {
    switch (widget.unit.level) {
      case DifficultyLevel.beginner:
        return Icons.school;
      case DifficultyLevel.intermediate:
        return Icons.trending_up;
      case DifficultyLevel.advanced:
        return Icons.star;
      case DifficultyLevel.expert:
        return Icons.emoji_events;
    }
  }

  /// الحصول على لون خلفية الدرس
  Color _getLessonBackgroundColor(LessonStatus status) {
    switch (status) {
      case LessonStatus.locked:
        return Colors.grey.shade100;
      case LessonStatus.available:
        return Colors.blue.shade50;
      case LessonStatus.inProgress:
        return Colors.orange.shade50;
      case LessonStatus.completed:
        return Colors.green.shade50;
      case LessonStatus.mastered:
        return Colors.purple.shade50;
    }
  }

  /// الحصول على لون حدود الدرس
  Color _getLessonBorderColor(LessonStatus status) {
    switch (status) {
      case LessonStatus.locked:
        return Colors.grey.shade300;
      case LessonStatus.available:
        return Colors.blue.shade200;
      case LessonStatus.inProgress:
        return Colors.orange.shade200;
      case LessonStatus.completed:
        return Colors.green.shade200;
      case LessonStatus.mastered:
        return Colors.purple.shade200;
    }
  }

  /// الحصول على لون أيقونة الدرس
  Color _getLessonIconColor(LessonStatus status) {
    switch (status) {
      case LessonStatus.locked:
        return Colors.grey;
      case LessonStatus.available:
        return Colors.blue;
      case LessonStatus.inProgress:
        return Colors.orange;
      case LessonStatus.completed:
        return const Color(0xFF58CC02);
      case LessonStatus.mastered:
        return Colors.purple;
    }
  }

  /// الحصول على أيقونة الدرس
  IconData _getLessonIcon(LessonStatus status) {
    switch (status) {
      case LessonStatus.locked:
        return Icons.lock;
      case LessonStatus.available:
        return Icons.play_arrow;
      case LessonStatus.inProgress:
        return Icons.pause;
      case LessonStatus.completed:
        return Icons.check;
      case LessonStatus.mastered:
        return Icons.star;
    }
  }
}
