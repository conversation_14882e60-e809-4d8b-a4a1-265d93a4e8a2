import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/app_state.dart';
import '../../config/app_theme.dart';
import '../../widgets/custom_app_bar.dart';
import '../../config/constants.dart';
import 'package:url_launcher/url_launcher.dart';

/// شاشة الإعدادات
class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppState>(context);

    return Scaffold(
      appBar: const CustomAppBar(title: 'الإعدادات'),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // قسم المظهر
          _buildSectionTitle('المظهر'),
          _buildSettingItem(
            icon: Icons.dark_mode,
            title: 'وضع السمة',
            subtitle: _getThemeModeText(appState.themeMode),
            onTap: () => _showThemeModeDialog(appState),
          ),
          _buildSettingItem(
            icon: Icons.font_download,
            title: 'حجم الخط',
            trailing: DropdownButton<double>(
              value: appState.fontSize,
              items: const [
                DropdownMenuItem(value: 14.0, child: Text('صغير')),
                DropdownMenuItem(value: 16.0, child: Text('متوسط')),
                DropdownMenuItem(value: 18.0, child: Text('كبير')),
              ],
              onChanged: (value) {
                if (value != null) {
                  appState.setFontSize(value);
                }
              },
              underline: Container(),
            ),
          ),

          const Divider(),

          // قسم اللغة
          _buildSectionTitle('اللغة'),
          _buildSettingItem(
            icon: Icons.language,
            title: 'لغة التطبيق',
            trailing: DropdownButton<String>(
              value: appState.appLanguage,
              items: const [
                DropdownMenuItem(value: 'ar', child: Text('العربية')),
                DropdownMenuItem(value: 'en', child: Text('English')),
              ],
              onChanged: (value) {
                if (value != null) {
                  appState.setAppLanguage(value);
                }
              },
              underline: Container(),
            ),
          ),

          const Divider(),

          // قسم الإشعارات
          _buildSectionTitle('الإشعارات'),
          _buildSettingItem(
            icon: Icons.notifications,
            title: 'تفعيل الإشعارات',
            trailing: Switch(
              value: appState.notificationsEnabled,
              onChanged: (value) {
                appState.setNotificationsEnabled(value);
              },
              activeColor: AppTheme.primaryColor,
            ),
          ),

          const Divider(),

          // قسم الخصوصية
          _buildSectionTitle('الخصوصية والأمان'),
          _buildSettingItem(
            icon: Icons.privacy_tip,
            title: 'سياسة الخصوصية',
            onTap: () => _launchURL(AppConstants.privacyPolicyUrl),
          ),
          _buildSettingItem(
            icon: Icons.description,
            title: 'شروط الاستخدام',
            onTap: () => _launchURL(AppConstants.termsOfServiceUrl),
          ),
          _buildSettingItem(
            icon: Icons.delete,
            title: 'حذف بياناتي',
            onTap: _showDeleteDataDialog,
          ),

          const Divider(),

          // قسم الدعم
          _buildSectionTitle('الدعم'),
          _buildSettingItem(
            icon: Icons.help,
            title: 'مركز المساعدة',
            onTap: () => _launchURL(AppConstants.supportUrl),
          ),
          _buildSettingItem(
            icon: Icons.email,
            title: 'تواصل معنا',
            onTap: () => _launchURL(AppConstants.contactUsUrl),
          ),
          _buildSettingItem(
            icon: Icons.star,
            title: 'تقييم التطبيق',
            onTap: _rateApp,
          ),

          const Divider(),

          // قسم حول التطبيق
          _buildSectionTitle('حول التطبيق'),
          _buildSettingItem(
            icon: Icons.info,
            title: 'الإصدار',
            subtitle: AppConstants.appVersion,
          ),
        ],
      ),
    );
  }

  /// بناء عنوان القسم
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppTheme.primaryColor,
        ),
      ),
    );
  }

  /// بناء عنصر الإعدادات
  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    String? subtitle,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: AppTheme.primaryColor),
      title: Text(title),
      subtitle: subtitle != null ? Text(subtitle) : null,
      trailing: trailing,
      onTap: onTap,
      contentPadding: EdgeInsets.zero,
    );
  }

  /// الحصول على نص وضع السمة
  String _getThemeModeText(String themeMode) {
    switch (themeMode) {
      case AppConstants.themeModeLight:
        return 'الوضع الفاتح';
      case AppConstants.themeModeDark:
        return 'الوضع الداكن';
      case AppConstants.themeModeAmoled:
        return 'وضع AMOLED الداكن';
      default:
        return 'الوضع الفاتح';
    }
  }

  /// عرض مربع حوار اختيار وضع السمة
  Future<void> _showThemeModeDialog(AppState appState) async {
    final themeModes = [
      {
        'code': AppConstants.themeModeLight,
        'name': 'الوضع الفاتح',
        'icon': Icons.light_mode,
      },
      {
        'code': AppConstants.themeModeDark,
        'name': 'الوضع الداكن',
        'icon': Icons.dark_mode,
      },
      {
        'code': AppConstants.themeModeAmoled,
        'name': 'وضع AMOLED الداكن',
        'icon': Icons.nights_stay,
        'description': 'أسود خالص لشاشات AMOLED',
      },
    ];

    final currentThemeMode = appState.themeMode;

    final selectedThemeMode = await showDialog<String>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('اختر وضع السمة'),
            content: SizedBox(
              width: double.maxFinite,
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: themeModes.length,
                itemBuilder: (context, index) {
                  final themeMode = themeModes[index];
                  final isSelected = themeMode['code'] == currentThemeMode;

                  return ListTile(
                    leading: Icon(
                      themeMode['icon'] as IconData,
                      color: isSelected ? AppTheme.primaryColor : null,
                    ),
                    title: Text(themeMode['name'] as String),
                    subtitle:
                        themeMode['description'] != null
                            ? Text(themeMode['description'] as String)
                            : null,
                    trailing:
                        isSelected
                            ? const Icon(
                              Icons.check_circle,
                              color: AppTheme.primaryColor,
                            )
                            : null,
                    onTap: () {
                      Navigator.pop(context, themeMode['code'] as String);
                    },
                    selected: isSelected,
                  );
                },
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
            ],
          ),
    );

    if (selectedThemeMode != null) {
      appState.setThemeMode(selectedThemeMode);
    }
  }

  /// فتح رابط URL
  Future<void> _launchURL(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('لا يمكن فتح الرابط')));
      }
    }
  }

  /// تقييم التطبيق
  void _rateApp() {
    // تنفيذ فتح متجر التطبيقات للتقييم
    debugPrint('فتح صفحة التقييم في متجر التطبيقات');
  }

  /// عرض مربع حوار حذف البيانات
  void _showDeleteDataDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('حذف البيانات'),
            content: const Text(
              'هل أنت متأكد من رغبتك في حذف جميع بياناتك؟ لا يمكن التراجع عن هذا الإجراء.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  _deleteUserData();
                },
                child: const Text('حذف', style: TextStyle(color: Colors.red)),
              ),
            ],
          ),
    );
  }

  /// حذف بيانات المستخدم
  void _deleteUserData() {
    // تنفيذ حذف البيانات
    debugPrint('حذف بيانات المستخدم');

    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('تم حذف بياناتك بنجاح')));
  }
}
