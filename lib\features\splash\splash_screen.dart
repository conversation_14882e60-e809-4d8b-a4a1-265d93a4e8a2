import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:lottie/lottie.dart';
import '../../config/app_theme.dart';
import '../../screens/auth/simple_login_screen.dart';

/// شاشة البداية
class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  bool _showContent = false;
  bool _showButton = false;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );

    _animationController.forward();

    // إظهار المحتوى بعد 500 مللي ثانية
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          _showContent = true;
        });
      }
    });

    // إظهار الزر بعد 1.5 ثانية
    Future.delayed(const Duration(milliseconds: 1500), () {
      if (mounted) {
        setState(() {
          _showButton = true;
        });
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// الانتقال إلى الصفحة التالية
  void _navigateToNextScreen() {
    // الانتقال مباشرة إلى شاشة تسجيل الدخول
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(builder: (_) => const SimpleLoginScreen()),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(gradient: AppTheme.primaryGradient),
        child: SafeArea(
          child: Stack(
            children: [
              // خلفية متحركة
              ...List.generate(20, (index) {
                final random = math.Random();
                final size = random.nextDouble() * 20 + 5;
                final initialX =
                    random.nextDouble() * MediaQuery.of(context).size.width;
                final initialY =
                    random.nextDouble() * MediaQuery.of(context).size.height;

                return Positioned(
                  left: initialX,
                  top: initialY,
                  child: AnimatedBuilder(
                    animation: _animationController,
                    builder: (context, child) {
                      return Transform.translate(
                        offset: Offset(
                          math.sin(
                                _animationController.value * math.pi * 2 +
                                    index,
                              ) *
                              20,
                          math.cos(
                                _animationController.value * math.pi * 2 +
                                    index,
                              ) *
                              20,
                        ),
                        child: Container(
                          width: size,
                          height: size,
                          decoration: BoxDecoration(
                            color: Colors.white.withAlpha(50),
                            shape: BoxShape.circle,
                          ),
                        ),
                      );
                    },
                  ),
                );
              }),

              // المحتوى الرئيسي
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // شعار التطبيق
                    if (_showContent)
                      Container(
                            width: 180,
                            height: 180,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(40),
                              boxShadow: [
                                BoxShadow(
                                  color: AppTheme.shadowColor.withValues(
                                    alpha: 0.3,
                                  ),
                                  blurRadius: 10,
                                  offset: const Offset(0, 5),
                                ),
                              ],
                            ),
                            child: Center(
                              child: Icon(
                                Icons.translate,
                                size: 100,
                                color: AppTheme.primaryColor,
                              ),
                            ),
                          )
                          .animate()
                          .fadeIn(duration: 600.ms)
                          .scale(
                            duration: 600.ms,
                            curve: Curves.easeOutBack,
                            begin: const Offset(0.5, 0.5),
                            end: const Offset(1.0, 1.0),
                          ),

                    const SizedBox(height: 40),

                    // اسم التطبيق
                    if (_showContent)
                      Text(
                            'المترجم الذكي AI',
                            style: TextStyle(
                              fontSize: 36,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                              shadows: [
                                Shadow(
                                  color: Colors.black.withAlpha(50),
                                  blurRadius: 10,
                                  offset: const Offset(0, 5),
                                ),
                              ],
                            ),
                          )
                          .animate()
                          .fadeIn(delay: 200.ms, duration: 600.ms)
                          .slideY(
                            delay: 200.ms,
                            duration: 600.ms,
                            begin: 0.5,
                            end: 0,
                            curve: Curves.easeOutBack,
                          ),

                    const SizedBox(height: 16),

                    // وصف التطبيق
                    if (_showContent)
                      Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 24,
                              vertical: 12,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white.withAlpha(30),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Text(
                              'ترجمة ذكية بتقنيات الذكاء الاصطناعي',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w500,
                                color: Colors.white,
                              ),
                            ),
                          )
                          .animate()
                          .fadeIn(delay: 400.ms, duration: 600.ms)
                          .slideY(
                            delay: 400.ms,
                            duration: 600.ms,
                            begin: 0.5,
                            end: 0,
                            curve: Curves.easeOutBack,
                          ),

                    const SizedBox(height: 60),

                    // رسوم متحركة للتحميل
                    if (_showContent && !_showButton)
                      SizedBox(
                        width: 100,
                        height: 100,
                        child: Lottie.asset(
                          'assets/animations/loading.json',
                          fit: BoxFit.contain,
                        ),
                      ).animate().fadeIn(delay: 600.ms, duration: 600.ms),

                    // زر البدء
                    if (_showButton)
                      SizedBox(
                            width: MediaQuery.of(context).size.width * 0.7,
                            child: ElevatedButton(
                              onPressed: _navigateToNextScreen,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.white,
                                foregroundColor: AppTheme.primaryColor,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 32,
                                  vertical: 16,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                elevation: 5,
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Text(
                                    'ابدأ الآن',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Icon(Icons.arrow_forward_ios, size: 18),
                                ],
                              ),
                            ),
                          )
                          .animate()
                          .fadeIn(duration: 600.ms)
                          .scale(
                            duration: 600.ms,
                            curve: Curves.easeOutBack,
                            begin: const Offset(0.8, 0.8),
                            end: const Offset(1.0, 1.0),
                          ),
                  ],
                ),
              ),

              // نسخة التطبيق
              if (_showContent)
                Positioned(
                  bottom: 20,
                  left: 0,
                  right: 0,
                  child: Text(
                    'الإصدار 1.0.0',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.white.withAlpha(150),
                      fontSize: 14,
                    ),
                  ).animate().fadeIn(delay: 800.ms, duration: 600.ms),
                ),
            ],
          ),
        ),
      ),
    );
  }
}

/// صفحة مؤقتة للصفحة الرئيسية
class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المترجم الذكي AI'),
        backgroundColor: AppTheme.primaryColor,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.check_circle,
              color: AppTheme.successColor,
              size: 100,
            ),
            const SizedBox(height: 20),
            const Text(
              'تم تشغيل التطبيق بنجاح!',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            const Text(
              'مرحبًا بك في تطبيق المترجم الذكي',
              style: TextStyle(fontSize: 18),
            ),
            const SizedBox(height: 40),
            ElevatedButton(
              onPressed: () {
                // إنشاء صفحة ترجمة مؤقتة والانتقال إليها
                Navigator.of(context).push(
                  MaterialPageRoute(builder: (_) => const TranslationScreen()),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text(
                'ابدأ الترجمة',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// صفحة الترجمة المؤقتة
class TranslationScreen extends StatefulWidget {
  const TranslationScreen({super.key});

  @override
  State<TranslationScreen> createState() => _TranslationScreenState();
}

class _TranslationScreenState extends State<TranslationScreen> {
  final TextEditingController _sourceTextController = TextEditingController();
  String _translatedText = '';
  String _sourceLanguage = 'ar';
  String _targetLanguage = 'en';
  bool _isTranslating = false;

  @override
  void dispose() {
    _sourceTextController.dispose();
    super.dispose();
  }

  /// ترجمة النص (محاكاة)
  void _translateText() {
    if (_sourceTextController.text.isEmpty) {
      return;
    }

    setState(() {
      _isTranslating = true;
    });

    // محاكاة عملية الترجمة
    Future.delayed(const Duration(seconds: 1), () {
      if (!mounted) return;

      setState(() {
        // ترجمة بسيطة للتوضيح فقط
        if (_sourceLanguage == 'ar' && _targetLanguage == 'en') {
          if (_sourceTextController.text == 'مرحبا') {
            _translatedText = 'Hello';
          } else if (_sourceTextController.text == 'كيف حالك') {
            _translatedText = 'How are you';
          } else {
            _translatedText = 'Translated text will appear here';
          }
        } else {
          if (_sourceTextController.text == 'Hello') {
            _translatedText = 'مرحبا';
          } else if (_sourceTextController.text == 'How are you') {
            _translatedText = 'كيف حالك';
          } else {
            _translatedText = 'النص المترجم سيظهر هنا';
          }
        }

        _isTranslating = false;
      });
    });
  }

  /// تبديل اللغات
  void _swapLanguages() {
    setState(() {
      final temp = _sourceLanguage;
      _sourceLanguage = _targetLanguage;
      _targetLanguage = temp;
      _translatedText = '';
    });
  }

  /// الحصول على اسم اللغة
  String _getLanguageName(String code) {
    switch (code) {
      case 'ar':
        return '🇸🇦 العربية';
      case 'en':
        return '🇺🇸 English';
      default:
        return code;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('ترجمة النصوص'),
        backgroundColor: AppTheme.primaryColor,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // اختيار اللغات
            Row(
              children: [
                // اللغة المصدر
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _getLanguageName(_sourceLanguage),
                      textAlign: TextAlign.center,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                ),

                // زر تبديل اللغات
                IconButton(
                  onPressed: _swapLanguages,
                  icon: const Icon(Icons.swap_horiz),
                  color: AppTheme.primaryColor,
                ),

                // اللغة الهدف
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _getLanguageName(_targetLanguage),
                      textAlign: TextAlign.center,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // حقل النص المصدر
            TextField(
              controller: _sourceTextController,
              decoration: InputDecoration(
                hintText: 'أدخل النص للترجمة...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Colors.grey.shade100,
              ),
              maxLines: 5,
              textDirection:
                  _sourceLanguage == 'ar'
                      ? TextDirection.rtl
                      : TextDirection.ltr,
            ),

            const SizedBox(height: 20),

            // زر الترجمة
            ElevatedButton(
              onPressed: _isTranslating ? null : _translateText,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                minimumSize: const Size(double.infinity, 50),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child:
                  _isTranslating
                      ? const CircularProgressIndicator(color: Colors.white)
                      : const Text(
                        'ترجم',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
            ),

            const SizedBox(height: 20),

            // النص المترجم
            if (_translatedText.isNotEmpty)
              Expanded(
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: AppTheme.primaryColor.withAlpha(75),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الترجمة:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                      const SizedBox(height: 10),
                      Text(
                        _translatedText,
                        style: const TextStyle(fontSize: 18),
                        textDirection:
                            _targetLanguage == 'ar'
                                ? TextDirection.rtl
                                : TextDirection.ltr,
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
