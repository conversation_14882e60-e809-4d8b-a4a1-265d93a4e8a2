import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:lottie/lottie.dart';
import 'package:provider/provider.dart';
import '../../config/app_theme.dart';
import '../../core/app_state.dart';
import '../home/<USER>';
import '../../screens/auth/simple_login_screen.dart';
import 'onboarding_data.dart';

/// شاشة الترحيب
class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  final List<OnboardingItem> _items = onboardingData;

  // حساب ما إذا كانت الصفحة الحالية هي الصفحة الأخيرة
  bool get _isLastPage => _currentPage == _items.length - 1;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  /// الانتقال إلى الصفحة التالية
  void _nextPage() {
    if (_isLastPage) {
      _navigateToHome();
    } else {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  // تم إزالة دالة _previousPage لأنها غير مستخدمة

  /// تخطي الترحيب والانتقال إلى الصفحة الرئيسية
  void _skipOnboarding() {
    _navigateToHome();
  }

  /// الانتقال إلى الصفحة الرئيسية
  void _navigateToHome() {
    // تحديث حالة الاستخدام الأول
    final appState = Provider.of<AppState>(context, listen: false);
    appState.setFirstTimeStatus(false);

    // استخدام الانتقال العادي لتجنب المشاكل
    if (appState.isLoggedIn) {
      Navigator.of(
        context,
      ).pushReplacement(MaterialPageRoute(builder: (_) => const HomeScreen()));
    } else {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (_) => const SimpleLoginScreen()),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            // زر التخطي
            Align(
              alignment: Alignment.topLeft,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: TextButton(
                  onPressed: _skipOnboarding,
                  child: const Text(
                    'تخطي',
                    style: TextStyle(
                      color: AppTheme.secondaryColor,
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
            ),

            // عارض الصفحات
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                itemCount: _items.length,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                itemBuilder: (context, index) {
                  return _buildOnboardingPage(_items[index]);
                },
              ),
            ),

            // مؤشرات الصفحات
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(
                _items.length,
                (index) => Container(
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  width: 10,
                  height: 10,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color:
                        _currentPage == index
                            ? AppTheme.primaryColor
                            : Colors.grey.shade300,
                  ),
                ),
              ),
            ),

            const SizedBox(height: 20),

            // زر التالي
            Padding(
              padding: const EdgeInsets.all(20.0),
              child: ElevatedButton(
                onPressed: _nextPage,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  minimumSize: const Size(double.infinity, 56),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                child: Text(
                  _currentPage == _items.length - 1 ? 'لنبدأ' : 'التالي',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء صفحة الترحيب
  Widget _buildOnboardingPage(OnboardingItem item) {
    try {
      return Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أيقونة
            Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    color: Color.fromRGBO(0, 122, 255, 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Lottie.asset(
                    item.animationPath,
                    width: 100,
                    height: 100,
                    errorBuilder: (context, error, stackTrace) {
                      debugPrint('Error loading Lottie animation: $error');
                      return Icon(
                        Icons.image_not_supported,
                        size: 100,
                        color: AppTheme.primaryColor,
                      );
                    },
                  ),
                )
                .animate()
                .fadeIn(duration: 500.ms)
                .scale(delay: 200.ms, duration: 500.ms),

            const SizedBox(height: 40),

            // العنوان
            Text(
                  item.title,
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.onBackground,
                  ),
                )
                .animate()
                .fadeIn(delay: 300.ms, duration: 500.ms)
                .slideY(begin: 0.3, end: 0, delay: 300.ms, duration: 500.ms),

            const SizedBox(height: 20),

            // الوصف
            Text(
              item.description,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.onSurfaceVariant,
                height: 1.5,
              ),
            ).animate().fadeIn(delay: 500.ms, duration: 500.ms),
          ],
        ),
      );
    } catch (e) {
      // في حالة حدوث خطأ في الرسوم المتحركة، نعيد الصفحة بدون رسوم متحركة
      debugPrint('Error in onboarding page animation: $e');
      return Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أيقونة
            Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                color: Color.fromRGBO(0, 122, 255, 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.info_outline,
                size: 100,
                color: AppTheme.primaryColor,
              ),
            ),

            const SizedBox(height: 40),

            // العنوان
            Text(
              item.title,
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: AppTheme.onBackground,
              ),
            ),

            const SizedBox(height: 20),

            // الوصف
            Text(
              item.description,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.onSurfaceVariant,
                height: 1.5,
              ),
            ),
          ],
        ),
      );
    }
  }
}
