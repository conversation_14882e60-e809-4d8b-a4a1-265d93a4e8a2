rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // قواعد رفع الصور للترجمة - يمكن للمستخدمين المسجلين فقط
    match /user_uploads/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // قواعد رفع المستندات للترجمة - يمكن للمستخدمين المسجلين فقط
    match /documents/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId
        && request.resource.size < 10 * 1024 * 1024; // حد أقصى 10 ميجابايت
    }
    
    // قواعد صور الملف الشخصي - يمكن للمستخدم تحديث صورته فقط
    match /profile_images/{userId} {
      allow read: if true; // يمكن للجميع قراءة صور الملف الشخصي
      allow write: if request.auth != null && request.auth.uid == userId
        && request.resource.size < 5 * 1024 * 1024 // حد أقصى 5 ميجابايت
        && request.resource.contentType.matches('image/.*'); // صور فقط
    }
    
    // قواعد التسجيلات الصوتية - يمكن للمستخدمين المسجلين فقط
    match /audio_recordings/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId
        && request.resource.size < 50 * 1024 * 1024; // حد أقصى 50 ميجابايت
    }
    
    // منع الوصول لأي ملفات أخرى
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
