<?xml version="1.0" encoding="utf-8"?>
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="@color/menuItemRipple">
    <item android:id="@android:id/mask">
        <shape android:shape="rectangle">
            <solid android:color="@color/menuItemRipple" />
            <corners android:radius="4dp" />
        </shape>
    </item>
    <item android:id="@android:id/background">
        <selector>
            <item android:state_pressed="true">
                <shape android:shape="rectangle">
                    <solid android:color="@color/menuItemSelected" />
                    <corners android:radius="4dp" />
                </shape>
            </item>
            <item android:state_selected="true">
                <shape android:shape="rectangle">
                    <solid android:color="@color/menuItemSelected" />
                    <corners android:radius="4dp" />
                </shape>
            </item>
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="@color/menuBackground" />
                    <corners android:radius="4dp" />
                </shape>
            </item>
        </selector>
    </item>
</ripple>
