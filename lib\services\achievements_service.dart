import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/educational_content.dart';

/// خدمة إدارة الإنجازات
class AchievementsService {
  static const String _achievementsKey = 'user_achievements';
  static const String _statsKey = 'user_stats';

  List<Achievement> _unlockedAchievements = [];
  UserStats _userStats = UserStats();
  bool _isInitialized = false;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadAchievements();
      await _loadUserStats();
      _isInitialized = true;
      debugPrint('✅ تم تهيئة خدمة الإنجازات بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة الإنجازات: $e');
    }
  }

  /// الحصول على جميع الإنجازات المتاحة
  List<AchievementDefinition> getAllAchievements() {
    return [
      // إنجازات البداية
      AchievementDefinition(
        id: 'first_lesson',
        title: 'البداية',
        titleAr: 'البداية',
        description: 'Complete your first lesson',
        descriptionAr: 'أكمل درسك الأول',
        iconPath: 'assets/achievements/first_lesson.png',
        color: Colors.blue,
        type: AchievementType.firstLesson,
        requirement: 1,
      ),
      AchievementDefinition(
        id: 'first_perfect',
        title: 'مثالي',
        titleAr: 'مثالي',
        description: 'Get a perfect score',
        descriptionAr: 'احصل على نتيجة مثالية',
        iconPath: 'assets/achievements/perfect.png',
        color: Colors.amber,
        type: AchievementType.perfectScore,
        requirement: 1,
      ),

      // إنجازات التقدم
      AchievementDefinition(
        id: 'lessons_5',
        title: 'متعلم نشط',
        titleAr: 'متعلم نشط',
        description: 'Complete 5 lessons',
        descriptionAr: 'أكمل 5 دروس',
        iconPath: 'assets/achievements/active_learner.png',
        color: Colors.green,
        type: AchievementType.categoryMaster,
        requirement: 5,
      ),
      AchievementDefinition(
        id: 'lessons_10',
        title: 'متعلم متفاني',
        titleAr: 'متعلم متفاني',
        description: 'Complete 10 lessons',
        descriptionAr: 'أكمل 10 دروس',
        iconPath: 'assets/achievements/dedicated.png',
        color: Colors.purple,
        type: AchievementType.persistent,
        requirement: 10,
      ),
      AchievementDefinition(
        id: 'lessons_25',
        title: 'خبير التعلم',
        titleAr: 'خبير التعلم',
        description: 'Complete 25 lessons',
        descriptionAr: 'أكمل 25 درس',
        iconPath: 'assets/achievements/expert.png',
        color: Colors.orange,
        type: AchievementType.persistent,
        requirement: 25,
      ),

      // إنجازات السرعة
      AchievementDefinition(
        id: 'speed_demon',
        title: 'سريع البرق',
        titleAr: 'سريع البرق',
        description: 'Complete a lesson in under 2 minutes',
        descriptionAr: 'أكمل درساً في أقل من دقيقتين',
        iconPath: 'assets/achievements/speed.png',
        color: Colors.red,
        type: AchievementType.speedLearner,
        requirement: 1,
      ),

      // إنجازات الأيام المتتالية
      AchievementDefinition(
        id: 'streak_3',
        title: 'ثابت',
        titleAr: 'ثابت',
        description: 'Learn for 3 days in a row',
        descriptionAr: 'تعلم لمدة 3 أيام متتالية',
        iconPath: 'assets/achievements/streak_3.png',
        color: Colors.cyan,
        type: AchievementType.streakDays,
        requirement: 3,
      ),
      AchievementDefinition(
        id: 'streak_7',
        title: 'مثابر',
        titleAr: 'مثابر',
        description: 'Learn for 7 days in a row',
        descriptionAr: 'تعلم لمدة 7 أيام متتالية',
        iconPath: 'assets/achievements/streak_7.png',
        color: Colors.indigo,
        type: AchievementType.streakDays,
        requirement: 7,
      ),
      AchievementDefinition(
        id: 'streak_30',
        title: 'أسطورة',
        titleAr: 'أسطورة',
        description: 'Learn for 30 days in a row',
        descriptionAr: 'تعلم لمدة 30 يوم متتالي',
        iconPath: 'assets/achievements/legend.png',
        color: Colors.deepPurple,
        type: AchievementType.streakDays,
        requirement: 30,
      ),

      // إنجازات النقاط
      AchievementDefinition(
        id: 'points_100',
        title: 'جامع النقاط',
        titleAr: 'جامع النقاط',
        description: 'Earn 100 points',
        descriptionAr: 'اجمع 100 نقطة',
        iconPath: 'assets/achievements/points_100.png',
        color: Colors.amber,
        type: AchievementType.categoryMaster,
        requirement: 100,
      ),
      AchievementDefinition(
        id: 'points_500',
        title: 'ملك النقاط',
        titleAr: 'ملك النقاط',
        description: 'Earn 500 points',
        descriptionAr: 'اجمع 500 نقطة',
        iconPath: 'assets/achievements/points_500.png',
        color: Colors.deepOrange,
        type: AchievementType.categoryMaster,
        requirement: 500,
      ),
    ];
  }

  /// الحصول على الإنجازات المفتوحة
  List<Achievement> getUnlockedAchievements() => _unlockedAchievements;

  /// الحصول على إحصائيات المستخدم
  UserStats getUserStats() => _userStats;

  /// تحديث التقدم وفحص الإنجازات الجديدة
  Future<List<Achievement>> updateProgress({
    int? lessonsCompleted,
    int? totalScore,
    int? perfectScores,
    bool? fastCompletion,
    int? currentStreak,
  }) async {
    final newAchievements = <Achievement>[];

    // تحديث الإحصائيات
    if (lessonsCompleted != null) {
      _userStats = _userStats.copyWith(lessonsCompleted: lessonsCompleted);
    }
    if (totalScore != null) {
      _userStats = _userStats.copyWith(totalScore: totalScore);
    }
    if (perfectScores != null) {
      _userStats = _userStats.copyWith(perfectScores: perfectScores);
    }
    if (currentStreak != null) {
      _userStats = _userStats.copyWith(currentStreak: currentStreak);
      if (currentStreak > _userStats.longestStreak) {
        _userStats = _userStats.copyWith(longestStreak: currentStreak);
      }
    }
    if (fastCompletion == true) {
      _userStats = _userStats.copyWith(
        fastCompletions: _userStats.fastCompletions + 1,
      );
    }

    // فحص الإنجازات الجديدة
    final allAchievements = getAllAchievements();
    for (final achievementDef in allAchievements) {
      if (!_isAchievementUnlocked(achievementDef.id) &&
          _checkAchievementRequirement(achievementDef)) {
        final newAchievement = Achievement(
          id: achievementDef.id,
          title: achievementDef.title,
          titleAr: achievementDef.titleAr,
          description: achievementDef.description,
          descriptionAr: achievementDef.descriptionAr,
          iconPath: achievementDef.iconPath,
          color: achievementDef.color,
          earnedAt: DateTime.now(),
          type: achievementDef.type,
        );

        _unlockedAchievements.add(newAchievement);
        newAchievements.add(newAchievement);
      }
    }

    // حفظ البيانات
    await _saveAchievements();
    await _saveUserStats();

    return newAchievements;
  }

  /// فحص متطلبات الإنجاز
  bool _checkAchievementRequirement(AchievementDefinition achievement) {
    switch (achievement.type) {
      case AchievementType.firstLesson:
        return _userStats.lessonsCompleted >= achievement.requirement;
      case AchievementType.perfectScore:
        return _userStats.perfectScores >= achievement.requirement;
      case AchievementType.streakDays:
        return _userStats.longestStreak >= achievement.requirement;
      case AchievementType.categoryMaster:
        if (achievement.id.startsWith('lessons_')) {
          return _userStats.lessonsCompleted >= achievement.requirement;
        } else if (achievement.id.startsWith('points_')) {
          return _userStats.totalScore >= achievement.requirement;
        }
        return false;
      case AchievementType.speedLearner:
        return _userStats.fastCompletions >= achievement.requirement;
      case AchievementType.persistent:
        return _userStats.lessonsCompleted >= achievement.requirement;
    }
  }

  /// فحص إذا كان الإنجاز مفتوح
  bool _isAchievementUnlocked(String achievementId) {
    return _unlockedAchievements.any(
      (achievement) => achievement.id == achievementId,
    );
  }

  /// تحميل الإنجازات
  Future<void> _loadAchievements() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final achievementsData = prefs.getString(_achievementsKey);

      if (achievementsData != null) {
        final List<dynamic> achievementsList = jsonDecode(achievementsData);
        _unlockedAchievements =
            achievementsList.map((data) {
              return Achievement(
                id: data['id'],
                title: data['title'],
                titleAr: data['titleAr'],
                description: data['description'],
                descriptionAr: data['descriptionAr'],
                iconPath: data['iconPath'],
                color: Color.fromARGB(
                  data['colorAlpha'] as int,
                  data['colorRed'] as int,
                  data['colorGreen'] as int,
                  data['colorBlue'] as int,
                ),
                earnedAt: DateTime.parse(data['earnedAt']),
                type: AchievementType.values.firstWhere(
                  (type) => type.toString() == data['type'],
                  orElse: () => AchievementType.categoryMaster,
                ),
              );
            }).toList();
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الإنجازات: $e');
    }
  }

  /// حفظ الإنجازات
  Future<void> _saveAchievements() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final achievementsData =
          _unlockedAchievements.map((achievement) {
            return {
              'id': achievement.id,
              'title': achievement.title,
              'titleAr': achievement.titleAr,
              'description': achievement.description,
              'descriptionAr': achievement.descriptionAr,
              'iconPath': achievement.iconPath,
              'colorRed': achievement.color.r,
              'colorGreen': achievement.color.g,
              'colorBlue': achievement.color.b,
              'colorAlpha': achievement.color.a,
              'earnedAt': achievement.earnedAt.toIso8601String(),
              'type': achievement.type.toString(),
            };
          }).toList();

      await prefs.setString(_achievementsKey, jsonEncode(achievementsData));
    } catch (e) {
      debugPrint('❌ خطأ في حفظ الإنجازات: $e');
    }
  }

  /// تحميل إحصائيات المستخدم
  Future<void> _loadUserStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final statsData = prefs.getString(_statsKey);

      if (statsData != null) {
        final Map<String, dynamic> data = jsonDecode(statsData);
        _userStats = UserStats(
          lessonsCompleted: data['lessonsCompleted'] ?? 0,
          totalScore: data['totalScore'] ?? 0,
          perfectScores: data['perfectScores'] ?? 0,
          currentStreak: data['currentStreak'] ?? 0,
          longestStreak: data['longestStreak'] ?? 0,
          fastCompletions: data['fastCompletions'] ?? 0,
          lastActivityDate:
              data['lastActivityDate'] != null
                  ? DateTime.parse(data['lastActivityDate'])
                  : DateTime.now(),
        );
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل إحصائيات المستخدم: $e');
    }
  }

  /// حفظ إحصائيات المستخدم
  Future<void> _saveUserStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final statsData = {
        'lessonsCompleted': _userStats.lessonsCompleted,
        'totalScore': _userStats.totalScore,
        'perfectScores': _userStats.perfectScores,
        'currentStreak': _userStats.currentStreak,
        'longestStreak': _userStats.longestStreak,
        'fastCompletions': _userStats.fastCompletions,
        'lastActivityDate': _userStats.lastActivityDate.toIso8601String(),
      };

      await prefs.setString(_statsKey, jsonEncode(statsData));
    } catch (e) {
      debugPrint('❌ خطأ في حفظ إحصائيات المستخدم: $e');
    }
  }
}

/// تعريف الإنجاز
class AchievementDefinition {
  final String id;
  final String title;
  final String titleAr;
  final String description;
  final String descriptionAr;
  final String iconPath;
  final Color color;
  final AchievementType type;
  final int requirement;

  const AchievementDefinition({
    required this.id,
    required this.title,
    required this.titleAr,
    required this.description,
    required this.descriptionAr,
    required this.iconPath,
    required this.color,
    required this.type,
    required this.requirement,
  });
}

/// إحصائيات المستخدم
class UserStats {
  final int lessonsCompleted;
  final int totalScore;
  final int perfectScores;
  final int currentStreak;
  final int longestStreak;
  final int fastCompletions;
  final DateTime lastActivityDate;

  UserStats({
    this.lessonsCompleted = 0,
    this.totalScore = 0,
    this.perfectScores = 0,
    this.currentStreak = 0,
    this.longestStreak = 0,
    this.fastCompletions = 0,
    DateTime? lastActivityDate,
  }) : lastActivityDate = lastActivityDate ?? DateTime.now();

  UserStats copyWith({
    int? lessonsCompleted,
    int? totalScore,
    int? perfectScores,
    int? currentStreak,
    int? longestStreak,
    int? fastCompletions,
    DateTime? lastActivityDate,
  }) {
    return UserStats(
      lessonsCompleted: lessonsCompleted ?? this.lessonsCompleted,
      totalScore: totalScore ?? this.totalScore,
      perfectScores: perfectScores ?? this.perfectScores,
      currentStreak: currentStreak ?? this.currentStreak,
      longestStreak: longestStreak ?? this.longestStreak,
      fastCompletions: fastCompletions ?? this.fastCompletions,
      lastActivityDate: lastActivityDate ?? this.lastActivityDate,
    );
  }
}
