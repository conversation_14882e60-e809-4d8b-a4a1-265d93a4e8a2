import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart';
import 'package:pdf/pdf.dart' as pdf_lib;
import 'package:pdf/widgets.dart' as pw;
import '../../../services/api/translation_service.dart';

/// خدمة ترجمة المستندات
class DocumentTranslationService {
  final TranslationService _translationService;

  DocumentTranslationService(this._translationService);

  /// استخراج النص من ملف PDF
  Future<String> extractTextFromPdf(File pdfFile) async {
    try {
      // تحميل ملف PDF
      final PdfDocument document = PdfDocument(
        inputBytes: await pdfFile.readAsBytes(),
      );

      // استخراج النص
      String text = '';

      // استخراج النص من كل صفحة
      for (int i = 0; i < document.pages.count; i++) {
        final PdfTextExtractor extractor = PdfTextExtractor(document);
        final String pageText = extractor.extractText(
          startPageIndex: i,
          endPageIndex: i,
        );
        text = '$text$pageText\n\n';
      }

      // إغلاق المستند
      document.dispose();

      return text;
    } catch (e) {
      debugPrint('Error extracting text from PDF: $e');
      throw Exception('فشل استخراج النص من ملف PDF: $e');
    }
  }

  /// ترجمة نص المستند
  Future<String> translateDocumentText({
    required String text,
    required String sourceLanguage,
    required String targetLanguage,
  }) async {
    try {
      // تقسيم النص إلى أجزاء أصغر للترجمة (لتجنب تجاوز حدود API)
      final List<String> paragraphs = text.split('\n\n');
      final List<String> translatedParagraphs = [];

      // ترجمة كل فقرة على حدة
      for (final paragraph in paragraphs) {
        if (paragraph.trim().isEmpty) {
          translatedParagraphs.add('');
          continue;
        }

        final translatedParagraph = await _translationService.translateText(
          text: paragraph,
          sourceLanguage: sourceLanguage,
          targetLanguage: targetLanguage,
        );

        translatedParagraphs.add(translatedParagraph);
      }

      // دمج الفقرات المترجمة
      return translatedParagraphs.join('\n\n');
    } catch (e) {
      debugPrint('Error translating document text: $e');
      throw Exception('فشل ترجمة نص المستند: $e');
    }
  }

  /// إنشاء ملف PDF مترجم
  Future<File> createTranslatedPdf({
    required String originalText,
    required String translatedText,
    required String sourceLanguage,
    required String targetLanguage,
    required String fileName,
  }) async {
    try {
      // إنشاء مستند PDF جديد
      final pdf = pw.Document();

      // تقسيم النصوص إلى فقرات
      final List<String> originalParagraphs = originalText.split('\n\n');
      final List<String> translatedParagraphs = translatedText.split('\n\n');

      // إضافة صفحة العنوان
      pdf.addPage(
        pw.Page(
          build: (pw.Context context) {
            return pw.Center(
              child: pw.Column(
                mainAxisAlignment: pw.MainAxisAlignment.center,
                children: [
                  pw.Text(
                    'المستند المترجم',
                    style: pw.TextStyle(
                      fontSize: 24,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.SizedBox(height: 20),
                  pw.Text(
                    'من: $sourceLanguage',
                    style: const pw.TextStyle(fontSize: 16),
                  ),
                  pw.Text(
                    'إلى: $targetLanguage',
                    style: const pw.TextStyle(fontSize: 16),
                  ),
                  pw.SizedBox(height: 20),
                  pw.Text(
                    'تمت الترجمة بواسطة: المترجم الذكي AI',
                    style: const pw.TextStyle(fontSize: 12),
                  ),
                ],
              ),
            );
          },
        ),
      );

      // إضافة صفحات المحتوى
      for (
        int i = 0;
        i < originalParagraphs.length && i < translatedParagraphs.length;
        i++
      ) {
        if (originalParagraphs[i].trim().isEmpty) continue;

        pdf.addPage(
          pw.Page(
            build: (pw.Context context) {
              return pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'النص الأصلي:',
                    style: pw.TextStyle(
                      fontSize: 14,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.SizedBox(height: 10),
                  pw.Container(
                    padding: const pw.EdgeInsets.all(10),
                    decoration: pw.BoxDecoration(
                      color: pdf_lib.PdfColors.grey200,
                      borderRadius: pw.BorderRadius.circular(5),
                    ),
                    child: pw.Text(
                      originalParagraphs[i],
                      style: const pw.TextStyle(fontSize: 12),
                    ),
                  ),
                  pw.SizedBox(height: 20),
                  pw.Text(
                    'النص المترجم:',
                    style: pw.TextStyle(
                      fontSize: 14,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.SizedBox(height: 10),
                  pw.Container(
                    padding: const pw.EdgeInsets.all(10),
                    decoration: pw.BoxDecoration(
                      color: pdf_lib.PdfColors.blue50,
                      borderRadius: pw.BorderRadius.circular(5),
                    ),
                    child: pw.Text(
                      translatedParagraphs[i],
                      style: const pw.TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              );
            },
          ),
        );
      }

      // حفظ المستند
      final output = await getTemporaryDirectory();
      final file = File('${output.path}/$fileName-translated.pdf');
      await file.writeAsBytes(await pdf.save());

      return file;
    } catch (e) {
      debugPrint('Error creating translated PDF: $e');
      throw Exception('فشل إنشاء ملف PDF مترجم: $e');
    }
  }
}
