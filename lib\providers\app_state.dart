import 'dart:async';
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';

import '../services/subscription/subscription_service.dart';
import '../services/subscription/usage_tracking_service.dart';
import '../services/api/api_key_manager.dart';
import '../models/user_model.dart';
import '../config/constants.dart';

/// مزود حالة التطبيق
class AppState extends ChangeNotifier {
  // مثيل Firebase Auth
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // مثيل Firestore
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // خدمة الاشتراكات
  late SubscriptionService _subscriptionService;

  // خدمة تتبع الاستخدام
  late UsageTrackingService _usageTrackingService;

  // مدير مفاتيح API
  late ApiKeyManager _apiKeyManager;

  // Firebase Remote Config
  late FirebaseRemoteConfig _remoteConfig;

  // نموذج المستخدم الحالي
  UserModel? _currentUser;

  // حالة تحميل بيانات المستخدم
  bool _isLoadingUser = true;

  // حالة تسجيل الدخول
  bool _isLoggedIn = false;

  // حالة الاستخدام الأول
  bool _isFirstTime = true;

  // حالة الاشتراك
  bool _isSubscribed = false;

  // إعدادات السمة
  String _themeMode =
      AppConstants.themeModeLight; // وضع السمة (فاتح، داكن، AMOLED)
  bool _isDarkMode = false; // للتوافق مع الإصدارات القديمة

  // اللغة المختارة
  String _selectedLanguage = 'ar';

  // رسالة الخطأ
  String? _errorMessage;

  /// المُنشئ
  AppState() {
    _init();
  }

  /// تهيئة الحالة
  Future<void> _init() async {
    // تحميل الإعدادات من التخزين المحلي
    await _loadSettings();

    // تهيئة الخدمات
    await _initServices();

    // تهيئة مستمع المصادقة
    _initAuthListener();
  }

  /// تحميل الإعدادات من التخزين المحلي
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // تحميل وضع السمة
      _themeMode =
          prefs.getString(AppConstants.keyThemeMode) ??
          AppConstants.themeModeLight;

      // للتوافق مع الإصدارات القديمة
      _isDarkMode = prefs.getBool('is_dark_mode') ?? false;
      if (_isDarkMode && _themeMode == AppConstants.themeModeLight) {
        _themeMode = AppConstants.themeModeDark;
      }

      // تحميل اللغة المختارة
      _selectedLanguage = prefs.getString('selected_language') ?? 'ar';

      // تحميل حالة الاستخدام الأول
      _isFirstTime = prefs.getBool('is_first_time') ?? true;
    } catch (e) {
      debugPrint('خطأ في تحميل الإعدادات: $e');
    }
  }

  /// تهيئة الخدمات
  Future<void> _initServices() async {
    // تهيئة Remote Config
    _remoteConfig = FirebaseRemoteConfig.instance;
    await _remoteConfig.setConfigSettings(
      RemoteConfigSettings(
        fetchTimeout: const Duration(minutes: 1),
        minimumFetchInterval: const Duration(hours: 1),
      ),
    );
    await _remoteConfig.fetchAndActivate();

    // تهيئة SharedPreferences
    final prefs = await SharedPreferences.getInstance();

    _subscriptionService = SubscriptionService();
    _usageTrackingService = UsageTrackingService();
    _apiKeyManager = ApiKeyManager(_remoteConfig, prefs);

    // الاستماع لتدفق حالة الاشتراك
    _subscriptionService.subscriptionStatus.listen((subscription) {
      _isSubscribed = subscription != null && subscription.isActive();
      notifyListeners();
    });
  }

  /// تهيئة مستمع المصادقة
  void _initAuthListener() {
    _auth.authStateChanges().listen((User? user) {
      _isLoggedIn = user != null;

      if (user != null) {
        _loadUserData(user.uid);
      } else {
        _currentUser = null;
        _isLoadingUser = false;
      }

      notifyListeners();
    });
  }

  /// تحميل بيانات المستخدم
  Future<void> _loadUserData(String userId) async {
    _isLoadingUser = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final doc = await _firestore.collection('users').doc(userId).get();

      if (doc.exists) {
        _currentUser = UserModel.fromFirestore(doc.data()!, userId);
      } else {
        // إنشاء مستخدم جديد إذا لم يكن موجودًا
        final user = _auth.currentUser!;
        final newUser = UserModel(
          id: userId,
          email: user.email ?? '',
          name: user.displayName ?? '',
          photoUrl: user.photoURL,
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
        );

        await _firestore
            .collection('users')
            .doc(userId)
            .set(newUser.toFirestore());
        _currentUser = newUser;
      }

      _isLoadingUser = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = 'حدث خطأ أثناء تحميل بيانات المستخدم: $e';
      _isLoadingUser = false;
      notifyListeners();
    }
  }

  /// التحقق مما إذا كان يمكن استخدام الخدمة
  Future<bool> canUseService(String serviceType) async {
    // إذا كان المستخدم مشتركًا، يمكنه استخدام الخدمة بدون قيود
    if (_isSubscribed) {
      return true;
    }

    // التحقق مما إذا كان المستخدم قد تجاوز حد الاستخدام المجاني
    return await _usageTrackingService.canUseService(_isSubscribed);
  }

  /// تسجيل استخدام خدمة
  Future<void> trackServiceUsage(
    String serviceType,
    double durationInMinutes,
  ) async {
    await _usageTrackingService.trackUsage(serviceType, durationInMinutes);
  }

  /// الحصول على خدمة الاشتراكات
  SubscriptionService get subscriptionService => _subscriptionService;

  /// الحصول على خدمة تتبع الاستخدام
  UsageTrackingService get usageTrackingService => _usageTrackingService;

  /// الحصول على مدير مفاتيح API
  ApiKeyManager get apiKeyManager => _apiKeyManager;

  /// الحصول على Firebase Remote Config
  FirebaseRemoteConfig get remoteConfig => _remoteConfig;

  /// الحصول على المستخدم الحالي
  UserModel? get currentUser => _currentUser;

  /// الحصول على حالة تحميل بيانات المستخدم
  bool get isLoadingUser => _isLoadingUser;

  /// الحصول على حالة تسجيل الدخول
  bool get isLoggedIn => _isLoggedIn;

  /// الحصول على حالة الاشتراك
  bool get isSubscribed => _isSubscribed;

  /// تعيين حالة الاشتراك
  void setSubscriptionStatus(bool status) {
    _isSubscribed = status;
    notifyListeners();
  }

  /// الحصول على وضع الظلام
  bool get isDarkMode => _themeMode != AppConstants.themeModeLight;

  /// الحصول على وضع AMOLED
  bool get isAmoledMode => _themeMode == AppConstants.themeModeAmoled;

  /// الحصول على وضع السمة
  String get themeMode => _themeMode;

  /// تعيين وضع الظلام
  Future<void> setDarkMode(bool value) async {
    if (isDarkMode != value) {
      if (value) {
        await setThemeMode(AppConstants.themeModeDark);
      } else {
        await setThemeMode(AppConstants.themeModeLight);
      }
    }
  }

  /// تعيين وضع السمة
  Future<void> setThemeMode(String mode) async {
    if (_themeMode != mode) {
      _themeMode = mode;

      // حفظ الإعداد في التخزين المحلي
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConstants.keyThemeMode, mode);

      // للتوافق مع الإصدارات القديمة
      await prefs.setBool('is_dark_mode', mode != AppConstants.themeModeLight);

      notifyListeners();
    }
  }

  /// الحصول على اللغة المختارة
  String get selectedLanguage => _selectedLanguage;

  /// تعيين اللغة المختارة
  Future<void> setSelectedLanguage(String languageCode) async {
    if (_selectedLanguage != languageCode) {
      _selectedLanguage = languageCode;

      // حفظ الإعداد في التخزين المحلي
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('selected_language', languageCode);

      notifyListeners();
    }
  }

  /// الحصول على رسالة الخطأ
  String? get errorMessage => _errorMessage;

  /// الحصول على حالة الاستخدام الأول
  bool get isFirstTime => _isFirstTime;

  /// تعيين حالة الاستخدام الأول
  Future<void> setFirstTimeStatus(bool value) async {
    if (_isFirstTime != value) {
      _isFirstTime = value;

      // حفظ الإعداد في التخزين المحلي
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('is_first_time', value);

      notifyListeners();
    }
  }

  // متغيرات إضافية للإعدادات
  double _fontSize = 16.0;
  bool _notificationsEnabled = true;

  // الحصول على قيم الإعدادات
  double get fontSize => _fontSize;
  String get appLanguage => _selectedLanguage;
  bool get notificationsEnabled => _notificationsEnabled;

  /// تعيين حجم الخط
  void setFontSize(double value) {
    _fontSize = value;
    notifyListeners();
  }

  /// تعيين لغة التطبيق
  void setAppLanguage(String value) {
    setSelectedLanguage(value);
  }

  /// تعيين حالة الإشعارات
  void setNotificationsEnabled(bool value) {
    _notificationsEnabled = value;
    notifyListeners();
  }

  /// الحصول على سجل الترجمات
  Future<List<dynamic>> getTranslationHistory({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    // هذه دالة مؤقتة تعيد قائمة فارغة
    // في التطبيق الفعلي، ستقوم بجلب البيانات من Firestore
    return [];
  }

  /// إضافة عنصر إلى المفضلة
  Future<void> addToFavorites(dynamic item) async {
    // هذه دالة مؤقتة لا تقوم بأي شيء
    // في التطبيق الفعلي، ستقوم بإضافة العنصر إلى Firestore
  }

  /// حذف عنصر من سجل الترجمات
  Future<void> deleteFromHistory(String id) async {
    // هذه دالة مؤقتة لا تقوم بأي شيء
    // في التطبيق الفعلي، ستقوم بحذف العنصر من Firestore
  }

  /// حذف كل سجل الترجمات
  Future<void> clearHistory() async {
    // هذه دالة مؤقتة لا تقوم بأي شيء
    // في التطبيق الفعلي، ستقوم بحذف كل العناصر من Firestore
  }

  /// الحصول على العناصر المفضلة
  Future<List<dynamic>> getFavorites() async {
    // هذه دالة مؤقتة تعيد قائمة فارغة
    // في التطبيق الفعلي، ستقوم بجلب البيانات من Firestore
    return [];
  }

  /// إزالة عنصر من المفضلة
  Future<void> removeFromFavorites(String id) async {
    // هذه دالة مؤقتة لا تقوم بأي شيء
    // في التطبيق الفعلي، ستقوم بحذف العنصر من Firestore
  }

  /// تسجيل الخروج
  Future<void> signOut() async {
    await _auth.signOut();
  }

  @override
  void dispose() {
    _subscriptionService.dispose();
    super.dispose();
  }
}
