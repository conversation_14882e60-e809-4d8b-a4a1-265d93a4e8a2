import 'package:flutter/material.dart';
import '../config/app_theme.dart';

/// زر مخصص
class CustomButton extends StatelessWidget {
  /// نص الزر
  final String text;

  /// أيقونة الزر
  final IconData? icon;

  /// إجراء الضغط على الزر
  final VoidCallback? onPressed;

  /// لون الزر
  final Color? color;

  /// لون النص
  final Color? textColor;

  /// عرض الزر
  final double? width;

  /// ارتفاع الزر
  final double? height;

  /// حجم النص
  final double? fontSize;

  /// نوع الزر
  final ButtonType type;

  /// نصف قطر الحواف
  final double borderRadius;

  /// سماكة الحدود
  final double borderWidth;

  /// لون الحدود
  final Color? borderColor;

  /// الارتفاع
  final double? elevation;

  /// المسافة الداخلية
  final EdgeInsetsGeometry? padding;

  /// موضع الأيقونة
  final IconPosition iconPosition;

  const CustomButton({
    super.key,
    required this.text,
    this.icon,
    this.onPressed,
    this.color,
    this.textColor,
    this.width,
    this.height = 48,
    this.fontSize,
    this.type = ButtonType.elevated,
    this.borderRadius = 8,
    this.borderWidth = 1,
    this.borderColor,
    this.elevation,
    this.padding,
    this.iconPosition = IconPosition.start,
  });

  @override
  Widget build(BuildContext context) {
    final buttonColor = color ?? AppTheme.primaryColor;
    final buttonTextColor = textColor ?? Colors.white;
    final buttonElevation = elevation ?? (type == ButtonType.elevated ? 2 : 0);
    final buttonPadding =
        padding ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 12);

    final buttonStyle = _getButtonStyle(
      context,
      buttonColor,
      buttonTextColor,
      buttonElevation,
      buttonPadding,
    );

    final buttonChild = _buildButtonChild();

    Widget button;
    switch (type) {
      case ButtonType.elevated:
        button = ElevatedButton(
          onPressed: onPressed,
          style: buttonStyle,
          child: buttonChild,
        );
        break;
      case ButtonType.outlined:
        button = OutlinedButton(
          onPressed: onPressed,
          style: buttonStyle,
          child: buttonChild,
        );
        break;
      case ButtonType.text:
        button = TextButton(
          onPressed: onPressed,
          style: buttonStyle,
          child: buttonChild,
        );
        break;
    }

    if (width != null || height != null) {
      return SizedBox(width: width, height: height, child: button);
    }

    return button;
  }

  /// بناء محتوى الزر
  Widget _buildButtonChild() {
    final textWidget = Text(
      text,
      style: TextStyle(fontSize: fontSize, fontWeight: FontWeight.bold),
    );

    if (icon == null) {
      return textWidget;
    }

    final iconWidget = Icon(icon);

    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (iconPosition == IconPosition.start) ...[
          iconWidget,
          const SizedBox(width: 8),
        ],
        textWidget,
        if (iconPosition == IconPosition.end) ...[
          const SizedBox(width: 8),
          iconWidget,
        ],
      ],
    );
  }

  /// الحصول على نمط الزر
  ButtonStyle _getButtonStyle(
    BuildContext context,
    Color buttonColor,
    Color buttonTextColor,
    double buttonElevation,
    EdgeInsetsGeometry buttonPadding,
  ) {
    final shape = RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(borderRadius),
      side:
          type == ButtonType.outlined
              ? BorderSide(
                color: borderColor ?? buttonColor,
                width: borderWidth,
              )
              : BorderSide.none,
    );

    switch (type) {
      case ButtonType.elevated:
        return ElevatedButton.styleFrom(
          backgroundColor: buttonColor,
          foregroundColor: buttonTextColor,
          elevation: buttonElevation,
          shape: shape,
          padding: buttonPadding,
          disabledBackgroundColor: buttonColor.withAlpha(128),
          disabledForegroundColor: buttonTextColor.withAlpha(128),
        );
      case ButtonType.outlined:
        return OutlinedButton.styleFrom(
          foregroundColor: buttonColor,
          shape: shape,
          padding: buttonPadding,
          disabledForegroundColor: buttonColor.withAlpha(128),
        );
      case ButtonType.text:
        return TextButton.styleFrom(
          foregroundColor: buttonColor,
          shape: shape,
          padding: buttonPadding,
          disabledForegroundColor: buttonColor.withAlpha(128),
        );
    }
  }
}

/// أنواع الأزرار
enum ButtonType {
  /// زر بارز
  elevated,

  /// زر محدد
  outlined,

  /// زر نصي
  text,
}

/// موضع الأيقونة
enum IconPosition {
  /// في البداية
  start,

  /// في النهاية
  end,
}
