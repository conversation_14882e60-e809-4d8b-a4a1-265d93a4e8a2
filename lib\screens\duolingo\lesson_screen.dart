import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/duolingo_models.dart';
import '../../services/duolingo_service.dart';
import 'widgets/exercise_widgets.dart';

/// شاشة الدرس التفاعلية مثل Duolingo
class LessonScreen extends StatefulWidget {
  final Lesson lesson;
  final LearningUnit unit;
  final DuolingoService duolingoService;

  const LessonScreen({
    super.key,
    required this.lesson,
    required this.unit,
    required this.duolingoService,
  });

  @override
  State<LessonScreen> createState() => _LessonScreenState();
}

class _LessonScreenState extends State<LessonScreen>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late AnimationController _heartController;
  late AnimationController _feedbackController;

  int _currentExerciseIndex = 0;
  int _hearts = 5;
  int _score = 0;
  int _correctAnswers = 0;
  List<bool> _exerciseResults = [];
  bool _isAnswering = false;
  bool _showFeedback = false;
  String? _selectedAnswer;
  DateTime? _lessonStartTime;

  @override
  void initState() {
    super.initState();
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _heartController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _feedbackController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    _lessonStartTime = DateTime.now();
    _exerciseResults = List.filled(widget.lesson.exercises.length, false);
  }

  @override
  void dispose() {
    _progressController.dispose();
    _heartController.dispose();
    _feedbackController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_currentExerciseIndex >= widget.lesson.exercises.length) {
      return _buildLessonCompleteScreen();
    }

    final currentExercise = widget.lesson.exercises[_currentExerciseIndex];

    return Scaffold(
      backgroundColor: const Color(0xFFF7F8FA),
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildProgressBar(),
          _buildHeartsRow(),
          Expanded(
            child: _buildExerciseContent(currentExercise),
          ),
          _buildBottomSection(currentExercise),
        ],
      ),
    );
  }

  /// شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leading: IconButton(
        onPressed: _showExitDialog,
        icon: const Icon(Icons.close, color: Colors.grey),
      ),
      title: Text(
        widget.lesson.titleAr,
        style: const TextStyle(
          color: Colors.black87,
          fontWeight: FontWeight.bold,
        ),
      ),
      centerTitle: true,
    );
  }

  /// شريط التقدم
  Widget _buildProgressBar() {
    final progress = (_currentExerciseIndex + 1) / widget.lesson.exercises.length;
    
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${_currentExerciseIndex + 1} من ${widget.lesson.exercises.length}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                'النقاط: $_score',
                style: const TextStyle(
                  fontSize: 14,
                  color: Color(0xFF58CC02),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
          AnimatedBuilder(
            animation: _progressController,
            builder: (context, child) {
              return Container(
                height: 12,
                decoration: BoxDecoration(
                  color: Colors.grey.shade200,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(6),
                  child: LinearProgressIndicator(
                    value: progress,
                    backgroundColor: Colors.transparent,
                    valueColor: const AlwaysStoppedAnimation<Color>(
                      Color(0xFF58CC02),
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  /// صف القلوب
  Widget _buildHeartsRow() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(5, (index) {
          return AnimatedBuilder(
            animation: _heartController,
            builder: (context, child) {
              return Transform.scale(
                scale: index < _hearts ? 1.0 : 0.8,
                child: Icon(
                  index < _hearts ? Icons.favorite : Icons.favorite_border,
                  color: index < _hearts ? Colors.red : Colors.grey.shade300,
                  size: 24,
                ),
              );
            },
          );
        }),
      ),
    );
  }

  /// محتوى التمرين
  Widget _buildExerciseContent(Exercise exercise) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          const SizedBox(height: 20),
          _buildExerciseQuestion(exercise),
          const SizedBox(height: 30),
          Expanded(
            child: _buildExerciseWidget(exercise),
          ),
        ],
      ),
    );
  }

  /// سؤال التمرين
  Widget _buildExerciseQuestion(Exercise exercise) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            _getExerciseTypeTitle(exercise.type),
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 15),
          Text(
            exercise.questionAr,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
          if (exercise.audioPath != null) ...[
            const SizedBox(height: 15),
            GestureDetector(
              onTap: () => _playAudio(exercise),
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF1CB0F6),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: const Icon(
                  Icons.volume_up,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// ويدجت التمرين
  Widget _buildExerciseWidget(Exercise exercise) {
    switch (exercise.type) {
      case ExerciseType.multipleChoice:
        return MultipleChoiceWidget(
          exercise: exercise,
          selectedAnswer: _selectedAnswer,
          onAnswerSelected: _onAnswerSelected,
          showFeedback: _showFeedback,
        );
      case ExerciseType.translation:
        return TranslationWidget(
          exercise: exercise,
          selectedAnswer: _selectedAnswer,
          onAnswerSelected: _onAnswerSelected,
          showFeedback: _showFeedback,
        );
      case ExerciseType.listening:
        return ListeningWidget(
          exercise: exercise,
          selectedAnswer: _selectedAnswer,
          onAnswerSelected: _onAnswerSelected,
          showFeedback: _showFeedback,
          onPlayAudio: () => _playAudio(exercise),
        );
      default:
        return MultipleChoiceWidget(
          exercise: exercise,
          selectedAnswer: _selectedAnswer,
          onAnswerSelected: _onAnswerSelected,
          showFeedback: _showFeedback,
        );
    }
  }

  /// القسم السفلي
  Widget _buildBottomSection(Exercise exercise) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: [
          if (_showFeedback) _buildFeedbackSection(exercise),
          const SizedBox(height: 15),
          SizedBox(
            width: double.infinity,
            height: 50,
            child: ElevatedButton(
              onPressed: _selectedAnswer != null && !_isAnswering
                  ? () => _showFeedback ? _nextExercise() : _checkAnswer(exercise)
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: _showFeedback
                    ? (_exerciseResults[_currentExerciseIndex] 
                        ? const Color(0xFF58CC02) 
                        : Colors.red)
                    : const Color(0xFF1CB0F6),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                _showFeedback ? 'متابعة' : 'تحقق',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// قسم التغذية الراجعة
  Widget _buildFeedbackSection(Exercise exercise) {
    final isCorrect = _exerciseResults[_currentExerciseIndex];
    
    return AnimatedBuilder(
      animation: _feedbackController,
      builder: (context, child) {
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0, 1),
            end: Offset.zero,
          ).animate(_feedbackController),
          child: Container(
            padding: const EdgeInsets.all(15),
            decoration: BoxDecoration(
              color: isCorrect 
                  ? Colors.green.shade50 
                  : Colors.red.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isCorrect 
                    ? Colors.green.shade200 
                    : Colors.red.shade200,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  isCorrect ? Icons.check_circle : Icons.cancel,
                  color: isCorrect ? Colors.green : Colors.red,
                  size: 24,
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        isCorrect ? 'ممتاز!' : 'إجابة خاطئة',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: isCorrect ? Colors.green : Colors.red,
                        ),
                      ),
                      if (!isCorrect) ...[
                        const SizedBox(height: 4),
                        Text(
                          'الإجابة الصحيحة: ${exercise.correctAnswer}',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade700,
                          ),
                        ),
                      ],
                      const SizedBox(height: 4),
                      Text(
                        exercise.explanationAr,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// شاشة إكمال الدرس
  Widget _buildLessonCompleteScreen() {
    final accuracy = _correctAnswers / widget.lesson.exercises.length;
    final stars = accuracy >= 0.95 ? 3 : accuracy >= 0.8 ? 2 : 1;
    
    return Scaffold(
      backgroundColor: const Color(0xFF58CC02),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.emoji_events,
              size: 100,
              color: Colors.white,
            ),
            const SizedBox(height: 20),
            const Text(
              'درس مكتمل!',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 10),
            Text(
              'حصلت على $_score نقطة',
              style: const TextStyle(
                fontSize: 18,
                color: Colors.white70,
              ),
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(3, (index) {
                return Icon(
                  index < stars ? Icons.star : Icons.star_border,
                  color: Colors.amber,
                  size: 40,
                );
              }),
            ),
            const SizedBox(height: 40),
            ElevatedButton(
              onPressed: _completeLesson,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 15),
              ),
              child: const Text(
                'متابعة',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF58CC02),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// اختيار الإجابة
  void _onAnswerSelected(String answer) {
    if (_isAnswering) return;
    
    setState(() {
      _selectedAnswer = answer;
    });
    
    HapticFeedback.lightImpact();
  }

  /// فحص الإجابة
  void _checkAnswer(Exercise exercise) {
    if (_selectedAnswer == null || _isAnswering) return;

    setState(() {
      _isAnswering = true;
    });

    final isCorrect = _selectedAnswer == exercise.correctAnswer;
    
    setState(() {
      _exerciseResults[_currentExerciseIndex] = isCorrect;
      _showFeedback = true;
    });

    if (isCorrect) {
      _correctAnswers++;
      _score += exercise.points;
      HapticFeedback.mediumImpact();
    } else {
      _hearts = (_hearts - 1).clamp(0, 5);
      _heartController.forward().then((_) => _heartController.reverse());
      HapticFeedback.heavyImpact();
    }

    _feedbackController.forward();

    // حفظ نتيجة التمرين
    widget.duolingoService.completeExercise(
      exerciseId: exercise.id,
      lessonId: widget.lesson.id,
      unitId: widget.unit.id,
      languageCode: widget.unit.languageCode,
      isCorrect: isCorrect,
      timeSpent: const Duration(seconds: 30), // تقدير
    );
  }

  /// الانتقال للتمرين التالي
  void _nextExercise() {
    setState(() {
      _currentExerciseIndex++;
      _selectedAnswer = null;
      _showFeedback = false;
      _isAnswering = false;
    });

    _feedbackController.reset();
    _progressController.animateTo(
      (_currentExerciseIndex + 1) / widget.lesson.exercises.length,
    );
  }

  /// إكمال الدرس
  void _completeLesson() {
    final timeSpent = DateTime.now().difference(_lessonStartTime!);
    final accuracy = _correctAnswers / widget.lesson.exercises.length;

    widget.duolingoService.completeLesson(
      lessonId: widget.lesson.id,
      unitId: widget.unit.id,
      languageCode: widget.unit.languageCode,
      accuracy: accuracy,
      timeSpent: timeSpent,
    );

    Navigator.pop(context);
  }

  /// تشغيل الصوت
  void _playAudio(Exercise exercise) {
    if (exercise.audioPath != null) {
      // تشغيل الملف الصوتي
      widget.duolingoService.speak(
        exercise.correctAnswer,
        widget.unit.languageCode,
      );
    }
  }

  /// الحصول على عنوان نوع التمرين
  String _getExerciseTypeTitle(ExerciseType type) {
    switch (type) {
      case ExerciseType.multipleChoice:
        return 'اختر الإجابة الصحيحة';
      case ExerciseType.translation:
        return 'ترجم الجملة';
      case ExerciseType.listening:
        return 'استمع واختر';
      case ExerciseType.speaking:
        return 'انطق الجملة';
      case ExerciseType.writing:
        return 'اكتب الترجمة';
      default:
        return 'أجب على السؤال';
    }
  }

  /// عرض حوار الخروج
  void _showExitDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('هل تريد الخروج؟'),
        content: const Text('ستفقد تقدمك في هذا الدرس.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
            child: const Text('خروج'),
          ),
        ],
      ),
    );
  }
}
