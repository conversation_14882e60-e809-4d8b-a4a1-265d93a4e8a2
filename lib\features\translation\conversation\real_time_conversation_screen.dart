import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../config/app_theme.dart';
import '../../../config/constants.dart';
import '../../../core/app_state.dart';
import '../../../services/speech/real_time_translation_service.dart';
import '../../../utils/helpers.dart';
import '../../../widgets/three_d_button.dart';

/// شاشة المحادثة والترجمة الفورية
class RealTimeConversationScreen extends StatefulWidget {
  const RealTimeConversationScreen({super.key});

  @override
  State<RealTimeConversationScreen> createState() =>
      _RealTimeConversationScreenState();
}

class _RealTimeConversationScreenState
    extends State<RealTimeConversationScreen> {
  late RealTimeTranslationService _translationService;
  bool _isListening = false;
  String _sourceLanguage = 'ar';
  String _targetLanguage = 'en';
  double _micSensitivity = 0.5; // حساسية الميكروفون (0.0 إلى 1.0)
  bool _showSettings = false; // عرض إعدادات متقدمة

  final List<RealTimeTranslationResult> _conversationHistory = [];
  StreamSubscription<RealTimeTranslationResult>? _translationSubscription;

  @override
  void initState() {
    super.initState();

    // الحصول على خدمة الترجمة الفورية
    final appState = Provider.of<AppState>(context, listen: false);
    _translationService = RealTimeTranslationService(appState.remoteConfig);

    // الاستماع لنتائج الترجمة
    _translationSubscription = _translationService.translationResultStream
        .listen((result) {
          setState(() {
            // إضافة النتيجة إلى سجل المحادثة
            _conversationHistory.add(result);
          });
        });
  }

  @override
  void dispose() {
    _translationSubscription?.cancel();
    _translationService.dispose();
    super.dispose();
  }

  /// بدء الاستماع
  Future<void> _startListening() async {
    if (_isListening) return;

    // التحقق من أن الويدجت لا يزال مثبتًا قبل البدء
    if (!mounted) return;

    try {
      final success = await _translationService.startListening(
        sourceLanguage: _sourceLanguage,
        targetLanguage: _targetLanguage,
      );

      // التحقق من أن الويدجت لا يزال مثبتًا بعد العملية غير المتزامنة
      if (!mounted) return;

      setState(() {
        _isListening = success;
      });

      if (!success) {
        // التحقق من أن الويدجت لا يزال مثبتًا قبل عرض رسالة الخطأ
        if (!mounted) return;

        AppHelpers.showSnackBar(
          context,
          'فشل بدء الاستماع. يرجى التحقق من إذن الميكروفون.',
          isError: true,
        );
      }
    } catch (e) {
      // التحقق من أن الويدجت لا يزال مثبتًا بعد الخطأ
      if (!mounted) return;

      setState(() {
        _isListening = false;
      });

      // التحقق من أن الويدجت لا يزال مثبتًا قبل عرض رسالة الخطأ
      if (!mounted) return;

      AppHelpers.showSnackBar(
        context,
        'حدث خطأ أثناء بدء الاستماع: $e',
        isError: true,
      );
    }
  }

  /// إيقاف الاستماع
  Future<void> _stopListening() async {
    if (!_isListening) return;

    // التحقق من أن الويدجت لا يزال مثبتًا قبل البدء
    if (!mounted) return;

    try {
      await _translationService.stopListening();

      // التحقق من أن الويدجت لا يزال مثبتًا بعد العملية غير المتزامنة
      if (!mounted) return;

      setState(() {
        _isListening = false;
      });
    } catch (e) {
      // التحقق من أن الويدجت لا يزال مثبتًا قبل عرض رسالة الخطأ
      if (!mounted) return;

      AppHelpers.showSnackBar(
        context,
        'حدث خطأ أثناء إيقاف الاستماع: $e',
        isError: true,
      );
    }
  }

  /// تبديل حالة الاستماع
  Future<void> _toggleListening() async {
    if (_isListening) {
      await _stopListening();
    } else {
      await _startListening();
    }
  }

  /// مسح سجل المحادثة
  void _clearConversation() {
    setState(() {
      _conversationHistory.clear();
    });
  }

  /// بناء محدد اللغة
  Widget _buildLanguageSelector(
    String selectedLanguage,
    Function(String) onLanguageSelected,
    String labelText,
  ) {
    return GestureDetector(
      onTap:
          () => _showLanguageSelectionDialog(
            selectedLanguage,
            onLanguageSelected,
          ),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(labelText),
            const SizedBox(width: 8),
            Text(
              _getLanguageName(selectedLanguage),
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ),
    );
  }

  /// عرض مربع حوار اختيار اللغة
  void _showLanguageSelectionDialog(
    String selectedLanguage,
    Function(String) onLanguageSelected,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('اختر اللغة'),
            content: SizedBox(
              width: double.maxFinite,
              height: 300,
              child: ListView.builder(
                itemCount: AppConstants.supportedLanguages.length,
                itemBuilder: (context, index) {
                  final language = AppConstants.supportedLanguages[index];
                  final languageCode = language['code'] as String;
                  final isSelected = languageCode == selectedLanguage;

                  return ListTile(
                    title: Text(language['name'] as String),
                    trailing: Text(language['flag'] as String),
                    selected: isSelected,
                    selectedTileColor: Colors.blue.shade50,
                    onTap: () {
                      onLanguageSelected(languageCode);
                      Navigator.pop(context);
                    },
                  );
                },
              ),
            ),
          ),
    );
  }

  /// الحصول على اسم اللغة من الرمز
  String _getLanguageName(String languageCode) {
    final language = AppConstants.supportedLanguages.firstWhere(
      (lang) => lang['code'] == languageCode,
      orElse: () => {'name': languageCode, 'flag': '🏳️'},
    );

    return '${language['name']} ${language['flag']}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المحادثة والترجمة الفورية'),
        backgroundColor: AppTheme.primaryColor,
        actions: [
          // زر الإعدادات المتقدمة
          IconButton(
            icon: Icon(
              _showSettings ? Icons.settings : Icons.settings_outlined,
            ),
            onPressed: () {
              setState(() {
                _showSettings = !_showSettings;
              });
            },
            tooltip: 'إعدادات متقدمة',
          ),

          // زر مسح المحادثة
          IconButton(
            icon: const Icon(Icons.delete_outline),
            onPressed: _clearConversation,
            tooltip: 'مسح المحادثة',
          ),
        ],
      ),
      body: Column(
        children: [
          // محددات اللغة
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // اللغة المصدر
                Expanded(
                  child: _buildLanguageSelector(_sourceLanguage, (language) {
                    setState(() {
                      _sourceLanguage = language;
                    });
                  }, 'من'),
                ),

                // زر تبديل اللغات
                IconButton(
                  icon: const Icon(Icons.swap_horiz),
                  onPressed: () {
                    setState(() {
                      final temp = _sourceLanguage;
                      _sourceLanguage = _targetLanguage;
                      _targetLanguage = temp;
                    });
                  },
                ),

                // اللغة الهدف
                Expanded(
                  child: _buildLanguageSelector(_targetLanguage, (language) {
                    setState(() {
                      _targetLanguage = language;
                    });
                  }, 'إلى'),
                ),
              ],
            ),
          ),

          // إعدادات متقدمة
          if (_showSettings)
            Card(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'إعدادات متقدمة',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),

                    const SizedBox(height: 16),

                    // ضبط حساسية الميكروفون
                    Row(
                      children: [
                        const Text('حساسية الميكروفون:'),
                        Expanded(
                          child: Slider(
                            value: _micSensitivity,
                            onChanged: (value) {
                              setState(() {
                                _micSensitivity = value;
                              });

                              // تطبيق الإعداد على خدمة الترجمة الفورية
                              _translationService.setMicrophoneSensitivity(
                                value,
                              );
                            },
                            min: 0.0,
                            max: 1.0,
                            divisions: 10,
                            label: '${(_micSensitivity * 100).round()}%',
                          ),
                        ),
                        Text('${(_micSensitivity * 100).round()}%'),
                      ],
                    ),
                  ],
                ),
              ),
            ),

          // سجل المحادثة
          Expanded(
            child:
                _conversationHistory.isEmpty
                    ? const Center(
                      child: Text(
                        'ابدأ المحادثة بالضغط على زر الميكروفون',
                        style: TextStyle(color: Colors.grey),
                      ),
                    )
                    : ListView.builder(
                      itemCount: _conversationHistory.length,
                      itemBuilder: (context, index) {
                        final result = _conversationHistory[index];

                        return Card(
                          margin: const EdgeInsets.all(8),
                          child: Padding(
                            padding: const EdgeInsets.all(12),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // النص الأصلي
                                Text(
                                  result.originalText,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),

                                const Divider(),

                                // النص المترجم
                                Text(
                                  result.translatedText,
                                  style: const TextStyle(
                                    fontStyle: FontStyle.italic,
                                    color: AppTheme.secondaryColor,
                                  ),
                                ),

                                // الوقت
                                Align(
                                  alignment: Alignment.bottomRight,
                                  child: Text(
                                    '${result.timestamp.hour}:${result.timestamp.minute.toString().padLeft(2, '0')}',
                                    style: const TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
          ),

          // أزرار التحكم
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // زر الاستماع
                CircleButton3D(
                  icon: _isListening ? Icons.stop : Icons.mic,
                  size: 80,
                  iconSize: 40,
                  onPressed: _toggleListening,
                  color: _isListening ? Colors.red : AppTheme.primaryColor,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
