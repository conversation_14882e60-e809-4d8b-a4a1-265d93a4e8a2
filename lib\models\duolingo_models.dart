import 'package:flutter/material.dart';

/// مستوى الصعوبة
enum DifficultyLevel {
  beginner,    // مبتدئ
  intermediate, // متوسط
  advanced,    // متقدم
  expert       // خبير
}

/// نوع التمرين
enum ExerciseType {
  multipleChoice,    // اختيار متعدد
  translation,       // ترجمة
  listening,         // استماع
  speaking,          // نطق
  writing,           // كتابة
  matching,          // مطابقة
  fillInBlanks,      // ملء الفراغات
  wordOrder,         // ترتيب الكلمات
  pronunciation,     // تقييم النطق
  conversation       // محادثة
}

/// حالة الدرس
enum LessonStatus {
  locked,      // مقفل
  available,   // متاح
  inProgress,  // قيد التقدم
  completed,   // مكتمل
  mastered     // متقن
}

/// نوع المكافأة
enum RewardType {
  xp,          // نقاط خبرة
  gems,        // جواهر
  hearts,      // قلوب
  streak,      // سلسلة
  achievement  // إنجاز
}

/// وحدة التعلم (مثل الوحدات في Duolingo)
class LearningUnit {
  final String id;
  final String title;
  final String titleAr;
  final String description;
  final String descriptionAr;
  final String languageCode;
  final DifficultyLevel level;
  final int order;
  final Color themeColor;
  final String iconPath;
  final List<Lesson> lessons;
  final int requiredXP;
  final bool isUnlocked;
  final int completedLessons;
  final double progress; // 0.0 to 1.0

  const LearningUnit({
    required this.id,
    required this.title,
    required this.titleAr,
    required this.description,
    required this.descriptionAr,
    required this.languageCode,
    required this.level,
    required this.order,
    required this.themeColor,
    required this.iconPath,
    required this.lessons,
    this.requiredXP = 0,
    this.isUnlocked = false,
    this.completedLessons = 0,
    this.progress = 0.0,
  });

  LearningUnit copyWith({
    String? id,
    String? title,
    String? titleAr,
    String? description,
    String? descriptionAr,
    String? languageCode,
    DifficultyLevel? level,
    int? order,
    Color? themeColor,
    String? iconPath,
    List<Lesson>? lessons,
    int? requiredXP,
    bool? isUnlocked,
    int? completedLessons,
    double? progress,
  }) {
    return LearningUnit(
      id: id ?? this.id,
      title: title ?? this.title,
      titleAr: titleAr ?? this.titleAr,
      description: description ?? this.description,
      descriptionAr: descriptionAr ?? this.descriptionAr,
      languageCode: languageCode ?? this.languageCode,
      level: level ?? this.level,
      order: order ?? this.order,
      themeColor: themeColor ?? this.themeColor,
      iconPath: iconPath ?? this.iconPath,
      lessons: lessons ?? this.lessons,
      requiredXP: requiredXP ?? this.requiredXP,
      isUnlocked: isUnlocked ?? this.isUnlocked,
      completedLessons: completedLessons ?? this.completedLessons,
      progress: progress ?? this.progress,
    );
  }
}

/// الدرس
class Lesson {
  final String id;
  final String title;
  final String titleAr;
  final String unitId;
  final int order;
  final List<Exercise> exercises;
  final LessonStatus status;
  final int xpReward;
  final int stars; // 0-3 نجوم
  final double accuracy; // دقة الإجابات
  final Duration timeSpent;
  final DateTime? completedAt;
  final int attempts;

  const Lesson({
    required this.id,
    required this.title,
    required this.titleAr,
    required this.unitId,
    required this.order,
    required this.exercises,
    this.status = LessonStatus.locked,
    this.xpReward = 10,
    this.stars = 0,
    this.accuracy = 0.0,
    this.timeSpent = Duration.zero,
    this.completedAt,
    this.attempts = 0,
  });

  Lesson copyWith({
    String? id,
    String? title,
    String? titleAr,
    String? unitId,
    int? order,
    List<Exercise>? exercises,
    LessonStatus? status,
    int? xpReward,
    int? stars,
    double? accuracy,
    Duration? timeSpent,
    DateTime? completedAt,
    int? attempts,
  }) {
    return Lesson(
      id: id ?? this.id,
      title: title ?? this.title,
      titleAr: titleAr ?? this.titleAr,
      unitId: unitId ?? this.unitId,
      order: order ?? this.order,
      exercises: exercises ?? this.exercises,
      status: status ?? this.status,
      xpReward: xpReward ?? this.xpReward,
      stars: stars ?? this.stars,
      accuracy: accuracy ?? this.accuracy,
      timeSpent: timeSpent ?? this.timeSpent,
      completedAt: completedAt ?? this.completedAt,
      attempts: attempts ?? this.attempts,
    );
  }
}

/// التمرين
class Exercise {
  final String id;
  final String lessonId;
  final ExerciseType type;
  final String question;
  final String questionAr;
  final String? audioPath;
  final String? imagePath;
  final List<String> options;
  final String correctAnswer;
  final String explanation;
  final String explanationAr;
  final int points;
  final Duration timeLimit;
  final Map<String, dynamic> metadata; // بيانات إضافية حسب نوع التمرين

  const Exercise({
    required this.id,
    required this.lessonId,
    required this.type,
    required this.question,
    required this.questionAr,
    this.audioPath,
    this.imagePath,
    required this.options,
    required this.correctAnswer,
    required this.explanation,
    required this.explanationAr,
    this.points = 10,
    this.timeLimit = const Duration(seconds: 30),
    this.metadata = const {},
  });
}

/// ملف تعريف المستخدم
class UserProfile {
  final String id;
  final String name;
  final String email;
  final String? avatarPath;
  final String nativeLanguage;
  final List<String> learningLanguages;
  final int totalXP;
  final int currentStreak;
  final int longestStreak;
  final int gems;
  final int hearts;
  final DateTime joinDate;
  final DateTime lastActiveDate;
  final Map<String, int> languageXP; // XP لكل لغة
  final List<String> achievements;
  final UserSettings settings;

  const UserProfile({
    required this.id,
    required this.name,
    required this.email,
    this.avatarPath,
    required this.nativeLanguage,
    required this.learningLanguages,
    this.totalXP = 0,
    this.currentStreak = 0,
    this.longestStreak = 0,
    this.gems = 0,
    this.hearts = 5,
    required this.joinDate,
    required this.lastActiveDate,
    this.languageXP = const {},
    this.achievements = const [],
    required this.settings,
  });

  UserProfile copyWith({
    String? id,
    String? name,
    String? email,
    String? avatarPath,
    String? nativeLanguage,
    List<String>? learningLanguages,
    int? totalXP,
    int? currentStreak,
    int? longestStreak,
    int? gems,
    int? hearts,
    DateTime? joinDate,
    DateTime? lastActiveDate,
    Map<String, int>? languageXP,
    List<String>? achievements,
    UserSettings? settings,
  }) {
    return UserProfile(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      avatarPath: avatarPath ?? this.avatarPath,
      nativeLanguage: nativeLanguage ?? this.nativeLanguage,
      learningLanguages: learningLanguages ?? this.learningLanguages,
      totalXP: totalXP ?? this.totalXP,
      currentStreak: currentStreak ?? this.currentStreak,
      longestStreak: longestStreak ?? this.longestStreak,
      gems: gems ?? this.gems,
      hearts: hearts ?? this.hearts,
      joinDate: joinDate ?? this.joinDate,
      lastActiveDate: lastActiveDate ?? this.lastActiveDate,
      languageXP: languageXP ?? this.languageXP,
      achievements: achievements ?? this.achievements,
      settings: settings ?? this.settings,
    );
  }
}

/// إعدادات المستخدم
class UserSettings {
  final bool soundEnabled;
  final bool notificationsEnabled;
  final bool autoplayAudio;
  final double speechRate;
  final String preferredVoice;
  final bool hapticFeedback;
  final bool darkMode;
  final String interfaceLanguage;
  final int dailyGoalXP;
  final TimeOfDay reminderTime;

  const UserSettings({
    this.soundEnabled = true,
    this.notificationsEnabled = true,
    this.autoplayAudio = true,
    this.speechRate = 1.0,
    this.preferredVoice = 'default',
    this.hapticFeedback = true,
    this.darkMode = false,
    this.interfaceLanguage = 'ar',
    this.dailyGoalXP = 50,
    this.reminderTime = const TimeOfDay(hour: 19, minute: 0),
  });

  UserSettings copyWith({
    bool? soundEnabled,
    bool? notificationsEnabled,
    bool? autoplayAudio,
    double? speechRate,
    String? preferredVoice,
    bool? hapticFeedback,
    bool? darkMode,
    String? interfaceLanguage,
    int? dailyGoalXP,
    TimeOfDay? reminderTime,
  }) {
    return UserSettings(
      soundEnabled: soundEnabled ?? this.soundEnabled,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      autoplayAudio: autoplayAudio ?? this.autoplayAudio,
      speechRate: speechRate ?? this.speechRate,
      preferredVoice: preferredVoice ?? this.preferredVoice,
      hapticFeedback: hapticFeedback ?? this.hapticFeedback,
      darkMode: darkMode ?? this.darkMode,
      interfaceLanguage: interfaceLanguage ?? this.interfaceLanguage,
      dailyGoalXP: dailyGoalXP ?? this.dailyGoalXP,
      reminderTime: reminderTime ?? this.reminderTime,
    );
  }
}

/// إحصائيات التعلم
class LearningStats {
  final String userId;
  final String languageCode;
  final int totalXP;
  final int lessonsCompleted;
  final int exercisesCompleted;
  final double averageAccuracy;
  final Duration totalTimeSpent;
  final int currentStreak;
  final int longestStreak;
  final DateTime lastStudyDate;
  final Map<ExerciseType, int> exerciseTypeStats;
  final Map<String, double> skillLevels; // مستوى المهارة لكل موضوع

  const LearningStats({
    required this.userId,
    required this.languageCode,
    this.totalXP = 0,
    this.lessonsCompleted = 0,
    this.exercisesCompleted = 0,
    this.averageAccuracy = 0.0,
    this.totalTimeSpent = Duration.zero,
    this.currentStreak = 0,
    this.longestStreak = 0,
    required this.lastStudyDate,
    this.exerciseTypeStats = const {},
    this.skillLevels = const {},
  });
}

/// المكافأة
class Reward {
  final String id;
  final RewardType type;
  final int amount;
  final String title;
  final String titleAr;
  final String description;
  final String descriptionAr;
  final String? iconPath;
  final DateTime earnedAt;

  const Reward({
    required this.id,
    required this.type,
    required this.amount,
    required this.title,
    required this.titleAr,
    required this.description,
    required this.descriptionAr,
    this.iconPath,
    required this.earnedAt,
  });
}
