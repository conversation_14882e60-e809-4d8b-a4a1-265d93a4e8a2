import 'package:flutter/material.dart';
import '../../../config/app_theme.dart';
import '../../../utils/helpers.dart';
import '../../../widgets/three_d_button.dart';
import 'kids_mode_service.dart';

/// شاشة وضع الأطفال
class KidsModeScreen extends StatefulWidget {
  const KidsModeScreen({super.key});

  @override
  State<KidsModeScreen> createState() => _KidsModeScreenState();
}

class _KidsModeScreenState extends State<KidsModeScreen>
    with SingleTickerProviderStateMixin {
  final KidsModeService _kidsModeService = KidsModeService();
  final PageController _pageController = PageController();

  String _sourceLanguage = 'ar';
  String _targetLanguage = 'en';
  bool _isListening = false;
  bool _isSpeaking = false;
  int _currentCategory = 0;
  int _currentWordIndex = 0;
  late AnimationController _animationController;

  // قائمة الفئات
  final List<Map<String, dynamic>> _categories = [
    {
      'name': 'الحيوانات',
      'icon': Icons.pets,
      'color': Colors.orange,
      'words': [
        {'ar': 'قطة', 'en': 'cat', 'image': 'assets/images/kids/cat.png'},
        {'ar': 'كلب', 'en': 'dog', 'image': 'assets/images/kids/dog.png'},
        {'ar': 'أسد', 'en': 'lion', 'image': 'assets/images/kids/lion.png'},
        {
          'ar': 'فيل',
          'en': 'elephant',
          'image': 'assets/images/kids/elephant.png',
        },
        {
          'ar': 'زرافة',
          'en': 'giraffe',
          'image': 'assets/images/kids/giraffe.png',
        },
      ],
    },
    {
      'name': 'الألوان',
      'icon': Icons.palette,
      'color': Colors.purple,
      'words': [
        {'ar': 'أحمر', 'en': 'red', 'image': 'assets/images/kids/red.png'},
        {'ar': 'أزرق', 'en': 'blue', 'image': 'assets/images/kids/blue.png'},
        {'ar': 'أخضر', 'en': 'green', 'image': 'assets/images/kids/green.png'},
        {
          'ar': 'أصفر',
          'en': 'yellow',
          'image': 'assets/images/kids/yellow.png',
        },
        {
          'ar': 'برتقالي',
          'en': 'orange',
          'image': 'assets/images/kids/orange.png',
        },
      ],
    },
    {
      'name': 'الأرقام',
      'icon': Icons.format_list_numbered,
      'color': Colors.blue,
      'words': [
        {'ar': 'واحد', 'en': 'one', 'image': 'assets/images/kids/one.png'},
        {'ar': 'اثنان', 'en': 'two', 'image': 'assets/images/kids/two.png'},
        {'ar': 'ثلاثة', 'en': 'three', 'image': 'assets/images/kids/three.png'},
        {'ar': 'أربعة', 'en': 'four', 'image': 'assets/images/kids/four.png'},
        {'ar': 'خمسة', 'en': 'five', 'image': 'assets/images/kids/five.png'},
      ],
    },
    {
      'name': 'الفواكه',
      'icon': Icons.apple,
      'color': Colors.red,
      'words': [
        {'ar': 'تفاحة', 'en': 'apple', 'image': 'assets/images/kids/apple.png'},
        {
          'ar': 'موزة',
          'en': 'banana',
          'image': 'assets/images/kids/banana.png',
        },
        {
          'ar': 'برتقالة',
          'en': 'orange',
          'image': 'assets/images/kids/orange_fruit.png',
        },
        {
          'ar': 'فراولة',
          'en': 'strawberry',
          'image': 'assets/images/kids/strawberry.png',
        },
        {'ar': 'عنب', 'en': 'grapes', 'image': 'assets/images/kids/grapes.png'},
      ],
    },
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );

    // تهيئة خدمة وضع الأطفال
    _initKidsModeService();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pageController.dispose();
    _kidsModeService.dispose();
    super.dispose();
  }

  /// تهيئة خدمة وضع الأطفال
  Future<void> _initKidsModeService() async {
    try {
      await _kidsModeService.initialize();
    } catch (e) {
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء تهيئة خدمة وضع الأطفال: $e',
          isError: true,
        );
      }
    }
  }

  /// نطق الكلمة
  Future<void> _speakWord(String word, String languageCode) async {
    if (_isSpeaking) return;

    setState(() {
      _isSpeaking = true;
    });

    try {
      await _kidsModeService.speakText(
        text: word,
        languageCode: languageCode,
        onDone: () {
          if (mounted) {
            setState(() {
              _isSpeaking = false;
            });
          }
        },
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _isSpeaking = false;
        });
      }
    }
  }

  /// بدء الاستماع
  Future<void> _startListening() async {
    try {
      setState(() {
        _isListening = true;
      });

      _animationController.repeat(reverse: true);

      // بدء الاستماع
      await _kidsModeService.startListening(
        languageCode: _sourceLanguage,
        onResult: (text) {
          // التحقق مما إذا كان النص المنطوق يطابق الكلمة الحالية
          final currentWord = _getCurrentWord();
          final currentWordText = currentWord[_sourceLanguage] as String;

          if (text.toLowerCase() == currentWordText.toLowerCase()) {
            _stopListening();

            // عرض رسالة نجاح
            AppHelpers.showSnackBar(context, 'أحسنت! 👏');

            // الانتقال إلى الكلمة التالية بعد فترة قصيرة
            Future.delayed(const Duration(seconds: 1), () {
              if (mounted) {
                _nextWord();
              }
            });
          }
        },
        onDone: () {
          setState(() {
            _isListening = false;
          });
          _animationController.stop();
          _animationController.reset();
        },
      );
    } catch (e) {
      setState(() {
        _isListening = false;
      });
      _animationController.stop();
      _animationController.reset();

      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء الاستماع: $e',
          isError: true,
        );
      }
    }
  }

  /// إيقاف الاستماع
  Future<void> _stopListening() async {
    try {
      await _kidsModeService.stopListening();

      setState(() {
        _isListening = false;
      });

      _animationController.stop();
      _animationController.reset();
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  /// الحصول على الكلمة الحالية
  Map<String, dynamic> _getCurrentWord() {
    return _categories[_currentCategory]['words'][_currentWordIndex]
        as Map<String, dynamic>;
  }

  /// الانتقال إلى الكلمة التالية
  void _nextWord() {
    setState(() {
      if (_currentWordIndex <
          (_categories[_currentCategory]['words'] as List).length - 1) {
        _currentWordIndex++;
      } else {
        _currentWordIndex = 0;
        if (_currentCategory < _categories.length - 1) {
          _currentCategory++;
        } else {
          _currentCategory = 0;
        }
      }
    });

    // نطق الكلمة الجديدة
    final currentWord = _getCurrentWord();
    _speakWord(currentWord[_sourceLanguage] as String, _sourceLanguage);
  }

  /// الانتقال إلى الكلمة السابقة
  void _previousWord() {
    setState(() {
      if (_currentWordIndex > 0) {
        _currentWordIndex--;
      } else {
        if (_currentCategory > 0) {
          _currentCategory--;
          _currentWordIndex =
              (_categories[_currentCategory]['words'] as List).length - 1;
        } else {
          _currentCategory = _categories.length - 1;
          _currentWordIndex =
              (_categories[_currentCategory]['words'] as List).length - 1;
        }
      }
    });

    // نطق الكلمة الجديدة
    final currentWord = _getCurrentWord();
    _speakWord(currentWord[_sourceLanguage] as String, _sourceLanguage);
  }

  /// تبديل اللغات
  void _swapLanguages() {
    setState(() {
      final temp = _sourceLanguage;
      _sourceLanguage = _targetLanguage;
      _targetLanguage = temp;
    });

    // نطق الكلمة بعد تبديل اللغات
    final currentWord = _getCurrentWord();
    _speakWord(currentWord[_sourceLanguage] as String, _sourceLanguage);
  }

  /// الحصول على اسم اللغة
  String _getLanguageName(String code) {
    switch (code) {
      case 'ar':
        return '🇸🇦 العربية';
      case 'en':
        return '🇺🇸 English';
      case 'fr':
        return '🇫🇷 Français';
      case 'es':
        return '🇪🇸 Español';
      default:
        return code;
    }
  }

  /// عرض مربع حوار اختيار اللغة
  void _showLanguageSelector(bool isSource) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                isSource ? 'اختر لغة التعلم' : 'اختر لغة الترجمة',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              Expanded(
                child: ListView(
                  children: [
                    _buildLanguageItem(context, 'ar', isSource),
                    _buildLanguageItem(context, 'en', isSource),
                    _buildLanguageItem(context, 'fr', isSource),
                    _buildLanguageItem(context, 'es', isSource),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// بناء عنصر اللغة
  Widget _buildLanguageItem(BuildContext context, String code, bool isSource) {
    final isSelected =
        isSource ? _sourceLanguage == code : _targetLanguage == code;

    return ListTile(
      title: Text(_getLanguageName(code)),
      trailing:
          isSelected
              ? const Icon(Icons.check, color: AppTheme.primaryColor)
              : null,
      onTap: () {
        setState(() {
          if (isSource) {
            _sourceLanguage = code;
          } else {
            _targetLanguage = code;
          }
        });
        Navigator.pop(context);

        // نطق الكلمة بعد تغيير اللغة
        final currentWord = _getCurrentWord();
        _speakWord(currentWord[_sourceLanguage] as String, _sourceLanguage);
      },
    );
  }

  /// عرض مربع حوار اختيار الفئة
  void _showCategorySelector() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'اختر الفئة',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 20),
              Expanded(
                child: GridView.builder(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 1.5,
                    crossAxisSpacing: 10,
                    mainAxisSpacing: 10,
                  ),
                  itemCount: _categories.length,
                  itemBuilder: (context, index) {
                    final category = _categories[index];
                    final isSelected = _currentCategory == index;

                    return InkWell(
                      onTap: () {
                        setState(() {
                          _currentCategory = index;
                          _currentWordIndex = 0;
                        });
                        Navigator.pop(context);

                        // نطق الكلمة بعد تغيير الفئة
                        final currentWord = _getCurrentWord();
                        _speakWord(
                          currentWord[_sourceLanguage] as String,
                          _sourceLanguage,
                        );
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          color:
                              isSelected
                                  ? (category['color'] as Color).withAlpha(50)
                                  : Colors.grey.shade100,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color:
                                isSelected
                                    ? category['color'] as Color
                                    : Colors.grey.shade300,
                            width: 2,
                          ),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              category['icon'] as IconData,
                              color: category['color'] as Color,
                              size: 32,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              category['name'] as String,
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color:
                                    isSelected
                                        ? category['color'] as Color
                                        : AppTheme.onBackground,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final currentWord = _getCurrentWord();
    final currentCategory = _categories[_currentCategory];

    return Scaffold(
      appBar: AppBar(
        title: const Text('وضع الأطفال'),
        backgroundColor: AppTheme.primaryColor,
        actions: [
          // زر اختيار الفئة
          IconButton(
            icon: const Icon(Icons.category),
            onPressed: _showCategorySelector,
            tooltip: 'اختر الفئة',
          ),
        ],
      ),
      body: Column(
        children: [
          // اختيار اللغات
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(10),
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                // اللغة المصدر
                Expanded(
                  child: InkWell(
                    onTap: () => _showLanguageSelector(true),
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        _getLanguageName(_sourceLanguage),
                        textAlign: TextAlign.center,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                  ),
                ),

                // زر تبديل اللغات
                IconButton(
                  icon: const Icon(Icons.swap_horiz),
                  onPressed: _swapLanguages,
                  color: AppTheme.primaryColor,
                ),

                // اللغة الهدف
                Expanded(
                  child: InkWell(
                    onTap: () => _showLanguageSelector(false),
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        _getLanguageName(_targetLanguage),
                        textAlign: TextAlign.center,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // عنوان الفئة
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  currentCategory['icon'] as IconData,
                  color: currentCategory['color'] as Color,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  currentCategory['name'] as String,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: currentCategory['color'] as Color,
                  ),
                ),
              ],
            ),
          ),

          // بطاقة الكلمة
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(20),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // صورة الكلمة
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(24.0),
                      child: Image.asset(
                        currentWord['image'] as String,
                        fit: BoxFit.contain,
                      ),
                    ),
                  ),

                  // الكلمة باللغة المصدر
                  GestureDetector(
                    onTap:
                        () => _speakWord(
                          currentWord[_sourceLanguage] as String,
                          _sourceLanguage,
                        ),
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      margin: const EdgeInsets.symmetric(horizontal: 24),
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            currentWord[_sourceLanguage] as String,
                            style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(width: 8),
                          const Icon(Icons.volume_up, color: Colors.white),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // الكلمة باللغة الهدف
                  GestureDetector(
                    onTap:
                        () => _speakWord(
                          currentWord[_targetLanguage] as String,
                          _targetLanguage,
                        ),
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      margin: const EdgeInsets.symmetric(horizontal: 24),
                      decoration: BoxDecoration(
                        color: AppTheme.secondaryColor,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            currentWord[_targetLanguage] as String,
                            style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(width: 8),
                          const Icon(Icons.volume_up, color: Colors.white),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),
                ],
              ),
            ),
          ),

          // أزرار التنقل
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // زر الكلمة السابقة
                CircleButton3D(
                  icon: Icons.arrow_back,
                  size: 60,
                  iconSize: 30,
                  onPressed: _previousWord,
                  color: AppTheme.secondaryColor,
                ),

                // زر الاستماع
                CircleButton3D(
                  icon: _isListening ? Icons.stop : Icons.mic,
                  size: 80,
                  iconSize: 40,
                  onPressed: _isListening ? _stopListening : _startListening,
                  color: _isListening ? Colors.red : AppTheme.primaryColor,
                ),

                // زر الكلمة التالية
                CircleButton3D(
                  icon: Icons.arrow_forward,
                  size: 60,
                  iconSize: 30,
                  onPressed: _nextWord,
                  color: AppTheme.secondaryColor,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
