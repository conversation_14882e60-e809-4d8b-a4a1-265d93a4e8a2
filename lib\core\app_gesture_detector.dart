import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// كاشف الإيماءات المخصص للتطبيق
/// يستخدم لتحسين استجابة الضغطات في جميع أنحاء التطبيق
class AppGestureDetector extends StatelessWidget {
  /// دالة يتم تنفيذها عند الضغط على العنصر
  final VoidCallback? onTap;

  /// دالة يتم تنفيذها عند الضغط المطول على العنصر
  final VoidCallback? onLongPress;

  /// العنصر الذي سيتم تغليفه
  final Widget child;

  /// ما إذا كان العنصر معطلاً
  final bool isDisabled;

  /// لون تأثير الضغط
  final Color? splashColor;

  /// نصف قطر الحواف
  final double? borderRadius;

  /// منشئ كاشف الإيماءات المخصص
  const AppGestureDetector({
    super.key,
    this.onTap,
    this.onLongPress,
    required this.child,
    this.isDisabled = false,
    this.splashColor,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap:
            isDisabled
                ? null
                : () {
                  if (onTap != null) {
                    // إضافة تأثير لمسي للمستخدم
                    HapticFeedback.lightImpact();
                    onTap!();
                  }
                },
        onLongPress:
            isDisabled
                ? null
                : () {
                  if (onLongPress != null) {
                    // إضافة تأثير لمسي للمستخدم
                    HapticFeedback.mediumImpact();
                    onLongPress!();
                  }
                },
        splashColor:
            splashColor ?? Theme.of(context).primaryColor.withAlpha(77),
        highlightColor:
            splashColor != null
                ? splashColor!.withAlpha(26)
                : Theme.of(context).primaryColor.withAlpha(26),
        borderRadius:
            borderRadius != null ? BorderRadius.circular(borderRadius!) : null,
        child: child,
      ),
    );
  }
}

/// توسيع Widget لإضافة طريقة مختصرة لتغليف أي عنصر بكاشف الإيماءات المخصص
extension GestureDetectorExtension on Widget {
  /// تغليف العنصر بكاشف الإيماءات المخصص
  Widget withTap(
    VoidCallback? onTap, {
    VoidCallback? onLongPress,
    bool isDisabled = false,
    Color? splashColor,
    double? borderRadius,
  }) {
    return AppGestureDetector(
      onTap: onTap,
      onLongPress: onLongPress,
      isDisabled: isDisabled,
      splashColor: splashColor,
      borderRadius: borderRadius,
      child: this,
    );
  }
}
