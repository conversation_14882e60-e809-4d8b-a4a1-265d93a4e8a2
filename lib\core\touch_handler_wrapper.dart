import 'package:flutter/material.dart';

/// مغلف للتعامل مع اللمسات والضغطات في التطبيق
/// يستخدم لحل مشكلة عدم استجابة العناصر التفاعلية
class TouchHandlerWrapper extends StatelessWidget {
  /// الويدجت الذي سيتم تغليفه
  final Widget child;

  /// منشئ المغلف
  const TouchHandlerWrapper({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    // تمرير الويدجت مباشرة بدون أي معالجة إضافية
    // لتجنب التداخل مع معالجة اللمسات الأصلية
    return child;
  }
}
