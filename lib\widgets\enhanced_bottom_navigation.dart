import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// شريط تنقل سفلي محسن مع تأثيرات بصرية ولمسية
class EnhancedBottomNavigation extends StatefulWidget {
  /// عناصر شريط التنقل
  final List<EnhancedBottomNavigationItem> items;

  /// الفهرس الحالي
  final int currentIndex;

  /// دالة يتم تنفيذها عند تغيير العنصر
  final Function(int) onTap;

  /// لون الخلفية
  final Color? backgroundColor;

  /// لون العنصر المحدد
  final Color? selectedItemColor;

  /// لون العنصر غير المحدد
  final Color? unselectedItemColor;

  /// حجم الأيقونة المحددة
  final double selectedIconSize;

  /// حجم الأيقونة غير المحددة
  final double unselectedIconSize;

  /// حجم خط التسمية المحددة
  final double selectedLabelSize;

  /// حجم خط التسمية غير المحددة
  final double unselectedLabelSize;

  /// ارتفاع شريط التنقل
  final double height;

  /// الارتفاع
  final double elevation;

  /// نصف قطر الحواف
  final double borderRadius;

  /// تباعد داخلي
  final EdgeInsetsGeometry padding;

  /// منشئ شريط التنقل السفلي المحسن
  const EnhancedBottomNavigation({
    super.key,
    required this.items,
    required this.currentIndex,
    required this.onTap,
    this.backgroundColor,
    this.selectedItemColor,
    this.unselectedItemColor,
    this.selectedIconSize = 28,
    this.unselectedIconSize = 24,
    this.selectedLabelSize = 12,
    this.unselectedLabelSize = 11,
    this.height = 64,
    this.elevation = 8,
    this.borderRadius = 0,
    this.padding = const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
  });

  @override
  State<EnhancedBottomNavigation> createState() =>
      _EnhancedBottomNavigationState();
}

class _EnhancedBottomNavigationState extends State<EnhancedBottomNavigation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isDark = theme.brightness == Brightness.dark;

    // ألوان متناسقة من Material Design 3
    final effectiveBackgroundColor =
        widget.backgroundColor ??
        (isDark
            ? colorScheme.surfaceContainerHighest
            : colorScheme.surfaceContainerHigh);

    final effectiveSelectedItemColor =
        widget.selectedItemColor ?? colorScheme.primary;

    final effectiveUnselectedItemColor =
        widget.unselectedItemColor ??
        (isDark
            ? colorScheme.onSurface.withAlpha(180)
            : colorScheme.onSurfaceVariant);

    // ظلال متعددة لتأثير Neumorphism
    final List<BoxShadow> shadows =
        isDark
            ? [
              BoxShadow(
                color: Colors.black.withAlpha(60),
                blurRadius: widget.elevation,
                offset: const Offset(0, -3),
                spreadRadius: 1,
              ),
              BoxShadow(
                color: colorScheme.surface.withAlpha(30),
                blurRadius: widget.elevation / 2,
                offset: const Offset(0, 1),
                spreadRadius: 0,
              ),
            ]
            : [
              BoxShadow(
                color: Colors.black.withAlpha(20),
                blurRadius: widget.elevation,
                offset: const Offset(0, -3),
                spreadRadius: 1,
              ),
              BoxShadow(
                color: Colors.white.withAlpha(100),
                blurRadius: widget.elevation / 2,
                offset: const Offset(0, 1),
                spreadRadius: 0,
              ),
            ];

    return Container(
      height: widget.height,
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      padding: widget.padding,
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius + 16),
        boxShadow: shadows,
        border: Border.all(
          color:
              isDark
                  ? colorScheme.outline.withAlpha(40)
                  : colorScheme.outline.withAlpha(30),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: List.generate(widget.items.length, (index) {
          final item = widget.items[index];
          final isSelected = index == widget.currentIndex;

          return Expanded(
            child: _buildNavigationItem(
              item: item,
              isSelected: isSelected,
              index: index,
              selectedColor: effectiveSelectedItemColor,
              unselectedColor: effectiveUnselectedItemColor,
            ),
          );
        }),
      ),
    );
  }

  /// بناء عنصر التنقل
  Widget _buildNavigationItem({
    required EnhancedBottomNavigationItem item,
    required bool isSelected,
    required int index,
    required Color selectedColor,
    required Color unselectedColor,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isDark = theme.brightness == Brightness.dark;

    return InkWell(
      onTap: () {
        HapticFeedback.selectionClick();
        widget.onTap(index);
      },
      splashColor: selectedColor.withAlpha(25),
      highlightColor: selectedColor.withAlpha(15),
      borderRadius: BorderRadius.circular(20),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOutQuint,
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? (isDark
                      ? selectedColor.withAlpha(40)
                      : selectedColor.withAlpha(30))
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
          border:
              isSelected
                  ? Border.all(
                    color: selectedColor.withAlpha(isDark ? 60 : 40),
                    width: 1,
                  )
                  : null,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أيقونة العنصر مع تأثير انتقالي
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOutQuint,
              padding: EdgeInsets.all(isSelected ? 4 : 0),
              decoration:
                  isSelected
                      ? BoxDecoration(
                        color:
                            isDark
                                ? colorScheme.surface.withAlpha(100)
                                : colorScheme.surface.withAlpha(150),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: selectedColor.withAlpha(isDark ? 40 : 30),
                            blurRadius: 8,
                            spreadRadius: 1,
                          ),
                        ],
                      )
                      : null,
              child: Icon(
                item.icon,
                color: isSelected ? selectedColor : unselectedColor,
                size:
                    isSelected
                        ? widget.selectedIconSize
                        : widget.unselectedIconSize,
              ),
            ),

            const SizedBox(height: 4),

            // تسمية العنصر مع تأثير انتقالي
            AnimatedDefaultTextStyle(
              duration: const Duration(milliseconds: 200),
              style: TextStyle(
                color: isSelected ? selectedColor : unselectedColor,
                fontSize:
                    isSelected
                        ? widget.selectedLabelSize
                        : widget.unselectedLabelSize,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                letterSpacing: isSelected ? 0.2 : 0,
              ),
              child: Text(item.label),
            ),
          ],
        ),
      ),
    );
  }
}

/// عنصر شريط التنقل السفلي المحسن
class EnhancedBottomNavigationItem {
  /// أيقونة العنصر
  final IconData icon;

  /// تسمية العنصر
  final String label;

  /// منشئ عنصر شريط التنقل السفلي المحسن
  const EnhancedBottomNavigationItem({required this.icon, required this.label});
}
