import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import '../../config/constants.dart';

/// خدمة التفاعل مع Gemini API
class GeminiService {
  final FirebaseRemoteConfig _remoteConfig;
  
  /// منشئ الخدمة
  GeminiService(this._remoteConfig);
  
  /// إرسال رسالة إلى Gemini API
  Future<String> sendMessage(String message) async {
    try {
      // الحصول على مفتاح API من Firebase Remote Config
      final apiKey = _getApiKey();
      
      if (apiKey.isEmpty) {
        return 'عذراً، مفتاح API غير متوفر. الرجاء التحقق من إعدادات التطبيق.';
      }
      
      final url = Uri.parse(
        'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=$apiKey',
      );
      
      // بناء سياق المحادثة
      final prompt = '''
أنت مساعد ذكي للترجمة يتحدث العربية بطلاقة. ساعد المستخدم في:
1. الإجابة على أسئلة حول اللغات والترجمة
2. تقديم نصائح للتعلم والتواصل باللغات المختلفة
3. شرح الفروق الثقافية بين اللغات
4. مساعدة المستخدم في فهم التعبيرات والمصطلحات

رسالة المستخدم: $message
      ''';
      
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'contents': [
            {
              'parts': [
                {'text': prompt},
              ],
            },
          ],
          'generationConfig': {
            'temperature': 0.7,
            'topK': 40,
            'topP': 0.95,
            'maxOutputTokens': 1000,
          },
        }),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['candidates'][0]['content']['parts'][0]['text'].trim();
      } else {
        debugPrint('Gemini API error: ${response.body}');
        return 'عذراً، حدث خطأ أثناء الاتصال بالخدمة. الرجاء المحاولة مرة أخرى لاحقاً.';
      }
    } catch (e) {
      debugPrint('Gemini service error: $e');
      return 'عذراً، حدث خطأ غير متوقع. الرجاء المحاولة مرة أخرى.';
    }
  }
  
  /// الحصول على مفتاح API من Firebase Remote Config
  String _getApiKey() {
    // محاولة الحصول على مفتاح Gemini API
    String apiKey = _remoteConfig.getString(AppConstants.keyGeminiApiKey);
    
    // إذا كان مفتاح Gemini غير متوفر، استخدم مفتاح OpenAI
    if (apiKey.isEmpty) {
      apiKey = _remoteConfig.getString(AppConstants.keyOpenAIApiKey);
    }
    
    return apiKey;
  }
  
  /// إرسال رسالة للحصول على ترجمة
  Future<String> getTranslation({
    required String text,
    required String sourceLanguage,
    required String targetLanguage,
  }) async {
    try {
      final apiKey = _getApiKey();
      
      if (apiKey.isEmpty) {
        return 'عذراً، مفتاح API غير متوفر. الرجاء التحقق من إعدادات التطبيق.';
      }
      
      final url = Uri.parse(
        'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=$apiKey',
      );
      
      // بناء سياق الترجمة
      final prompt = '''
ترجم النص التالي من $sourceLanguage إلى $targetLanguage. قدم الترجمة فقط دون أي شروحات أو تعليقات إضافية.

النص: $text
      ''';
      
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'contents': [
            {
              'parts': [
                {'text': prompt},
              ],
            },
          ],
          'generationConfig': {
            'temperature': 0.2,
            'topK': 40,
            'topP': 0.95,
            'maxOutputTokens': 1000,
          },
        }),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['candidates'][0]['content']['parts'][0]['text'].trim();
      } else {
        debugPrint('Gemini API error: ${response.body}');
        return 'عذراً، حدث خطأ أثناء الترجمة. الرجاء المحاولة مرة أخرى.';
      }
    } catch (e) {
      debugPrint('Gemini translation error: $e');
      return 'عذراً، حدث خطأ غير متوقع. الرجاء المحاولة مرة أخرى.';
    }
  }
}
