import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/material.dart';
import '../../config/constants.dart';

/// خدمة إدارة Firebase Remote Config
class RemoteConfigService {
  final FirebaseRemoteConfig _remoteConfig;

  RemoteConfigService(this._remoteConfig);

  /// تهيئة خدمة Remote Config
  Future<void> initialize() async {
    try {
      await _remoteConfig.setConfigSettings(
        RemoteConfigSettings(
          fetchTimeout: const Duration(minutes: 1),
          minimumFetchInterval: const Duration(hours: 1),
        ),
      );

      await _setDefaults();
      await _fetchAndActivate();
    } catch (e) {
      debugPrint('Remote Config initialization error: $e');
    }
  }

  /// تعيين القيم الافتراضية
  Future<void> _setDefaults() async {
    try {
      await _remoteConfig.setDefaults({
        // مفاتيح API
        AppConstants.keyOpenAIApiKey:
            '********************************************************************************************************************************************************************',
        AppConstants.keyGeminiApiKey: 'AIzaSyDzPBZY2TeUIm6L3WVRyF_3DkeTJpx-988',
        AppConstants.keyOpenRouterApiKey:
            '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',
        AppConstants.keyGoogleTranslateApiKey: '',
        AppConstants.keyActiveApiService:
            AppConstants.apiServiceOpenAI, // البدء بـ OpenAI
        AppConstants.keyOpenAIEndDate:
            '2025-12-31', // تاريخ انتهاء استخدام OpenAI
        AppConstants.keyGeminiEndDate:
            '2025-12-31', // تاريخ انتهاء استخدام Gemini
        AppConstants.keyDailyLimitOpenAI: 100, // الحد اليومي لاستخدام OpenAI
        AppConstants.keyDailyLimitGemini: 100, // الحد اليومي لاستخدام Gemini
        AppConstants.keyDailyLimitOpenRouter:
            100, // الحد اليومي لاستخدام OpenRouter
        // مفاتيح خدمات التعرف على الكلام
        AppConstants.keyAzureSpeechKey: 'your_azure_speech_key_here',
        AppConstants.keyAzureSpeechRegion: 'eastus',
        AppConstants.keyGoogleSpeechKey: 'your_google_speech_key_here',
        // إعدادات الإعلانات
        AppConstants.keyShowAds: true,
        AppConstants.keyAdFrequency: 5,

        // روابط
        AppConstants.privacyPolicyUrl: 'https://example.com/privacy-policy',
        AppConstants.termsOfServiceUrl: 'https://example.com/terms-of-service',
        AppConstants.supportUrl: 'https://example.com/support',
        AppConstants.contactUsUrl: 'https://example.com/contact',
      });
    } catch (e) {
      debugPrint('Remote Config set defaults error: $e');
    }
  }

  /// جلب وتفعيل التكوين
  Future<void> _fetchAndActivate() async {
    try {
      await _remoteConfig.fetchAndActivate();
      debugPrint('Remote Config fetched and activated');
    } catch (e) {
      debugPrint('Remote Config fetch and activate error: $e');
    }
  }

  /// الحصول على قيمة نصية
  String getString(String key) {
    try {
      return _remoteConfig.getString(key);
    } catch (e) {
      debugPrint('Remote Config get string error: $e');
      return '';
    }
  }

  /// الحصول على قيمة منطقية
  bool getBool(String key) {
    try {
      return _remoteConfig.getBool(key);
    } catch (e) {
      debugPrint('Remote Config get bool error: $e');
      return false;
    }
  }

  /// الحصول على قيمة عددية
  int getInt(String key) {
    try {
      return _remoteConfig.getInt(key);
    } catch (e) {
      debugPrint('Remote Config get int error: $e');
      return 0;
    }
  }

  /// الحصول على قيمة عشرية
  double getDouble(String key) {
    try {
      return _remoteConfig.getDouble(key);
    } catch (e) {
      debugPrint('Remote Config get double error: $e');
      return 0.0;
    }
  }

  /// تحديث التكوين
  Future<void> refresh() async {
    try {
      await _remoteConfig.fetchAndActivate();
      debugPrint('Remote Config refreshed');
    } catch (e) {
      debugPrint('Remote Config refresh error: $e');
    }
  }
}
