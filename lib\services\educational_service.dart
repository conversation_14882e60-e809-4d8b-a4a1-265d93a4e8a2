import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_tts/flutter_tts.dart';
import '../models/educational_content.dart';
import 'arabic_speech_service.dart';

/// خدمة المحتوى التعليمي للأطفال
class EducationalService {
  static const String _progressKey = 'educational_progress';

  final FlutterTts _tts = FlutterTts();
  final ArabicSpeechService _arabicSpeech = ArabicSpeechService();

  List<EducationalLesson> _lessons = [];
  StudentProgress? _currentProgress;
  bool _isInitialized = false;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🎓 تهيئة خدمة المحتوى التعليمي للأطفال');

      // تهيئة خدمات النطق
      await _tts.setLanguage('en-US');
      await _arabicSpeech.initialize();

      // تحميل الدروس
      _lessons = _generateEducationalLessons();

      // تحميل تقدم الطالب
      await _loadStudentProgress();

      _isInitialized = true;
      debugPrint('✅ تم تهيئة خدمة المحتوى التعليمي بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة المحتوى التعليمي: $e');
      rethrow;
    }
  }

  /// الحصول على جميع الدروس
  List<EducationalLesson> getAllLessons() => _lessons;

  /// الحصول على الدروس حسب الفئة
  List<EducationalLesson> getLessonsByCategory(LessonCategory category) {
    return _lessons.where((lesson) => lesson.category == category).toList();
  }

  /// الحصول على الدروس المتاحة
  List<EducationalLesson> getAvailableLessons() {
    return _lessons.where((lesson) => lesson.isUnlocked).toList();
  }

  /// الحصول على الدرس التالي
  EducationalLesson? getNextLesson() {
    final availableLessons = getAvailableLessons();
    if (availableLessons.isEmpty) return null;

    // البحث عن أول درس غير مكتمل
    for (final lesson in availableLessons) {
      final progress = _currentProgress?.lessonProgress[lesson.id];
      if (progress == null || !progress.isCompleted) {
        return lesson;
      }
    }

    return null;
  }

  /// الحصول على تقدم الطالب
  StudentProgress? getStudentProgress() => _currentProgress;

  /// نطق النص بالعربية
  Future<void> speakArabic(String text) async {
    try {
      await _arabicSpeech.speakArabic(text);
    } catch (e) {
      debugPrint('❌ خطأ في النطق العربي: $e');
    }
  }

  /// نطق النص بالإنجليزية
  Future<void> speakEnglish(String text) async {
    try {
      await _tts.setLanguage('en-US');
      await _tts.setSpeechRate(0.6);
      await _tts.setPitch(1.0);
      await _tts.setVolume(1.0);
      await _tts.speak(text);
    } catch (e) {
      debugPrint('❌ خطأ في النطق الإنجليزي: $e');
    }
  }

  /// نطق النص بلغة محددة
  Future<void> speakInLanguage(String text, String languageCode) async {
    try {
      final languageMap = {
        'ar': 'ar-SA', // العربية
        'en': 'en-US', // الإنجليزية
        'fr': 'fr-FR', // الفرنسية
        'de': 'de-DE', // الألمانية
        'es': 'es-ES', // الإسبانية
        'it': 'it-IT', // الإيطالية
        'ja': 'ja-JP', // اليابانية
        'zh': 'zh-CN', // الصينية
        'tr': 'tr-TR', // التركية
        'ru': 'ru-RU', // الروسية
      };

      final ttsLanguage = languageMap[languageCode] ?? 'en-US';

      if (languageCode == 'ar') {
        // استخدام خدمة النطق العربي المحسنة
        await speakArabic(text);
      } else {
        // استخدام خدمة النطق العامة
        await _tts.setLanguage(ttsLanguage);
        await _tts.setSpeechRate(0.6);
        await _tts.setPitch(1.0);
        await _tts.setVolume(1.0);
        await _tts.speak(text);
      }

      debugPrint('🔊 تم نطق النص بلغة $languageCode: "$text"');
    } catch (e) {
      debugPrint('❌ خطأ في النطق بلغة $languageCode: $e');
      // محاولة النطق بالإنجليزية كبديل
      try {
        await speakEnglish(text);
      } catch (fallbackError) {
        debugPrint('❌ خطأ في النطق البديل: $fallbackError');
      }
    }
  }

  /// تحديث تقدم الدرس
  Future<void> updateLessonProgress(String lessonId, int score) async {
    try {
      _currentProgress ??= StudentProgress(
        studentId: 'default',
        lessonProgress: {},
        lastActivity: DateTime.now(),
      );

      final lesson = _lessons.firstWhere((l) => l.id == lessonId);
      final isCompleted = score >= (lesson.maxScore * 0.7); // 70% للنجاح

      final newProgress = LessonProgress(
        lessonId: lessonId,
        score: score,
        maxScore: lesson.maxScore,
        isCompleted: isCompleted,
        completedAt: isCompleted ? DateTime.now() : null,
      );

      final updatedLessonProgress = Map<String, LessonProgress>.from(
        _currentProgress!.lessonProgress,
      );
      updatedLessonProgress[lessonId] = newProgress;

      _currentProgress = StudentProgress(
        studentId: _currentProgress!.studentId,
        lessonProgress: updatedLessonProgress,
        totalScore: _calculateTotalScore(updatedLessonProgress),
        totalLessonsCompleted: _calculateCompletedLessons(
          updatedLessonProgress,
        ),
        achievements: _currentProgress!.achievements,
        lastActivity: DateTime.now(),
      );

      // فتح الدرس التالي إذا تم إكمال الدرس الحالي
      if (isCompleted) {
        _unlockNextLesson(lessonId);
      }

      // حفظ التقدم
      await _saveStudentProgress();

      debugPrint('✅ تم تحديث تقدم الدرس: $lessonId - النتيجة: $score');
    } catch (e) {
      debugPrint('❌ خطأ في تحديث تقدم الدرس: $e');
    }
  }

  /// حساب النتيجة الإجمالية
  int _calculateTotalScore(Map<String, LessonProgress> lessonProgress) {
    return lessonProgress.values.fold(
      0,
      (sum, progress) => sum + progress.score,
    );
  }

  /// حساب عدد الدروس المكتملة
  int _calculateCompletedLessons(Map<String, LessonProgress> lessonProgress) {
    return lessonProgress.values
        .where((progress) => progress.isCompleted)
        .length;
  }

  /// فتح الدرس التالي
  void _unlockNextLesson(String completedLessonId) {
    final completedLesson = _lessons.firstWhere(
      (l) => l.id == completedLessonId,
    );
    final nextLessons =
        _lessons
            .where(
              (l) =>
                  l.category == completedLesson.category &&
                  l.order == completedLesson.order + 1,
            )
            .toList();

    for (final lesson in nextLessons) {
      final index = _lessons.indexOf(lesson);
      if (index != -1) {
        _lessons[index] = EducationalLesson(
          id: lesson.id,
          title: lesson.title,
          titleAr: lesson.titleAr,
          description: lesson.description,
          descriptionAr: lesson.descriptionAr,
          category: lesson.category,
          level: lesson.level,
          order: lesson.order,
          iconPath: lesson.iconPath,
          primaryColor: lesson.primaryColor,
          activities: lesson.activities,
          requiredScore: lesson.requiredScore,
          isUnlocked: true, // فتح الدرس
          maxScore: lesson.maxScore,
        );
      }
    }
  }

  /// تحميل تقدم الطالب
  Future<void> _loadStudentProgress() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final progressData = prefs.getString(_progressKey);

      if (progressData != null) {
        final data = jsonDecode(progressData) as Map<String, dynamic>;

        // تحويل البيانات إلى StudentProgress
        final lessonProgressMap = <String, LessonProgress>{};
        if (data['lessonProgress'] != null) {
          final lessonData = data['lessonProgress'] as Map<String, dynamic>;
          for (final entry in lessonData.entries) {
            final progressInfo = entry.value as Map<String, dynamic>;
            lessonProgressMap[entry.key] = LessonProgress(
              lessonId: progressInfo['lessonId'] as String,
              score: progressInfo['score'] as int? ?? 0,
              maxScore: progressInfo['maxScore'] as int? ?? 100,
              isCompleted: progressInfo['isCompleted'] as bool? ?? false,
              completedAt:
                  progressInfo['completedAt'] != null
                      ? DateTime.parse(progressInfo['completedAt'] as String)
                      : null,
            );
          }
        }

        _currentProgress = StudentProgress(
          studentId: data['studentId'] as String? ?? 'default',
          lessonProgress: lessonProgressMap,
          totalScore: data['totalScore'] as int? ?? 0,
          totalLessonsCompleted: data['totalLessonsCompleted'] as int? ?? 0,
          lastActivity:
              data['lastActivity'] != null
                  ? DateTime.parse(data['lastActivity'] as String)
                  : DateTime.now(),
        );

        debugPrint('✅ تم تحميل تقدم الطالب بنجاح');
      }

      // إنشاء تقدم افتراضي إذا لم يوجد
      _currentProgress ??= StudentProgress(
        studentId: 'default',
        lessonProgress: {},
        lastActivity: DateTime.now(),
      );
    } catch (e) {
      debugPrint('❌ خطأ في تحميل تقدم الطالب: $e');
    }
  }

  /// حفظ تقدم الطالب
  Future<void> _saveStudentProgress() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      if (_currentProgress != null) {
        // تحويل StudentProgress إلى JSON
        final progressData = {
          'studentId': _currentProgress!.studentId,
          'totalScore': _currentProgress!.totalScore,
          'totalLessonsCompleted': _currentProgress!.totalLessonsCompleted,
          'lastActivity': _currentProgress!.lastActivity.toIso8601String(),
          'lessonProgress': _currentProgress!.lessonProgress.map(
            (key, value) => MapEntry(key, {
              'lessonId': value.lessonId,
              'score': value.score,
              'maxScore': value.maxScore,
              'isCompleted': value.isCompleted,
              'completedAt': value.completedAt?.toIso8601String(),
            }),
          ),
        };

        await prefs.setString(_progressKey, jsonEncode(progressData));
        debugPrint('✅ تم حفظ تقدم الطالب بنجاح');
      }
    } catch (e) {
      debugPrint('❌ خطأ في حفظ تقدم الطالب: $e');
    }
  }

  /// إنشاء الدروس التعليمية
  List<EducationalLesson> _generateEducationalLessons() {
    final lessons = <EducationalLesson>[];

    // دروس الحروف العربية
    lessons.addAll(_generateArabicLettersLessons());

    // دروس الحروف الإنجليزية
    lessons.addAll(_generateEnglishLettersLessons());

    // دروس الأرقام
    lessons.addAll(_generateNumbersLessons());

    // دروس الألوان
    lessons.addAll(_generateColorsLessons());

    // دروس الحيوانات
    lessons.addAll(_generateAnimalsLessons());

    // دروس العائلة
    lessons.addAll(_generateFamilyLessons());

    // دروس أجزاء الجسم
    lessons.addAll(_generateBodyPartsLessons());

    // دروس الطعام
    lessons.addAll(_generateFoodLessons());

    // دروس المواصلات
    lessons.addAll(_generateTransportLessons());

    // دروس الأشكال
    lessons.addAll(_generateShapesLessons());

    // الحوارات التفاعلية
    lessons.addAll(_generateDialogueLessons());

    return lessons;
  }

  /// دروس الحروف العربية
  List<EducationalLesson> _generateArabicLettersLessons() {
    return [
      EducationalLesson(
        id: 'arabic_letters_1',
        title: 'Arabic Letters 1',
        titleAr: 'الحروف العربية ١',
        description: 'Learn the first 5 Arabic letters',
        descriptionAr: 'تعلم أول ٥ حروف عربية',
        category: LessonCategory.letters,
        level: 1,
        order: 1,
        iconPath: 'assets/icons/arabic_letters.png',
        primaryColor: Colors.blue,
        isUnlocked: true, // الدرس الأول مفتوح
        activities: _generateArabicLettersActivities(['أ', 'ب', 'ت', 'ث', 'ج']),
      ),
      // المزيد من دروس الحروف العربية...
    ];
  }

  /// دروس الحروف الإنجليزية
  List<EducationalLesson> _generateEnglishLettersLessons() {
    return [
      EducationalLesson(
        id: 'english_letters_1',
        title: 'English Letters 1',
        titleAr: 'الحروف الإنجليزية ١',
        description: 'Learn the first 5 English letters',
        descriptionAr: 'تعلم أول ٥ حروف إنجليزية',
        category: LessonCategory.letters,
        level: 1,
        order: 2,
        iconPath: 'assets/icons/english_letters.png',
        primaryColor: Colors.green,
        isUnlocked: false,
        activities: _generateEnglishLettersActivities([
          'A',
          'B',
          'C',
          'D',
          'E',
        ]),
      ),
    ];
  }

  /// دروس الأرقام
  List<EducationalLesson> _generateNumbersLessons() {
    return [
      EducationalLesson(
        id: 'numbers_1',
        title: 'Numbers 1-5',
        titleAr: 'الأرقام ١-٥',
        description: 'Learn numbers from 1 to 5',
        descriptionAr: 'تعلم الأرقام من ١ إلى ٥',
        category: LessonCategory.numbers,
        level: 1,
        order: 1,
        iconPath: 'assets/icons/numbers.png',
        primaryColor: Colors.orange,
        isUnlocked: false,
        activities: _generateNumbersActivities([1, 2, 3, 4, 5]),
      ),
    ];
  }

  /// دروس الألوان
  List<EducationalLesson> _generateColorsLessons() {
    return [
      EducationalLesson(
        id: 'colors_1',
        title: 'Basic Colors',
        titleAr: 'الألوان الأساسية',
        description: 'Learn basic colors',
        descriptionAr: 'تعلم الألوان الأساسية',
        category: LessonCategory.colors,
        level: 1,
        order: 1,
        iconPath: 'assets/icons/colors.png',
        primaryColor: Colors.purple,
        isUnlocked: false,
        activities: _generateColorsActivities(),
      ),
    ];
  }

  /// دروس الحيوانات
  List<EducationalLesson> _generateAnimalsLessons() {
    return [
      EducationalLesson(
        id: 'animals_1',
        title: 'Farm Animals',
        titleAr: 'حيوانات المزرعة',
        description: 'Learn about farm animals',
        descriptionAr: 'تعلم عن حيوانات المزرعة',
        category: LessonCategory.animals,
        level: 1,
        order: 1,
        iconPath: 'assets/icons/animals.png',
        primaryColor: Colors.teal,
        isUnlocked: false,
        activities: _generateAnimalsActivities(),
      ),
    ];
  }

  /// إنشاء أنشطة الحروف العربية
  List<LessonActivity> _generateArabicLettersActivities(List<String> letters) {
    return [
      LessonActivity(
        id: 'arabic_letters_recognition',
        title: 'Letter Recognition',
        titleAr: 'التعرف على الحروف',
        type: ActivityType.multipleChoice,
        instructions: 'Choose the correct letter',
        instructionsAr: 'اختر الحرف الصحيح',
        items:
            letters
                .map(
                  (letter) => ActivityItem(
                    id: 'letter_$letter',
                    question: 'Which letter is this?',
                    questionAr: 'ما هذا الحرف؟',
                    correctAnswer: letter,
                    options: [letter, 'خ', 'د', 'ر'],
                    imagePath: 'assets/letters/arabic/$letter.png',
                  ),
                )
                .toList(),
      ),
    ];
  }

  /// إنشاء أنشطة الحروف الإنجليزية
  List<LessonActivity> _generateEnglishLettersActivities(List<String> letters) {
    return [
      LessonActivity(
        id: 'english_letters_recognition',
        title: 'Letter Recognition',
        titleAr: 'التعرف على الحروف',
        type: ActivityType.multipleChoice,
        instructions: 'Choose the correct letter',
        instructionsAr: 'اختر الحرف الصحيح',
        items:
            letters
                .map(
                  (letter) => ActivityItem(
                    id: 'letter_$letter',
                    question: 'Which letter is this?',
                    questionAr: 'ما هذا الحرف؟',
                    correctAnswer: letter,
                    options: [letter, 'F', 'G', 'H'],
                    imagePath: 'assets/letters/english/$letter.png',
                  ),
                )
                .toList(),
      ),
    ];
  }

  /// إنشاء أنشطة الأرقام
  List<LessonActivity> _generateNumbersActivities(List<int> numbers) {
    return [
      LessonActivity(
        id: 'numbers_recognition',
        title: 'Number Recognition',
        titleAr: 'التعرف على الأرقام',
        type: ActivityType.multipleChoice,
        instructions: 'Choose the correct number',
        instructionsAr: 'اختر الرقم الصحيح',
        items:
            numbers
                .map(
                  (number) => ActivityItem(
                    id: 'number_$number',
                    question: 'How many objects do you see?',
                    questionAr: 'كم عدد الأشياء التي تراها؟',
                    correctAnswer: number.toString(),
                    options: [number.toString(), '6', '7', '8'],
                    imagePath: 'assets/numbers/$number.png',
                  ),
                )
                .toList(),
      ),
    ];
  }

  /// إنشاء أنشطة الألوان
  List<LessonActivity> _generateColorsActivities() {
    final colors = ['أحمر', 'أزرق', 'أخضر', 'أصفر'];
    return [
      LessonActivity(
        id: 'colors_recognition',
        title: 'Color Recognition',
        titleAr: 'التعرف على الألوان',
        type: ActivityType.multipleChoice,
        instructions: 'Choose the correct color',
        instructionsAr: 'اختر اللون الصحيح',
        items:
            colors
                .map(
                  (color) => ActivityItem(
                    id: 'color_$color',
                    question: 'What color is this?',
                    questionAr: 'ما هذا اللون؟',
                    correctAnswer: color,
                    options: [color, 'بنفسجي', 'برتقالي', 'وردي'],
                    imagePath: 'assets/colors/$color.png',
                  ),
                )
                .toList(),
      ),
    ];
  }

  /// إنشاء أنشطة الحيوانات
  List<LessonActivity> _generateAnimalsActivities() {
    final animals = ['قطة', 'كلب', 'بقرة', 'حصان'];
    return [
      LessonActivity(
        id: 'animals_recognition',
        title: 'Animal Recognition',
        titleAr: 'التعرف على الحيوانات',
        type: ActivityType.multipleChoice,
        instructions: 'Choose the correct animal',
        instructionsAr: 'اختر الحيوان الصحيح',
        items:
            animals
                .map(
                  (animal) => ActivityItem(
                    id: 'animal_$animal',
                    question: 'What animal is this?',
                    questionAr: 'ما هذا الحيوان؟',
                    correctAnswer: animal,
                    options: [animal, 'فيل', 'أسد', 'نمر'],
                    imagePath: 'assets/animals/$animal.png',
                  ),
                )
                .toList(),
      ),
    ];
  }

  /// دروس العائلة
  List<EducationalLesson> _generateFamilyLessons() {
    return [
      EducationalLesson(
        id: 'family_1',
        title: 'Family Members',
        titleAr: 'أفراد العائلة',
        description: 'Learn about family members',
        descriptionAr: 'تعلم عن أفراد العائلة',
        category: LessonCategory.family,
        level: 1,
        order: 1,
        iconPath: 'assets/icons/family.png',
        primaryColor: Colors.pink,
        isUnlocked: false,
        activities: _generateFamilyActivities(),
      ),
    ];
  }

  /// دروس أجزاء الجسم
  List<EducationalLesson> _generateBodyPartsLessons() {
    return [
      EducationalLesson(
        id: 'body_1',
        title: 'Body Parts',
        titleAr: 'أجزاء الجسم',
        description: 'Learn about body parts',
        descriptionAr: 'تعلم عن أجزاء الجسم',
        category: LessonCategory.body,
        level: 1,
        order: 1,
        iconPath: 'assets/icons/body.png',
        primaryColor: Colors.indigo,
        isUnlocked: false,
        activities: _generateBodyPartsActivities(),
      ),
    ];
  }

  /// دروس الطعام
  List<EducationalLesson> _generateFoodLessons() {
    return [
      EducationalLesson(
        id: 'food_1',
        title: 'Food & Drinks',
        titleAr: 'الطعام والشراب',
        description: 'Learn about food and drinks',
        descriptionAr: 'تعلم عن الطعام والشراب',
        category: LessonCategory.food,
        level: 1,
        order: 1,
        iconPath: 'assets/icons/food.png',
        primaryColor: Colors.amber,
        isUnlocked: false,
        activities: _generateFoodActivities(),
      ),
    ];
  }

  /// دروس المواصلات
  List<EducationalLesson> _generateTransportLessons() {
    return [
      EducationalLesson(
        id: 'transport_1',
        title: 'Transportation',
        titleAr: 'وسائل المواصلات',
        description: 'Learn about transportation',
        descriptionAr: 'تعلم عن وسائل المواصلات',
        category: LessonCategory.transport,
        level: 1,
        order: 1,
        iconPath: 'assets/icons/transport.png',
        primaryColor: Colors.cyan,
        isUnlocked: false,
        activities: _generateTransportActivities(),
      ),
    ];
  }

  /// دروس الأشكال
  List<EducationalLesson> _generateShapesLessons() {
    return [
      EducationalLesson(
        id: 'shapes_1',
        title: 'Basic Shapes',
        titleAr: 'الأشكال الأساسية',
        description: 'Learn about basic shapes',
        descriptionAr: 'تعلم عن الأشكال الأساسية',
        category: LessonCategory.shapes,
        level: 1,
        order: 1,
        iconPath: 'assets/icons/shapes.png',
        primaryColor: Colors.deepPurple,
        isUnlocked: false,
        activities: _generateShapesActivities(),
      ),
    ];
  }

  /// دروس الحوارات
  List<EducationalLesson> _generateDialogueLessons() {
    return [
      EducationalLesson(
        id: 'dialogue_1',
        title: 'Daily Conversations',
        titleAr: 'المحادثات اليومية',
        description: 'Learn daily conversations',
        descriptionAr: 'تعلم المحادثات اليومية',
        category: LessonCategory.dialogue,
        level: 1,
        order: 1,
        iconPath: 'assets/icons/dialogue.png',
        primaryColor: Colors.brown,
        isUnlocked: false,
        activities: _generateDialogueActivities(),
      ),
    ];
  }

  /// أنشطة العائلة
  List<LessonActivity> _generateFamilyActivities() {
    final family = ['أب', 'أم', 'أخ', 'أخت'];
    return [
      LessonActivity(
        id: 'family_recognition',
        title: 'Family Recognition',
        titleAr: 'التعرف على العائلة',
        type: ActivityType.multipleChoice,
        instructions: 'Choose the correct family member',
        instructionsAr: 'اختر فرد العائلة الصحيح',
        items:
            family
                .map(
                  (member) => ActivityItem(
                    id: 'family_$member',
                    question: 'Who is this?',
                    questionAr: 'من هذا؟',
                    correctAnswer: member,
                    options: [member, 'جد', 'جدة', 'عم'],
                    imagePath: 'assets/family/$member.png',
                  ),
                )
                .toList(),
      ),
    ];
  }

  /// أنشطة أجزاء الجسم
  List<LessonActivity> _generateBodyPartsActivities() {
    final bodyParts = ['رأس', 'يد', 'قدم', 'عين'];
    return [
      LessonActivity(
        id: 'body_recognition',
        title: 'Body Parts Recognition',
        titleAr: 'التعرف على أجزاء الجسم',
        type: ActivityType.multipleChoice,
        instructions: 'Choose the correct body part',
        instructionsAr: 'اختر جزء الجسم الصحيح',
        items:
            bodyParts
                .map(
                  (part) => ActivityItem(
                    id: 'body_$part',
                    question: 'What body part is this?',
                    questionAr: 'ما هذا الجزء من الجسم؟',
                    correctAnswer: part,
                    options: [part, 'أنف', 'أذن', 'فم'],
                    imagePath: 'assets/body/$part.png',
                  ),
                )
                .toList(),
      ),
    ];
  }

  /// أنشطة الطعام
  List<LessonActivity> _generateFoodActivities() {
    final foods = ['تفاح', 'موز', 'خبز', 'ماء'];
    return [
      LessonActivity(
        id: 'food_recognition',
        title: 'Food Recognition',
        titleAr: 'التعرف على الطعام',
        type: ActivityType.multipleChoice,
        instructions: 'Choose the correct food',
        instructionsAr: 'اختر الطعام الصحيح',
        items:
            foods
                .map(
                  (food) => ActivityItem(
                    id: 'food_$food',
                    question: 'What food is this?',
                    questionAr: 'ما هذا الطعام؟',
                    correctAnswer: food,
                    options: [food, 'برتقال', 'لحم', 'حليب'],
                    imagePath: 'assets/food/$food.png',
                  ),
                )
                .toList(),
      ),
    ];
  }

  /// أنشطة المواصلات
  List<LessonActivity> _generateTransportActivities() {
    final transport = ['سيارة', 'طائرة', 'قطار', 'دراجة'];
    return [
      LessonActivity(
        id: 'transport_recognition',
        title: 'Transport Recognition',
        titleAr: 'التعرف على المواصلات',
        type: ActivityType.multipleChoice,
        instructions: 'Choose the correct transport',
        instructionsAr: 'اختر وسيلة المواصلات الصحيحة',
        items:
            transport
                .map(
                  (vehicle) => ActivityItem(
                    id: 'transport_$vehicle',
                    question: 'What transport is this?',
                    questionAr: 'ما هذه الوسيلة؟',
                    correctAnswer: vehicle,
                    options: [vehicle, 'باص', 'سفينة', 'مترو'],
                    imagePath: 'assets/transport/$vehicle.png',
                  ),
                )
                .toList(),
      ),
    ];
  }

  /// أنشطة الأشكال
  List<LessonActivity> _generateShapesActivities() {
    final shapes = ['دائرة', 'مربع', 'مثلث', 'مستطيل'];
    return [
      LessonActivity(
        id: 'shapes_recognition',
        title: 'Shapes Recognition',
        titleAr: 'التعرف على الأشكال',
        type: ActivityType.multipleChoice,
        instructions: 'Choose the correct shape',
        instructionsAr: 'اختر الشكل الصحيح',
        items:
            shapes
                .map(
                  (shape) => ActivityItem(
                    id: 'shape_$shape',
                    question: 'What shape is this?',
                    questionAr: 'ما هذا الشكل؟',
                    correctAnswer: shape,
                    options: [shape, 'نجمة', 'قلب', 'معين'],
                    imagePath: 'assets/shapes/$shape.png',
                  ),
                )
                .toList(),
      ),
    ];
  }

  /// أنشطة الحوارات
  List<LessonActivity> _generateDialogueActivities() {
    return [
      LessonActivity(
        id: 'dialogue_greetings',
        title: 'Greetings Dialogue',
        titleAr: 'حوار التحيات',
        type: ActivityType.dialogue,
        instructions: 'Watch and learn the dialogue',
        instructionsAr: 'شاهد وتعلم الحوار',
        items: [
          ActivityItem(
            id: 'greeting_dialogue',
            question: 'Learn greetings',
            questionAr: 'تعلم التحيات',
            correctAnswer: 'مرحبا',
            options: ['مرحبا', 'وداعا', 'شكرا', 'عفوا'],
          ),
        ],
      ),
    ];
  }

  /// الحصول على الحوارات التعليمية
  List<EducationalDialogue> getEducationalDialogues() {
    return [
      _createGreetingDialogue(),
      _createSchoolDialogue(),
      _createHomeDialogue(),
    ];
  }

  /// حوار التحيات
  EducationalDialogue _createGreetingDialogue() {
    return EducationalDialogue(
      id: 'greeting_dialogue',
      title: 'Meeting Friends',
      titleAr: 'لقاء الأصدقاء',
      scenario: 'Two friends meet at school',
      scenarioAr: 'صديقان يلتقيان في المدرسة',
      backgroundImage: 'assets/backgrounds/school_yard.png',
      characters: [
        DialogueCharacter(
          id: 'ahmed',
          name: 'Ahmed',
          nameAr: 'أحمد',
          imagePath: 'assets/characters/boy1.png',
          description: 'A friendly boy',
          descriptionAr: 'ولد ودود',
          primaryColor: Colors.blue,
        ),
        DialogueCharacter(
          id: 'fatima',
          name: 'Fatima',
          nameAr: 'فاطمة',
          imagePath: 'assets/characters/girl1.png',
          description: 'A smart girl',
          descriptionAr: 'فتاة ذكية',
          primaryColor: Colors.pink,
        ),
      ],
      lines: [
        DialogueLine(
          id: 'line1',
          characterId: 'ahmed',
          text: 'Hello Fatima!',
          textAr: 'مرحبا فاطمة!',
          audioPath: 'assets/audio/hello_fatima_en.mp3',
          audioPathAr: 'assets/audio/hello_fatima_ar.mp3',
          duration: const Duration(seconds: 2),
        ),
        DialogueLine(
          id: 'line2',
          characterId: 'fatima',
          text: 'Hello Ahmed! How are you?',
          textAr: 'مرحبا أحمد! كيف حالك؟',
          audioPath: 'assets/audio/hello_ahmed_en.mp3',
          audioPathAr: 'assets/audio/hello_ahmed_ar.mp3',
          duration: const Duration(seconds: 3),
        ),
        DialogueLine(
          id: 'line3',
          characterId: 'ahmed',
          text: 'I am fine, thank you!',
          textAr: 'أنا بخير، شكرا لك!',
          audioPath: 'assets/audio/fine_thanks_en.mp3',
          audioPathAr: 'assets/audio/fine_thanks_ar.mp3',
          duration: const Duration(seconds: 3),
        ),
      ],
    );
  }

  /// حوار المدرسة
  EducationalDialogue _createSchoolDialogue() {
    return EducationalDialogue(
      id: 'school_dialogue',
      title: 'At School',
      titleAr: 'في المدرسة',
      scenario: 'Student talking to teacher',
      scenarioAr: 'طالب يتحدث مع المعلم',
      backgroundImage: 'assets/backgrounds/classroom.png',
      characters: [
        DialogueCharacter(
          id: 'student',
          name: 'Student',
          nameAr: 'الطالب',
          imagePath: 'assets/characters/student.png',
          description: 'A curious student',
          descriptionAr: 'طالب فضولي',
          primaryColor: Colors.green,
        ),
        DialogueCharacter(
          id: 'teacher',
          name: 'Teacher',
          nameAr: 'المعلم',
          imagePath: 'assets/characters/teacher.png',
          description: 'A kind teacher',
          descriptionAr: 'معلم لطيف',
          primaryColor: Colors.orange,
        ),
      ],
      lines: [
        DialogueLine(
          id: 'school_line1',
          characterId: 'student',
          text: 'Good morning, teacher!',
          textAr: 'صباح الخير، أستاذ!',
          audioPath: 'assets/audio/good_morning_teacher_en.mp3',
          audioPathAr: 'assets/audio/good_morning_teacher_ar.mp3',
          duration: const Duration(seconds: 2),
        ),
        DialogueLine(
          id: 'school_line2',
          characterId: 'teacher',
          text: 'Good morning! Ready to learn?',
          textAr: 'صباح الخير! مستعد للتعلم؟',
          audioPath: 'assets/audio/ready_learn_en.mp3',
          audioPathAr: 'assets/audio/ready_learn_ar.mp3',
          duration: const Duration(seconds: 3),
        ),
      ],
    );
  }

  /// حوار البيت
  EducationalDialogue _createHomeDialogue() {
    return EducationalDialogue(
      id: 'home_dialogue',
      title: 'At Home',
      titleAr: 'في البيت',
      scenario: 'Family dinner time',
      scenarioAr: 'وقت عشاء العائلة',
      backgroundImage: 'assets/backgrounds/dining_room.png',
      characters: [
        DialogueCharacter(
          id: 'mother',
          name: 'Mother',
          nameAr: 'الأم',
          imagePath: 'assets/characters/mother.png',
          description: 'A loving mother',
          descriptionAr: 'أم حنونة',
          primaryColor: Colors.purple,
        ),
        DialogueCharacter(
          id: 'child',
          name: 'Child',
          nameAr: 'الطفل',
          imagePath: 'assets/characters/child.png',
          description: 'A happy child',
          descriptionAr: 'طفل سعيد',
          primaryColor: Colors.yellow,
        ),
      ],
      lines: [
        DialogueLine(
          id: 'home_line1',
          characterId: 'mother',
          text: 'Dinner is ready!',
          textAr: 'العشاء جاهز!',
          audioPath: 'assets/audio/dinner_ready_en.mp3',
          audioPathAr: 'assets/audio/dinner_ready_ar.mp3',
          duration: const Duration(seconds: 2),
        ),
        DialogueLine(
          id: 'home_line2',
          characterId: 'child',
          text: 'Thank you, mom!',
          textAr: 'شكرا لك، ماما!',
          audioPath: 'assets/audio/thank_you_mom_en.mp3',
          audioPathAr: 'assets/audio/thank_you_mom_ar.mp3',
          duration: const Duration(seconds: 2),
        ),
      ],
    );
  }

  /// الحصول على ترجمات الكلمات بجميع اللغات المدعومة
  Map<String, String> getWordTranslations(String arabicWord) {
    final wordTranslations = {
      // الألوان
      'أحمر': {
        'ar': 'أحمر',
        'en': 'Red',
        'fr': 'Rouge',
        'de': 'Rot',
        'es': 'Rojo',
        'it': 'Rosso',
        'ja': '赤',
        'zh': '红色',
        'tr': 'Kırmızı',
        'ru': 'Красный',
      },
      'أزرق': {
        'ar': 'أزرق',
        'en': 'Blue',
        'fr': 'Bleu',
        'de': 'Blau',
        'es': 'Azul',
        'it': 'Blu',
        'ja': '青',
        'zh': '蓝色',
        'tr': 'Mavi',
        'ru': 'Синий',
      },
      'أخضر': {
        'ar': 'أخضر',
        'en': 'Green',
        'fr': 'Vert',
        'de': 'Grün',
        'es': 'Verde',
        'it': 'Verde',
        'ja': '緑',
        'zh': '绿色',
        'tr': 'Yeşil',
        'ru': 'Зелёный',
      },
      'أصفر': {
        'ar': 'أصفر',
        'en': 'Yellow',
        'fr': 'Jaune',
        'de': 'Gelb',
        'es': 'Amarillo',
        'it': 'Giallo',
        'ja': '黄色',
        'zh': '黄色',
        'tr': 'Sarı',
        'ru': 'Жёлтый',
      },
      // الحيوانات
      'قطة': {
        'ar': 'قطة',
        'en': 'Cat',
        'fr': 'Chat',
        'de': 'Katze',
        'es': 'Gato',
        'it': 'Gatto',
        'ja': '猫',
        'zh': '猫',
        'tr': 'Kedi',
        'ru': 'Кошка',
      },
      'كلب': {
        'ar': 'كلب',
        'en': 'Dog',
        'fr': 'Chien',
        'de': 'Hund',
        'es': 'Perro',
        'it': 'Cane',
        'ja': '犬',
        'zh': '狗',
        'tr': 'Köpek',
        'ru': 'Собака',
      },
      'بقرة': {
        'ar': 'بقرة',
        'en': 'Cow',
        'fr': 'Vache',
        'de': 'Kuh',
        'es': 'Vaca',
        'it': 'Mucca',
        'ja': '牛',
        'zh': '牛',
        'tr': 'İnek',
        'ru': 'Корова',
      },
      'حصان': {
        'ar': 'حصان',
        'en': 'Horse',
        'fr': 'Cheval',
        'de': 'Pferd',
        'es': 'Caballo',
        'it': 'Cavallo',
        'ja': '馬',
        'zh': '马',
        'tr': 'At',
        'ru': 'Лошадь',
      },
      // العائلة
      'أب': {
        'ar': 'أب',
        'en': 'Father',
        'fr': 'Père',
        'de': 'Vater',
        'es': 'Padre',
        'it': 'Padre',
        'ja': '父',
        'zh': '父亲',
        'tr': 'Baba',
        'ru': 'Отец',
      },
      'أم': {
        'ar': 'أم',
        'en': 'Mother',
        'fr': 'Mère',
        'de': 'Mutter',
        'es': 'Madre',
        'it': 'Madre',
        'ja': '母',
        'zh': '母亲',
        'tr': 'Anne',
        'ru': 'Мать',
      },
      'أخ': {
        'ar': 'أخ',
        'en': 'Brother',
        'fr': 'Frère',
        'de': 'Bruder',
        'es': 'Hermano',
        'it': 'Fratello',
        'ja': '兄弟',
        'zh': '兄弟',
        'tr': 'Kardeş',
        'ru': 'Брат',
      },
      'أخت': {
        'ar': 'أخت',
        'en': 'Sister',
        'fr': 'Sœur',
        'de': 'Schwester',
        'es': 'Hermana',
        'it': 'Sorella',
        'ja': '姉妹',
        'zh': '姐妹',
        'tr': 'Kız kardeş',
        'ru': 'Сестра',
      },
    };

    return wordTranslations[arabicWord] ?? {'ar': arabicWord, 'en': arabicWord};
  }

  /// الحصول على قائمة اللغات المدعومة
  List<Map<String, String>> getSupportedLanguages() {
    return [
      {'code': 'ar', 'name': 'العربية', 'englishName': 'Arabic'},
      {'code': 'en', 'name': 'الإنجليزية', 'englishName': 'English'},
      {'code': 'fr', 'name': 'الفرنسية', 'englishName': 'French'},
      {'code': 'de', 'name': 'الألمانية', 'englishName': 'German'},
      {'code': 'es', 'name': 'الإسبانية', 'englishName': 'Spanish'},
      {'code': 'it', 'name': 'الإيطالية', 'englishName': 'Italian'},
      {'code': 'ja', 'name': 'اليابانية', 'englishName': 'Japanese'},
      {'code': 'zh', 'name': 'الصينية', 'englishName': 'Chinese'},
      {'code': 'tr', 'name': 'التركية', 'englishName': 'Turkish'},
      {'code': 'ru', 'name': 'الروسية', 'englishName': 'Russian'},
    ];
  }
}
