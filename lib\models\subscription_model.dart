import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج بيانات الاشتراك
class SubscriptionModel {
  /// معرف المنتج (الاشتراك)
  final String productId;

  /// اسم الاشتراك
  final String name;

  /// وصف الاشتراك
  final String description;

  /// السعر كنص (مثل $9.99)
  final String price;

  /// القيمة الرقمية للسعر
  final double priceValue;

  /// عملة السعر
  final String currency;

  /// تاريخ بدء الاشتراك
  final DateTime startDate;

  /// تاريخ انتهاء الاشتراك
  final DateTime endDate;

  /// حالة الاشتراك (نشط، منتهي، ملغي)
  final SubscriptionStatus status;

  /// نوع الاشتراك (شهري، سنوي)
  final SubscriptionType type;

  /// هل يتم التجديد تلقائيًا
  final bool autoRenew;

  /// معرف المعاملة
  final String? transactionId;

  /// معرف الإيصال
  final String? receiptData;

  /// تاريخ آخر تحديث
  final DateTime lastUpdated;

  /// عنوان الاشتراك (للعرض في واجهة المستخدم)
  String title = '';

  /// فترة الاشتراك (للعرض في واجهة المستخدم)
  String period = '';

  /// ميزات الاشتراك (للعرض في واجهة المستخدم)
  List<String> features = [];

  SubscriptionModel({
    required this.productId,
    required this.name,
    required this.description,
    required this.price,
    required this.priceValue,
    required this.currency,
    required this.startDate,
    required this.endDate,
    required this.status,
    required this.type,
    required this.autoRenew,
    this.transactionId,
    this.receiptData,
    required this.lastUpdated,
  });

  /// إنشاء نموذج من بيانات Firestore
  factory SubscriptionModel.fromFirestore(Map<String, dynamic> data) {
    return SubscriptionModel(
      productId: data['productId'] ?? '',
      name: data['name'] ?? '',
      description: data['description'] ?? '',
      price: data['price'] ?? '',
      priceValue: (data['priceValue'] ?? 0.0).toDouble(),
      currency: data['currency'] ?? 'USD',
      startDate: (data['startDate'] as Timestamp).toDate(),
      endDate: (data['endDate'] as Timestamp).toDate(),
      status: _parseStatus(data['status'] ?? 'inactive'),
      type: _parseType(data['type'] ?? 'monthly'),
      autoRenew: data['autoRenew'] ?? false,
      transactionId: data['transactionId'],
      receiptData: data['receiptData'],
      lastUpdated: (data['lastUpdated'] as Timestamp).toDate(),
    );
  }

  /// تحويل النموذج إلى بيانات Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'productId': productId,
      'name': name,
      'description': description,
      'price': price,
      'priceValue': priceValue,
      'currency': currency,
      'startDate': Timestamp.fromDate(startDate),
      'endDate': Timestamp.fromDate(endDate),
      'status': _statusToString(status),
      'type': _typeToString(type),
      'autoRenew': autoRenew,
      'transactionId': transactionId,
      'receiptData': receiptData,
      'lastUpdated': Timestamp.fromDate(lastUpdated),
    };
  }

  /// تحويل النص إلى حالة الاشتراك
  static SubscriptionStatus _parseStatus(String status) {
    switch (status) {
      case 'active':
        return SubscriptionStatus.active;
      case 'expired':
        return SubscriptionStatus.expired;
      case 'canceled':
        return SubscriptionStatus.canceled;
      case 'pending':
        return SubscriptionStatus.pending;
      default:
        return SubscriptionStatus.inactive;
    }
  }

  /// تحويل حالة الاشتراك إلى نص
  static String _statusToString(SubscriptionStatus status) {
    switch (status) {
      case SubscriptionStatus.active:
        return 'active';
      case SubscriptionStatus.expired:
        return 'expired';
      case SubscriptionStatus.canceled:
        return 'canceled';
      case SubscriptionStatus.pending:
        return 'pending';
      case SubscriptionStatus.inactive:
        return 'inactive';
    }
  }

  /// تحويل النص إلى نوع الاشتراك
  static SubscriptionType _parseType(String type) {
    switch (type) {
      case 'monthly':
        return SubscriptionType.monthly;
      case 'yearly':
        return SubscriptionType.yearly;
      case 'weekly':
        return SubscriptionType.weekly;
      default:
        return SubscriptionType.monthly;
    }
  }

  /// تحويل نوع الاشتراك إلى نص
  static String _typeToString(SubscriptionType type) {
    switch (type) {
      case SubscriptionType.monthly:
        return 'monthly';
      case SubscriptionType.yearly:
        return 'yearly';
      case SubscriptionType.weekly:
        return 'weekly';
    }
  }

  /// التحقق مما إذا كان الاشتراك نشطًا
  bool isActive() {
    return status == SubscriptionStatus.active &&
        DateTime.now().isBefore(endDate);
  }

  /// التحقق مما إذا كان الاشتراك منتهيًا
  bool isExpired() {
    return DateTime.now().isAfter(endDate);
  }

  /// نسخة محدثة من النموذج
  SubscriptionModel copyWith({
    String? productId,
    String? name,
    String? description,
    String? price,
    double? priceValue,
    String? currency,
    DateTime? startDate,
    DateTime? endDate,
    SubscriptionStatus? status,
    SubscriptionType? type,
    bool? autoRenew,
    String? transactionId,
    String? receiptData,
    DateTime? lastUpdated,
  }) {
    return SubscriptionModel(
      productId: productId ?? this.productId,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      priceValue: priceValue ?? this.priceValue,
      currency: currency ?? this.currency,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      status: status ?? this.status,
      type: type ?? this.type,
      autoRenew: autoRenew ?? this.autoRenew,
      transactionId: transactionId ?? this.transactionId,
      receiptData: receiptData ?? this.receiptData,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}

/// حالات الاشتراك
enum SubscriptionStatus {
  active, // نشط
  expired, // منتهي
  canceled, // ملغي
  pending, // قيد المعالجة
  inactive, // غير نشط
}

/// أنواع الاشتراك
enum SubscriptionType {
  monthly, // شهري
  yearly, // سنوي
  weekly, // أسبوعي
}
