import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../models/tourism_phrase.dart';
import '../../../data/tourism_phrases_data.dart';
import '../../../config/constants.dart';

/// خدمة العبارات السياحية
class TourismPhrasesService {
  final FlutterTts _flutterTts = FlutterTts();
  bool _isInitialized = false;

  // قائمة العبارات
  final List<TourismPhrase> _phrases = [];

  // قائمة العبارات المفضلة
  final List<TourismPhrase> _favorites = [];

  // تدفقات البيانات
  final StreamController<List<TourismPhrase>> _phrasesController =
      StreamController<List<TourismPhrase>>.broadcast();
  final StreamController<List<TourismPhrase>> _favoritesController =
      StreamController<List<TourismPhrase>>.broadcast();

  // الوصول إلى التدفقات
  Stream<List<TourismPhrase>> get phrasesStream => _phrasesController.stream;
  Stream<List<TourismPhrase>> get favoritesStream =>
      _favoritesController.stream;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // تهيئة خدمة تحويل النص إلى كلام
      await _flutterTts.setLanguage('ar');
      await _flutterTts.setSpeechRate(0.5);
      await _flutterTts.setVolume(1.0);
      await _flutterTts.setPitch(1.0);

      // تحميل العبارات
      await _loadPhrases();

      // تحميل المفضلة
      await _loadFavorites();

      _isInitialized = true;
    } catch (e) {
      debugPrint('Error initializing tourism phrases service: $e');
      rethrow;
    }
  }

  /// تحميل العبارات
  Future<void> _loadPhrases() async {
    _phrases.clear();

    // تحويل البيانات إلى نماذج TourismPhrase
    tourismPhrasesData.forEach((category, phrases) {
      for (final phrase in phrases) {
        _phrases.add(
          TourismPhrase(
            category: category,
            subcategory: '',
            translations: Map<String, String>.from(phrase),
          ),
        );
      }
    });

    // إرسال العبارات إلى التدفق
    _phrasesController.add(_phrases);
  }

  /// تحميل المفضلة
  Future<void> _loadFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson =
          prefs.getStringList(AppConstants.keyTourismFavorites) ?? [];

      _favorites.clear();

      for (final json in favoritesJson) {
        final map = jsonDecode(json) as Map<String, dynamic>;
        _favorites.add(TourismPhrase.fromMap(map));
      }

      // تحديث حالة المفضلة في قائمة العبارات الرئيسية
      for (final favorite in _favorites) {
        final index = _phrases.indexWhere((p) => p.id == favorite.id);
        if (index != -1) {
          _phrases[index] = _phrases[index].copyWith(
            isFavorite: true,
            addedToFavoritesAt: favorite.addedToFavoritesAt,
          );
        }
      }

      // إرسال المفضلة إلى التدفق
      _favoritesController.add(_favorites);

      // تحديث قائمة العبارات
      _phrasesController.add(_phrases);
    } catch (e) {
      debugPrint('Error loading favorites: $e');
    }
  }

  /// الحصول على العبارات حسب الفئة
  List<TourismPhrase> getPhrasesByCategory(String category) {
    return _phrases.where((p) => p.category == category).toList();
  }

  /// الحصول على جميع العبارات
  List<TourismPhrase> getAllPhrases() {
    return List.from(_phrases);
  }

  /// الحصول على العبارات المفضلة
  List<TourismPhrase> getFavorites() {
    return List.from(_favorites);
  }

  /// البحث في العبارات
  List<TourismPhrase> searchPhrases(String query, {String? category}) {
    if (query.isEmpty) {
      return category != null
          ? getPhrasesByCategory(category)
          : getAllPhrases();
    }

    final normalizedQuery = query.toLowerCase();

    return _phrases.where((phrase) {
      // التحقق من الفئة إذا تم تحديدها
      if (category != null && phrase.category != category) {
        return false;
      }

      // البحث في جميع الترجمات
      for (final translation in phrase.translations.values) {
        if (translation.toLowerCase().contains(normalizedQuery)) {
          return true;
        }
      }

      return false;
    }).toList();
  }

  /// إضافة عبارة إلى المفضلة
  Future<bool> addToFavorites(TourismPhrase phrase) async {
    try {
      // التحقق مما إذا كانت العبارة موجودة بالفعل في المفضلة
      if (_favorites.any((p) => p.id == phrase.id)) {
        return true;
      }

      // إضافة العبارة إلى المفضلة
      final updatedPhrase = phrase.copyWith(
        isFavorite: true,
        addedToFavoritesAt: DateTime.now(),
      );

      _favorites.add(updatedPhrase);

      // تحديث العبارة في القائمة الرئيسية
      final index = _phrases.indexWhere((p) => p.id == phrase.id);
      if (index != -1) {
        _phrases[index] = updatedPhrase;
      }

      // حفظ المفضلة
      await _saveFavorites();

      // تحديث التدفقات
      _favoritesController.add(_favorites);
      _phrasesController.add(_phrases);

      return true;
    } catch (e) {
      debugPrint('Error adding to favorites: $e');
      return false;
    }
  }

  /// إزالة عبارة من المفضلة
  Future<bool> removeFromFavorites(String id) async {
    try {
      // إزالة العبارة من المفضلة
      _favorites.removeWhere((p) => p.id == id);

      // تحديث العبارة في القائمة الرئيسية
      final index = _phrases.indexWhere((p) => p.id == id);
      if (index != -1) {
        _phrases[index] = _phrases[index].copyWith(
          isFavorite: false,
          addedToFavoritesAt: null,
        );
      }

      // حفظ المفضلة
      await _saveFavorites();

      // تحديث التدفقات
      _favoritesController.add(_favorites);
      _phrasesController.add(_phrases);

      return true;
    } catch (e) {
      debugPrint('Error removing from favorites: $e');
      return false;
    }
  }

  /// حفظ المفضلة
  Future<void> _saveFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson =
          _favorites.map((p) => jsonEncode(p.toJson())).toList();

      await prefs.setStringList(
        AppConstants.keyTourismFavorites,
        favoritesJson,
      );
    } catch (e) {
      debugPrint('Error saving favorites: $e');
    }
  }

  /// نطق النص
  Future<void> speakText({
    required String text,
    required String languageCode,
    required Function() onDone,
  }) async {
    try {
      // استخدام معرف اللغة المناسب
      final localeId = _getLocaleId(languageCode);
      await _flutterTts.setLanguage(localeId);

      // تعديل إعدادات النطق حسب اللغة
      if (languageCode == 'ar') {
        await _flutterTts.setSpeechRate(0.4); // أبطأ للغة العربية
        await _flutterTts.setPitch(1.1); // أعلى قليلاً للغة العربية
      } else {
        await _flutterTts.setSpeechRate(0.5);
        await _flutterTts.setPitch(1.0);
      }

      // تعيين معالج الانتهاء
      _flutterTts.setCompletionHandler(onDone);

      // تعيين معالج الخطأ
      _flutterTts.setErrorHandler((error) {
        debugPrint('TTS error: $error');
      });

      debugPrint('Speaking text in language: $localeId');
      final result = await _flutterTts.speak(text);

      // إذا فشل النطق، نعيد المحاولة مرة واحدة
      if (result == 0) {
        debugPrint('First attempt to speak failed, trying again...');
        await Future.delayed(const Duration(milliseconds: 500));

        // محاولة استخدام لغة بديلة إذا كانت اللغة العربية
        if (languageCode == 'ar') {
          try {
            await _flutterTts.setLanguage('ar-EG');
            await _flutterTts.speak(text);
          } catch (e) {
            debugPrint('Error trying alternative language: $e');
            await _flutterTts.setLanguage(localeId);
            await _flutterTts.speak(text);
          }
        } else {
          await _flutterTts.speak(text);
        }
      }
    } catch (e) {
      debugPrint('Error speaking text: $e');
      rethrow;
    }
  }

  /// إيقاف النطق
  Future<void> stopSpeaking() async {
    try {
      await _flutterTts.stop();
    } catch (e) {
      debugPrint('Error stopping speaking: $e');
      rethrow;
    }
  }

  /// الحصول على معرف اللغة
  String _getLocaleId(String languageCode) {
    switch (languageCode) {
      case 'ar':
        return 'ar-SA';
      case 'en':
        return 'en-US';
      case 'fr':
        return 'fr-FR';
      case 'es':
        return 'es-ES';
      case 'de':
        return 'de-DE';
      case 'it':
        return 'it-IT';
      case 'ru':
        return 'ru-RU';
      case 'zh':
        return 'zh-CN';
      case 'ja':
        return 'ja-JP';
      case 'ko':
        return 'ko-KR';
      default:
        return languageCode;
    }
  }

  /// التخلص من الموارد
  void dispose() {
    _flutterTts.stop();
    _phrasesController.close();
    _favoritesController.close();
  }
}
