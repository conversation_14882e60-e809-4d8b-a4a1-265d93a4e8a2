@echo off
echo ========================================
echo    Remote Config Update Script
echo    AI Smart Translator New Project
echo    Project ID: ar-project-4063d
echo ========================================

echo.
echo [1/3] Getting current Remote Config...
call firebase remoteconfig:get > current-config.json
if %errorlevel% neq 0 (
    echo Warning: Could not get current config!
)

echo.
echo [2/3] Updating Remote Config...
call firebase remoteconfig:set remoteconfig.template.json
if %errorlevel% neq 0 (
    echo Error: Remote Config update failed!
    pause
    exit /b 1
)

echo.
echo [3/3] Publishing Remote Config...
call firebase remoteconfig:publish
if %errorlevel% neq 0 (
    echo Error: Remote Config publish failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo    Remote Config Updated Successfully!
echo ========================================
echo.
echo The new configuration is now live.
echo Apps will receive the update on next fetch.
echo.
pause
