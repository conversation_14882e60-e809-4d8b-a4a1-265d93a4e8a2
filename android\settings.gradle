import java.util.Properties

pluginManagement {
    def flutterSdkPath = null
    def localPropertiesFile = new File(rootProject.projectDir, "local.properties")
    if (localPropertiesFile.exists()) {
        Properties properties = new Properties()
        localPropertiesFile.withInputStream { properties.load(it) }
        flutterSdkPath = properties.getProperty('flutter.sdk')
    }

    if (flutterSdkPath == null) {
        throw new Exception("flutter.sdk not set in local.properties")
    }

    includeBuild("${flutterSdkPath}/packages/flutter_tools/gradle")

    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
        maven { url 'https://plugins.gradle.org/m2/' }
    }
}

plugins {
    id 'dev.flutter.flutter-plugin-loader' version '1.0.0'
}

include ':app'
