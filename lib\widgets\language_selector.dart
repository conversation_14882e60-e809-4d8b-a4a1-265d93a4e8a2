import 'package:flutter/material.dart';
import '../config/constants.dart';

/// محدد اللغة
class LanguageSelector extends StatelessWidget {
  /// عنوان المحدد
  final String label;

  /// اللغة المحددة
  final String selectedLanguage;

  /// دالة يتم تنفيذها عند تغيير اللغة
  final Function(String) onChanged;

  /// حجم المحدد
  final double? width;

  /// ارتفاع المحدد
  final double? height;

  /// منشئ محدد اللغة
  const LanguageSelector({
    super.key,
    required this.label,
    required this.selectedLanguage,
    required this.onChanged,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان المحدد
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color:
                Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey.shade300
                    : Colors.grey.shade700,
          ),
        ),
        const SizedBox(height: 8),

        // محدد اللغة
        Container(
          width: width,
          height: height ?? 56,
          decoration: BoxDecoration(
            color:
                Theme.of(context).brightness == Brightness.dark
                    ? const Color(0xFF252A40)
                    : Colors.grey.shade50,
            borderRadius: BorderRadius.circular(12),
            boxShadow:
                Theme.of(context).brightness == Brightness.dark
                    ? []
                    : [
                      BoxShadow(
                        color: Colors.black.withAlpha(10),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: selectedLanguage,
              icon: const Icon(Icons.arrow_drop_down),
              iconSize: 24,
              elevation: 16,
              isExpanded: true,
              style: TextStyle(
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.white
                        : Colors.black,
                fontSize: 16,
              ),
              dropdownColor:
                  Theme.of(context).brightness == Brightness.dark
                      ? const Color(0xFF252A40)
                      : Colors.white,
              onChanged: (String? newValue) {
                if (newValue != null) {
                  onChanged(newValue);
                }
              },
              items: _buildLanguageItems(),
            ),
          ),
        ),
      ],
    );
  }

  /// بناء عناصر اللغات
  List<DropdownMenuItem<String>> _buildLanguageItems() {
    return AppConstants.supportedLanguages.map<DropdownMenuItem<String>>((
      language,
    ) {
      return DropdownMenuItem<String>(
        value: language['code'],
        child: Row(
          children: [
            // علم اللغة
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                image: DecorationImage(
                  image: AssetImage(
                    'assets/images/flags/${language['code']}.png',
                  ),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            const SizedBox(width: 12),

            // اسم اللغة
            Text(language['name']),
          ],
        ),
      );
    }).toList();
  }
}
