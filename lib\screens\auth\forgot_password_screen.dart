import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../config/app_theme.dart';
import '../../utils/app_helpers.dart';
import '../../widgets/custom_button.dart';

/// شاشة نسيت كلمة المرور
class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  // متحكم حقل البريد الإلكتروني
  final _emailController = TextEditingController();

  // مفتاح النموذج للتحقق من صحة الإدخال
  final _formKey = GlobalKey<FormState>();

  // حالة تحميل إرسال البريد
  bool _isLoading = false;

  // مثيل Firebase Auth
  final _auth = FirebaseAuth.instance;

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('نسيت كلمة المرور')),
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // الأيقونة
                  Icon(
                    Icons.lock_reset,
                    size: 80,
                    color: AppTheme.primaryColor,
                  ),
                  const SizedBox(height: 24),

                  // العنوان
                  const Text(
                    'إعادة تعيين كلمة المرور',
                    style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),

                  // النص التوضيحي
                  const Text(
                    'أدخل بريدك الإلكتروني وسنرسل لك رابطًا لإعادة تعيين كلمة المرور.',
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 32),

                  // حقل البريد الإلكتروني
                  TextFormField(
                    controller: _emailController,
                    keyboardType: TextInputType.emailAddress,
                    decoration: const InputDecoration(
                      labelText: 'البريد الإلكتروني',
                      prefixIcon: Icon(Icons.email),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال البريد الإلكتروني';
                      }
                      if (!AppHelpers.isValidEmail(value)) {
                        return 'يرجى إدخال بريد إلكتروني صحيح';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 32),

                  // زر إرسال
                  CustomButton(
                    text: 'إرسال رابط إعادة التعيين',
                    onPressed: _isLoading ? null : _resetPassword,
                  ),
                  const SizedBox(height: 16),

                  // زر العودة
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: const Text('العودة إلى تسجيل الدخول'),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// إرسال رابط إعادة تعيين كلمة المرور
  Future<void> _resetPassword() async {
    // التحقق من صحة النموذج
    if (_formKey.currentState == null || !_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // إرسال رابط إعادة تعيين كلمة المرور
      await _auth.sendPasswordResetEmail(email: _emailController.text.trim());

      // عرض رسالة نجاح
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني.',
        );

        // العودة إلى شاشة تسجيل الدخول
        Navigator.pop(context);
      }
    } on FirebaseAuthException catch (e) {
      // معالجة أخطاء إرسال البريد
      String errorMessage;

      switch (e.code) {
        case 'user-not-found':
          errorMessage = 'لم يتم العثور على مستخدم بهذا البريد الإلكتروني';
          break;
        case 'invalid-email':
          errorMessage = 'البريد الإلكتروني غير صالح';
          break;
        default:
          errorMessage = 'حدث خطأ أثناء إرسال رابط إعادة التعيين: ${e.message}';
      }

      // عرض رسالة الخطأ
      if (mounted) {
        AppHelpers.showSnackBar(context, errorMessage, isError: true);
      }
    } catch (e) {
      // معالجة الأخطاء الأخرى
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء إرسال رابط إعادة التعيين: $e',
          isError: true,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
