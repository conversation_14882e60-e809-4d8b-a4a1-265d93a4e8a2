import 'package:flutter/material.dart';
import '../../widgets/custom_app_bar.dart';

/// شاشة سياسة الخصوصية
class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'سياسة الخصوصية',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'سياسة الخصوصية',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'آخر تحديث: ${DateTime.now().year}/${DateTime.now().month}/${DateTime.now().day}',
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 24),
            _buildSection(
              title: 'مقدمة',
              content:
                  'نحن نقدر خصوصيتك ونلتزم بحماية بياناتك الشخصية. توضح سياسة الخصوصية هذه كيفية جمع واستخدام ومشاركة بياناتك عند استخدام تطبيق المترجم الذكي ("التطبيق").',
            ),
            _buildSection(
              title: 'البيانات التي نجمعها',
              content:
                  'نحن نجمع أنواعًا مختلفة من المعلومات منك، بما في ذلك:\n\n'
                  '• معلومات الحساب: عند إنشاء حساب، قد نجمع اسمك وبريدك الإلكتروني ورقم هاتفك وكلمة المرور.\n\n'
                  '• معلومات الاستخدام: نجمع معلومات حول كيفية استخدامك للتطبيق، بما في ذلك النصوص التي تترجمها والصور والمستندات التي تقوم بتحميلها للترجمة.\n\n'
                  '• المحتوى الصوتي: عند استخدام ميزات الترجمة الصوتية، نقوم بجمع ومعالجة التسجيلات الصوتية الخاصة بك.\n\n'
                  '• معلومات الجهاز: نجمع معلومات حول جهازك، بما في ذلك نوع الجهاز ونظام التشغيل وإصدار التطبيق ومعرفات الجهاز الفريدة.\n\n'
                  '• معلومات الدفع: عند شراء اشتراك، نجمع معلومات الدفع الخاصة بك من خلال متجر التطبيقات (Google Play أو App Store).',
            ),
            _buildSection(
              title: 'كيف نستخدم بياناتك',
              content:
                  'نستخدم البيانات التي نجمعها للأغراض التالية:\n\n'
                  '• توفير وتحسين خدماتنا، بما في ذلك ميزات الترجمة والتعرف على الكلام.\n\n'
                  '• إدارة حسابك واشتراكك.\n\n'
                  '• التواصل معك بشأن التطبيق والميزات الجديدة والتحديثات.\n\n'
                  '• تخصيص تجربتك وتقديم محتوى مخصص.\n\n'
                  '• تحليل استخدام التطبيق وتحسين أدائه.\n\n'
                  '• الكشف عن الاحتيال ومنعه وحماية أمان التطبيق.',
            ),
            _buildSection(
              title: 'مشاركة البيانات',
              content:
                  'قد نشارك بياناتك مع الأطراف التالية:\n\n'
                  '• مزودي الخدمات: نستخدم أطرافًا ثالثة لتقديم خدمات مثل الترجمة والتعرف على الكلام وتمييز المتحدثين. تشمل هذه الخدمات Microsoft Azure Speech Service وGoogle Cloud Speech-to-Text وDeepL API.\n\n'
                  '• شركاء الدفع: نستخدم خدمات الدفع التي يوفرها Google Play وApp Store لمعالجة المدفوعات.\n\n'
                  '• متطلبات قانونية: قد نكشف عن بياناتك إذا كان ذلك مطلوبًا بموجب القانون أو في إطار إجراءات قانونية.',
            ),
            _buildSection(
              title: 'الاشتراكات والمدفوعات',
              content:
                  '• يقدم التطبيق اشتراكات مدفوعة تتيح الوصول إلى ميزات متقدمة.\n\n'
                  '• تتم معالجة المدفوعات من خلال Google Play أو App Store، وتخضع لشروط وسياسات الخصوصية الخاصة بهم.\n\n'
                  '• سيتم تجديد الاشتراكات تلقائيًا ما لم يتم إلغاؤها قبل 24 ساعة على الأقل من نهاية الفترة الحالية.\n\n'
                  '• يمكن إدارة الاشتراكات وإلغاؤها من خلال إعدادات الحساب في Google Play أو App Store.\n\n'
                  '• لا يتم تقديم استرداد للفترات الجزئية غير المستخدمة من الاشتراك.',
            ),
            _buildSection(
              title: 'حقوقك',
              content:
                  'اعتمادًا على موقعك، قد يكون لديك حقوق معينة فيما يتعلق ببياناتك الشخصية، بما في ذلك:\n\n'
                  '• الوصول إلى بياناتك الشخصية.\n\n'
                  '• تصحيح بياناتك غير الدقيقة.\n\n'
                  '• حذف بياناتك.\n\n'
                  '• تقييد معالجة بياناتك.\n\n'
                  '• نقل بياناتك.\n\n'
                  '• الاعتراض على معالجة بياناتك.\n\n'
                  'لممارسة هذه الحقوق، يرجى التواصل معنا عبر البريد الإلكتروني المذكور أدناه.',
            ),
            _buildSection(
              title: 'أمان البيانات',
              content:
                  'نحن نتخذ تدابير أمنية معقولة لحماية بياناتك الشخصية من الفقدان أو الوصول غير المصرح به أو الإفصاح أو التغيير. ومع ذلك، لا يمكن ضمان أمان البيانات المرسلة عبر الإنترنت بنسبة 100%.',
            ),
            _buildSection(
              title: 'الأطفال',
              content:
                  'لا نجمع عن قصد معلومات شخصية من الأطفال دون سن 13 عامًا. إذا كنت والدًا أو وصيًا وتعتقد أن طفلك قد قدم لنا معلومات شخصية، يرجى الاتصال بنا.',
            ),
            _buildSection(
              title: 'التغييرات على سياسة الخصوصية',
              content:
                  'قد نقوم بتحديث سياسة الخصوصية هذه من وقت لآخر. سنخطرك بأي تغييرات جوهرية من خلال نشر السياسة الجديدة على هذه الصفحة وتحديث تاريخ "آخر تحديث".',
            ),
            _buildSection(
              title: 'اتصل بنا',
              content:
                  'إذا كانت لديك أي أسئلة حول سياسة الخصوصية هذه، يرجى التواصل معنا عبر البريد الإلكتروني: <EMAIL>',
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم
  Widget _buildSection({
    required String title,
    required String content,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: const TextStyle(
              fontSize: 16,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }
}
