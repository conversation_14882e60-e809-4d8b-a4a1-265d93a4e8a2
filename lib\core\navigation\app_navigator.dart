import 'package:flutter/material.dart';
import '../animations/app_animations.dart';

/// نظام التنقل المحسن مع الرسوم المتحركة
class AppNavigator {
  /// التنقل مع رسوم متحركة للانزلاق
  static Future<T?> pushSlide<T>(
    BuildContext context,
    Widget page, {
    SlideDirection direction = SlideDirection.right,
  }) {
    return Navigator.push<T>(
      context,
      AppAnimations.createSlideRoute<T>(page, direction: direction),
    );
  }

  /// التنقل مع رسوم متحركة للتلاشي
  static Future<T?> pushFade<T>(BuildContext context, Widget page) {
    return Navigator.push<T>(
      context,
      AppAnimations.createFadeRoute<T>(page),
    );
  }

  /// التنقل مع رسوم متحركة للتكبير
  static Future<T?> pushScale<T>(BuildContext context, Widget page) {
    return Navigator.push<T>(
      context,
      AppAnimations.createScaleRoute<T>(page),
    );
  }

  /// التنقل مع رسوم متحركة للبطاقة
  static Future<T?> pushCard<T>(BuildContext context, Widget page) {
    return Navigator.push<T>(
      context,
      AppAnimations.createCardRoute<T>(page),
    );
  }

  /// التنقل مع رسوم متحركة للدوران
  static Future<T?> pushRotation<T>(BuildContext context, Widget page) {
    return Navigator.push<T>(
      context,
      AppAnimations.createRotationRoute<T>(page),
    );
  }

  /// الاستبدال مع رسوم متحركة للانزلاق
  static Future<T?> pushReplacementSlide<T, TO>(
    BuildContext context,
    Widget page, {
    SlideDirection direction = SlideDirection.right,
    TO? result,
  }) {
    return Navigator.pushReplacement<T, TO>(
      context,
      AppAnimations.createSlideRoute<T>(page, direction: direction),
      result: result,
    );
  }

  /// الاستبدال مع رسوم متحركة للتلاشي
  static Future<T?> pushReplacementFade<T, TO>(
    BuildContext context,
    Widget page, {
    TO? result,
  }) {
    return Navigator.pushReplacement<T, TO>(
      context,
      AppAnimations.createFadeRoute<T>(page),
      result: result,
    );
  }

  /// إزالة جميع الصفحات والانتقال لصفحة جديدة
  static Future<T?> pushAndRemoveUntil<T>(
    BuildContext context,
    Widget page, {
    bool Function(Route<dynamic>)? predicate,
    RouteTransitionsBuilder? transitionsBuilder,
  }) {
    return Navigator.pushAndRemoveUntil<T>(
      context,
      transitionsBuilder != null
          ? PageRouteBuilder<T>(
              pageBuilder: (context, animation, secondaryAnimation) => page,
              transitionsBuilder: transitionsBuilder,
            )
          : AppAnimations.createFadeRoute<T>(page),
      predicate ?? (route) => false,
    );
  }

  /// العودة مع نتيجة
  static void pop<T>(BuildContext context, [T? result]) {
    Navigator.pop<T>(context, result);
  }

  /// العودة حتى صفحة معينة
  static void popUntil(BuildContext context, bool Function(Route<dynamic>) predicate) {
    Navigator.popUntil(context, predicate);
  }

  /// العودة للصفحة الرئيسية
  static void popToRoot(BuildContext context) {
    Navigator.popUntil(context, (route) => route.isFirst);
  }

  /// التحقق من إمكانية العودة
  static bool canPop(BuildContext context) {
    return Navigator.canPop(context);
  }

  /// عرض نافذة منبثقة مع رسوم متحركة
  static Future<T?> showAnimatedDialog<T>({
    required BuildContext context,
    required Widget child,
    bool barrierDismissible = true,
    Color? barrierColor,
    String? barrierLabel,
    Duration transitionDuration = const Duration(milliseconds: 300),
  }) {
    return showGeneralDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      barrierColor: barrierColor ?? Colors.black54,
      barrierLabel: barrierLabel,
      transitionDuration: transitionDuration,
      pageBuilder: (context, animation, secondaryAnimation) => child,
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return ScaleTransition(
          scale: CurvedAnimation(
            parent: animation,
            curve: Curves.easeOutBack,
          ),
          child: FadeTransition(
            opacity: animation,
            child: child,
          ),
        );
      },
    );
  }

  /// عرض نافذة منبثقة من الأسفل
  static Future<T?> showBottomSheetModal<T>({
    required BuildContext context,
    required Widget child,
    bool isScrollControlled = true,
    bool isDismissible = true,
    bool enableDrag = true,
    Color? backgroundColor,
    double? elevation,
    ShapeBorder? shape,
  }) {
    return showModalBottomSheet<T>(
      context: context,
      isScrollControlled: isScrollControlled,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      backgroundColor: backgroundColor,
      elevation: elevation,
      shape: shape ??
          const RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(20),
            ),
          ),
      builder: (context) => child,
    );
  }

  /// عرض Snackbar مخصص
  static void showSnackBar(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 3),
    SnackBarAction? action,
    Color? backgroundColor,
    Color? textColor,
    IconData? icon,
    bool isError = false,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            if (icon != null) ...[
              Icon(
                icon,
                color: textColor ?? colorScheme.onSurface,
                size: 20,
              ),
              const SizedBox(width: 12),
            ],
            Expanded(
              child: Text(
                message,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: textColor ?? colorScheme.onSurface,
                ),
              ),
            ),
          ],
        ),
        duration: duration,
        action: action,
        backgroundColor: backgroundColor ??
            (isError ? colorScheme.error : colorScheme.surface),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  /// عرض رسالة نجاح
  static void showSuccessSnackBar(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 3),
  }) {
    showSnackBar(
      context,
      message,
      duration: duration,
      icon: Icons.check_circle_outline,
      backgroundColor: Colors.green,
      textColor: Colors.white,
    );
  }

  /// عرض رسالة خطأ
  static void showErrorSnackBar(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 4),
  }) {
    showSnackBar(
      context,
      message,
      duration: duration,
      icon: Icons.error_outline,
      isError: true,
    );
  }

  /// عرض رسالة تحذير
  static void showWarningSnackBar(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 3),
  }) {
    showSnackBar(
      context,
      message,
      duration: duration,
      icon: Icons.warning_outlined,
      backgroundColor: Colors.orange,
      textColor: Colors.white,
    );
  }

  /// عرض رسالة معلومات
  static void showInfoSnackBar(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 3),
  }) {
    showSnackBar(
      context,
      message,
      duration: duration,
      icon: Icons.info_outline,
      backgroundColor: Colors.blue,
      textColor: Colors.white,
    );
  }
}
