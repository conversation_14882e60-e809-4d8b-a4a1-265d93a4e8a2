import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/educational_content.dart';
import '../../services/educational_service.dart';

/// شاشة الحوارات التفاعلية المصورة والناطقة
class DialogueScreen extends StatefulWidget {
  final EducationalDialogue dialogue;
  final EducationalService educationalService;

  const DialogueScreen({
    super.key,
    required this.dialogue,
    required this.educationalService,
  });

  @override
  State<DialogueScreen> createState() => _DialogueScreenState();
}

class _DialogueScreenState extends State<DialogueScreen>
    with TickerProviderStateMixin {
  late AnimationController _characterAnimationController;
  late AnimationController _textAnimationController;
  late Animation<double> _characterAnimation;
  late Animation<double> _textAnimation;

  int _currentLineIndex = 0;
  bool _isPlaying = false;
  bool _isArabic = true;
  bool _autoPlay = true;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startDialogue();
  }

  @override
  void dispose() {
    _characterAnimationController.dispose();
    _textAnimationController.dispose();
    super.dispose();
  }

  /// تهيئة الرسوم المتحركة
  void _initializeAnimations() {
    _characterAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _textAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _characterAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _characterAnimationController,
      curve: Curves.elasticOut,
    ));

    _textAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _textAnimationController,
      curve: Curves.easeInOut,
    ));
  }

  /// بدء الحوار
  void _startDialogue() {
    _playCurrentLine();
  }

  /// تشغيل السطر الحالي
  Future<void> _playCurrentLine() async {
    if (_currentLineIndex >= widget.dialogue.lines.length) return;

    setState(() {
      _isPlaying = true;
    });

    final currentLine = widget.dialogue.lines[_currentLineIndex];

    // تحريك الشخصية
    _characterAnimationController.forward();

    // تحريك النص
    await Future.delayed(const Duration(milliseconds: 200));
    _textAnimationController.forward();

    // تشغيل الصوت
    try {
      final audioPath = _isArabic ? currentLine.audioPathAr : currentLine.audioPath;
      final text = _isArabic ? currentLine.textAr : currentLine.text;
      
      if (_isArabic) {
        await widget.educationalService.speakArabic(text);
      } else {
        await widget.educationalService.speakEnglish(text);
      }
    } catch (e) {
      debugPrint('❌ خطأ في تشغيل الصوت: $e');
    }

    setState(() {
      _isPlaying = false;
    });

    // الانتقال التلقائي للسطر التالي
    if (_autoPlay) {
      await Future.delayed(const Duration(milliseconds: 1000));
      _nextLine();
    }
  }

  /// الانتقال للسطر التالي
  void _nextLine() {
    if (_currentLineIndex < widget.dialogue.lines.length - 1) {
      setState(() {
        _currentLineIndex++;
      });
      
      _characterAnimationController.reset();
      _textAnimationController.reset();
      
      _playCurrentLine();
    } else {
      _showCompletionDialog();
    }
  }

  /// الانتقال للسطر السابق
  void _previousLine() {
    if (_currentLineIndex > 0) {
      setState(() {
        _currentLineIndex--;
      });
      
      _characterAnimationController.reset();
      _textAnimationController.reset();
      
      _playCurrentLine();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(widget.dialogue.backgroundImage),
            fit: BoxFit.cover,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              Expanded(
                child: _buildDialogueContent(),
              ),
              _buildControls(),
            ],
          ),
        ),
      ),
    );
  }

  /// رأس الشاشة
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.3),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.arrow_back, color: Colors.white),
          ),
          Expanded(
            child: Column(
              children: [
                Text(
                  _isArabic ? widget.dialogue.titleAr : widget.dialogue.title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  _isArabic ? widget.dialogue.scenarioAr : widget.dialogue.scenario,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              setState(() {
                _isArabic = !_isArabic;
              });
            },
            icon: Icon(
              _isArabic ? Icons.translate : Icons.language,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// محتوى الحوار
  Widget _buildDialogueContent() {
    if (widget.dialogue.lines.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد أسطر حوار',
          style: TextStyle(fontSize: 18, color: Colors.white),
        ),
      );
    }

    final currentLine = widget.dialogue.lines[_currentLineIndex];
    final character = widget.dialogue.characters.firstWhere(
      (c) => c.id == currentLine.characterId,
      orElse: () => widget.dialogue.characters.first,
    );

    return Column(
      children: [
        // مؤشر التقدم
        _buildProgressIndicator(),
        
        Expanded(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // الشخصية
                _buildCharacter(character),
                
                const SizedBox(height: 30),
                
                // فقاعة النص
                _buildTextBubble(currentLine, character),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// مؤشر التقدم
  Widget _buildProgressIndicator() {
    final progress = (_currentLineIndex + 1) / widget.dialogue.lines.length;
    
    return Container(
      margin: const EdgeInsets.all(20),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${_currentLineIndex + 1}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              Text(
                '${widget.dialogue.lines.length}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.white.withOpacity(0.3),
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
            minHeight: 6,
          ),
        ],
      ),
    );
  }

  /// الشخصية
  Widget _buildCharacter(DialogueCharacter character) {
    return AnimatedBuilder(
      animation: _characterAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: 0.8 + (_characterAnimation.value * 0.2),
          child: Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: character.primaryColor,
                width: 4,
              ),
              boxShadow: [
                BoxShadow(
                  color: character.primaryColor.withOpacity(0.3),
                  spreadRadius: 5,
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: ClipOval(
              child: Image.asset(
                character.imagePath,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: character.primaryColor.withOpacity(0.2),
                    child: Icon(
                      Icons.person,
                      size: 60,
                      color: character.primaryColor,
                    ),
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }

  /// فقاعة النص
  Widget _buildTextBubble(DialogueLine line, DialogueCharacter character) {
    return AnimatedBuilder(
      animation: _textAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _textAnimation.value,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 20),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  spreadRadius: 2,
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              children: [
                // اسم الشخصية
                Text(
                  _isArabic ? character.nameAr : character.name,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: character.primaryColor,
                  ),
                ),
                const SizedBox(height: 10),
                
                // النص
                Text(
                  _isArabic ? line.textAr : line.text,
                  style: const TextStyle(
                    fontSize: 18,
                    height: 1.5,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// أدوات التحكم
  Widget _buildControls() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.3),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // السابق
          IconButton(
            onPressed: _currentLineIndex > 0 ? _previousLine : null,
            icon: const Icon(Icons.skip_previous, color: Colors.white),
            iconSize: 32,
          ),
          
          // تشغيل/إيقاف
          IconButton(
            onPressed: _isPlaying ? null : _playCurrentLine,
            icon: Icon(
              _isPlaying ? Icons.pause : Icons.play_arrow,
              color: Colors.white,
            ),
            iconSize: 40,
          ),
          
          // التالي
          IconButton(
            onPressed: _currentLineIndex < widget.dialogue.lines.length - 1 
                ? _nextLine 
                : null,
            icon: const Icon(Icons.skip_next, color: Colors.white),
            iconSize: 32,
          ),
          
          // التشغيل التلقائي
          IconButton(
            onPressed: () {
              setState(() {
                _autoPlay = !_autoPlay;
              });
            },
            icon: Icon(
              _autoPlay ? Icons.autorenew : Icons.autorenew_outlined,
              color: _autoPlay ? Colors.blue : Colors.white,
            ),
            iconSize: 28,
          ),
        ],
      ),
    );
  }

  /// حوار الإكمال
  void _showCompletionDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('🎉 أحسنت!'),
        content: const Text('لقد أكملت الحوار بنجاح!\nهل تريد إعادة المشاهدة؟'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
            child: const Text('العودة'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _currentLineIndex = 0;
              });
              _characterAnimationController.reset();
              _textAnimationController.reset();
              _startDialogue();
            },
            child: const Text('إعادة المشاهدة'),
          ),
        ],
      ),
    );
  }
}
