// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDzPBZY2TeUIm6L3WVRyF_3DkeTJpx-988',
    appId: '1:706005855038:web:new_web_app_id',
    messagingSenderId: '706005855038',
    projectId: 'ar-project-4063d',
    authDomain: 'ar-project-4063d.firebaseapp.com',
    storageBucket: 'ar-project-4063d.appspot.com',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDzPBZY2TeUIm6L3WVRyF_3DkeTJpx-988',
    appId: '1:706005855038:android:new_app_id',
    messagingSenderId: '706005855038',
    projectId: 'ar-project-4063d',
    storageBucket: 'ar-project-4063d.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDzPBZY2TeUIm6L3WVRyF_3DkeTJpx-988',
    appId: '1:706005855038:ios:new_ios_app_id',
    messagingSenderId: '706005855038',
    projectId: 'ar-project-4063d',
    storageBucket: 'ar-project-4063d.appspot.com',
    iosBundleId: 'com.ar.ai-smart-translator-new',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDzPBZY2TeUIm6L3WVRyF_3DkeTJpx-988',
    appId: '1:706005855038:macos:new_macos_app_id',
    messagingSenderId: '706005855038',
    projectId: 'ar-project-4063d',
    storageBucket: 'ar-project-4063d.appspot.com',
    iosBundleId: 'com.ar.ai-smart-translator-new.macos',
  );
}
