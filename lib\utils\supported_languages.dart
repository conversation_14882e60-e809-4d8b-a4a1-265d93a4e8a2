/// فئة اللغات المدعومة
class SupportedLanguages {
  /// قائمة جميع اللغات المدعومة
  static const Map<String, Map<String, String>> _languages = {
    'ar': {
      'name': 'العربية',
      'flag': '🇸🇦',
      'ttsCode': 'ar-SA',
    },
    'en': {
      'name': 'English',
      'flag': '🇺🇸',
      'ttsCode': 'en-US',
    },
    'fr': {
      'name': 'Français',
      'flag': '🇫🇷',
      'ttsCode': 'fr-FR',
    },
    'de': {
      'name': 'Deutsch',
      'flag': '🇩🇪',
      'ttsCode': 'de-DE',
    },
    'es': {
      'name': 'Español',
      'flag': '🇪🇸',
      'ttsCode': 'es-ES',
    },
    'it': {
      'name': 'Italiano',
      'flag': '🇮🇹',
      'ttsCode': 'it-IT',
    },
    'ja': {
      'name': '日本語',
      'flag': '🇯🇵',
      'ttsCode': 'ja-JP',
    },
    'zh': {
      'name': '中文',
      'flag': '🇨🇳',
      'ttsCode': 'zh-CN',
    },
    'tr': {
      'name': 'Türkçe',
      'flag': '🇹🇷',
      'ttsCode': 'tr-TR',
    },
    'ru': {
      'name': 'Русский',
      'flag': '🇷🇺',
      'ttsCode': 'ru-RU',
    },
  };

  /// الحصول على جميع رموز اللغات
  static List<String> getAllLanguages() {
    return _languages.keys.toList();
  }

  /// الحصول على اسم اللغة
  static String getLanguageName(String code) {
    return _languages[code]?['name'] ?? code;
  }

  /// الحصول على علم اللغة
  static String getLanguageFlag(String code) {
    return _languages[code]?['flag'] ?? '🌍';
  }

  /// الحصول على كود TTS للغة
  static String getTTSCode(String code) {
    return _languages[code]?['ttsCode'] ?? code;
  }

  /// التحقق من دعم اللغة
  static bool isSupported(String code) {
    return _languages.containsKey(code);
  }

  /// الحصول على معلومات اللغة كاملة
  static Map<String, String>? getLanguageInfo(String code) {
    return _languages[code];
  }
}
