import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import '../api/translation_service.dart';

/// نموذج بيانات المتحدث
class Speaker {
  final String id;
  final String name;
  final Color color;

  Speaker({required this.id, required this.name, required this.color});
}

/// نموذج بيانات مقطع الكلام
class SpeechSegment {
  final String text;
  final String speakerId;
  final DateTime timestamp;
  String? translatedText;

  SpeechSegment({
    required this.text,
    required this.speakerId,
    required this.timestamp,
    this.translatedText,
  });
}

/// خدمة التعرف على المتحدثين المتعددين
class MultiSpeakerRecognitionService {
  final TranslationService _translationService;

  final List<Speaker> _speakers = [];
  final List<SpeechSegment> _segments = [];

  bool _isRecording = false;

  // تدفق بيانات المقاطع
  final _segmentsController = StreamController<List<SpeechSegment>>.broadcast();
  Stream<List<SpeechSegment>> get segmentsStream => _segmentsController.stream;

  // تدفق بيانات حالة التسجيل
  final _recordingStatusController = StreamController<bool>.broadcast();
  Stream<bool> get recordingStatusStream => _recordingStatusController.stream;

  /// منشئ الخدمة
  MultiSpeakerRecognitionService({
    required TranslationService translationService,
  }) : _translationService = translationService;

  /// إضافة متحدث جديد
  Speaker addSpeaker(String name) {
    // إنشاء لون عشوائي للمتحدث
    final random = Random();
    final color = Color.fromRGBO(
      random.nextInt(200) + 55,
      random.nextInt(200) + 55,
      random.nextInt(200) + 55,
      1,
    );

    final speaker = Speaker(
      id: 'speaker_${_speakers.length + 1}',
      name: name,
      color: color,
    );

    _speakers.add(speaker);
    return speaker;
  }

  /// بدء التسجيل
  Future<bool> startRecording() async {
    if (_isRecording) return true;

    try {
      // محاكاة بدء التسجيل
      await Future.delayed(const Duration(milliseconds: 500));

      _isRecording = true;
      _recordingStatusController.add(true);

      return true;
    } catch (e) {
      debugPrint('Error starting recording: $e');
      _isRecording = false;
      _recordingStatusController.add(false);
      return false;
    }
  }

  /// إيقاف التسجيل والتعرف على المتحدثين
  Future<List<SpeechSegment>> stopRecordingAndRecognize() async {
    if (!_isRecording) {
      return [];
    }

    try {
      // محاكاة إيقاف التسجيل
      await Future.delayed(const Duration(seconds: 1));
      _isRecording = false;
      _recordingStatusController.add(false);

      // محاكاة التعرف على المتحدثين (في التطبيق الفعلي، سيتم استخدام Azure Speech Service)
      await Future.delayed(const Duration(seconds: 1));

      // إضافة مقطع جديد للمتحدث الأول (محاكاة)
      if (_speakers.isNotEmpty) {
        final segment = SpeechSegment(
          text: 'هذا نص تجريبي للتعرف على المتحدثين المتعددين',
          speakerId: _speakers[0].id,
          timestamp: DateTime.now(),
        );

        _segments.add(segment);
        _segmentsController.add(_segments);

        return _segments;
      }

      return [];
    } catch (e) {
      debugPrint('Error stopping recording and recognizing: $e');
      _isRecording = false;
      _recordingStatusController.add(false);
      return [];
    }
  }

  /// ترجمة مقطع محدد
  Future<void> translateSegment(
    SpeechSegment segment,
    String targetLanguage,
  ) async {
    try {
      final translatedText = await _translationService.translateWithAI(
        text: segment.text,
        sourceLanguage: 'ar',
        targetLanguage: targetLanguage,
      );

      segment.translatedText = translatedText;
      _segmentsController.add(_segments);
    } catch (e) {
      debugPrint('Error translating segment: $e');
    }
  }

  /// الحصول على المتحدث بواسطة المعرف
  Speaker? getSpeakerById(String id) {
    return _speakers.firstWhere(
      (s) => s.id == id,
      orElse: () => _speakers.first,
    );
  }

  /// التخلص من الموارد
  void dispose() {
    _segmentsController.close();
    _recordingStatusController.close();
  }
}
