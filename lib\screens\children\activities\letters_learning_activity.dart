import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math';

/// نشاط تعلم الحروف التفاعلي
class LettersLearningActivity extends StatefulWidget {
  final Function(int score) onComplete;

  const LettersLearningActivity({super.key, required this.onComplete});

  @override
  State<LettersLearningActivity> createState() => _LettersLearningActivityState();
}

class _LettersLearningActivityState extends State<LettersLearningActivity>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _letterController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _letterAnimation;

  int _currentLetterIndex = 0;
  int _score = 0;
  bool _showResult = false;
  bool _gameCompleted = false;
  String? _selectedLetter;

  final List<Map<String, dynamic>> _arabicLetters = [
    {'letter': 'أ', 'name': 'ألف', 'word': 'أسد', 'emoji': '🦁'},
    {'letter': 'ب', 'name': 'باء', 'word': 'بطة', 'emoji': '🦆'},
    {'letter': 'ت', 'name': 'تاء', 'word': 'تفاحة', 'emoji': '🍎'},
    {'letter': 'ث', 'name': 'ثاء', 'word': 'ثعلب', 'emoji': '🦊'},
    {'letter': 'ج', 'name': 'جيم', 'word': 'جمل', 'emoji': '🐪'},
    {'letter': 'ح', 'name': 'حاء', 'word': 'حصان', 'emoji': '🐴'},
    {'letter': 'خ', 'name': 'خاء', 'word': 'خروف', 'emoji': '🐑'},
    {'letter': 'د', 'name': 'دال', 'word': 'دب', 'emoji': '🐻'},
    {'letter': 'ذ', 'name': 'ذال', 'word': 'ذئب', 'emoji': '🐺'},
    {'letter': 'ر', 'name': 'راء', 'word': 'رجل', 'emoji': '👨'},
  ];

  List<String> _shuffledOptions = [];

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _generateQuestion();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _letterController.dispose();
    super.dispose();
  }

  void _initAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _letterController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _letterAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _letterController,
      curve: Curves.bounceOut,
    ));

    _animationController.forward();
    _letterController.forward();
  }

  void _generateQuestion() {
    if (_currentLetterIndex >= _arabicLetters.length) {
      setState(() {
        _gameCompleted = true;
      });
      return;
    }

    setState(() {
      // إنشاء خيارات الإجابة
      _shuffledOptions = [_arabicLetters[_currentLetterIndex]['letter']];
      
      // إضافة خيارات خاطئة
      while (_shuffledOptions.length < 4) {
        final randomLetter = _arabicLetters[Random().nextInt(_arabicLetters.length)]['letter'];
        if (!_shuffledOptions.contains(randomLetter)) {
          _shuffledOptions.add(randomLetter);
        }
      }
      
      _shuffledOptions.shuffle();
      _selectedLetter = null;
      _showResult = false;
    });

    _animationController.reset();
    _letterController.reset();
    _animationController.forward();
    _letterController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.deepPurple.shade100,
              Colors.indigo.shade100,
              Colors.blue.shade100,
            ],
          ),
        ),
        child: SafeArea(
          child: _gameCompleted
              ? _buildCompletionScreen()
              : Column(
                  children: [
                    _buildHeader(),
                    _buildProgressBar(),
                    Expanded(child: _buildGameScreen()),
                  ],
                ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.deepPurple),
            onPressed: () => Navigator.pop(context),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'تعلم الحروف 📝',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.deepPurple,
                  ),
                ),
                Text(
                  'اختر الحرف الصحيح',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.deepPurple.shade600,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(15),
              boxShadow: [
                BoxShadow(
                  color: Colors.deepPurple.withValues(alpha: 0.2),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Text(
              'النقاط: $_score',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.deepPurple,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressBar() {
    final progress = _currentLetterIndex / _arabicLetters.length;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'الحرف ${_currentLetterIndex + 1} من ${_arabicLetters.length}',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.deepPurple,
                ),
              ),
              Text(
                '${(progress * 100).round()}%',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.deepPurple,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.deepPurple.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(Colors.deepPurple),
            minHeight: 8,
          ),
        ],
      ),
    );
  }

  Widget _buildGameScreen() {
    final currentLetter = _arabicLetters[_currentLetterIndex];
    
    return ScaleTransition(
      scale: _scaleAnimation,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            const SizedBox(height: 20),
            
            // عرض الكلمة والرمز التعبيري
            Container(
              padding: const EdgeInsets.all(30),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(25),
                boxShadow: [
                  BoxShadow(
                    color: Colors.deepPurple.withValues(alpha: 0.2),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: Column(
                children: [
                  ScaleTransition(
                    scale: _letterAnimation,
                    child: Text(
                      currentLetter['emoji'],
                      style: const TextStyle(fontSize: 80),
                    ),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    currentLetter['word'],
                    style: const TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: Colors.deepPurple,
                    ),
                  ),
                  const SizedBox(height: 10),
                  const Text(
                    'اختر الحرف الأول من هذه الكلمة:',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 40),

            // خيارات الحروف
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 1.2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: _shuffledOptions.length,
              itemBuilder: (context, index) {
                return _buildLetterOption(_shuffledOptions[index]);
              },
            ),

            if (_showResult) ...[
              const SizedBox(height: 30),
              _buildResultMessage(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildLetterOption(String letter) {
    final isSelected = _selectedLetter == letter;
    final isCorrect = letter == _arabicLetters[_currentLetterIndex]['letter'];
    final showResult = _showResult && isSelected;

    return GestureDetector(
      onTap: _showResult ? null : () => _selectLetter(letter),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: showResult
                ? (isCorrect 
                    ? [Colors.green.shade400, Colors.green.shade600]
                    : [Colors.red.shade400, Colors.red.shade600])
                : (isSelected
                    ? [Colors.deepPurple.shade400, Colors.deepPurple.shade600]
                    : [Colors.white, Colors.grey.shade100]),
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: showResult
                ? (isCorrect ? Colors.green : Colors.red)
                : (isSelected ? Colors.deepPurple : Colors.grey.shade300),
            width: 3,
          ),
          boxShadow: [
            BoxShadow(
              color: (showResult
                  ? (isCorrect ? Colors.green : Colors.red)
                  : (isSelected ? Colors.deepPurple : Colors.grey))
                  .withValues(alpha: 0.3),
              blurRadius: isSelected ? 15 : 8,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (showResult)
                Icon(
                  isCorrect ? Icons.check_circle : Icons.cancel,
                  color: Colors.white,
                  size: 24,
                ),
              Text(
                letter,
                style: TextStyle(
                  fontSize: 48,
                  fontWeight: FontWeight.bold,
                  color: showResult || isSelected ? Colors.white : Colors.deepPurple,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildResultMessage() {
    final isCorrect = _selectedLetter == _arabicLetters[_currentLetterIndex]['letter'];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isCorrect ? Colors.green : Colors.red,
        borderRadius: BorderRadius.circular(15),
      ),
      child: Text(
        isCorrect 
            ? 'ممتاز! إجابة صحيحة! 🎉' 
            : 'الحرف الصحيح هو: ${_arabicLetters[_currentLetterIndex]['letter']}',
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  void _selectLetter(String letter) {
    setState(() {
      _selectedLetter = letter;
      _showResult = true;
    });

    final isCorrect = letter == _arabicLetters[_currentLetterIndex]['letter'];
    
    HapticFeedback.lightImpact();

    if (isCorrect) {
      _score += 10;
      HapticFeedback.heavyImpact();
    }

    // الانتقال للحرف التالي
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _currentLetterIndex++;
        });
        _generateQuestion();
      }
    });
  }

  Widget _buildCompletionScreen() {
    final percentage = (_score / (_arabicLetters.length * 10) * 100).round();
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(40),
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.deepPurple.withValues(alpha: 0.3),
                    blurRadius: 30,
                    offset: const Offset(0, 15),
                  ),
                ],
              ),
              child: Text(
                percentage >= 80 ? '🏆' : percentage >= 60 ? '⭐' : '📝',
                style: const TextStyle(fontSize: 80),
              ),
            ),

            const SizedBox(height: 30),

            Text(
              percentage >= 80 ? 'ممتاز!' : percentage >= 60 ? 'جيد جداً!' : 'أحسنت!',
              style: const TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Colors.deepPurple,
              ),
            ),

            const SizedBox(height: 16),

            Text(
              'لقد حصلت على $_score نقطة من ${_arabicLetters.length * 10}',
              style: const TextStyle(
                fontSize: 18,
                color: Colors.deepPurple,
              ),
            ),

            Text(
              'النسبة: $percentage%',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.deepPurple,
              ),
            ),

            const SizedBox(height: 40),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  onPressed: _restartGame,
                  icon: const Icon(Icons.refresh),
                  label: const Text('إعادة اللعب'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 12,
                    ),
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () {
                    widget.onComplete(_score);
                    Navigator.pop(context);
                  },
                  icon: const Icon(Icons.check),
                  label: const Text('إنهاء'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 12,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _restartGame() {
    setState(() {
      _currentLetterIndex = 0;
      _score = 0;
      _selectedLetter = null;
      _showResult = false;
      _gameCompleted = false;
    });
    
    _generateQuestion();
  }
}
