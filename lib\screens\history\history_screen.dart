import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../providers/app_state.dart';
import '../../config/app_theme.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/enhanced_list_item_fixed.dart';
import '../../utils/app_helpers.dart';
import '../../models/translation_item.dart';

/// شاشة سجل الترجمات
class HistoryScreen extends StatefulWidget {
  const HistoryScreen({super.key});

  @override
  State<HistoryScreen> createState() => _HistoryScreenState();
}

class _HistoryScreenState extends State<HistoryScreen> {
  bool _isLoading = true;
  List<TranslationItem> _historyItems = [];
  String _filterType = 'all';
  DateTime? _startDate;
  DateTime? _endDate;

  @override
  void initState() {
    super.initState();
    _loadHistory();
  }

  /// تحميل سجل الترجمات
  Future<void> _loadHistory() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // حفظ مرجع للـ AppState قبل العملية غير المتزامنة
      final appState = Provider.of<AppState>(context, listen: false);

      // في التطبيق الفعلي، سيتم استخدام البيانات المسترجعة
      await appState.getTranslationHistory(
        startDate: _startDate,
        endDate: _endDate,
      );

      setState(() {
        _historyItems =
            []; // في التطبيق الفعلي، سيتم تحويل البيانات إلى TranslationItem
        _isLoading = false;
      });
    } catch (e) {
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء تحميل السجل: $e',
          isError: true,
        );
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// إضافة عنصر إلى المفضلة
  Future<void> _addToFavorites(TranslationItem item) async {
    try {
      final appState = Provider.of<AppState>(context, listen: false);
      await appState.addToFavorites(item);

      if (mounted) {
        AppHelpers.showSnackBar(context, 'تمت إضافة العنصر إلى المفضلة');
      }
    } catch (e) {
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء إضافة العنصر إلى المفضلة: $e',
          isError: true,
        );
      }
    }
  }

  /// حذف عنصر من السجل
  Future<void> _deleteHistoryItem(String id) async {
    try {
      final appState = Provider.of<AppState>(context, listen: false);
      await appState.deleteFromHistory(id);

      setState(() {
        _historyItems.removeWhere((item) => item.id == id);
      });

      if (mounted) {
        AppHelpers.showSnackBar(context, 'تم حذف العنصر من السجل');
      }
    } catch (e) {
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء حذف العنصر من السجل: $e',
          isError: true,
        );
      }
    }
  }

  /// حذف كل السجل
  void _clearHistory() {
    _showClearHistoryDialog();
  }

  /// عرض مربع حوار تأكيد حذف السجل
  Future<void> _showClearHistoryDialog() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('حذف السجل'),
            content: const Text('هل أنت متأكد من رغبتك في حذف كل السجل؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: const Text('حذف'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      setState(() {
        _isLoading = true;
      });

      // تخزين مرجع للسياق قبل العملية غير المتزامنة
      final BuildContext contextBeforeAsync = context;

      // استخدام دالة منفصلة لتنفيذ العملية غير المتزامنة
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _performClearHistory(contextBeforeAsync);
      });
    }
  }

  /// تنفيذ عملية حذف السجل
  Future<void> _performClearHistory(BuildContext contextParam) async {
    // حفظ مراجع للعناصر التي سنحتاجها بعد العملية غير المتزامنة
    final appState = Provider.of<AppState>(contextParam, listen: false);
    final scaffoldMessengerState = ScaffoldMessenger.of(contextParam);

    try {
      await appState.clearHistory();

      if (!mounted) return;

      setState(() {
        _historyItems = [];
        _isLoading = false;
      });

      scaffoldMessengerState.showSnackBar(
        SnackBar(
          content: const Text('تم حذف السجل بنجاح'),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          margin: const EdgeInsets.all(10),
        ),
      );
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      scaffoldMessengerState.showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء حذف السجل: $e'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          margin: const EdgeInsets.all(10),
        ),
      );
    }
  }

  /// تصفية العناصر حسب النوع
  List<TranslationItem> _getFilteredItems() {
    if (_filterType == 'all') {
      return _historyItems;
    }
    return _historyItems.where((item) => item.type == _filterType).toList();
  }

  /// عرض منتقي التاريخ
  Future<void> _showDatePicker() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange:
          _startDate != null && _endDate != null
              ? DateTimeRange(start: _startDate!, end: _endDate!)
              : null,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppTheme.primaryColor,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
      _loadHistory();
    }
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return DateFormat('yyyy/MM/dd HH:mm', 'ar').format(date);
  }

  /// الحصول على أيقونة نوع الترجمة
  IconData _getTypeIcon(String type) {
    switch (type) {
      case 'voice':
        return Icons.mic;
      case 'text':
        return Icons.text_fields;
      case 'image':
        return Icons.camera_alt;
      case 'document':
        return Icons.description;
      case 'conversation':
        return Icons.forum;
      default:
        return Icons.translate;
    }
  }

  /// الحصول على اسم نوع الترجمة
  String _getTypeName(String type) {
    switch (type) {
      case 'voice':
        return 'ترجمة صوتية';
      case 'text':
        return 'ترجمة نصية';
      case 'image':
        return 'ترجمة صورة';
      case 'document':
        return 'ترجمة مستند';
      case 'conversation':
        return 'محادثة';
      default:
        return 'ترجمة';
    }
  }

  @override
  Widget build(BuildContext context) {
    final filteredItems = _getFilteredItems();

    return Scaffold(
      appBar: CustomAppBar(
        title: 'سجل الترجمات',
        actions: [
          // زر اختيار التاريخ
          IconButton(
            icon: const Icon(Icons.date_range),
            onPressed: _showDatePicker,
            tooltip: 'تصفية حسب التاريخ',
          ),
          // زر التصفية
          PopupMenuButton<String>(
            icon: const Icon(Icons.filter_list),
            tooltip: 'تصفية حسب النوع',
            onSelected: (value) {
              setState(() {
                _filterType = value;
              });
            },
            itemBuilder:
                (context) => [
                  const PopupMenuItem(value: 'all', child: Text('الكل')),
                  const PopupMenuItem(
                    value: 'voice',
                    child: Text('ترجمة صوتية'),
                  ),
                  const PopupMenuItem(value: 'text', child: Text('ترجمة نصية')),
                  const PopupMenuItem(
                    value: 'image',
                    child: Text('ترجمة صورة'),
                  ),
                  const PopupMenuItem(
                    value: 'document',
                    child: Text('ترجمة مستند'),
                  ),
                  const PopupMenuItem(
                    value: 'conversation',
                    child: Text('محادثة'),
                  ),
                ],
          ),
          // زر حذف السجل
          IconButton(
            icon: const Icon(Icons.delete_sweep),
            onPressed: _historyItems.isEmpty ? null : _clearHistory,
            tooltip: 'حذف السجل',
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : filteredItems.isEmpty
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.history, size: 80, color: Colors.grey),
                    const SizedBox(height: 16),
                    Text(
                      _filterType == 'all'
                          ? 'لا توجد عناصر في السجل'
                          : 'لا توجد عناصر من هذا النوع في السجل',
                      style: const TextStyle(fontSize: 18, color: Colors.grey),
                    ),
                    if (_startDate != null && _endDate != null)
                      Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: Text(
                          'الفترة: ${_formatDate(_startDate!)} - ${_formatDate(_endDate!)}',
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                      ),
                  ],
                ),
              )
              : Column(
                children: [
                  // عرض نطاق التاريخ المحدد
                  if (_startDate != null && _endDate != null)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      color: Colors.grey[100],
                      child: Row(
                        children: [
                          const Icon(Icons.date_range, size: 16),
                          const SizedBox(width: 8),
                          Text(
                            'الفترة: ${DateFormat('yyyy/MM/dd', 'ar').format(_startDate!)} - ${DateFormat('yyyy/MM/dd', 'ar').format(_endDate!)}',
                            style: const TextStyle(fontSize: 14),
                          ),
                          const Spacer(),
                          TextButton(
                            onPressed: () {
                              setState(() {
                                _startDate = null;
                                _endDate = null;
                              });
                              _loadHistory();
                            },
                            child: const Text('إلغاء'),
                          ),
                        ],
                      ),
                    ),
                  // قائمة العناصر
                  Expanded(
                    child: ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: filteredItems.length,
                      itemBuilder: (context, index) {
                        final item = filteredItems[index];
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 12),
                          child: EnhancedListItem(
                            leading: CircleAvatar(
                              backgroundColor: AppTheme.primaryColor.withAlpha(
                                26,
                              ),
                              child: Icon(
                                _getTypeIcon(item.type),
                                color: AppTheme.primaryColor,
                              ),
                            ),
                            title:
                                item.sourceText.length > 50
                                    ? '${item.sourceText.substring(0, 50)}...'
                                    : item.sourceText,
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  item.translatedText.length > 50
                                      ? '${item.translatedText.substring(0, 50)}...'
                                      : item.translatedText,
                                  style: const TextStyle(color: Colors.grey),
                                ),
                                const SizedBox(height: 4),
                                Row(
                                  children: [
                                    Text(
                                      _getTypeName(item.type),
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      _formatDate(item.timestamp),
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            trailing: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                IconButton(
                                  icon: const Icon(
                                    Icons.favorite_border,
                                    color: AppTheme.primaryColor,
                                  ),
                                  onPressed: () => _addToFavorites(item),
                                  tooltip: 'إضافة إلى المفضلة',
                                ),
                                IconButton(
                                  icon: const Icon(
                                    Icons.delete,
                                    color: Colors.red,
                                  ),
                                  onPressed: () => _deleteHistoryItem(item.id),
                                  tooltip: 'حذف من السجل',
                                ),
                              ],
                            ),
                            onTap: () {
                              // عرض تفاصيل العنصر
                              _showItemDetails(item);
                            },
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
    );
  }

  /// عرض تفاصيل العنصر
  void _showItemDetails(TranslationItem item) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    CircleAvatar(
                      backgroundColor: AppTheme.primaryColor.withAlpha(26),
                      child: Icon(
                        _getTypeIcon(item.type),
                        color: AppTheme.primaryColor,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      _getTypeName(item.type),
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      _formatDate(item.timestamp),
                      style: const TextStyle(fontSize: 14, color: Colors.grey),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                const Text(
                  'النص الأصلي:',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(12),
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    item.sourceText,
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  'الترجمة:',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(12),
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withAlpha(26),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    item.translatedText,
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildActionButton(
                      icon: Icons.copy,
                      label: 'نسخ الأصل',
                      onTap: () {
                        // نسخ النص الأصلي
                        Clipboard.setData(ClipboardData(text: item.sourceText));
                        Navigator.pop(context);
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('تم نسخ النص الأصلي')),
                        );
                      },
                    ),
                    _buildActionButton(
                      icon: Icons.copy_all,
                      label: 'نسخ الترجمة',
                      onTap: () {
                        // نسخ الترجمة
                        Clipboard.setData(
                          ClipboardData(text: item.translatedText),
                        );
                        Navigator.pop(context);
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('تم نسخ الترجمة')),
                        );
                      },
                    ),
                    _buildActionButton(
                      icon: Icons.favorite,
                      label: 'إضافة للمفضلة',
                      onTap: () {
                        Navigator.pop(context);
                        _addToFavorites(item);
                      },
                    ),
                    _buildActionButton(
                      icon: Icons.delete,
                      label: 'حذف',
                      onTap: () {
                        Navigator.pop(context);
                        _deleteHistoryItem(item.id);
                      },
                      color: Colors.red,
                    ),
                  ],
                ),
              ],
            ),
          ),
    );
  }

  /// بناء زر إجراء
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    Color? color,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          children: [
            Icon(icon, color: color ?? AppTheme.primaryColor, size: 24),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: color ?? AppTheme.primaryColor,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
