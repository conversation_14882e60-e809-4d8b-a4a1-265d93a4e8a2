import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../core/app_state.dart';
import '../../config/app_theme.dart';
import '../../widgets/enhanced_app_bar.dart';
import '../../widgets/button_3d.dart';
import '../../widgets/three_d_transition.dart';
import '../subscription/subscription_screen.dart';
import '../settings/settings_screen.dart';
import '../favorites/favorites_screen.dart';
import '../history/history_screen.dart';

/// شاشة الملف الشخصي
class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppState>(context);
    final user = appState.currentUser;

    return Scaffold(
      appBar: EnhancedAppBar(
        title: const Text('الملف الشخصي'),
        backgroundColor: AppTheme.primaryColor,
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // معلومات المستخدم
                    _buildUserInfo(user, appState),

                    const SizedBox(height: 24),

                    // إحصائيات الاستخدام
                    _buildUsageStatistics(appState),

                    const SizedBox(height: 24),

                    // الأقسام
                    _buildSections(context, appState),
                  ],
                ),
              ),
    );
  }

  /// بناء معلومات المستخدم
  Widget _buildUserInfo(dynamic user, AppState appState) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // صورة المستخدم
            CircleAvatar(
              radius: 50,
              backgroundColor: AppTheme.primaryColor.withAlpha(26),
              backgroundImage:
                  user?['photoUrl'] != null
                      ? NetworkImage(user!['photoUrl'])
                      : null,
              child:
                  user?['photoUrl'] == null
                      ? const Icon(
                        Icons.person,
                        size: 50,
                        color: AppTheme.primaryColor,
                      )
                      : null,
            ),

            const SizedBox(height: 16),

            // اسم المستخدم
            Text(
              user?['displayName'] ?? 'مستخدم',
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),

            const SizedBox(height: 8),

            // البريد الإلكتروني
            Text(
              user?['email'] ?? 'لا يوجد بريد إلكتروني',
              style: const TextStyle(fontSize: 16, color: Colors.grey),
            ),

            const SizedBox(height: 16),

            // حالة الاشتراك
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color:
                    appState.isSubscribed
                        ? Colors.green.withAlpha(26)
                        : Colors.orange.withAlpha(26),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                appState.isSubscribed
                    ? 'مشترك في الخطة المميزة'
                    : 'الخطة المجانية',
                style: TextStyle(
                  color: appState.isSubscribed ? Colors.green : Colors.orange,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            if (appState.isSubscribed && appState.subscriptionEndDate != null)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  'ينتهي في ${_formatDate(appState.subscriptionEndDate!)}',
                  style: const TextStyle(fontSize: 14, color: Colors.grey),
                ),
              ),

            const SizedBox(height: 16),

            // زر الترقية أو إدارة الاشتراك
            Button3D(
              onPressed: () => _navigateToSubscriptions(context),
              color:
                  appState.isSubscribed ? Colors.green : AppTheme.primaryColor,
              width: double.infinity,
              height: 50,
              child: Text(
                appState.isSubscribed ? 'إدارة الاشتراك' : 'ترقية إلى المميز',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء إحصائيات الاستخدام
  Widget _buildUsageStatistics(AppState appState) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إحصائيات الاستخدام',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildStatItem(
              icon: Icons.mic,
              title: 'الترجمات الصوتية',
              value: appState.usageStats.voiceTranslations.toString(),
            ),
            _buildStatItem(
              icon: Icons.text_fields,
              title: 'الترجمات النصية',
              value: appState.usageStats.textTranslations.toString(),
            ),
            _buildStatItem(
              icon: Icons.camera_alt,
              title: 'ترجمات الصور',
              value: appState.usageStats.imageTranslations.toString(),
            ),
            _buildStatItem(
              icon: Icons.description,
              title: 'ترجمات المستندات',
              value: appState.usageStats.documentTranslations.toString(),
            ),
            _buildStatItem(
              icon: Icons.forum,
              title: 'المحادثات المترجمة',
              value: appState.usageStats.conversations.toString(),
            ),
            _buildStatItem(
              icon: Icons.smart_toy,
              title: 'محادثات الذكاء الاصطناعي',
              value: appState.usageStats.aiChats.toString(),
            ),
            const SizedBox(height: 8),
            const Divider(),
            const SizedBox(height: 8),
            _buildStatItem(
              icon: Icons.calendar_today,
              title: 'تاريخ التسجيل',
              value: _formatDate(appState.registrationDate),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر إحصائي
  Widget _buildStatItem({
    required IconData icon,
    required String title,
    required String value,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, color: AppTheme.primaryColor, size: 20),
          const SizedBox(width: 12),
          Text(title, style: const TextStyle(fontSize: 16)),
          const Spacer(),
          Text(
            value,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  /// بناء الأقسام
  Widget _buildSections(BuildContext context, AppState appState) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الأقسام',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildSectionItem(
              icon: Icons.favorite,
              title: 'المفضلة',
              onTap: () => _navigateToFavorites(context),
            ),
            _buildSectionItem(
              icon: Icons.history,
              title: 'السجل',
              onTap: () => _navigateToHistory(context),
            ),
            _buildSectionItem(
              icon: Icons.settings,
              title: 'الإعدادات',
              onTap: () => _navigateToSettings(context),
            ),
            _buildSectionItem(
              icon: Icons.logout,
              title: 'تسجيل الخروج',
              onTap: _signOut,
              color: Colors.red,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر قسم
  Widget _buildSectionItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? color,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        child: Row(
          children: [
            Icon(icon, color: color ?? AppTheme.primaryColor, size: 24),
            const SizedBox(width: 16),
            Text(title, style: TextStyle(fontSize: 16, color: color)),
            const Spacer(),
            Icon(Icons.arrow_forward_ios, color: Colors.grey[400], size: 16),
          ],
        ),
      ),
    );
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime? date) {
    if (date == null) return 'غير متوفر';
    return DateFormat('yyyy/MM/dd', 'ar').format(date);
  }

  /// الانتقال إلى صفحة الاشتراكات
  void _navigateToSubscriptions(BuildContext context) {
    Navigator.of(context).push(
      Transition3D(
        page: const SubscriptionScreen(),
        type: TransitionType.rightToLeft,
      ),
    );
  }

  /// الانتقال إلى صفحة الإعدادات
  void _navigateToSettings(BuildContext context) {
    Navigator.of(context).push(
      Transition3D(
        page: const SettingsScreen(),
        type: TransitionType.rightToLeft,
      ),
    );
  }

  /// الانتقال إلى صفحة المفضلة
  void _navigateToFavorites(BuildContext context) {
    Navigator.of(context).push(
      Transition3D(
        page: const FavoritesScreen(),
        type: TransitionType.rightToLeft,
      ),
    );
  }

  /// الانتقال إلى صفحة السجل
  void _navigateToHistory(BuildContext context) {
    Navigator.of(context).push(
      Transition3D(
        page: const HistoryScreen(),
        type: TransitionType.rightToLeft,
      ),
    );
  }

  /// تسجيل الخروج
  Future<void> _signOut() async {
    final appState = Provider.of<AppState>(context, listen: false);

    // عرض مربع حوار للتأكيد
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تسجيل الخروج'),
            content: const Text('هل أنت متأكد من رغبتك في تسجيل الخروج؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: const Text('تسجيل الخروج'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      setState(() {
        _isLoading = true;
      });

      try {
        // تنفيذ تسجيل الخروج
        await appState.signOut();

        // التحقق من أن الحالة لا تزال مثبتة قبل استخدام السياق
        if (mounted) {
          Navigator.pop(context); // العودة إلى الشاشة السابقة
        }
      } catch (e) {
        // التحقق من أن الحالة لا تزال مثبتة قبل تحديث الحالة واستخدام السياق
        if (mounted) {
          setState(() {
            _isLoading = false;
          });

          // عرض رسالة الخطأ
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('حدث خطأ أثناء تسجيل الخروج: $e'),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              margin: const EdgeInsets.all(10),
            ),
          );
        }
      }
    }
  }
}
