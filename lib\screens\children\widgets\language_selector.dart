import 'package:flutter/material.dart';
import '../../../services/educational_service.dart';

/// واجهة اختيار اللغة للتعلم التفاعلي
class LanguageSelector extends StatefulWidget {
  final String selectedLanguage;
  final Function(String) onLanguageChanged;
  final EducationalService educationalService;

  const LanguageSelector({
    super.key,
    required this.selectedLanguage,
    required this.onLanguageChanged,
    required this.educationalService,
  });

  @override
  State<LanguageSelector> createState() => _LanguageSelectorState();
}

class _LanguageSelectorState extends State<LanguageSelector> {
  late List<Map<String, String>> _supportedLanguages;

  @override
  void initState() {
    super.initState();
    _supportedLanguages = widget.educationalService.getSupportedLanguages();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 2,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'اختر لغة التعلم',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 15),
          _buildLanguageGrid(),
        ],
      ),
    );
  }

  /// شبكة اللغات
  Widget _buildLanguageGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 3,
        crossAxisSpacing: 10,
        mainAxisSpacing: 10,
      ),
      itemCount: _supportedLanguages.length,
      itemBuilder: (context, index) {
        final language = _supportedLanguages[index];
        return _buildLanguageCard(language);
      },
    );
  }

  /// بطاقة اللغة
  Widget _buildLanguageCard(Map<String, String> language) {
    final isSelected = widget.selectedLanguage == language['code'];
    
    return GestureDetector(
      onTap: () => _selectLanguage(language['code']!),
      child: Container(
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue.shade50 : Colors.grey.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.grey.shade300,
            width: 2,
          ),
        ),
        child: Row(
          children: [
            // علم الدولة (أيقونة)
            Container(
              width: 40,
              height: 40,
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _getLanguageColor(language['code']!),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Text(
                  _getLanguageFlag(language['code']!),
                  style: const TextStyle(fontSize: 20),
                ),
              ),
            ),
            
            // اسم اللغة
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    language['name']!,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: isSelected ? Colors.blue : Colors.black87,
                    ),
                  ),
                  Text(
                    language['englishName']!,
                    style: TextStyle(
                      fontSize: 12,
                      color: isSelected ? Colors.blue.shade700 : Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            
            // أيقونة التحديد
            if (isSelected)
              Container(
                margin: const EdgeInsets.only(right: 8),
                child: Icon(
                  Icons.check_circle,
                  color: Colors.blue,
                  size: 20,
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// اختيار اللغة
  void _selectLanguage(String languageCode) {
    widget.onLanguageChanged(languageCode);
    
    // تشغيل صوت ترحيبي بالغة المختارة
    _playWelcomeMessage(languageCode);
  }

  /// تشغيل رسالة ترحيبية بالغة المختارة
  Future<void> _playWelcomeMessage(String languageCode) async {
    final welcomeMessages = {
      'ar': 'مرحباً! لنتعلم معاً',
      'en': 'Hello! Let\'s learn together',
      'fr': 'Bonjour! Apprenons ensemble',
      'de': 'Hallo! Lass uns zusammen lernen',
      'es': '¡Hola! Aprendamos juntos',
      'it': 'Ciao! Impariamo insieme',
      'ja': 'こんにちは！一緒に学びましょう',
      'zh': '你好！让我们一起学习',
      'tr': 'Merhaba! Birlikte öğrenelim',
      'ru': 'Привет! Давайте учиться вместе',
    };

    final message = welcomeMessages[languageCode] ?? welcomeMessages['en']!;
    await widget.educationalService.speakInLanguage(message, languageCode);
  }

  /// الحصول على لون اللغة
  Color _getLanguageColor(String languageCode) {
    final colorMap = {
      'ar': Colors.green,
      'en': Colors.blue,
      'fr': Colors.indigo,
      'de': Colors.red,
      'es': Colors.orange,
      'it': Colors.green,
      'ja': Colors.red,
      'zh': Colors.red,
      'tr': Colors.red,
      'ru': Colors.blue,
    };
    
    return colorMap[languageCode] ?? Colors.grey;
  }

  /// الحصول على علم الدولة (إيموجي)
  String _getLanguageFlag(String languageCode) {
    final flagMap = {
      'ar': '🇸🇦',
      'en': '🇺🇸',
      'fr': '🇫🇷',
      'de': '🇩🇪',
      'es': '🇪🇸',
      'it': '🇮🇹',
      'ja': '🇯🇵',
      'zh': '🇨🇳',
      'tr': '🇹🇷',
      'ru': '🇷🇺',
    };
    
    return flagMap[languageCode] ?? '🌍';
  }
}

/// حوار اختيار اللغة
class LanguageSelectorDialog extends StatelessWidget {
  final String currentLanguage;
  final Function(String) onLanguageSelected;
  final EducationalService educationalService;

  const LanguageSelectorDialog({
    super.key,
    required this.currentLanguage,
    required this.onLanguageSelected,
    required this.educationalService,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.language,
                  color: Colors.blue,
                  size: 28,
                ),
                const SizedBox(width: 10),
                const Expanded(
                  child: Text(
                    'اختر لغة التعلم',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 20),
            LanguageSelector(
              selectedLanguage: currentLanguage,
              onLanguageChanged: (language) {
                onLanguageSelected(language);
                Navigator.pop(context);
              },
              educationalService: educationalService,
            ),
          ],
        ),
      ),
    );
  }

  /// عرض حوار اختيار اللغة
  static Future<void> show(
    BuildContext context, {
    required String currentLanguage,
    required Function(String) onLanguageSelected,
    required EducationalService educationalService,
  }) {
    return showDialog(
      context: context,
      builder: (context) => LanguageSelectorDialog(
        currentLanguage: currentLanguage,
        onLanguageSelected: onLanguageSelected,
        educationalService: educationalService,
      ),
    );
  }
}
