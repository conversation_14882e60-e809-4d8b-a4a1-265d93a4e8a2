import 'package:flutter/material.dart';
import '../../services/achievements_service.dart';
import '../../models/educational_content.dart';

/// شاشة عرض الإنجازات
class AchievementsScreen extends StatefulWidget {
  final AchievementsService achievementsService;

  const AchievementsScreen({super.key, required this.achievementsService});

  @override
  State<AchievementsScreen> createState() => _AchievementsScreenState();
}

class _AchievementsScreenState extends State<AchievementsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;

  List<Achievement> _unlockedAchievements = [];
  List<AchievementDefinition> _allAchievements = [];
  UserStats _userStats = UserStats();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  void _loadData() {
    _unlockedAchievements =
        widget.achievementsService.getUnlockedAchievements();
    _allAchievements = widget.achievementsService.getAllAchievements();
    _userStats = widget.achievementsService.getUserStats();
    _animationController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text('🏆 إنجازاتي'),
        backgroundColor: Colors.amber,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'الإنجازات المفتوحة'),
            Tab(text: 'جميع الإنجازات'),
          ],
        ),
      ),
      body: Column(
        children: [
          _buildStatsHeader(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [_buildUnlockedAchievements(), _buildAllAchievements()],
            ),
          ),
        ],
      ),
    );
  }

  /// رأس الإحصائيات
  Widget _buildStatsHeader() {
    return Container(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.amber.shade400, Colors.orange.shade400],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.amber.withValues(alpha: 0.3),
            spreadRadius: 2,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem(
                '${_unlockedAchievements.length}',
                'إنجازات مفتوحة',
                Icons.emoji_events,
              ),
              _buildStatItem(
                '${_userStats.totalScore}',
                'إجمالي النقاط',
                Icons.star,
              ),
              _buildStatItem(
                '${_userStats.currentStreak}',
                'الأيام المتتالية',
                Icons.local_fire_department,
              ),
            ],
          ),
          const SizedBox(height: 15),
          LinearProgressIndicator(
            value: _unlockedAchievements.length / _allAchievements.length,
            backgroundColor: Colors.white.withValues(alpha: 0.3),
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
            minHeight: 8,
          ),
          const SizedBox(height: 8),
          Text(
            '${_unlockedAchievements.length} من ${_allAchievements.length} إنجاز',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// عنصر إحصائية
  Widget _buildStatItem(String value, String label, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.white, size: 28),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: const TextStyle(color: Colors.white70, fontSize: 12),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// الإنجازات المفتوحة
  Widget _buildUnlockedAchievements() {
    if (_unlockedAchievements.isEmpty) {
      return _buildEmptyState(
        'لم تحصل على أي إنجازات بعد',
        'ابدأ التعلم لتحصل على إنجازاتك الأولى!',
        Icons.emoji_events_outlined,
      );
    }

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return ListView.builder(
          padding: const EdgeInsets.all(20),
          itemCount: _unlockedAchievements.length,
          itemBuilder: (context, index) {
            final achievement = _unlockedAchievements[index];
            return SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(1, 0),
                end: Offset.zero,
              ).animate(
                CurvedAnimation(
                  parent: _animationController,
                  curve: Interval(
                    index * 0.1,
                    (index * 0.1) + 0.3,
                    curve: Curves.easeOut,
                  ),
                ),
              ),
              child: _buildAchievementCard(achievement, isUnlocked: true),
            );
          },
        );
      },
    );
  }

  /// جميع الإنجازات
  Widget _buildAllAchievements() {
    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: _allAchievements.length,
      itemBuilder: (context, index) {
        final achievementDef = _allAchievements[index];
        final isUnlocked = _unlockedAchievements.any(
          (a) => a.id == achievementDef.id,
        );

        if (isUnlocked) {
          final achievement = _unlockedAchievements.firstWhere(
            (a) => a.id == achievementDef.id,
          );
          return _buildAchievementCard(achievement, isUnlocked: true);
        } else {
          return _buildLockedAchievementCard(achievementDef);
        }
      },
    );
  }

  /// بطاقة الإنجاز
  Widget _buildAchievementCard(
    Achievement achievement, {
    required bool isUnlocked,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isUnlocked ? achievement.color : Colors.grey.shade300,
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color:
                isUnlocked
                    ? achievement.color.withValues(alpha: 0.2)
                    : Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 2,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // أيقونة الإنجاز
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color:
                    isUnlocked
                        ? achievement.color.withValues(alpha: 0.1)
                        : Colors.grey.shade100,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                _getAchievementIcon(achievement.type),
                size: 32,
                color: isUnlocked ? achievement.color : Colors.grey,
              ),
            ),

            const SizedBox(width: 16),

            // معلومات الإنجاز
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    achievement.titleAr,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: isUnlocked ? Colors.black87 : Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    achievement.descriptionAr,
                    style: TextStyle(
                      fontSize: 14,
                      color: isUnlocked ? Colors.grey.shade600 : Colors.grey,
                    ),
                  ),
                  if (isUnlocked) ...[
                    const SizedBox(height: 8),
                    Text(
                      'تم الحصول عليه: ${_formatDate(achievement.earnedAt)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: achievement.color,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ],
              ),
            ),

            // حالة الإنجاز
            if (isUnlocked)
              Icon(Icons.check_circle, color: achievement.color, size: 28)
            else
              Icon(Icons.lock, color: Colors.grey, size: 28),
          ],
        ),
      ),
    );
  }

  /// بطاقة الإنجاز المقفل
  Widget _buildLockedAchievementCard(AchievementDefinition achievementDef) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // أيقونة مقفلة
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(Icons.lock, size: 32, color: Colors.grey),
            ),

            const SizedBox(width: 16),

            // معلومات الإنجاز
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    achievementDef.titleAr,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    achievementDef.descriptionAr,
                    style: const TextStyle(fontSize: 14, color: Colors.grey),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _getProgressText(achievementDef),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
            ),

            Icon(Icons.lock, color: Colors.grey, size: 28),
          ],
        ),
      ),
    );
  }

  /// حالة فارغة
  Widget _buildEmptyState(String title, String subtitle, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 80, color: Colors.grey.shade400),
          const SizedBox(height: 20),
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// الحصول على أيقونة الإنجاز
  IconData _getAchievementIcon(AchievementType type) {
    switch (type) {
      case AchievementType.firstLesson:
        return Icons.school;
      case AchievementType.perfectScore:
        return Icons.star;
      case AchievementType.streakDays:
        return Icons.local_fire_department;
      case AchievementType.categoryMaster:
        return Icons.emoji_events;
      case AchievementType.speedLearner:
        return Icons.flash_on;
      case AchievementType.persistent:
        return Icons.trending_up;
    }
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// نص التقدم للإنجازات المقفلة
  String _getProgressText(AchievementDefinition achievementDef) {
    switch (achievementDef.type) {
      case AchievementType.firstLesson:
        return 'أكمل درسك الأول';
      case AchievementType.perfectScore:
        return 'احصل على نتيجة مثالية';
      case AchievementType.streakDays:
        return 'تعلم لمدة ${achievementDef.requirement} أيام متتالية (الحالي: ${_userStats.currentStreak})';
      case AchievementType.categoryMaster:
        if (achievementDef.id.startsWith('lessons_')) {
          return 'أكمل ${achievementDef.requirement} درس (الحالي: ${_userStats.lessonsCompleted})';
        } else if (achievementDef.id.startsWith('points_')) {
          return 'اجمع ${achievementDef.requirement} نقطة (الحالي: ${_userStats.totalScore})';
        }
        return 'متطلبات خاصة';
      case AchievementType.speedLearner:
        return 'أكمل درساً بسرعة';
      case AchievementType.persistent:
        return 'أكمل ${achievementDef.requirement} درس (الحالي: ${_userStats.lessonsCompleted})';
    }
  }
}
