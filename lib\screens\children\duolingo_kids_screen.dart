import 'package:flutter/material.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:flutter/services.dart';

// نموذج بنية احترافية للوحدات والدروس والتمارين
class LanguageExercise {
  final String type; // 'choice', 'speak', 'listen', 'write'
  final String question;
  final List<String>? choices;
  final int? correctIndex;
  final String? image;
  final String? audio;
  final String? answer;
  LanguageExercise({
    required this.type,
    required this.question,
    this.choices,
    this.correctIndex,
    this.image,
    this.audio,
    this.answer,
  });
}

class LanguageLesson {
  final String title;
  final List<LanguageExercise> exercises;
  LanguageLesson({required this.title, required this.exercises});
}

class LanguageUnit {
  final String name;
  final List<LanguageLesson> lessons;
  LanguageUnit({required this.name, required this.lessons});
}

final Map<String, List<LanguageUnit>> languageUnits = {
  'en': [
    LanguageUnit(
      name: 'Basics',
      lessons: [
        LanguageLesson(
          title: 'Lesson 1',
          exercises: [
            LanguageExercise(
              type: 'choice',
              question: 'What is "cat" in English?',
              choices: ['Cat', 'Dog', 'Bird'],
              correctIndex: 0,
              image: 'assets/images/kids/cat.png',
              audio: 'Cat',
            ),
            LanguageExercise(
              type: 'speak',
              question: 'Say "Dog"',
              audio: 'Dog',
              image: 'assets/images/kids/dog.png',
              answer: 'dog',
            ),
            LanguageExercise(
              type: 'choice',
              question: 'What is "apple" in English?',
              choices: ['Banana', 'Apple', 'Orange'],
              correctIndex: 1,
              image: 'assets/images/kids/apple.png',
              audio: 'Apple',
            ),
          ],
        ),
      ],
    ),
  ],
  'ar': [
    LanguageUnit(
      name: 'الأساسيات',
      lessons: [
        LanguageLesson(
          title: 'الدرس 1',
          exercises: [
            LanguageExercise(
              type: 'choice',
              question: 'ما معنى "قطة" بالعربية؟',
              choices: ['قطة', 'كلب', 'عصفور'],
              correctIndex: 0,
              image: 'assets/images/kids/cat.png',
              audio: 'قطة',
            ),
            LanguageExercise(
              type: 'speak',
              question: 'قل "كلب"',
              audio: 'كلب',
              image: 'assets/images/kids/dog.png',
              answer: 'كلب',
            ),
            LanguageExercise(
              type: 'choice',
              question: 'ما معنى "تفاحة" بالعربية؟',
              choices: ['موز', 'تفاحة', 'برتقال'],
              correctIndex: 1,
              image: 'assets/images/kids/apple.png',
              audio: 'تفاحة',
            ),
          ],
        ),
      ],
    ),
  ],
  'fr': [
    LanguageUnit(
      name: 'Notions de base',
      lessons: [
        LanguageLesson(
          title: 'Leçon 1',
          exercises: [
            LanguageExercise(
              type: 'choice',
              question: 'Comment dit-on "chat" en français ?',
              choices: ['Chat', 'Chien', 'Oiseau'],
              correctIndex: 0,
              image: 'assets/images/kids/cat.png',
              audio: 'Chat',
            ),
            LanguageExercise(
              type: 'speak',
              question: 'Dites "Chien"',
              audio: 'Chien',
              image: 'assets/images/kids/dog.png',
              answer: 'chien',
            ),
            LanguageExercise(
              type: 'choice',
              question: 'Comment dit-on "pomme" en français ?',
              choices: ['Banane', 'Pomme', 'Orange'],
              correctIndex: 1,
              image: 'assets/images/kids/apple.png',
              audio: 'Pomme',
            ),
          ],
        ),
      ],
    ),
  ],
  'es': [
    LanguageUnit(
      name: 'Básico',
      lessons: [
        LanguageLesson(
          title: 'Lección 1',
          exercises: [
            LanguageExercise(
              type: 'choice',
              question: '¿Cómo se dice "gato" en español?',
              choices: ['Gato', 'Perro', 'Pájaro'],
              correctIndex: 0,
              image: 'assets/images/kids/cat.png',
              audio: 'Gato',
            ),
            LanguageExercise(
              type: 'speak',
              question: 'Di "Perro"',
              audio: 'Perro',
              image: 'assets/images/kids/dog.png',
              answer: 'perro',
            ),
            LanguageExercise(
              type: 'choice',
              question: '¿Cómo se dice "manzana" en español?',
              choices: ['Banana', 'Manzana', 'Naranja'],
              correctIndex: 1,
              image: 'assets/images/kids/apple.png',
              audio: 'Manzana',
            ),
          ],
        ),
      ],
    ),
  ],
  'de': [
    LanguageUnit(
      name: 'Grundlagen',
      lessons: [
        LanguageLesson(
          title: 'Lektion 1',
          exercises: [
            LanguageExercise(
              type: 'choice',
              question: 'Was heißt "Katze" auf Deutsch?',
              choices: ['Katze', 'Hund', 'Vogel'],
              correctIndex: 0,
              image: 'assets/images/kids/cat.png',
              audio: 'Katze',
            ),
            LanguageExercise(
              type: 'speak',
              question: 'Sag "Hund"',
              audio: 'Hund',
              image: 'assets/images/kids/dog.png',
              answer: 'hund',
            ),
            LanguageExercise(
              type: 'choice',
              question: 'Was heißt "Apfel" auf Deutsch?',
              choices: ['Banane', 'Apfel', 'Orange'],
              correctIndex: 1,
              image: 'assets/images/kids/apple.png',
              audio: 'Apfel',
            ),
          ],
        ),
      ],
    ),
  ],
  'it': [
    LanguageUnit(
      name: 'Nozioni di base',
      lessons: [
        LanguageLesson(
          title: 'Lezione 1',
          exercises: [
            LanguageExercise(
              type: 'choice',
              question: 'Come si dice "gatto" in italiano?',
              choices: ['Gatto', 'Cane', 'Uccello'],
              correctIndex: 0,
              image: 'assets/images/kids/cat.png',
              audio: 'Gatto',
            ),
            LanguageExercise(
              type: 'speak',
              question: 'Dì "Cane"',
              audio: 'Cane',
              image: 'assets/images/kids/dog.png',
              answer: 'cane',
            ),
            LanguageExercise(
              type: 'choice',
              question: 'Come si dice "mela" in italiano?',
              choices: ['Banana', 'Mela', 'Arancia'],
              correctIndex: 1,
              image: 'assets/images/kids/apple.png',
              audio: 'Mela',
            ),
          ],
        ),
      ],
    ),
  ],
  'tr': [
    LanguageUnit(
      name: 'Temel',
      lessons: [
        LanguageLesson(
          title: 'Ders 1',
          exercises: [
            LanguageExercise(
              type: 'choice',
              question: '"Kedi" Türkçede nedir?',
              choices: ['Kedi', 'Köpek', 'Kuş'],
              correctIndex: 0,
              image: 'assets/images/kids/cat.png',
              audio: 'Kedi',
            ),
            LanguageExercise(
              type: 'speak',
              question: '"Köpek" de',
              audio: 'Köpek',
              image: 'assets/images/kids/dog.png',
              answer: 'köpek',
            ),
            LanguageExercise(
              type: 'choice',
              question: '"Elma" Türkçede nedir?',
              choices: ['Muz', 'Elma', 'Portakal'],
              correctIndex: 1,
              image: 'assets/images/kids/apple.png',
              audio: 'Elma',
            ),
          ],
        ),
      ],
    ),
  ],
  'hi': [
    LanguageUnit(
      name: 'मूल बातें',
      lessons: [
        LanguageLesson(
          title: 'पाठ 1',
          exercises: [
            LanguageExercise(
              type: 'choice',
              question: 'हिंदी में "बिल्ली" क्या है?',
              choices: ['बिल्ली', 'कुत्ता', 'पक्षी'],
              correctIndex: 0,
              image: 'assets/images/kids/cat.png',
              audio: 'बिल्ली',
            ),
            LanguageExercise(
              type: 'speak',
              question: '"कुत्ता" बोलो',
              audio: 'कुत्ता',
              image: 'assets/images/kids/dog.png',
              answer: 'कुत्ता',
            ),
            LanguageExercise(
              type: 'choice',
              question: 'हिंदी में "सेब" क्या है?',
              choices: ['केला', 'सेब', 'संतरा'],
              correctIndex: 1,
              image: 'assets/images/kids/apple.png',
              audio: 'सेब',
            ),
          ],
        ),
      ],
    ),
  ],
  'ko': [
    LanguageUnit(
      name: '기초',
      lessons: [
        LanguageLesson(
          title: '레슨 1',
          exercises: [
            LanguageExercise(
              type: 'choice',
              question: '한국어로 "고양이"는?',
              choices: ['고양이', '개', '새'],
              correctIndex: 0,
              image: 'assets/images/kids/cat.png',
              audio: '고양이',
            ),
            LanguageExercise(
              type: 'speak',
              question: '"개"를 말하세요',
              audio: '개',
              image: 'assets/images/kids/dog.png',
              answer: '개',
            ),
            LanguageExercise(
              type: 'choice',
              question: '한국어로 "사과"는?',
              choices: ['바나나', '사과', '오렌지'],
              correctIndex: 1,
              image: 'assets/images/kids/apple.png',
              audio: '사과',
            ),
          ],
        ),
      ],
    ),
  ],
  'zh': [
    LanguageUnit(
      name: '基础',
      lessons: [
        LanguageLesson(
          title: '第1课',
          exercises: [
            LanguageExercise(
              type: 'choice',
              question: '中文里“猫”怎么说？',
              choices: ['猫', '狗', '鸟'],
              correctIndex: 0,
              image: 'assets/images/kids/cat.png',
              audio: '猫',
            ),
            LanguageExercise(
              type: 'speak',
              question: '说“狗”',
              audio: '狗',
              image: 'assets/images/kids/dog.png',
              answer: '狗',
            ),
            LanguageExercise(
              type: 'choice',
              question: '中文里“苹果”怎么说？',
              choices: ['香蕉', '苹果', '橙子'],
              correctIndex: 1,
              image: 'assets/images/kids/apple.png',
              audio: '苹果',
            ),
          ],
        ),
      ],
    ),
  ],
  'ja': [
    LanguageUnit(
      name: '基本',
      lessons: [
        LanguageLesson(
          title: 'レッスン1',
          exercises: [
            LanguageExercise(
              type: 'choice',
              question: '日本語で「猫」は？',
              choices: ['猫', '犬', '鳥'],
              correctIndex: 0,
              image: 'assets/images/kids/cat.png',
              audio: '猫',
            ),
            LanguageExercise(
              type: 'speak',
              question: '「犬」と言ってください',
              audio: '犬',
              image: 'assets/images/kids/dog.png',
              answer: '犬',
            ),
            LanguageExercise(
              type: 'choice',
              question: '日本語で「りんご」は？',
              choices: ['バナナ', 'りんご', 'オレンジ'],
              correctIndex: 1,
              image: 'assets/images/kids/apple.png',
              audio: 'りんご',
            ),
          ],
        ),
      ],
    ),
  ],
  // الصف الثالث الإعدادي - الترم الأول (منهج مصر)
  'egypt_3rd_prep_term1': [
    LanguageUnit(
      name: 'Unit 1: Welcome Back!',
      lessons: [
        LanguageLesson(
          title: 'Lesson 1: At School',
          exercises: [
            LanguageExercise(
              type: 'choice',
              question: 'What do you say to your teacher in the morning?',
              choices: ['Good morning', 'Good night', 'Goodbye'],
              correctIndex: 0,
              image: 'assets/images/kids/school_teacher.png',
              audio: 'Good morning, teacher!',
            ),
            LanguageExercise(
              type: 'speak',
              question: 'Say: Welcome back!',
              audio: 'Welcome back!',
              image: 'assets/images/kids/welcome.png',
              answer: 'welcome back',
            ),
            LanguageExercise(
              type: 'choice',
              question: 'Which item do you use to write?',
              choices: ['Pencil', 'Eraser', 'Ruler'],
              correctIndex: 0,
              image: 'assets/images/kids/pencil.png',
              audio: 'Pencil',
            ),
          ],
        ),
        // ... أضف جميع دروس الوحدة 1 ...
      ],
    ),
    // ... أضف جميع وحدات الترم الأول ...
  ],
  // الصف الثالث الإعدادي - الترم الثاني (منهج مصر)
  'egypt_3rd_prep_term2': [
    LanguageUnit(
      name: 'Unit 7: Our World',
      lessons: [
        LanguageLesson(
          title: 'Lesson 1: The Environment',
          exercises: [
            LanguageExercise(
              type: 'choice',
              question: 'What should we do to protect the environment?',
              choices: ['Throw rubbish', 'Plant trees', 'Waste water'],
              correctIndex: 1,
              image: 'assets/images/kids/plant_tree.png',
              audio: 'Plant trees',
            ),
            LanguageExercise(
              type: 'speak',
              question: 'Say: Save the environment!',
              audio: 'Save the environment!',
              image: 'assets/images/kids/environment.png',
              answer: 'save the environment',
            ),
            LanguageExercise(
              type: 'choice',
              question: 'Which is good for the environment?',
              choices: ['Recycling', 'Burning plastic', 'Littering'],
              correctIndex: 0,
              image: 'assets/images/kids/recycle.png',
              audio: 'Recycling',
            ),
          ],
        ),
        // ... أضف جميع دروس الوحدة 7 ...
      ],
    ),
    // ... أضف جميع وحدات الترم الثاني ...
  ],
  // الصف الثاني الإعدادي - المنهج الجديد (مصر)
  'egypt_2nd_prep_new': [
    LanguageUnit(
      name: 'Unit 1: My Family',
      lessons: [
        LanguageLesson(
          title: 'Lesson 1: Family Members',
          exercises: [
            LanguageExercise(
              type: 'choice',
              question: 'Who is your father’s brother?',
              choices: ['Uncle', 'Aunt', 'Cousin'],
              correctIndex: 0,
              image: 'assets/images/kids/uncle.png',
              audio: 'Uncle',
            ),
            LanguageExercise(
              type: 'speak',
              question: 'Say: This is my family.',
              audio: 'This is my family.',
              image: 'assets/images/kids/family.png',
              answer: 'this is my family',
            ),
            LanguageExercise(
              type: 'choice',
              question: 'Who is your mother’s daughter?',
              choices: ['Sister', 'Brother', 'Grandmother'],
              correctIndex: 0,
              image: 'assets/images/kids/sister.png',
              audio: 'Sister',
            ),
          ],
        ),
        // ... أضف جميع دروس الوحدة 1 ...
      ],
    ),
    // ... أضف جميع وحدات المنهج الجديد ...
  ],
  // الصف الثالث الثانوي أدبي (مصر)
  'egypt_3rd_secondary_lit': [
    LanguageUnit(
      name: 'Unit 1: Literature and Life',
      lessons: [
        LanguageLesson(
          title: 'Lesson 1: The Power of Words',
          exercises: [
            LanguageExercise(
              type: 'choice',
              question: 'What does literature teach us?',
              choices: ['Math', 'Life lessons', 'Sports'],
              correctIndex: 1,
              image: 'assets/images/kids/literature.png',
              audio: 'Life lessons',
            ),
            LanguageExercise(
              type: 'speak',
              question: 'Say: Literature is important.',
              audio: 'Literature is important.',
              image: 'assets/images/kids/literature.png',
              answer: 'literature is important',
            ),
            LanguageExercise(
              type: 'choice',
              question: 'Who is a famous English writer?',
              choices: ['Shakespeare', 'Newton', 'Einstein'],
              correctIndex: 0,
              image: 'assets/images/kids/shakespeare.png',
              audio: 'Shakespeare',
            ),
          ],
        ),
        // ... أضف جميع دروس الوحدة 1 ...
      ],
    ),
    // ... أضف جميع وحدات الصف الثالث الثانوي أدبي ...
  ],
};

/// شاشة تعليم اللغات للأطفال - شبيهة بدولينجو
class DuolingoKidsScreen extends StatefulWidget {
  final String? initialLanguage;
  const DuolingoKidsScreen({super.key, this.initialLanguage});
  @override
  State<DuolingoKidsScreen> createState() => _DuolingoKidsScreenState();
}

class _DuolingoKidsScreenState extends State<DuolingoKidsScreen> {
  final FlutterTts _tts = FlutterTts();
  String? _selectedLanguage;
  int _currentUnit = 0;
  int _currentLesson = 0;
  int _currentExercise = 0;
  int _score = 0;
  bool _isSpeaking = false;
  bool _showReward = false;
  bool _showCongrats = false;
  List<int> _stars = [];

  @override
  void initState() {
    super.initState();
    _selectedLanguage = widget.initialLanguage;
    _stars = List.filled(100, 0); // max lessons
  }

  @override
  void dispose() {
    _tts.stop();
    super.dispose();
  }

  void _selectLanguage(String lang) {
    setState(() {
      _selectedLanguage = lang;
      _currentUnit = 0;
      _currentLesson = 0;
      _currentExercise = 0;
      _score = 0;
      _showReward = false;
      _showCongrats = false;
      _stars = List.filled(100, 0);
    });
  }

  Future<void> _speak(String text) async {
    setState(() => _isSpeaking = true);
    // استخدم كود اللغة المختار
    String lang = _selectedLanguage ?? 'en';
    // دعم النطق العربي بشكل خاص
    if (lang == 'ar') {
      await _tts.setLanguage('ar-SA');
    } else if (lang == 'fr') {
      await _tts.setLanguage('fr-FR');
    } else if (lang == 'es') {
      await _tts.setLanguage('es-ES');
    } else if (lang == 'de') {
      await _tts.setLanguage('de-DE');
    } else if (lang == 'it') {
      await _tts.setLanguage('it-IT');
    } else if (lang == 'tr') {
      await _tts.setLanguage('tr-TR');
    } else if (lang == 'hi') {
      await _tts.setLanguage('hi-IN');
    } else if (lang == 'ko') {
      await _tts.setLanguage('ko-KR');
    } else if (lang == 'zh') {
      await _tts.setLanguage('zh-CN');
    } else if (lang == 'ja') {
      await _tts.setLanguage('ja-JP');
    } else {
      await _tts.setLanguage('en-US');
    }
    await _tts.setSpeechRate(0.5);
    await _tts.speak(text);
    setState(() => _isSpeaking = false);
  }

  void _nextExercise(bool correct) {
    final units = languageUnits[_selectedLanguage!]!;
    final lessons = units[_currentUnit].lessons;
    final exercises = lessons[_currentLesson].exercises;
    setState(() {
      if (correct) {
        _score += 5;
        _stars[_currentLesson] = (_stars[_currentLesson] + 1).clamp(0, 3);
      }
      if (_currentExercise < exercises.length - 1) {
        _currentExercise++;
      } else if (_currentLesson < lessons.length - 1) {
        _currentLesson++;
        _currentExercise = 0;
        _showReward = true;
      } else if (_currentUnit < units.length - 1) {
        _currentUnit++;
        _currentLesson = 0;
        _currentExercise = 0;
        _showCongrats = true;
      } else {
        _showCongrats = true;
      }
    });
  }

  void _closeReward() {
    setState(() => _showReward = false);
  }

  void _closeCongrats() {
    setState(() => _showCongrats = false);
  }

  @override
  Widget build(BuildContext context) {
    if (_selectedLanguage == null) {
      // عرض أقسام اللغات والمناهج المصرية بشكل واضح
      final Map<String, String> customSections = {
        'en': 'English (Kids)',
        'ar': 'العربية (أطفال)',
        'fr': 'Français (Kids)',
        'es': 'Español (Kids)',
        'de': 'Deutsch (Kids)',
        'it': 'Italiano (Kids)',
        'tr': 'Türkçe (Kids)',
        'hi': 'Hindi (Kids)',
        'ko': 'Korean (Kids)',
        'zh': 'Chinese (Kids)',
        'ja': 'Japanese (Kids)',
        'egypt_3rd_prep_term1': '🇪🇬 الصف الثالث الإعدادي ترم أول',
        'egypt_3rd_prep_term2': '🇪🇬 الصف الثالث الإعدادي ترم ثانٍ',
        'egypt_2nd_prep_new': '🇪🇬 الصف الثاني الإعدادي (منهج جديد)',
        'egypt_3rd_secondary_lit': '🇪🇬 الصف الثالث الثانوي أدبي',
      };
      return Scaffold(
        appBar: AppBar(
          title: const Text('اختر القسم أو المنهج'),
          backgroundColor: Colors.purple.shade400,
        ),
        body: Center(
          child: Wrap(
            spacing: 16,
            runSpacing: 16,
            children:
                customSections.keys
                    .map(
                      (key) => ElevatedButton(
                        onPressed: () => _selectLanguage(key),
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              key.startsWith('egypt_')
                                  ? Colors.orange.shade400
                                  : Colors.purple.shade400,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                        ),
                        child: Text(
                          customSections[key]!,
                          style: const TextStyle(fontSize: 18),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    )
                    .toList(),
          ),
        ),
      );
    }
    final units = languageUnits[_selectedLanguage!]!;
    final lessons = units[_currentUnit].lessons;
    final exercises = lessons[_currentLesson].exercises;
    final exercise = exercises[_currentExercise];
    return Scaffold(
      appBar: AppBar(
        title: Text('تعلم ${_selectedLanguage!.toUpperCase()}'),
        backgroundColor: Colors.purple.shade400,
        actions: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                const Icon(Icons.star, color: Colors.amber),
                Text(
                  '$_score',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
        ],
      ),
      body: Stack(
        children: [
          Container(
            width: double.infinity,
            height: double.infinity,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [Color(0xFFe3f0ff), Color(0xFFf7fbff)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),
          Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(12),
                child: Row(
                  children: [
                    for (int i = 0; i < lessons.length; i++)
                      Icon(
                        _stars[i] > 0 ? Icons.star : Icons.star_border,
                        color: Colors.amber,
                      ),
                    const Spacer(),
                    Text(
                      'الوحدة: ${units[_currentUnit].name}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(8),
                child: Text(
                  'الدرس: ${lessons[_currentLesson].title}',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.purple,
                  ),
                ),
              ),
              Expanded(
                child: Center(
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 400),
                    curve: Curves.easeInOut,
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(32),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.purple.withOpacity(0.08),
                          blurRadius: 24,
                          offset: const Offset(0, 8),
                        ),
                      ],
                    ),
                    width: 350,
                    child: _buildExerciseWidget(exercise),
                  ),
                ),
              ),
            ],
          ),
          if (_showReward)
            GestureDetector(
              onTap: _closeReward,
              child: Container(
                color: Colors.black.withOpacity(0.3),
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.all(32),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(32),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.amber.withOpacity(0.2),
                          blurRadius: 24,
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: const [
                        Text('🎉', style: TextStyle(fontSize: 48)),
                        SizedBox(height: 16),
                        Text(
                          'أحسنت! أنهيت الدرس!',
                          style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                            color: Colors.purple,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(
                          'لقد حصلت على نجمة ونقاط إضافية!',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.blueGrey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          if (_showCongrats)
            GestureDetector(
              onTap: _closeCongrats,
              child: Container(
                color: Colors.black.withOpacity(0.3),
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.all(32),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(32),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.green.withOpacity(0.2),
                          blurRadius: 24,
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: const [
                        Text('🏆', style: TextStyle(fontSize: 48)),
                        SizedBox(height: 16),
                        Text(
                          'مبروك! أنهيت الوحدة!',
                          style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(
                          'واصل التعلم للوصول للاحتراف!',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.blueGrey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildExerciseWidget(LanguageExercise exercise) {
    if (exercise.type == 'choice') {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (exercise.image != null) Image.asset(exercise.image!, height: 80),
          const SizedBox(height: 12),
          Text(
            exercise.question,
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          ...(exercise.choices ?? []).asMap().entries.map(
            (entry) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: ElevatedButton(
                onPressed:
                    () => _nextExercise(entry.key == exercise.correctIndex),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple.shade100,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                child: Text(entry.value, style: const TextStyle(fontSize: 18)),
              ),
            ),
          ),
          const SizedBox(height: 12),
          IconButton(
            icon: Icon(
              Icons.volume_up,
              color: Colors.purple.shade400,
              size: 32,
            ),
            onPressed:
                _isSpeaking
                    ? null
                    : () => _speak(exercise.audio ?? exercise.question),
            tooltip: 'استمع للنطق',
          ),
        ],
      );
    } else if (exercise.type == 'speak' || exercise.type == 'listen') {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (exercise.image != null) Image.asset(exercise.image!, height: 80),
          const SizedBox(height: 12),
          Text(
            exercise.question,
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          IconButton(
            icon: Icon(
              Icons.volume_up,
              color: Colors.purple.shade400,
              size: 32,
            ),
            onPressed:
                _isSpeaking
                    ? null
                    : () => _speak(exercise.audio ?? exercise.question),
            tooltip: 'استمع للنطق',
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => _nextExercise(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green.shade400,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
            ),
            child: const Text('تم التكرار', style: TextStyle(fontSize: 18)),
          ),
        ],
      );
    } else {
      return const Text('تمرين غير مدعوم بعد');
    }
  }
}
