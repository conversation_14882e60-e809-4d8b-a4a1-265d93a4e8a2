@echo off
echo ========================================
echo    Firebase Deployment Script
echo    AI Smart Translator New Project
echo    Project ID: ar-project-4063d
echo ========================================

echo.
echo [1/6] Cleaning previous builds...
call flutter clean

echo.
echo [2/6] Getting dependencies...
call flutter pub get
if %errorlevel% neq 0 (
    echo Error: Failed to get dependencies!
    pause
    exit /b 1
)

echo.
echo [3/6] Building Flutter Web App (Production)...
echo This may take several minutes...
call flutter build web --release --base-href / --pwa-strategy offline-first
if %errorlevel% neq 0 (
    echo Error: Flutter build failed!
    pause
    exit /b 1
)

echo.
echo [4/6] Deploying Firestore Rules...
call firebase deploy --only firestore:rules
if %errorlevel% neq 0 (
    echo Warning: Firestore rules deployment failed!
)

echo.
echo [5/6] Deploying Firestore Indexes...
call firebase deploy --only firestore:indexes
if %errorlevel% neq 0 (
    echo Warning: Firestore indexes deployment failed!
)

echo.
echo [6/6] Deploying Web App to Firebase Hosting...
call firebase deploy --only hosting
if %errorlevel% neq 0 (
    echo Error: Hosting deployment failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo    Deployment Completed Successfully!
echo ========================================
echo.
echo Your app is now live at:
echo https://ar-project-4063d.web.app
echo.
pause
