@echo off
echo ========================================
echo    Project Setup Script
echo    AI Smart Translator New Project
echo    Project ID: ar-project-4063d
echo ========================================

echo.
echo [1/6] Setting Firebase project...
call firebase use ar-project-4063d
if %errorlevel% neq 0 (
    echo Error: Failed to set Firebase project!
    echo Please make sure you have access to ar-project-4063d
    pause
    exit /b 1
)

echo.
echo [2/6] Getting Flutter dependencies...
call flutter pub get
if %errorlevel% neq 0 (
    echo Error: Flutter pub get failed!
    pause
    exit /b 1
)

echo.
echo [3/6] Cleaning previous builds...
call flutter clean
if %errorlevel% neq 0 (
    echo Warning: Flutter clean failed!
)

echo.
echo [4/6] Deploying Firestore rules...
call firebase deploy --only firestore:rules
if %errorlevel% neq 0 (
    echo Warning: Firestore rules deployment failed!
)

echo.
echo [5/6] Deploying Firestore indexes...
call firebase deploy --only firestore:indexes
if %errorlevel% neq 0 (
    echo Warning: Firestore indexes deployment failed!
)

echo.
echo [6/6] Deploying Storage rules...
echo Note: Storage needs to be enabled manually in Firebase Console
echo Skipping Storage rules deployment for now...

echo.
echo ========================================
echo    Project Setup Completed!
echo ========================================
echo.
echo Next steps:
echo 1. Run: flutter run (for development)
echo 2. Run: ./deploy.bat (for web deployment)
echo 3. Run: ./update-config.bat (to update Remote Config)
echo.
echo Project URL: https://ar-project-4063d.web.app
echo Firebase Console: https://console.firebase.google.com/project/ar-project-4063d
echo.
pause
