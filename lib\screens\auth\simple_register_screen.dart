import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../config/app_theme.dart';
import '../../utils/app_helpers.dart';
import '../home/<USER>';
import 'simple_login_screen.dart';

/// شاشة إنشاء حساب جديد مبسطة وموثوقة
class SimpleRegisterScreen extends StatefulWidget {
  const SimpleRegisterScreen({super.key});

  @override
  State<SimpleRegisterScreen> createState() => _SimpleRegisterScreenState();
}

class _SimpleRegisterScreenState extends State<SimpleRegisterScreen> {
  // متحكمات حقول الإدخال
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  // مفتاح النموذج للتحقق من صحة الإدخال
  final _formKey = GlobalKey<FormState>();

  // حالة تحميل إنشاء الحساب
  bool _isLoading = false;

  // حالة إظهار كلمة المرور
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  // حالة الموافقة على الشروط
  bool _agreeToTerms = false;

  // مثيل Firebase Auth
  final _auth = FirebaseAuth.instance;

  // مثيل Firestore
  final _firestore = FirebaseFirestore.instance;

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppTheme.onBackground),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // العنوان
                  _buildTitle(),
                  const SizedBox(height: 32),

                  // حقل الاسم
                  _buildNameField(),
                  const SizedBox(height: 16),

                  // حقل البريد الإلكتروني
                  _buildEmailField(),
                  const SizedBox(height: 16),

                  // حقل كلمة المرور
                  _buildPasswordField(),
                  const SizedBox(height: 16),

                  // حقل تأكيد كلمة المرور
                  _buildConfirmPasswordField(),
                  const SizedBox(height: 16),

                  // موافقة على الشروط
                  _buildTermsCheckbox(),
                  const SizedBox(height: 32),

                  // زر إنشاء الحساب
                  _buildRegisterButton(),
                  const SizedBox(height: 24),

                  // رابط تسجيل الدخول
                  _buildLoginLink(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// بناء العنوان
  Widget _buildTitle() {
    return Column(
      children: [
        Text(
          'إنشاء حساب جديد',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: AppTheme.onBackground,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'أنشئ حسابك للوصول إلى جميع ميزات التطبيق',
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 16, color: AppTheme.onSurfaceVariant),
        ),
      ],
    );
  }

  /// بناء حقل الاسم
  Widget _buildNameField() {
    return TextFormField(
      controller: _nameController,
      decoration: InputDecoration(
        labelText: 'الاسم الكامل',
        prefixIcon: const Icon(Icons.person_outline),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى إدخال الاسم الكامل';
        }
        if (value.length < 2) {
          return 'الاسم يجب أن يكون حرفين على الأقل';
        }
        return null;
      },
    );
  }

  /// بناء حقل البريد الإلكتروني
  Widget _buildEmailField() {
    return TextFormField(
      controller: _emailController,
      keyboardType: TextInputType.emailAddress,
      textDirection: TextDirection.ltr,
      decoration: InputDecoration(
        labelText: 'البريد الإلكتروني',
        hintText: '<EMAIL>',
        prefixIcon: const Icon(Icons.email_outlined),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى إدخال البريد الإلكتروني';
        }
        if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
          return 'يرجى إدخال بريد إلكتروني صالح';
        }
        return null;
      },
    );
  }

  /// بناء حقل كلمة المرور
  Widget _buildPasswordField() {
    return TextFormField(
      controller: _passwordController,
      obscureText: _obscurePassword,
      decoration: InputDecoration(
        labelText: 'كلمة المرور',
        prefixIcon: const Icon(Icons.lock_outline),
        suffixIcon: IconButton(
          icon: Icon(
            _obscurePassword ? Icons.visibility : Icons.visibility_off,
          ),
          onPressed: () {
            setState(() {
              _obscurePassword = !_obscurePassword;
            });
          },
        ),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى إدخال كلمة المرور';
        }
        if (value.length < 6) {
          return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
        }
        return null;
      },
    );
  }

  /// بناء حقل تأكيد كلمة المرور
  Widget _buildConfirmPasswordField() {
    return TextFormField(
      controller: _confirmPasswordController,
      obscureText: _obscureConfirmPassword,
      decoration: InputDecoration(
        labelText: 'تأكيد كلمة المرور',
        prefixIcon: const Icon(Icons.lock_outline),
        suffixIcon: IconButton(
          icon: Icon(
            _obscureConfirmPassword ? Icons.visibility : Icons.visibility_off,
          ),
          onPressed: () {
            setState(() {
              _obscureConfirmPassword = !_obscureConfirmPassword;
            });
          },
        ),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى تأكيد كلمة المرور';
        }
        if (value != _passwordController.text) {
          return 'كلمة المرور غير متطابقة';
        }
        return null;
      },
    );
  }

  /// بناء مربع الموافقة على الشروط
  Widget _buildTermsCheckbox() {
    return Row(
      children: [
        Checkbox(
          value: _agreeToTerms,
          onChanged: (value) {
            setState(() {
              _agreeToTerms = value ?? false;
            });
          },
        ),
        Expanded(
          child: Text(
            'أوافق على شروط الاستخدام وسياسة الخصوصية',
            style: TextStyle(fontSize: 14, color: AppTheme.onSurfaceVariant),
          ),
        ),
      ],
    );
  }

  /// بناء زر إنشاء الحساب
  Widget _buildRegisterButton() {
    return ElevatedButton(
      onPressed: (_isLoading || !_agreeToTerms) ? null : _register,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        elevation: 2,
      ),
      child:
          _isLoading
              ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
              : const Text(
                'إنشاء الحساب',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
    );
  }

  /// بناء رابط تسجيل الدخول
  Widget _buildLoginLink() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Text('لديك حساب بالفعل؟'),
        TextButton(
          onPressed: () {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (_) => const SimpleLoginScreen()),
            );
          },
          child: const Text('تسجيل الدخول'),
        ),
      ],
    );
  }

  /// إنشاء حساب جديد
  Future<void> _register() async {
    // التحقق من صحة النموذج
    if (_formKey.currentState == null || !_formKey.currentState!.validate()) {
      return;
    }

    if (!_agreeToTerms) {
      AppHelpers.showSnackBar(
        context,
        'يرجى الموافقة على شروط الاستخدام وسياسة الخصوصية',
        isError: true,
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // إنشاء حساب جديد
      final userCredential = await _auth.createUserWithEmailAndPassword(
        email: _emailController.text.trim(),
        password: _passwordController.text.trim(),
      );

      // تحديث اسم المستخدم
      await userCredential.user?.updateDisplayName(_nameController.text.trim());

      // إنشاء وثيقة المستخدم في Firestore
      await _firestore.collection('users').doc(userCredential.user?.uid).set({
        'name': _nameController.text.trim(),
        'email': _emailController.text.trim(),
        'createdAt': FieldValue.serverTimestamp(),
        'lastLoginAt': FieldValue.serverTimestamp(),
        'isEmailVerified': false,
        'isAdsRemoved': false,
      });

      // إرسال رسالة تأكيد البريد الإلكتروني
      await userCredential.user?.sendEmailVerification();

      // عرض رسالة نجاح
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'تم إنشاء الحساب بنجاح! يرجى تأكيد بريدك الإلكتروني.',
        );

        // الانتقال إلى الشاشة الرئيسية
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const HomeScreen()),
        );
      }
    } on FirebaseAuthException catch (e) {
      // معالجة أخطاء إنشاء الحساب
      String errorMessage;

      switch (e.code) {
        case 'email-already-in-use':
          errorMessage = 'البريد الإلكتروني مستخدم بالفعل';
          break;
        case 'invalid-email':
          errorMessage = 'البريد الإلكتروني غير صالح';
          break;
        case 'weak-password':
          errorMessage = 'كلمة المرور ضعيفة جدًا';
          break;
        case 'operation-not-allowed':
          errorMessage = 'تسجيل البريد الإلكتروني وكلمة المرور غير مفعل';
          break;
        default:
          errorMessage = 'حدث خطأ أثناء إنشاء الحساب: ${e.message}';
      }

      // عرض رسالة الخطأ
      if (mounted) {
        AppHelpers.showSnackBar(context, errorMessage, isError: true);
      }
    } catch (e) {
      // معالجة الأخطاء الأخرى
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء إنشاء الحساب: $e',
          isError: true,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
