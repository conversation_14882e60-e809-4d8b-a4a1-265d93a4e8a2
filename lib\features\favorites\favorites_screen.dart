import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../core/app_state.dart';
import '../../config/app_theme.dart';
import '../../widgets/enhanced_app_bar.dart';
import '../../widgets/enhanced_list_item_fixed.dart';
import '../../utils/helpers.dart';
import '../../models/translation_item.dart';

/// شاشة المفضلة
class FavoritesScreen extends StatefulWidget {
  const FavoritesScreen({super.key});

  @override
  State<FavoritesScreen> createState() => _FavoritesScreenState();
}

class _FavoritesScreenState extends State<FavoritesScreen> {
  bool _isLoading = true;
  List<TranslationItem> _favorites = [];
  String _filterType = 'all';

  @override
  void initState() {
    super.initState();
    _loadFavorites();
  }

  /// تحميل العناصر المفضلة
  Future<void> _loadFavorites() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final appState = Provider.of<AppState>(context, listen: false);
      final favorites = await appState.getFavorites();

      setState(() {
        _favorites = favorites;
        _isLoading = false;
      });
    } catch (e) {
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء تحميل المفضلة: $e',
          isError: true,
        );
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// إزالة عنصر من المفضلة
  Future<void> _removeFromFavorites(TranslationItem item) async {
    try {
      final appState = Provider.of<AppState>(context, listen: false);
      await appState.removeFromFavorites(item.id);

      setState(() {
        _favorites.removeWhere((favorite) => favorite.id == item.id);
      });

      if (mounted) {
        AppHelpers.showSnackBar(context, 'تمت إزالة العنصر من المفضلة');
      }
    } catch (e) {
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء إزالة العنصر من المفضلة: $e',
          isError: true,
        );
      }
    }
  }

  /// تصفية العناصر المفضلة حسب النوع
  List<TranslationItem> _getFilteredFavorites() {
    if (_filterType == 'all') {
      return _favorites;
    }
    return _favorites.where((item) => item.type == _filterType).toList();
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return DateFormat('yyyy/MM/dd HH:mm', 'ar').format(date);
  }

  /// الحصول على أيقونة نوع الترجمة
  IconData _getTypeIcon(String type) {
    switch (type) {
      case 'voice':
        return Icons.mic;
      case 'text':
        return Icons.text_fields;
      case 'image':
        return Icons.camera_alt;
      case 'document':
        return Icons.description;
      case 'conversation':
        return Icons.forum;
      default:
        return Icons.translate;
    }
  }

  /// الحصول على اسم نوع الترجمة
  String _getTypeName(String type) {
    switch (type) {
      case 'voice':
        return 'ترجمة صوتية';
      case 'text':
        return 'ترجمة نصية';
      case 'image':
        return 'ترجمة صورة';
      case 'document':
        return 'ترجمة مستند';
      case 'conversation':
        return 'محادثة';
      default:
        return 'ترجمة';
    }
  }

  @override
  Widget build(BuildContext context) {
    final filteredFavorites = _getFilteredFavorites();

    return Scaffold(
      appBar: EnhancedAppBar(
        title: const Text('المفضلة'),
        backgroundColor: AppTheme.primaryColor,
        actions: [
          // زر التصفية
          PopupMenuButton<String>(
            icon: const Icon(Icons.filter_list),
            tooltip: 'تصفية',
            onSelected: (value) {
              setState(() {
                _filterType = value;
              });
            },
            itemBuilder:
                (context) => [
                  const PopupMenuItem(value: 'all', child: Text('الكل')),
                  const PopupMenuItem(
                    value: 'voice',
                    child: Text('ترجمة صوتية'),
                  ),
                  const PopupMenuItem(value: 'text', child: Text('ترجمة نصية')),
                  const PopupMenuItem(
                    value: 'image',
                    child: Text('ترجمة صورة'),
                  ),
                  const PopupMenuItem(
                    value: 'document',
                    child: Text('ترجمة مستند'),
                  ),
                  const PopupMenuItem(
                    value: 'conversation',
                    child: Text('محادثة'),
                  ),
                ],
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : filteredFavorites.isEmpty
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.favorite_border,
                      size: 80,
                      color: Colors.grey,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      _filterType == 'all'
                          ? 'لا توجد عناصر في المفضلة'
                          : 'لا توجد عناصر من هذا النوع في المفضلة',
                      style: const TextStyle(fontSize: 18, color: Colors.grey),
                    ),
                  ],
                ),
              )
              : ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: filteredFavorites.length,
                itemBuilder: (context, index) {
                  final item = filteredFavorites[index];
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 12),
                    child: EnhancedListItem(
                      leading: CircleAvatar(
                        backgroundColor: AppTheme.primaryColor.withAlpha(26),
                        child: Icon(
                          _getTypeIcon(item.type),
                          color: AppTheme.primaryColor,
                        ),
                      ),
                      title:
                          item.sourceText.length > 50
                              ? '${item.sourceText.substring(0, 50)}...'
                              : item.sourceText,
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            item.translatedText.length > 50
                                ? '${item.translatedText.substring(0, 50)}...'
                                : item.translatedText,
                            style: const TextStyle(color: Colors.grey),
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Text(
                                _getTypeName(item.type),
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                _formatDate(item.timestamp),
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      trailing: IconButton(
                        icon: const Icon(Icons.delete, color: Colors.red),
                        onPressed: () => _removeFromFavorites(item),
                        tooltip: 'إزالة من المفضلة',
                      ),
                      onTap: () {
                        // عرض تفاصيل العنصر
                        _showItemDetails(item);
                      },
                    ),
                  );
                },
              ),
    );
  }

  /// عرض تفاصيل العنصر
  void _showItemDetails(TranslationItem item) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    CircleAvatar(
                      backgroundColor: AppTheme.primaryColor.withAlpha(26),
                      child: Icon(
                        _getTypeIcon(item.type),
                        color: AppTheme.primaryColor,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      _getTypeName(item.type),
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      _formatDate(item.timestamp),
                      style: const TextStyle(fontSize: 14, color: Colors.grey),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                const Text(
                  'النص الأصلي:',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(12),
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    item.sourceText,
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  'الترجمة:',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(12),
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withAlpha(26),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    item.translatedText,
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildActionButton(
                      icon: Icons.copy,
                      label: 'نسخ الأصل',
                      onTap: () {
                        AppHelpers.copyToClipboard(item.sourceText);
                        Navigator.pop(context);
                        AppHelpers.showSnackBar(context, 'تم نسخ النص الأصلي');
                      },
                    ),
                    _buildActionButton(
                      icon: Icons.copy_all,
                      label: 'نسخ الترجمة',
                      onTap: () {
                        AppHelpers.copyToClipboard(item.translatedText);
                        Navigator.pop(context);
                        AppHelpers.showSnackBar(context, 'تم نسخ الترجمة');
                      },
                    ),
                    _buildActionButton(
                      icon: Icons.delete,
                      label: 'إزالة',
                      onTap: () {
                        Navigator.pop(context);
                        _removeFromFavorites(item);
                      },
                      color: Colors.red,
                    ),
                  ],
                ),
              ],
            ),
          ),
    );
  }

  /// بناء زر إجراء
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    Color? color,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          children: [
            Icon(icon, color: color ?? AppTheme.primaryColor, size: 24),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: color ?? AppTheme.primaryColor,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
