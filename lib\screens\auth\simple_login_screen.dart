import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import '../../config/app_theme.dart';
import '../../utils/app_helpers.dart';
import '../home/<USER>';
import 'simple_register_screen.dart';
import 'simple_forgot_password_screen.dart';

/// شاشة تسجيل الدخول المبسطة والموثوقة
class SimpleLoginScreen extends StatefulWidget {
  const SimpleLoginScreen({super.key});

  @override
  State<SimpleLoginScreen> createState() => _SimpleLoginScreenState();
}

/// شاشة تسجيل الدخول (alias للتوافق مع الكود القديم)
class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  @override
  Widget build(BuildContext context) {
    return const SimpleLoginScreen();
  }
}

class _SimpleLoginScreenState extends State<SimpleLoginScreen> {
  // متحكمات حقول الإدخال
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  // مفتاح النموذج للتحقق من صحة الإدخال
  final _formKey = GlobalKey<FormState>();

  // حالة تحميل تسجيل الدخول
  bool _isLoading = false;

  // حالة إظهار كلمة المرور
  bool _obscurePassword = true;

  // مثيل Firebase Auth
  final _auth = FirebaseAuth.instance;

  // مثيل Google Sign In
  final _googleSignIn = GoogleSignIn();

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // الشعار
                  _buildLogo(),
                  const SizedBox(height: 32),

                  // العنوان
                  _buildTitle(),
                  const SizedBox(height: 32),

                  // حقل البريد الإلكتروني
                  _buildEmailField(),
                  const SizedBox(height: 16),

                  // حقل كلمة المرور
                  _buildPasswordField(),
                  const SizedBox(height: 8),

                  // رابط نسيت كلمة المرور
                  _buildForgotPasswordLink(),
                  const SizedBox(height: 32),

                  // زر تسجيل الدخول
                  _buildLoginButton(),
                  const SizedBox(height: 24),

                  // فاصل
                  _buildDivider(),
                  const SizedBox(height: 24),

                  // زر تسجيل الدخول بحساب Google
                  _buildGoogleSignInButton(),
                  const SizedBox(height: 24),

                  // رابط إنشاء حساب جديد
                  _buildSignUpLink(),
                  const SizedBox(height: 16),

                  // زر تخطي تسجيل الدخول
                  _buildSkipButton(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// بناء الشعار
  Widget _buildLogo() {
    return Container(
      height: 120,
      width: 120,
      decoration: BoxDecoration(
        color: AppTheme.primaryColor,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: const Icon(Icons.translate, size: 60, color: Colors.white),
    );
  }

  /// بناء العنوان
  Widget _buildTitle() {
    return Column(
      children: [
        Text(
          'مرحباً بك',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: AppTheme.onBackground,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'تسجيل الدخول',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.w600,
            color: AppTheme.primaryColor,
          ),
        ),
      ],
    );
  }

  /// بناء حقل البريد الإلكتروني
  Widget _buildEmailField() {
    return TextFormField(
      controller: _emailController,
      keyboardType: TextInputType.emailAddress,
      textDirection: TextDirection.ltr,
      decoration: InputDecoration(
        labelText: 'البريد الإلكتروني',
        hintText: '<EMAIL>',
        prefixIcon: const Icon(Icons.email_outlined),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى إدخال البريد الإلكتروني';
        }
        if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
          return 'يرجى إدخال بريد إلكتروني صالح';
        }
        return null;
      },
    );
  }

  /// بناء حقل كلمة المرور
  Widget _buildPasswordField() {
    return TextFormField(
      controller: _passwordController,
      obscureText: _obscurePassword,
      decoration: InputDecoration(
        labelText: 'كلمة المرور',
        prefixIcon: const Icon(Icons.lock_outline),
        suffixIcon: IconButton(
          icon: Icon(
            _obscurePassword ? Icons.visibility : Icons.visibility_off,
          ),
          onPressed: () {
            setState(() {
              _obscurePassword = !_obscurePassword;
            });
          },
        ),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى إدخال كلمة المرور';
        }
        if (value.length < 6) {
          return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
        }
        return null;
      },
    );
  }

  /// بناء رابط نسيت كلمة المرور
  Widget _buildForgotPasswordLink() {
    return Align(
      alignment: Alignment.centerLeft,
      child: TextButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (_) => const SimpleForgotPasswordScreen(),
            ),
          );
        },
        child: const Text('نسيت كلمة المرور؟'),
      ),
    );
  }

  /// بناء زر تسجيل الدخول
  Widget _buildLoginButton() {
    return ElevatedButton(
      onPressed: _isLoading ? null : _login,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        elevation: 2,
      ),
      child:
          _isLoading
              ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
              : const Text(
                'تسجيل الدخول',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
    );
  }

  /// بناء رابط إنشاء حساب جديد
  Widget _buildSignUpLink() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Text('ليس لديك حساب؟'),
        TextButton(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (_) => const SimpleRegisterScreen()),
            );
          },
          child: const Text('إنشاء حساب'),
        ),
      ],
    );
  }

  /// بناء زر تخطي تسجيل الدخول
  Widget _buildSkipButton() {
    return TextButton(
      onPressed: _isLoading ? null : _skipLogin,
      child: const Text('تخطي تسجيل الدخول'),
    );
  }

  /// بناء الفاصل
  Widget _buildDivider() {
    return Row(
      children: [
        Expanded(child: Divider(color: Colors.grey[300])),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text('أو', style: TextStyle(color: Colors.grey[600])),
        ),
        Expanded(child: Divider(color: Colors.grey[300])),
      ],
    );
  }

  /// بناء زر تسجيل الدخول بحساب Google
  Widget _buildGoogleSignInButton() {
    return OutlinedButton.icon(
      onPressed: _isLoading ? null : _signInWithGoogle,
      icon: const Icon(Icons.g_mobiledata, size: 24),
      label: const Text('تسجيل الدخول بحساب Google'),
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        side: BorderSide(color: Colors.grey[300]!),
      ),
    );
  }

  /// تسجيل الدخول
  Future<void> _login() async {
    // التحقق من صحة النموذج
    if (_formKey.currentState == null || !_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // تسجيل الدخول الحقيقي باستخدام Firebase Auth
      final userCredential = await _auth.signInWithEmailAndPassword(
        email: _emailController.text.trim(),
        password: _passwordController.text.trim(),
      );

      if (userCredential.user != null) {
        // تسجيل دخول ناجح
        if (mounted) {
          AppHelpers.showSnackBar(context, 'تم تسجيل الدخول بنجاح!');
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const SimpleHomeScreen()),
          );
        }
      }
    } on FirebaseAuthException catch (e) {
      // معالجة أخطاء Firebase Auth
      String errorMessage;

      switch (e.code) {
        case 'user-not-found':
          errorMessage = 'لم يتم العثور على مستخدم بهذا البريد الإلكتروني';
          break;
        case 'wrong-password':
          errorMessage = 'كلمة المرور غير صحيحة';
          break;
        case 'invalid-email':
          errorMessage = 'البريد الإلكتروني غير صالح';
          break;
        case 'user-disabled':
          errorMessage = 'تم تعطيل هذا الحساب';
          break;
        case 'too-many-requests':
          errorMessage = 'تم تجاوز عدد المحاولات المسموح. يرجى المحاولة لاحقاً';
          break;
        case 'operation-not-allowed':
          errorMessage =
              'تسجيل الدخول بالبريد الإلكتروني وكلمة المرور غير مفعل';
          break;
        default:
          errorMessage = 'حدث خطأ أثناء تسجيل الدخول: ${e.message ?? e.code}';
      }

      if (mounted) {
        AppHelpers.showSnackBar(context, errorMessage, isError: true);
      }
    } catch (e) {
      // معالجة الأخطاء الأخرى
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء تسجيل الدخول: $e',
          isError: true,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// تخطي تسجيل الدخول
  Future<void> _skipLogin() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // الانتقال مباشرة إلى الشاشة الرئيسية بدون مصادقة
      // هذا يسمح للمستخدم باستخدام التطبيق كضيف
      if (mounted) {
        AppHelpers.showSnackBar(context, 'تم الدخول كضيف بنجاح!');
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const SimpleHomeScreen()),
        );
      }
    } catch (e) {
      // معالجة الأخطاء
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء تخطي تسجيل الدخول: $e',
          isError: true,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// تسجيل الدخول بحساب Google
  Future<void> _signInWithGoogle() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // بدء عملية تسجيل الدخول بحساب Google
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        // المستخدم ألغى عملية تسجيل الدخول
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // الحصول على تفاصيل المصادقة
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      // إنشاء بيانات اعتماد Firebase
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // تسجيل الدخول إلى Firebase
      final userCredential = await _auth.signInWithCredential(credential);

      if (userCredential.user != null) {
        // الانتقال إلى الشاشة الرئيسية
        if (mounted) {
          AppHelpers.showSnackBar(
            context,
            'تم تسجيل الدخول بحساب Google بنجاح!',
          );
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const SimpleHomeScreen()),
          );
        }
      }
    } on FirebaseAuthException catch (e) {
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء تسجيل الدخول بحساب Google: ${e.message}',
          isError: true,
        );
      }
    } catch (e) {
      // معالجة الأخطاء
      if (mounted) {
        AppHelpers.showSnackBar(
          context,
          'حدث خطأ أثناء تسجيل الدخول بحساب Google: $e',
          isError: true,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
